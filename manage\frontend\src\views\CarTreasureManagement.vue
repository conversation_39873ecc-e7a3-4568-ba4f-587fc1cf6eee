<template>
  <div class="car-treasure-container">
    <div class="page-header">
      <h2>模拟抽奖-赛车夺宝配置</h2>
      <div class="header-actions">
        <el-button type="success" @click="showAddTreasureDialog">
          <el-icon><Plus /></el-icon>
          新增配置
        </el-button>
        <el-button @click="router.push('/')" type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-box">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="关键词搜索">
          <el-input 
            v-model="searchQuery" 
            placeholder="输入奖品名称搜索" 
            clearable 
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="品质筛选">
          <el-select 
            v-model="searchQuality" 
            placeholder="选择品质筛选" 
            clearable
            style="min-width: 180px"
          >
            <el-option 
              v-for="option in qualityOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型筛选">
          <el-select 
            v-model="searchItemType" 
            placeholder="选择类型筛选" 
            clearable
            style="min-width: 180px"
          >
            <el-option 
              v-for="option in itemTypeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="activeOnly">只显示启用的奖品</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="treasure-table">
      <el-table
        v-loading="loading"
        :data="treasureItems"
        border
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column prop="id" label="ID" width="100" align="center">
          <template #default="scope">
            {{ scope.row?.id || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="赛车名称" width="400">
          <template #default="scope">
            <div class="title-cell">
              <el-icon><Trophy /></el-icon>
              <span>{{ scope.row?.name || '未命名赛车' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="赛车图片" width="300" align="center">
          <template #default="scope">
            <el-image 
              v-if="scope.row?.image_url" 
              :src="getFullImageUrl(scope.row.image_url)" 
              :preview-src-list="[getFullImageUrl(scope.row.image_url)]"
              style="width: 50px; height: 50px; border-radius: 4px;"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div v-else class="image-error">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="quality" label="赛车品质" width="220" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row?.quality" :type="getQualityTagType(scope.row.quality)" size="small">
              {{ getQualityText(scope.row.quality) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="item_type" label="赛车类型" width="200" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row?.item_type" :type="scope.row.item_type === 'fixed' ? 'info' : 'warning'" size="small">
              {{ scope.row.item_type === 'fixed' ? '固定' : '自选' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="probability" label="概率" width="300" align="center">
          <template #default="scope">
            {{ 
              typeof scope.row?.probability === 'string' && scope.row.probability.includes('%')
                ? scope.row.probability
                : typeof scope.row?.probability === 'number' 
                  ? scope.row.probability + '%'
                  : typeof scope.row?.probability === 'string'
                    ? scope.row.probability + '%'
                    : '-' 
            }}
          </template>
        </el-table-column>
        <el-table-column prop="expiry_days" label="期限" width="300" align="center">
          <template #default="scope">
            <span v-if="typeof scope.row?.expiry_days === 'number'">
              {{ scope.row.expiry_days === 0 ? '永久' : `${scope.row.expiry_days}天` }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="fragment_value" label="分解碎片" width="300" align="center">
          <template #default="scope">
            <span v-if="typeof scope.row?.fragment_value === 'number'">
              {{ scope.row.fragment_value || '-' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                type="primary"
                link
                @click="handleViewDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                type="warning"
                link
                @click="handleEditTreasure(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleDeleteTreasure(scope.row)"
              >
                删除
              </el-button>
              <el-button
                :type="scope.row?.is_active ? 'danger' : 'success'"
                link
                @click="handleToggleStatus(scope.row)"
              >
                {{ scope.row?.is_active ? '禁用' : '启用' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="10"
          :total="total"
          :pager-count="7"
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          background
        />
      </div>
    </el-card>

    <!-- 新增/编辑夺宝配置对话框 -->
    <el-dialog
      v-model="treasureDialogVisible"
      :title="isEditMode ? '编辑夺宝配置' : '新增夺宝配置'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="treasureFormRef"
        :model="treasureForm"
        :rules="treasureRules"
        label-width="100px"
      >
        <!-- 添加赛车搜索功能 -->
        <el-form-item label="赛车搜索">
          <div class="search-container">
            <el-autocomplete
              v-model="carSearchQuery"
              :fetch-suggestions="searchCars"
              placeholder="输入赛车名称或编号搜索"
              class="search-input"
              :trigger-on-focus="false"
              :value-key="'name'"
              @select="selectCar"
              clearable
            >
              <template #suffix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
              <template #default="{ item }">
                <div class="car-suggestion-item">
                  <div class="car-name">{{ item.name }}</div>
                  <div class="car-id">ID: {{ item.car_id }}</div>
                </div>
              </template>
            </el-autocomplete>
          </div>
          <div v-if="carSearchError" class="search-error">
            {{ carSearchError }}
          </div>
        </el-form-item>
        
        <el-form-item label="奖品名称" prop="name">
          <el-input v-model="treasureForm.name" placeholder="请输入奖品名称" />
        </el-form-item>
        
        <el-form-item label="奖品图片" prop="image_url">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="treasureForm.image_url" :src="getFullImageUrl(treasureForm.image_url)" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议上传比例1:1的图片，大小不超过2MB</div>
        </el-form-item>
        
        <el-form-item label="中奖概率" prop="probability">
          <el-input
            v-model="treasureForm.probability"
            placeholder="请输入百分比概率，如 0.0002%"
            style="width: 100%"
          />
          <div class="form-tip">直接输入百分比值，例如：0.0002%表示0.0002%的概率</div>
        </el-form-item>
        
        <el-form-item label="奖品品质" prop="quality">
          <el-select v-model="treasureForm.quality" placeholder="请选择奖品品质" style="width: 100%">
            <el-option 
              v-for="option in qualityOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="奖品类型" prop="item_type">
          <el-radio-group v-model="treasureForm.item_type">
            <el-radio 
              v-for="option in itemTypeOptions" 
              :key="option.value" 
              :label="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          <!-- <div class="form-tip">固定奖品为系统指定，自选奖品允许玩家从特定范围内选择</div> -->
        </el-form-item>
        
        <el-form-item label="期限设置" prop="expiry_days">
          <el-select v-model="treasureForm.expiry_days" placeholder="请选择期限" style="width: 100%">
            <el-option 
              v-for="option in expiryOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value"
            />
          </el-select>
          <div class="form-tip">选择奖品的有效期，改装配件可不选择期限</div>
        </el-form-item>
        
        <el-form-item label="分解碎片" prop="fragment_value">
          <el-input-number 
            v-model="treasureForm.fragment_value" 
            :min="0" 
            :max="10000"
            :step="100"
            style="width: 100%"
          />
          <!-- <div class="form-tip">设置奖品分解后可获得的碎片数量，0表示不可分解，建议设置为100的倍数</div> -->
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="treasureForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入奖品描述"
            resize="none"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="treasureDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveTreasure" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 奖品详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="奖品详情"
      width="600px"
    >
      <div v-if="currentTreasure" class="treasure-detail">
        <div class="detail-item">
          <span class="label">奖品名称：</span>
          <span>{{ currentTreasure.name || '未命名奖品' }}</span>
        </div>
        <div class="detail-item" v-if="currentTreasure.car_id">
          <span class="label">赛车ID：</span>
          <span>{{ currentTreasure.car_id }}</span>
        </div>
        <div class="detail-item">
          <span class="label">奖品图片：</span>
          <el-image 
            v-if="currentTreasure.image_url"
            :src="getFullImageUrl(currentTreasure.image_url)" 
            :preview-src-list="[getFullImageUrl(currentTreasure.image_url)]"
            style="width: 80px; height: 80px; border-radius: 4px;"
            fit="cover"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span class="error-text">图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="image-error">
            <el-icon><Picture /></el-icon>
            <span class="error-text">无图片</span>
          </div>
        </div>
        <div class="detail-item">
          <span class="label">中奖概率：</span>
          <span>
            {{ 
              typeof currentTreasure.probability === 'string' && currentTreasure.probability.includes('%')
                ? currentTreasure.probability
                : typeof currentTreasure.probability === 'number' 
                  ? currentTreasure.probability + '%'
                  : typeof currentTreasure.probability === 'string'
                    ? currentTreasure.probability + '%'
                    : '-' 
            }}
          </span>
        </div>
        <div class="detail-item">
          <span class="label">奖品品质：</span>
          <el-tag v-if="currentTreasure.quality" :type="getQualityTagType(currentTreasure.quality)" size="small">
            {{ getQualityText(currentTreasure.quality) }}
          </el-tag>
          <span v-else>-</span>
        </div>
        <div class="detail-item">
          <span class="label">奖品类型：</span>
          <el-tag v-if="currentTreasure.item_type" :type="currentTreasure.item_type === 'fixed' ? 'info' : 'warning'" size="small">
            {{ currentTreasure.item_type === 'fixed' ? '固定' : '自选' }}
          </el-tag>
          <span v-else>-</span>
        </div>
        <div class="detail-item">
          <span class="label">期限：</span>
          <span v-if="typeof currentTreasure.expiry_days === 'number'">
            {{ getExpiryText(currentTreasure.expiry_days) }}
          </span>
          <span v-else>-</span>
        </div>
        <div class="detail-item">
          <span class="label">分解碎片：</span>
          <span>{{ typeof currentTreasure.fragment_value === 'number' && currentTreasure.fragment_value > 0 ? currentTreasure.fragment_value : '不可分解' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">描述：</span>
          <div class="content">{{ currentTreasure.description || '无' }}</div>
        </div>
      </div>
      <div v-else class="no-data">
        <el-icon><Picture /></el-icon>
        <p>无法加载奖品详情</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Back, 
  Trophy, 
  Picture,
  Search
} from '@element-plus/icons-vue'
import { searchCars as searchCarsAPI } from '@/api/car'
import { 
  getTreasureItems, 
  createTreasureItem, 
  updateTreasureItem, 
  deleteTreasureItem,
  toggleTreasureItemStatus
} from '@/api/treasure'

const router = useRouter()
const loading = ref(false)
const treasureItems = ref([])
const currentPage = ref(1)
const total = ref(0)
const treasureDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const saveLoading = ref(false)
const isEditMode = ref(false)
const currentTreasure = ref(null)
const treasureFormRef = ref(null)
const carSearchQuery = ref('')
const carSearchError = ref(null)

// 品质、类型和期限选项
const qualityOptions = ref([
  { value: 'top_s', label: '顶级227S车' },
  { value: 'normal_s', label: '普通S级赛车' },
  { value: 't1_mecha', label: 'T1机甲' },
  { value: 'normal_a', label: '普通A级赛车' },
  { value: 'renault', label: '雷诺类型赛车' },
  { value: 'parts', label: '改装配件' }
])

const itemTypeOptions = ref([
  { value: 'fixed', label: '固定' },
  { value: 'optional', label: '自选' }
])

const expiryOptions = ref([
  { value: 0, label: '永久' },
  { value: 30, label: '30天' },
  { value: 15, label: '15天' },
  { value: 7, label: '7天' }
])

// 搜索筛选条件
const searchQuery = ref('')
const searchQuality = ref('')
const searchItemType = ref('')
const activeOnly = ref(false)

/**
 * 夺宝配置表单
 * @type {import('vue').Ref<{
 *   id: number|null,
 *   name: string,
 *   probability: number,
 *   expiry_days: number,
 *   image_id: string|null,
 *   quality: string,
 *   item_type: string,
 *   fragment_value: number,
 *   is_active: boolean,
 *   description: string
 * }>}
 */
const treasureForm = ref({
  id: null,
  name: '',
  probability: '0%',
  expiry_days: 0, // 0表示永久
  image_id: null,
  image_url: '', // 确保初始化image_url字段
  car_id: '', // 初始化car_id字段
  quality: 'parts', // 默认为改装配件
  item_type: 'fixed', // 默认为固定奖品
  fragment_value: 0,
  is_active: true,
  description: ''
})

/**
 * 表单验证规则
 */
const treasureRules = {
  name: [
    { required: true, message: '请输入奖品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  image_url: [
    { required: true, message: '请上传奖品图片', trigger: 'change' }
  ],
  probability: [
    { required: true, message: '请输入中奖概率', trigger: 'blur' }
  ],
  quality: [
    { required: true, message: '请选择奖品品质', trigger: 'change' }
  ],
  item_type: [
    { required: true, message: '请选择奖品类型', trigger: 'change' }
  ]
  // 期限是可选的，所以不需要验证
}

/**
 * 加载夺宝配置列表
 */
const fetchTreasureItems = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = {
      page: currentPage.value,
      page_size: 10
    }
    
    // 添加搜索参数
    if (searchQuery.value) params.search = searchQuery.value
    if (searchQuality.value) params.quality = searchQuality.value
    if (searchItemType.value) params.item_type = searchItemType.value
    if (activeOnly.value) params.active_only = activeOnly.value
    
    console.log('获取夺宝配置列表参数:', params);
    
    // 调用API获取奖品列表
    const response = await getTreasureItems(params)
    
    console.log('获取夺宝配置列表响应:', response);
    
    if (response && response.success) {
      // 处理返回数据
      let resultsArray = [];
      
      if (response.data && response.data.results && Array.isArray(response.data.results)) {
        // 新格式: data.results中包含数组
        console.log('检测到返回数据在results数组中');
        resultsArray = response.data.results;
        
        // 处理分页
        if (response.data.count !== undefined) {
          total.value = response.data.count;
        }
      } else if (Array.isArray(response.data)) {
        // 旧格式: data直接是数组
        console.log('检测到返回数据直接是数组');
        resultsArray = response.data;
        
        // 处理分页
        if (response.pagination) {
          total.value = response.pagination.total || 0;
        } else {
          total.value = resultsArray.length;
        }
      } else if (response.data && typeof response.data === 'object') {
        // 单个对象
        console.log('检测到返回数据是单个对象');
        resultsArray = [response.data];
        total.value = 1;
      } else {
        console.warn('API返回的数据格式无效:', response.data);
        treasureItems.value = [];
        return;
      }
      
      // 处理每一项数据
      treasureItems.value = resultsArray.map(item => {
        // 基于image_id构建图片URL路径
        let imageUrl = '';
        if (item.image_id) {
          imageUrl = `/media/car_images/${item.image_id}`;
        }
        
        // 标准化每个项目的数据结构
        return {
          id: item.id || 0,
          name: item.name || '',
          car_id: item.car_id || item.id || '', // 如果没有car_id，使用id作为替代
          image_url: imageUrl,
          image_id: item.image_id || '',
          probability: typeof item.probability === 'number' ? item.probability : 
                      typeof item.probability === 'string' ? parseFloat(item.probability) : 0,
          quality: item.quality || 'parts',
          item_type: item.item_type || 'fixed',
          fragment_value: typeof item.fragment_value === 'number' ? item.fragment_value : 
                         typeof item.fragment_value === 'string' ? parseInt(item.fragment_value) : 0,
          expiry_days: typeof item.expiry_days === 'number' ? item.expiry_days : 
                      typeof item.expiry_days === 'string' ? parseInt(item.expiry_days) : 0,
          is_active: !!item.is_active,
          description: item.description || '',
          created_at: item.created_at || '',
          updated_at: item.updated_at || ''
        }
      });
      
      console.log('处理后的夺宝配置列表:', treasureItems.value);
    } else {
      treasureItems.value = []
      ElMessage.error(response?.message || '获取夺宝配置列表失败')
    }
    
    loading.value = false
  } catch (error) {
    console.error('获取夺宝配置列表失败:', error)
    treasureItems.value = []
    ElMessage.error('获取夺宝配置列表失败')
    loading.value = false
  }
}

/**
 * 处理页码变化
 * @param {number} val - 新的页码
 */
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTreasureItems()
}

/**
 * 获取品质标签类型
 * @param {string} quality - 品质值
 * @returns {string} - 标签类型
 */
const getQualityTagType = (quality) => {
  switch (quality) {
    case 'parts': return 'info'       // 改装配件
    case 'renault': return 'success'  // 雷诺类型赛车
    case 'normal_a': return 'warning' // 普通A级赛车
    case 't1_mecha': return 'danger'  // T1机甲
    case 'normal_s': return 'primary' // 普通S级赛车
    case 'top_s': return ''           // 顶级227S车
    default: return 'info'
  }
}

/**
 * 获取品质文本
 * @param {string} quality - 品质值
 * @returns {string} - 品质文本
 */
const getQualityText = (quality) => {
  switch (quality) {
    case 'parts': return '改装配件'
    case 'renault': return '雷诺类型赛车'
    case 'normal_a': return '普通A级赛车'
    case 't1_mecha': return 'T1机甲'
    case 'normal_s': return '普通S级赛车'
    case 'top_s': return '顶级227S车'
    default: return '未知'
  }
}

/**
 * 显示新增夺宝配置对话框
 */
const showAddTreasureDialog = () => {
  isEditMode.value = false
  treasureForm.value = {
    id: null,
    name: '',
    probability: '0%',
    expiry_days: 0,
    image_id: null,
    image_url: '',
    car_id: '',
    quality: 'parts',
    item_type: 'fixed',
    fragment_value: 0,
    is_active: true,
    description: ''
  }
  treasureDialogVisible.value = true
}

/**
 * 处理编辑夺宝配置
 * @param {Object} treasure - 夺宝配置对象
 */
const handleEditTreasure = (treasure) => {
  if (!treasure || typeof treasure !== 'object') {
    ElMessage.warning('编辑奖品失败，数据无效');
    return;
  }
  
  isEditMode.value = true;
  treasureForm.value = {
    id: treasure.id || null,
    name: treasure.name || '',
    probability: typeof treasure.probability === 'string' && treasure.probability.includes('%') 
      ? treasure.probability 
      : typeof treasure.probability === 'number' 
        ? `${treasure.probability}%` 
        : typeof treasure.probability === 'string' 
          ? `${treasure.probability}%` 
          : '0%',
    expiry_days: typeof treasure.expiry_days === 'number' ? treasure.expiry_days : 0,
    image_id: treasure.image_id || null,
    image_url: treasure.image_url || '',
    quality: treasure.quality || 'parts',
    item_type: treasure.item_type || 'fixed',
    fragment_value: typeof treasure.fragment_value === 'number' ? treasure.fragment_value : 0,
    is_active: !!treasure.is_active,
    description: treasure.description || '',
    car_id: treasure.car_id || ''
  };
  
  treasureDialogVisible.value = true;
}

/**
 * 处理删除夺宝配置
 * @param {Object} treasure - 夺宝配置对象
 */
const handleDeleteTreasure = async (treasure) => {
  if (!treasure || !treasure.id) {
    ElMessage.warning('删除奖品失败，数据无效');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除夺宝配置 "${treasure.name || '未命名奖品'}" 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API删除奖品
    const response = await deleteTreasureItem(treasure.id)
    
    if (response && response.success) {
      ElMessage.success(response.message || '夺宝配置删除成功')
      
      // 如果当前页只有一条数据，且不是第一页，则跳转到上一页
      if (treasureItems.value.length === 1 && currentPage.value > 1) {
        currentPage.value--
      }
      
      fetchTreasureItems()
    } else {
      ElMessage.error(response?.message || '删除夺宝配置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除夺宝配置失败:', error)
      ElMessage.error('删除夺宝配置失败')
    }
  }
}

/**
 * 处理查看详情
 * @param {Object} treasure - 夺宝配置对象
 */
const handleViewDetail = (treasure) => {
  if (!treasure || typeof treasure !== 'object') {
    ElMessage.warning('获取奖品详情失败，数据无效');
    return;
  }
  
  currentTreasure.value = {
    id: treasure.id || 0,
    name: treasure.name || '未命名奖品',
    car_id: treasure.car_id || '',
    image_url: treasure.image_url || '',
    image_id: treasure.image_id || '',
    probability: typeof treasure.probability === 'number' ? treasure.probability : 0,
    quality: treasure.quality || 'parts',
    item_type: treasure.item_type || 'fixed',
    fragment_value: typeof treasure.fragment_value === 'number' ? treasure.fragment_value : 0,
    expiry_days: typeof treasure.expiry_days === 'number' ? treasure.expiry_days : 0,
    is_active: !!treasure.is_active,
    description: treasure.description || ''
  };
  
  detailDialogVisible.value = true;
}

/**
 * 处理保存夺宝配置
 */
const handleSaveTreasure = async () => {
  if (!treasureFormRef.value) return
  
  await treasureFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saveLoading.value = true
        
        // 处理表单数据，确保数据类型正确
        const treasureData = {
          id: treasureForm.value.id,
          name: treasureForm.value.name,
          // 直接以文本形式带百分号提交
          probability: `${treasureForm.value.probability}%`,
          expiry_days: Number(treasureForm.value.expiry_days),
          image_id: treasureForm.value.image_id,
          car_id: treasureForm.value.car_id || '',
          image_url: treasureForm.value.image_url || '',
          quality: treasureForm.value.quality,
          item_type: treasureForm.value.item_type,
          fragment_value: Number(treasureForm.value.fragment_value),
          is_active: treasureForm.value.is_active,
          description: treasureForm.value.description
        }
        
        console.log('提交的夺宝配置数据:', treasureData);
        
        let response
        if (isEditMode.value) {
          response = await updateTreasureItem(treasureData)
        } else {
          response = await createTreasureItem(treasureData)
        }
        
        if (response && response.success) {
          // 检查返回的数据是否与提交的一致，如果不一致则提示用户
          if (response.data) {
            const returnedData = response.data;
            let warningMsg = '';
            
            // 检查概率
            if (returnedData.probability && 
                returnedData.probability !== treasureData.probability) {
              warningMsg += `概率值被修改为${returnedData.probability}；`;
            }
            
            // 检查碎片数量
            if (returnedData.fragment_value && 
                parseInt(returnedData.fragment_value) !== parseInt(treasureData.fragment_value)) {
              warningMsg += `碎片数量被修改为${returnedData.fragment_value}；`;
            }
            
            if (warningMsg) {
              ElMessage.warning(`注意：${warningMsg}`);
            }
          }
          
          ElMessage.success(response.message || `夺宝配置${isEditMode.value ? '更新' : '创建'}成功`)
          treasureDialogVisible.value = false
          fetchTreasureItems()
        } else {
          ElMessage.error(response?.message || `${isEditMode.value ? '更新' : '创建'}夺宝配置失败`)
        }
      } catch (error) {
        console.error(`${isEditMode.value ? '更新' : '创建'}夺宝配置失败:`, error)
        ElMessage.error(`${isEditMode.value ? '更新' : '创建'}夺宝配置失败: ${error.message || ''}`)
      } finally {
        saveLoading.value = false
      }
    }
  })
}

/**
 * 处理图片上传前的验证
 * @param {File} file - 上传的文件
 * @returns {boolean} - 是否通过验证
 */
const beforeImageUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isJpgOrPng) {
    ElMessage.error('只能上传JPG或PNG格式的图片!')
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
  }
  
  return isJpgOrPng && isLt2M
}

/**
 * 处理图片上传成功
 * @param {Object} response - 上传响应结果
 */
const handleImageSuccess = (response) => {
  // 实际项目中，这里应该从response中获取图片URL
  // treasureForm.value.image_url = response.data.url
  
  // 模拟上传成功
  treasureForm.value.image_url = 'https://via.placeholder.com/200x200'
}

/**
 * 处理赛车搜索，为autocomplete提供建议
 * @param {string} query - 搜索查询字符串
 * @param {Function} callback - 回调函数，用于返回建议列表
 */
const searchCars = async (query, callback) => {
  if (!query) {
    callback([]);
    carSearchError.value = null;
    return;
  }
  
  console.log('开始搜索赛车，关键词:', query);
  carSearchError.value = null;
  
  try {
    loading.value = true;
    
    // 使用API函数调用搜索接口
    const result = await searchCarsAPI(query);
    
    // 输出完整API响应信息，查看结构
    console.log('搜索结果完整响应:', result);
    
    let suggestions = [];
    
    if (result && result.success === true && Array.isArray(result.data)) {
      // 标准格式: {success: true, data: [...]}
      console.log('API返回标准格式数据');
      suggestions = result.data.map(item => {
        return {
          value: item.name,  // 必须包含value字段，用于显示
          name: item.name,
          car_id: item.car_id,
          image_id: item.image_id,
          image_url: item.image_url
        };
      });
    } else if (result && Array.isArray(result.data)) {
      // 另一种可能格式: {data: [...]}
      console.log('API返回仅包含data字段');
      suggestions = result.data.map(item => {
        return {
          value: item.name,  // 必须包含value字段，用于显示
          name: item.name,
          car_id: item.car_id || item.id,
          image_id: item.image_id,
          image_url: item.image_url
        };
      });
    } else if (Array.isArray(result)) {
      // 直接返回数组的情况
      console.log('API直接返回数组');
      suggestions = result.map(item => {
        return {
          value: item.name,  // 必须包含value字段，用于显示
          name: item.name,
          car_id: item.car_id || item.id,
          image_id: item.image_id,
          image_url: item.image_url
        };
      });
    } else {
      // API返回错误或无效格式，使用模拟数据
      console.warn('API返回异常，使用模拟数据:', result);
      
      // 使用模拟数据进行模糊匹配
      suggestions = mockCarData.filter(car => 
        car.name.toLowerCase().includes(query.toLowerCase()) || 
        car.id.toLowerCase().includes(query.toLowerCase())
      ).map(car => ({
        value: car.name,  // 必须包含value字段，用于显示
        name: car.name,
        car_id: car.id,
        image_id: '',
        image_url: car.image_url
      }));
      
      if (suggestions.length > 0) {
        ElMessage.warning(`API调用异常，使用模拟数据，找到 ${suggestions.length} 条匹配`);
      } else {
        carSearchError.value = '未找到匹配的赛车，请修改搜索词重试';
        ElMessage.warning('API调用异常，未找到匹配的模拟赛车');
      }
    }
    
    // 返回建议列表
    console.log('最终返回的建议数据:', suggestions);
    
    if (suggestions.length > 0) {
      ElMessage.success(`找到 ${suggestions.length} 条匹配记录`);
      carSearchError.value = null;
    } else {
      carSearchError.value = '未找到匹配的赛车，请修改搜索词重试';
      ElMessage.info('未找到匹配的赛车，请修改搜索词重试');
    }
    
    // 确保返回的是数组
    if (!Array.isArray(suggestions)) {
      console.error('suggestions不是数组:', suggestions);
      suggestions = [];
    }
    
    callback(suggestions);
    
    loading.value = false;
  } catch (error) {
    console.error('搜索赛车出现异常:', error);
    carSearchError.value = '搜索赛车时发生错误，请稍后重试';
    
    // 使用模拟数据作为备选
    const suggestions = mockCarData.filter(car => 
      car.name.toLowerCase().includes(query.toLowerCase()) || 
      car.id.toLowerCase().includes(query.toLowerCase())
    ).map(car => ({
      value: car.name,  // 必须包含value字段，用于显示
      name: car.name,
      car_id: car.id,
      image_id: '',
      image_url: car.image_url
    }));
    
    ElMessage.warning(`搜索API异常，使用模拟数据，找到 ${suggestions.length} 条匹配`);
    
    // 确保返回的是数组
    if (!Array.isArray(suggestions)) {
      console.error('错误处理后的suggestions不是数组:', suggestions);
      callback([]);
    } else {
      callback(suggestions);
    }
    
    loading.value = false;
  }
}

/**
 * 选择赛车
 * @param {Object} car - 赛车对象
 */
const selectCar = (car) => {
  console.log('选中的赛车数据:', car);
  
  try {
    if (!car || typeof car !== 'object') {
      console.error('选择的赛车数据无效:', car);
      ElMessage.error('选择的赛车数据无效');
      return;
    }

    // 设置表单值
    treasureForm.value.name = car.name || '';
    treasureForm.value.image_url = car.image_url ? getFullImageUrl(car.image_url) : '';
    treasureForm.value.car_id = car.car_id || car.id || '';
    treasureForm.value.image_id = car.image_id || '';
    
    // 如果有图片URL，设置图片
    if (car.image_url) {
      console.log('赛车图片URL:', car.image_url);
      console.log('处理后的完整URL:', getFullImageUrl(car.image_url));
    } else {
      console.log('赛车没有图片URL');
    }

    // 根据车名确定品质
    let quality = 'parts'; // 默认改装配件
    const name = car.name || '';
    
    if (typeof name === 'string') {
      // 使用正则表达式进行更准确的匹配
      if (/227S|顶级|至尊|火麒麟|玉麒麟/.test(name)) {
        quality = 'top_s'; // 顶级227S车或麒麟系列车
      } else if (/S级|S车/.test(name)) {
        quality = 'normal_s'; // 普通S级赛车
      } else if (/机甲|T1/.test(name)) {
        quality = 't1_mecha'; // T1机甲
      } else if (/A级|A车/.test(name)) {
        quality = 'normal_a'; // 普通A级赛车
      } else if (/雷诺/.test(name)) {
        quality = 'renault'; // 雷诺类型赛车
      }
    }
    
    treasureForm.value.quality = quality;
    console.log('设置的品质:', quality);

    // 设置概率默认值（根据品质）
    switch (quality) {
      case 'top_s': // 顶级227S车
        treasureForm.value.probability = '0.0005%';
        break;
      case 'normal_s': // 普通S级赛车
        treasureForm.value.probability = '0.001%';
        break;
      case 't1_mecha': // T1机甲
        treasureForm.value.probability = '0.003%';
        break;
      case 'normal_a': // 普通A级赛车
        treasureForm.value.probability = '0.005%';
        break;
      case 'renault': // 雷诺类型赛车
        treasureForm.value.probability = '0.01%';
        break;
      default: // 改装配件
        treasureForm.value.probability = '0.02%';
        break;
    }
    
    // 设置碎片数量默认值
    const fragmentMap = {
      'top_s': 1,
      'normal_s': 2,
      't1_mecha': 3,
      'normal_a': 4,
      'renault': 5,
      'parts': 6
    };
    treasureForm.value.fragment_value = fragmentMap[quality] || 1;
    
    // 设置默认期限
    if (quality === 'parts' || quality === 'renault') {
      treasureForm.value.expiry_days = 0; // 永久
    } else if (quality === 'normal_a' || quality === 't1_mecha') {
      treasureForm.value.expiry_days = 15; // 15天
    } else {
      treasureForm.value.expiry_days = 7; // 7天
    }

    // 清空搜索框
    carSearchQuery.value = '';
    
    ElMessage.success(`已选择赛车: ${car.name || '未知赛车'}`);
  } catch (error) {
    console.error('选择赛车时出错:', error);
    ElMessage.error(`选择赛车失败: ${error.message || '未知错误'}`);
  }
}

/**
 * 获取完整的图片URL
 * @param {string|null} imageUrl - 图片URL
 * @returns {string} 完整的图片URL
 */
const getFullImageUrl = (imageUrl) => {
  // 检查imageUrl是否有效
  if (!imageUrl || typeof imageUrl !== 'string') {
    console.log('图片URL无效:', imageUrl);
    return '/images/default-car.png';
  }
  
  try {
    console.log('处理图片URL - 原始URL:', imageUrl);
    
    // 如果是完整URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      console.log('URL已是完整路径，直接返回');
      return imageUrl;
    }
    
    // 使用.env.development中定义的API_URL
    const apiBaseUrl = import.meta.env?.VITE_API_URL || 'http://localhost:8000/api';
    // 计算基础媒体URL（去掉最后的/api部分）
    const baseMediaUrl = apiBaseUrl.endsWith('/api') 
      ? apiBaseUrl.substring(0, apiBaseUrl.length - 4) 
      : apiBaseUrl;
    
    console.log('基础媒体URL:', baseMediaUrl);
    
    // 处理相对路径
    if (imageUrl.startsWith('/')) {
      // 移除开头的斜杠，避免双斜杠
      const cleanPath = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl;
      const fullUrl = `${baseMediaUrl}/${cleanPath}`;
      console.log('处理结果 - 绝对路径:', fullUrl);
      return fullUrl;
    }
    
    // 对于其他情况，添加到媒体URL
    const fullUrl = `${baseMediaUrl}/media/car_images/${imageUrl}`;
    console.log('处理结果 - 相对路径:', fullUrl);
    return fullUrl;
  } catch (error) {
    console.error('处理图片URL时出错:', error, imageUrl);
    return '/images/default-car.png';
  }
}

/**
 * 获取期限文本
 * @param {string} expiryDays - 期限天数
 * @returns {string} - 期限文本
 */
const getExpiryText = (expiryDays) => {
  if (expiryDays === 0) return '永久'
  if (expiryDays === 30) return '30天'
  if (expiryDays === 15) return '15天'
  if (expiryDays === 7) return '7天'
  return '未知'
}

/**
 * 处理切换奖品状态
 * @param {Object} treasure - 夺宝配置对象
 */
const handleToggleStatus = async (treasure) => {
  if (!treasure || !treasure.id) {
    ElMessage.warning('操作失败，数据无效');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要${treasure.is_active ? '禁用' : '启用'}夺宝配置 "${treasure.name || '未命名奖品'}" 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API切换奖品状态
    const response = await toggleTreasureItemStatus(treasure.id)
    
    if (response && response.success) {
      ElMessage.success(response.message || `夺宝配置${treasure.is_active ? '禁用' : '启用'}成功`)
      fetchTreasureItems()
    } else {
      ElMessage.error(response?.message || `${treasure.is_active ? '禁用' : '启用'}夺宝配置失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${treasure.is_active ? '禁用' : '启用'}夺宝配置失败:`, error)
      ElMessage.error(`${treasure.is_active ? '禁用' : '启用'}夺宝配置失败`)
    }
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  currentPage.value = 1
  fetchTreasureItems()
}

/**
 * 重置搜索条件
 */
const handleResetSearch = () => {
  searchQuery.value = ''
  searchQuality.value = ''
  searchItemType.value = ''
  activeOnly.value = false
  currentPage.value = 1
  fetchTreasureItems()
}

// 组件挂载时加载数据
onMounted(() => {
  fetchTreasureItems()
})

// 测试数据，在API调用失败时使用
const mockCarData = [
  { id: 'C001', name: '顶级227S-极速', value: '顶级227S-极速', image_url: 'https://placehold.co/200x200' },
  { id: 'C002', name: 'S级赛车-闪电', value: 'S级赛车-闪电', image_url: 'https://placehold.co/200x200' },
  { id: 'C003', name: 'T1机甲-战神', value: 'T1机甲-战神', image_url: 'https://placehold.co/200x200' },
  { id: 'C004', name: 'A级赛车-疾风', value: 'A级赛车-疾风', image_url: 'https://placehold.co/200x200' },
  { id: 'C005', name: '雷诺-竞速', value: '雷诺-竞速', image_url: 'https://placehold.co/200x200' },
  { id: 'C006', name: '改装件-喷射器', value: '改装件-喷射器', image_url: 'https://placehold.co/200x200' }
]
</script>

<style scoped>
.car-treasure-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.treasure-table {
  margin-top: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 6px;
}

.title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.upload-tip,
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
  padding: 10px;
}

.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.error-text {
  font-size: 12px;
  text-align: center;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

.treasure-detail {
  padding: 20px;
}

.detail-item {
  margin-bottom: 16px;
  line-height: 1.5;
}

.detail-item .label {
  color: #606266;
  margin-right: 8px;
  display: inline-block;
  width: 84px;
  font-weight: 500;
}

.detail-item .content {
  margin-top: 8px;
  margin-left: 84px;
  white-space: pre-wrap;
  word-break: break-all;
  color: #303133;
  line-height: 1.6;
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.search-container {
  margin-bottom: 10px;
  display: flex;
}

.search-input {
  flex-grow: 1;
  width: 100%;
}

.search-icon {
  color: #909399;
}

.car-suggestion-item {
  padding: 8px 0;
  cursor: pointer;
}

.car-suggestion-item .car-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.car-suggestion-item .car-id {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.search-results {
  margin-top: 10px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.car-card {
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.car-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.car-image-container {
  height: 120px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.car-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.car-info {
  padding: 10px;
}

.car-info h3 {
  margin: 5px 0;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.car-info p {
  margin: 5px 0;
  font-size: 12px;
  color: #909399;
}

.text-center {
  text-align: center;
}

.search-box {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

:deep(.filter-form .el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

.search-error {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.no-data .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-data p {
  font-size: 16px;
  margin: 0;
}

/* 适配移动端的样式调整 */
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 16px;
    width: 100%;
    justify-content: space-between;
  }
  
  .filter-form {
    flex-direction: column;
  }
  
  :deep(.filter-form .el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
    width: 100%;
  }
  
  .operation-buttons {
    flex-wrap: wrap;
    gap: 8px 16px;
  }
}

/* 防止数据异常导致UI问题 */
.title-cell {
  max-width: 100%;
  word-break: break-word;
}

.detail-item .content {
  max-height: 200px;
  overflow-y: auto;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}
</style> 