{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:08.743Z", "name": "@parcel/watcher-linux-x64-glibc", "versions": {"2.2.0-alpha.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.2.0-alpha.0", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-JVcKG2pb1nFI0RCVPMnsZe1WnbDTtCLOQmuw58pVX0VOoEzj5nYb5727VXR7KwySXqP8cvShYlOddgtOR6qpbg==", "shasum": "8653572a8b03b50d7975dae49a5a93086a51368d", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.2.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 517349, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5XxZG87odr8o8mkRropCCTut8HlSz5Zea56P5jbQIuAIhAPO11hXOJt5GEKtHSlHeL8GMW607HJ6959Y1DYM7aSWk"}], "size": 193098}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687727618525, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.2.0-alpha.1", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-UpEId/DDy75JbCByHoTxc7qFOMvnGt8SMlbsfuyfizJwIExEZMIKzChLx8xuKd4Dx03us/XbG2iCDe4hHEzsbQ==", "shasum": "46a5244c21c2c54a0791b8fa4d735341553ef721", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 517349, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgarBkV0Ztjta6nRiIUjE2KDsGIk/yPoFcD3shlNla4AIgLVpT8j+KoIP5jKj4DpcFr6qPtQSVw8DpTJZHTza4ZPM="}], "size": 193098}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738112481, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.2.0", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-xJvJ7R2wJdi47WZBFS691RDOWvP1j/IAs3EXaWVhDI8FFITbWrWaln7KoNcR0Y3T+ZwimFY/cfb0PNht1q895g==", "shasum": "f6e878bf40874b1ce9530ec87e5c2e644e9ad5ac", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.2.0.tgz", "fileCount": 4, "unpackedSize": 517341, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHLz8inedCykiKcNaeI94qauO8qRT5nWvBQPsMOXA0c5AiEA8xmLWK6rk3Ye5fH6ZQMUcklK/ReGCPggwSZwGHx5eac="}], "size": 193094}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269825046, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.2.1-alpha.0", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-hO9PbAbyCQiDvPDAiGJ3Cx8YdFEtrpgzye3Duj6fAInO3cvAEhbnzeDLO8Aq28i1r09mYQwnCl49cGhy3xZOpg==", "shasum": "9b6c1ec91a6064f71119f0fa93ae353bd2790d67", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 517349, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFQwZ0EzkSlAqY4djDmfSDJYASPsftCo2Q61YpYtx39QIhAKEILpsp8Wifvr38AW3CyPpJpDrOdKkNEEzaiNtQpnER"}], "size": 193172}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571412433, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.3.0-alpha.0", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-tvioLcxq7bBRv+HzyEAN2NHnmpITdmMxw8ig13WWeUAXr8mhBJcTd4K5tqExKNXNeGv7Rvb1Rp5KmPY5qjcN6Q==", "shasum": "e85f7f24a360fba7e220e23aed5f908199fd444b", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 546029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsk0FeVYF2Q/mhZ1dMgt6NTpoatvQAZoThmJy5839ymgIhAN/lBPrIWHq4Frf2EFyd3hiLbYmaP/DrXt2bNN4fLCCt"}], "size": 204524}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750508275, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.3.0-alpha.1", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-SCnVZguaSCDYLO3WBlpHR7O4n9WsfB4LFt8/L/x8FLWiOZ0dzcO1N27ZXDEBnjGod7gQPbbjFdXh8cI8PV/cMA==", "shasum": "dcacd9f4e304487ab7f0381ef37db045f111db4e", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 546029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhQGS5S7VhOl1VAIJVKDxc20FXi2j0gBfhLtac+6JB9wIgKFmL3yIwY9zBrfnKEYmzKP9NYB3v2Iua7xWTNnC6Bqs="}], "size": 204524}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753405301, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.3.0-alpha.2", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-OXDcBEAifhcfNmEizP2kHpxjjZC15bdaKLSQZIuMUpOIxvhQLF0KyXmDFhoiMxdjnloppvW8PiTuOGX5Vnbdfw==", "shasum": "1518ad0797116f05ba4bb22a409170c5b6cac1f6", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 546029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhnYyV/A232W1dbbHJ/4GyvfbiyooHCvErUQ1JBUfeIAIgc4rhANGGJbNe7kF+23CyTHsHiWzNwPvazsI7gbojiw0="}], "size": 204524}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485207412, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.3.0-alpha.3", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-7UKwZAw5GgmnFZmsfTFdu9A1EntvB8zrM90MPV38LF/VczgCQ2THgISlpLX+gc0n01ehFhHrnPert8Vta1sJsw==", "shasum": "512de5e7168d3764e00670d512cbec877306d210", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 546029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjRIq7jwXvazEyxKTZdQ6vTktFrdXPGt0dtX8SmXaQIQIgdwMtQcDQk89icOFzHH3Mc66xnIYUdZq/yR+spwhlY+Q="}], "size": 204524}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557606224, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.3.0", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-P7Wo91lKSeSgMTtG7CnBS6WrA5otr1K7shhSjKHNePVmfBHDoAOHYRXgUmhiNfbcGk0uMCHVcdbfxtuiZCHVow==", "shasum": "193dd1c798003cdb5a1e59470ff26300f418a943", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.3.0.tgz", "fileCount": 4, "unpackedSize": 546021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzJry269kLn9rQy7zPygnxVxUu+UGU7Vx42AIo35Wg4gIhAPO0wLiMfRNuGd3b6BlcpNzA166L0bROmBsesDv6yrXa"}], "size": 204516}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850814822, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.4.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.4.0", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-KphV8awJmxU3q52JQvJot0QMu07CIyEjV+2Tb2ZtbucEgqyRcxOBDMsqp1JNq5nuDXtcCC0uHQICeiEz38dPBQ==", "shasum": "44cbbb1e5884a1ca900655f47a0775218318f934", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.4.0.tgz", "fileCount": 4, "unpackedSize": 546021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+aTjX50ELU2jUunT04eh+D5SQWIQKAsYuO1vnBis2uAiEAv8Z/ztTm2zOZ2VCtstQ7rChU4qhQmOKb2SjxK3tGyVc="}], "size": 204516}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360252203, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.4.1": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.4.1", "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-s9O3fByZ/2pyYDPoLM6zt92yu6P4E39a03zvO0qCHOTjxmt3GHRMLuRZEWhWLASTMSrrnVNWdVI/+pUElJBBBg==", "shasum": "0ce29966b082fb6cdd3de44f2f74057eef2c9e39", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.4.1.tgz", "fileCount": 4, "unpackedSize": 546021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6ipJIk2w5wLEukPsS4eODE8dFhWHSSekdNoSnm7DyEAIgT4aN7w+O0ELe0VYfQx6YrjzV/MRxMeB4njqtt+mVw3o="}], "size": 204516}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702680188, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.4.2-alpha.0", "directories": {}, "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "dist": {"integrity": "sha512-/WJJ3Y46ubwQW+Z+mzpzK3pvqn/AT7MA63NB0+k9GTLNxJQZNREensMtpJ/FJ+LVIiraEHTY22KQrsx9+DeNbw==", "shasum": "a4c8e026b29c95b2fb5ff81896a219152aff7e7e", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 554253, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5zJPNBnXDt/u3KNYJ8Q5jVbp+wLfQCLEGTmCxRAMshAiAKMyXOF04Uwcm9zXQRmT0kIMzNiDjQJ3NZCF6i8unu6Q=="}], "size": 211695}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259259250, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.5.0", "directories": {}, "os": ["linux"], "cpu": ["x64"], "libc": ["glibc"], "dist": {"integrity": "sha512-d9AOkusyXARkFD66S6zlGXyzx5RvY+chTP9Jp0ypSTC9d4lzyRs9ovGf/80VCxjKddcUvnsGwCHWuF2EoPgWjw==", "shasum": "2e254600fda4e32d83942384d1106e1eed84494d", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.0.tgz", "fileCount": 4, "unpackedSize": 516757, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICIGRox4p2qO+BxIDzTJnSuooBKcmblT/70AjPEGR+S5AiBdVL9MXDv17EkAbyKOSF1QP/6wmSPQcW+xaa4SE3yfgw=="}], "size": 197961}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701345977, "_source_registry_name": "default"}}, "_source_registry_name": "default"}