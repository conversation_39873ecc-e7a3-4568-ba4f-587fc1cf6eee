<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <!-- 处理API请求 -->
        <rule name="API Requests" stopProcessing="true">
          <match url="^api/(.*)" />
          <action type="None" />
        </rule>

        <!-- 处理静态文件 -->
        <rule name="Static Files" stopProcessing="true">
          <match url="^(assets|css|js|img|fonts|static|favicon\.ico|robots\.txt)/(.*)" />
          <action type="Rewrite" url="{R:0}" />
        </rule>

        <!-- 处理根路径 -->
        <rule name="Root Path" stopProcessing="true">
          <match url="^$" />
          <action type="Rewrite" url="/" />
        </rule>

        <!-- 处理应用路由 - 精确匹配 -->
        <rule name="Exact Application Routes" stopProcessing="true">
          <match url="^(login|404)$" />
          <action type="Rewrite" url="/" />
        </rule>

        <!-- 处理应用路由 - 前缀匹配 -->
        <rule name="Prefix Application Routes" stopProcessing="true">
          <match url="^(illustration|users|feedback|sql-executor|comments|lottery|car-treasure|vip-management|prize-management)(/.*)?$" />
          <action type="Rewrite" url="/" />
        </rule>

        <!-- 处理其他所有路由为404 -->
        <rule name="Handle 404" stopProcessing="true">
          <match url="(.*)" />
          <action type="Rewrite" url="/404.html" />
        </rule>
      </rules>
    </rewrite>
    <httpErrors>
      <remove statusCode="404" subStatusCode="-1" />
      <remove statusCode="500" subStatusCode="-1" />
      <error statusCode="404" path="/404.html" responseMode="ExecuteURL" />
      <error statusCode="500" path="/404.html" responseMode="ExecuteURL" />
    </httpErrors>
    <staticContent>
      <mimeMap fileExtension=".json" mimeType="application/json" />
    </staticContent>
  </system.webServer>
</configuration>
