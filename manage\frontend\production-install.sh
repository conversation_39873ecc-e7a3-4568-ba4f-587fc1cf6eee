#!/bin/bash

# 配置
LOG_DIR="logs"
LOG_FILE="$LOG_DIR/npm-install-$(date +%Y%m%d_%H%M%S).log"
ERROR_LOG="$LOG_DIR/npm-install-error.log"

# 创建日志目录
mkdir -p $LOG_DIR

# 输出时间戳函数
timestamp() {
    date "+%Y-%m-%d %H:%M:%S"
}

# 开始安装
echo "=== Installation Started at $(timestamp) ===" | tee -a $LOG_FILE

# 检查 node 和 npm 版本
echo "Node version: $(node -v)" | tee -a $LOG_FILE
echo "NPM version: $(npm -v)" | tee -a $LOG_FILE

# 清理旧的 node_modules 和 package-lock.json (可选)
echo "Cleaning old installation..." | tee -a $LOG_FILE
rm -rf node_modules package-lock.json

# 安装依赖
echo "Installing dependencies..." | tee -a $LOG_FILE
npm install --verbose 2>&1 | tee -a $LOG_FILE

# 检查安装结果
if [ $? -eq 0 ]; then
    echo "=== Installation Completed Successfully at $(timestamp) ===" | tee -a $LOG_FILE
else
    echo "=== Installation Failed at $(timestamp) ===" | tee -a $LOG_FILE
    # 将错误信息同时写入错误日志
    tail -n 50 $LOG_FILE > $ERROR_LOG
fi

# 输出依赖树
echo "Generating dependency tree..." | tee -a $LOG_FILE
npm list --depth=0 >> $LOG_FILE 2>&1

# 检查安全漏洞
echo "Checking for vulnerabilities..." | tee -a $LOG_FILE
npm audit >> $LOG_FILE 2>&1 