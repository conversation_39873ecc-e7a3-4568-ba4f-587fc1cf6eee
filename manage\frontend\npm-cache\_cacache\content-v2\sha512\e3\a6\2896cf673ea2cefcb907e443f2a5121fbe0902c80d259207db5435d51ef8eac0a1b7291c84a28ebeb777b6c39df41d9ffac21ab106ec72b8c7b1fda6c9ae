{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:36.645Z", "name": "@parcel/watcher-win32-ia32", "versions": {"2.3.0-alpha.0": {"name": "@parcel/watcher-win32-ia32", "version": "2.3.0-alpha.0", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-MESnIJtXOWnX1G9pZxBS5ss1EA3uRuDRVqchbgCcoBWi5os6D5hVRzvIEIGJzR5OyWCbOD5GUaTj1y7p0oYrqw==", "shasum": "c60821856a1f9f2da2c7af6a5694238ffd4da427", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 430855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZ7srN1Epk9Smoc1M19L7aDrf+GKunlvKTfFp7tO2rZQIhAJ7ac+ARizNggAWkCmfn1uIJDJwnC8yBCDayvHKj2dOb"}], "size": 221200}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750515453, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-win32-ia32", "version": "2.3.0-alpha.1", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-feXgsSLaWzqpDR6u2cozB78ES2vwPQ09erXeA8XN+P6DvjDSPlnMcoWVpNB4vejhp0VV8RPlMGtstuQqMMnVhg==", "shasum": "592cfeb79758f348865124c26f4bd432d2ee5c71", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 430855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfzPWW75kzUlup7+bp3YTGaBME3h+fioCpsZuX0SkoUAIhAL/+xwmdEC500jN7fwTiCfTMv7QJkUb2c8tOx9Sv/b24"}], "size": 221201}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": *************, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-win32-ia32", "version": "2.3.0-alpha.2", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-Re<PERSON>+j+6jfBwjYIn9+xxMs6zBK3w4Ztl+CLfAs4gorbhKq0U0ToWX9mZgKIbBaNkGRPh3Jgx7ADE2uEbV+jL5Yw==", "shasum": "4fa2211a33fd387c96d072611987e714b512195e", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 430855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoGYJYRccVVpzPAY/NuVYdd6G92fYRKWmStCAYYrE4uwIhAPLKzvzEL8I/mpYmaJ5PjWNVKokic3HchITekwwtlrZJ"}], "size": 221200}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": *************, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-win32-ia32", "version": "2.3.0-alpha.3", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-iZoPDyco+6tZ6cIkrb7t4XLFBrMpEzZXNwzavtiDo4fAnCZx0lQ6au22X8jP7gIDtTniyVK6+eScV62UlDzYmg==", "shasum": "2dd27ebe54969625ea7d073a82a80d8582db04b4", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 430855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDP0QyqIR+pah1Xw/Eq34aL1IL8YXNClpvR4h1H965sAiEAi9swLsZ1962lTFHxVgndTCUntRiakeuNTXouYimoNe0="}], "size": 221200}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557613427, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-win32-ia32", "version": "2.3.0", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-FJS/IBQHhRpZ6PiCjFt1UAcPr0YmCLHRbTc00IBTrelEjlmmgIVLeOx4MSXzx2HFEy5Jo5YdhGpxCuqCyDJ5ow==", "shasum": "3ee6a18b08929cd3b788e8cc9547fd9a540c013a", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.3.0.tgz", "fileCount": 4, "unpackedSize": 430847, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsQSj5G/qT7gwxQkwgWL5d0pykc/152Iz10sE1vZys3wIgPhbjv109+g5mTQotcxCEHekmYY2mYx9ICeIa2uZr24g="}], "size": 221188}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850822133, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-win32-ia32", "version": "2.4.0", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-IO/nM+K2YD/iwjWAfHFMBPz4Zqn6qBDqZxY4j2n9s+4+OuTSRM/y/irksnuqcspom5DjkSeF9d0YbO+qpys+JA==", "shasum": "279225b2ebe1fadd3c5137c9b2365ad422656904", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.4.0.tgz", "fileCount": 4, "unpackedSize": 413951, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHvtV++kEL2PkNpVPhTt5AzQUxB+iaL1vL1w31Tdrw0jAiA6IPM3DtGn/WrlnBoXg1r5qWJHr5eZYjvcLLG/ceYGqw=="}], "size": 208092}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360258848, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-win32-ia32", "version": "2.4.1", "os": ["win32"], "cpu": ["ia32"], "directories": {}, "dist": {"integrity": "sha512-maNRit5QQV2kgHFSYwftmPBxiuK5u4DXjbXx7q6eKjq5dsLXZ4FJiVvlcw35QXzk0KrUecJmuVFbj4uV9oYrcw==", "shasum": "94fbd4b497be39fd5c8c71ba05436927842c9df7", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.4.1.tgz", "fileCount": 4, "unpackedSize": 414975, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEJ9v+g3CN5S182lBLLOVT4+leLp/0hzCR8AF3WbpEkRAiA66FkqTm3MUEWlx7WV0Q95jojME7vh9ft790D97N4gKw=="}], "size": 208177}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702695024, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-win32-ia32", "version": "2.4.2-alpha.0", "directories": {}, "os": ["win32"], "cpu": ["ia32"], "dist": {"integrity": "sha512-ibFatsOMrWx3djO7UtiT9DqfqKWLM/6PDmqECnjhoJnZ1zi1DTVlawHGdVkqJzJf2N9uq6i4WleCADTWyPPlkA==", "shasum": "ced357baf759386f0c479f88d83311ba9582807a", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 418055, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeZwXsYBelz5shVY22oquT0ctrQzZWTnRu/fIe3NZPnQIgHfV9FCE++lpbxFLmw+6aYJGUISQTNR4iCQv9s8zT2Lg="}], "size": 215437}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259269177, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-win32-ia32", "version": "2.5.0", "directories": {}, "os": ["win32"], "cpu": ["ia32"], "dist": {"integrity": "sha512-+rgpsNRKwo8A53elqbbHXdOMtY/tAtTzManTWShB5Kk54N8Q9mzNWV7tV+IbGueCbcj826MfWGU3mprWtuf1TA==", "shasum": "778c39b56da33e045ba21c678c31a9f9d7c6b220", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.0.tgz", "fileCount": 4, "unpackedSize": 418047, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCM092621PKuf9HHSYmMtaoJez0ZF0eB/C6akz7Xi8dPgIgWFat0xJY0uVJlkrfUg1aa99dfZ5XiM/L8PrAgDZlGpQ="}], "size": 215387}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701356757, "_source_registry_name": "default"}}, "_source_registry_name": "default"}