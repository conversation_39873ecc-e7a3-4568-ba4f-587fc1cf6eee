{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:16.786Z", "name": "@parcel/watcher-linux-x64-musl", "versions": {"2.2.0-alpha.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.2.0-alpha.0", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-3OCNuRBs6DbrztFlMZlyGsN1/Kcanx1ejH+vUKZYOIoa2uC7Ia1OWvLpeVYtEi9pkDuKWCuHExJIGhBRH7fGtg==", "shasum": "bbf899ff07217839a7294c13aa774b7be6c90983", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.2.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 484314, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEfICg5rPjGU+Q51PucuO5lJZUXrBjDHv8qtliFr0ZfwAiB+dGK0RQ0DAA1RnYvxqNFPFvtlvvBCMcdlCFb1NOkVIA=="}], "size": 180024}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687727620284, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.2.0-alpha.1", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-D6mIsTOZaOdTC1yzAEoiK3YlDAK0t3gX4oi1+8dLC2fWLBtCjNd+LRLLgBIfkEHJ2rNg7pQY9SJQRgu6m4DwdA==", "shasum": "3a58fdef7069ee0f99fcce25839c43795abea339", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 484314, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHa2487bijCm4wWjq3gOGxFIj1o3k+NmuWDRzRhWV+7GAiAgyxFj4cVDSkE62aSm7pr90u63GcmlmbjqvzVhVle+yw=="}], "size": 180024}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738114227, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.2.0", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-D+NMpgr23a+RI5mu8ZPKWy7AqjBOkURFDgP5iIXXEf/K3hm0jJ3ogzi0Ed2237B/CdYREimCgXyeiAlE/FtwyA==", "shasum": "4a1b7a8f65b42f50b1819185ef56ed3c9e251425", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.2.0.tgz", "fileCount": 4, "unpackedSize": 484306, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFscpc2TPugKE4TH2trX3gzOZprOL2qT33vDl5QXJeHrAiEAndGXnm1gmFI1CzpMgdV/ZtFpexs46hBW71mcu8j9LDM="}], "size": 180015}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269826642, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.2.1-alpha.0", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-AgaxwfmUBU/82O2NJcDbak2hAYrWZArbmbALzRsf6zmm2FKwhhF2QaqGAosxFD5exR6wCE7volHXRUeNzkjrlw==", "shasum": "a7a60079da8e6ca3556fdf9ca7b4e67e64f89c54", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 484314, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH5/0CTgzeVdVNym6SHu+Qm07eViqQ2YwwypxNBsXs9bAiBfNKqrGVd1gI49Ct9/UEVUaeNLBHb+sXbnUdI7Hv+RMg=="}], "size": 180487}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571413885, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.3.0-alpha.0", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-yXukuFjksggI+syhtK/Q6ba0JdodVTr19GCtlIBi1VwNdA6GTJcNGBtImtPI5ch6j8g5WOZNIdGsOsNd77+MGg==", "shasum": "fe48fd21a04b5278dda05c81eb2069366b2ff04f", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 508898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGR2AlI0QS/t86MeNzttTotEh7t9QuSa5sRzq2jimkLwIhAKAWWvZgAcnpU9fGitAv0JqQpaTZXn0pHkRONj32wLsd"}], "size": 189498}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750509774, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.3.0-alpha.1", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-Q+BEluP6Sfp/kU6iwBlpCmO6SkQDmTzMv4050rXeLy6PQtqEla/sTGQBtMG6+dgig1RJZrSTogMVLly/qFEC4Q==", "shasum": "f37a9512953512b0122299833cd617c4055287aa", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 508898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDB1jtK/yLv+btdahAWqR3HdjZkj+UN1Yit+1OUPZq9SQIgPwbfntpilrntUeO3sZQjMYZ5fJcu+M0MT1gu0sJ+yfQ="}], "size": 189498}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753406965, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.3.0-alpha.2", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-R/SowLb53BVbWe+OBSnAgC665+hik0QWLlYUl3PIh2HztSjHiKbhzn8MK9vUiH9o2dfPTCkkxB6UWlb1p3jd/Q==", "shasum": "ca91fbe1bbf7310714abd18542093fef69318906", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 508898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQ+58GbovkidQDRfLAPmJOSifkgQ3sqwQhV0xWFuCNkAiByRidxDllDztmHqs56RQlNbsHWBTwFJ8gctHAeWMvrMw=="}], "size": 189498}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485209455, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.3.0-alpha.3", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-RMcAs7BzaEhGJLhoA9uhCVm8y8mmK9CnqSe4VoEATBbC9GbKqnVcOiWXo1/gsTLJbXzss5+FKqrDvK11xpKGew==", "shasum": "39aa0a08f17e0f2e4b02be337178568e1bbfeb4e", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 508898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4csJzgbHoqO/0eCbyAupM/wjjJeAB1ZIihbkzoKM4rwIgYw32nnUEU/7QgmWpzjKGBdTH/4cDth855nidOzMTcgc="}], "size": 189499}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557608086, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.3.0", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-+kiRE1JIq8QdxzwoYY+wzBs9YbJ34guBweTK8nlzLKimn5EQ2b2FSC+tAOpq302BuIMjyuUGvBiUhEcLIGMQ5g==", "shasum": "6dbdb86d96e955ab0fe4a4b60734ec0025a689dd", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.3.0.tgz", "fileCount": 4, "unpackedSize": 508890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAsX9AxUIeAveRZ4LDkN86VwwZt/jmgqRGO1vzHMewHgIhAMj0VreXC3474t3z2pbK8dw1/Wkmx8bx5T1k4f/PQORV"}], "size": 189491}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850816591, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.4.0", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-7jzcOonpXNWcSijPpKD5IbC6xC7yTibjJw9jviVzZostYLGxbz8LDJLUnLzLzhASPlPGgpeKLtFUMjAAzM+gSA==", "shasum": "4c33993618c8d5113722852806239cb80360494b", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.4.0.tgz", "fileCount": 4, "unpackedSize": 508890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmQtuCgvDDEuhG0+CSyQAp2MRTt0uHpLfVB2I6JfPfuQIgVQMtCe2LYiLWjQ5iCtQ7r90G8kZ1IA+Eya9OZum4xwc="}], "size": 189491}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360253749, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.4.1", "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-L2nZTYR1myLNST0O632g0Dx9LyMNHrn6TOt76sYxWLdff3cB22/GZX2UPtJnaqQPdCRoszoY5rcOj4oMTtp5fQ==", "shasum": "d2ebbf60e407170bb647cd6e447f4f2bab19ad16", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.4.1.tgz", "fileCount": 4, "unpackedSize": 508890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDK/phOWEQO6Hj/Pu39W34KJG83HjUQf9aBoWJgj37nFAIgA3KAL6sT+TcAaxhbXrfJI9ebj+XSNyfo2CdREoavR50="}], "size": 189491}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702682782, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.4.2-alpha.0", "directories": {}, "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "dist": {"integrity": "sha512-1dz4fTM5HaANk3RSRmdhALT+bNqTHawVDL1D77HwV/FuF/kSjlM3rGrJuFaCKwQ5E8CInHCcobqMN8Jh8LYaRg==", "shasum": "4c7cbef41c4cb3ef63272ac142860e5ada643887", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 512994, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOZnB4P1ksO+W+LTnq1xcYN90s68S3QssvWilY8kiHjAIhAJmUfuqs7VxkXbiZkH0U1HtlIlMTePLG/kWxZfkng1Ux"}], "size": 193225}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259261341, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-linux-x64-musl", "version": "2.5.0", "directories": {}, "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "dist": {"integrity": "sha512-iqOC+GoTDoFyk/VYSFHwjHhYrk8bljW6zOhPuhi5t9ulqiYq1togGJB5e3PwYVFFfeVgc6pbz3JdQyDoBszVaA==", "shasum": "01fcea60fedbb3225af808d3f0a7b11229792eef", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.0.tgz", "fileCount": 4, "unpackedSize": 512986, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmF4kGw59Abph6pyvNX9qZVKUiwoiHBhZV4zxX42TcaQIhAK60ZsPUpQf3vxpQC4fd7cgtYgf3SU2I/PiqMkv4RtlR"}], "size": 193123}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701348273, "_source_registry_name": "default"}}, "_source_registry_name": "default"}