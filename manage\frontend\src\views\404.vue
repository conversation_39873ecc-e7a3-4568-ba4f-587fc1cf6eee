<template>
  <div class="not-found">
    <el-result
      icon="warning"
      title="页面未找到"
      sub-title="您访问的页面不存在或已被移除"
    >
      <template #extra>
        <el-button type="primary" @click="goToHome">返回首页</el-button>
        <el-button @click="goToLogin">返回登录</el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const router = useRouter()
const isLoggedIn = computed(() => localStorage.getItem('token'))

/**
 * 返回首页
 * 如果用户已登录，则跳转到首页
 * 如果用户未登录，则跳转到登录页
 */
const goToHome = () => {
  if (isLoggedIn.value) {
    router.push('/')
  } else {
    router.push('/login')
  }
}

/**
 * 返回登录页
 * 清除登录状态并跳转到登录页
 */
const goToLogin = () => {
  // 清除登录状态
  localStorage.removeItem('token')
  router.push('/login')
}
</script>

<style scoped>
.not-found {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

:deep(.el-result__icon) {
  color: #f56c6c;
}

:deep(.el-result__title) {
  font-size: 24px;
  color: #303133;
}

:deep(.el-result__sub-title) {
  font-size: 16px;
  color: #606266;
}
</style>