<template>
  <div class="illustration-layout">
    <div class="content-wrapper">
      <div class="banner">
        <div class="banner-content">
          <h1>{{ bannerTitle }}</h1>
          <p>{{ bannerDesc }}</p>
        </div>
      </div>
      <el-tabs v-model="activeTab" class="illustration-tabs" @tab-click="handleTabClick">
        <el-tab-pane label="赛车图鉴管理" name="car">
          <router-view v-if="activeTab === 'car'" />
        </el-tab-pane>
        <el-tab-pane label="宠物图鉴管理" name="pet">
          <router-view v-if="activeTab === 'pet'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElTabs, ElTabPane } from 'element-plus'

const router = useRouter()
const route = useRoute()
const activeTab = ref('car')

// 根据当前标签页计算 banner 内容
const bannerTitle = computed(() => {
  return activeTab.value === 'car' ? 'QQ飞车赛车图鉴' : 'QQ飞车宠物图鉴'
})

const bannerDesc = computed(() => {
  return activeTab.value === 'car' 
    ? '收录海量赛车数据，助你轻松选择心仪赛车'
    : '收录海量宠物数据，助你轻松选择心仪宠物'
})

// 处理标签页切换
const handleTabClick = (tab) => {
  router.push(`/illustration/${tab.props.name}`)
}

// 监听路由变化
watch(
  () => route.path,
  (path) => {
    if (path.includes('/pet')) {
      activeTab.value = 'pet'
    } else {
      activeTab.value = 'car'
    }
  }
)

// 根据当前路由设置激活的标签
onMounted(() => {
  const path = route.path
  if (path.includes('/pet')) {
    activeTab.value = 'pet'
  } else {
    activeTab.value = 'car'
  }
})
</script>

<style scoped>
.illustration-layout {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px 60px;
}

.content-wrapper {
  max-width: 2200px;
  margin: 0 auto;
  padding: 0;
}

.banner {
  position: relative;
  width: 100%;
  height: 200px;
  margin-bottom: 20px;
  overflow: hidden;
  background: url('/banner.png') center/cover;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.banner-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 50px;
  background: linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.2) 100%);
  color: white;
}

.banner-content h1 {
  font-size: 36px;
  font-weight: bold;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
}

.banner-content p {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.4);
}

.illustration-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
  background: white;
  padding: 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  height: 56px;
  line-height: 56px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
}
</style> 