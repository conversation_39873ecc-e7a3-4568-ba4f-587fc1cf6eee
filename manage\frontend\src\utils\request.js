import axios from 'axios'
import { API_URL } from '@/config'

const service = axios.create({
  baseURL: API_URL,
  timeout: 60000,
  withCredentials: false,  // 关闭 withCredentials
  headers: {
    'Content-Type': 'application/json'
  }
})

// 添加请求拦截器
service.interceptors.request.use(
  config => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
service.interceptors.response.use(
  response => {
    console.log('Response:', response)  // 添加日志
    return response.data
  },
  error => {
    console.error('Response Error:', error.response || error)
    return Promise.reject(error)
  }
)

export default service
