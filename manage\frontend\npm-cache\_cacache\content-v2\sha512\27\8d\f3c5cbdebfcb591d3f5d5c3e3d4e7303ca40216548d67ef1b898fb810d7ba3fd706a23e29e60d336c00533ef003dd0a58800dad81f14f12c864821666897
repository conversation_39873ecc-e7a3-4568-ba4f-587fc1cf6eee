{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:03.028Z", "name": "@parcel/watcher-linux-arm64-musl", "versions": {"2.2.0-alpha.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.2.0-alpha.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-hVQm+7VXiIfU6mE/9B786RlLfCKDz648Tyv5ZCGo6NN6xsAHDJlVPmY023ZetitHzvWoZHGJ7IZZJ5j1NdqzxA==", "shasum": "a6e573d5cc6e1f20178823a9f499689e3c94ca61", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.2.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 438784, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2BOcvQng++i/Ju6QRSL7gndOOZrYR554Vy8Qm/ABPRAiBtAN5rSdqpSi0kavrazs7k2+iovV1sWVgSfT1ITE9NhQ=="}], "size": 166781}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687727616639, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.2.0-alpha.1", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-eenLYd1mIulgQdyDyMFVUpitqOKen6BXAU4FNZdCrEU3jW0ys9eeCq9XY61/U0/jhKcyiJA0bDj1M0xRXu+Qfw==", "shasum": "0aa98ca578f1aaa5381b4bf9d0cd58f50f046c45", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 438784, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHChitibmTR4lwGxDApMFnmSUJYmlMASxjRAzJ3mPRYCAiA4GSWh+9wB12uWfRvy1WCl0reExhvuS8Zcq6pnVcqraQ=="}], "size": 166781}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738109692, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.2.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-jS+qfhhoOBVWwMLP65MaG8xdInMK30pPW8wqTCg2AAuVJh5xepMbzkhHJ4zURqHiyY3EiIRuYu4ONJKCxt8iqA==", "shasum": "45511adc69c4c89b10965f0dd1519e56a00f512f", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.2.0.tgz", "fileCount": 4, "unpackedSize": 438776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAddj1fCQ7k0I5iU0+YUQC0BE4Fdu2vu0deBUrJmsJC4AiAYVLbmf+Zm9r22xhz/SRDMJUAofy6Kn+2/m37qLXzPrA=="}], "size": 166775}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269823238, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.2.1-alpha.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-+m7pMraCIyhQ2XnWH0G1b3uSEf2+bq2LqreNPHiUibe7BBpa7Z9d7hBMYmILLy+esEC2URcy6TafU+p1UHtnBA==", "shasum": "ba6293698017b1410a1d03b53ea2f1aa6b7a9b33", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 442888, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRXaY9znqvN6q73VpSr+FSK+xKpXPNkf8ClWinDkYlLAiEAuwc0uXXmfUmMY5zP43NjU+ePJicjSb+pbuAgERXBBUM="}], "size": 167619}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571410532, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.3.0-alpha.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-nRTglcNa9hO0HUDCL94GvrPqGyyq049mWEjHHOGxCm1orRC8P353Uq3C113Oo9QMveaAyLMF61qxV7AVDgN+wQ==", "shasum": "588fcdddcf56ab32c5030c3cecdc35f568d00248", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 463384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHgtJgFV8Y5N645dNv4UYuVVNAiEOpa5SX8PSWdCsWWaAiEAouziE68sBMQXk37oVvs2yGlxB5lqILQIW5FIKZIS6TA="}], "size": 174962}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750506679, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.3.0-alpha.1", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-XrBGsuoeCyf/UUJRoiLV/2/hp8/ZPBlAUwMNYWUl2H+BlFfCRQJEDcRyaHiokeOOZV5g9dKj0iLoiaSruE2iYg==", "shasum": "053f714cc4d1b4f5e238cdd5fcc8d3b76a68bd88", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 463384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkobdgirE37ZA7Ln1VjvofR1uwhlnojkoBFyvu0Tn1wgIgasvndpnoNpi6c7IG+CGxycQ946YQat8aAMWemAMXj4I="}], "size": 174962}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753403822, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.3.0-alpha.2", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-GAeTByVc7TZtZBTUVUFpuLouzBkGggkkwCITf4tpUNNMpTeJplgUixNt8vP/4MCQuPttEfUdVou2vBef0dt6iQ==", "shasum": "e4d24573d6790b353baec3da0396b8630ad8f848", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 463384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQD5+ffBR7gvfC67zo7y4nb9XIqJMQlxkmkkJcwz2qqITQIfEiCmMY4Ve3JbTVqoXGfRAKrUrMFhXEdAkCW6ibO/6Q=="}], "size": 174962}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485205370, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.3.0-alpha.3", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-PB/dBZLZ1JSae83WYfUbGIaKOqvQs980+32nD7bVm8kufed5OU42sQEr7mUZeshy5ElHn3YSem6h1+w3NeUeBQ==", "shasum": "e55cd508092a6c0fd358d35e5f75a7721a671262", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 463384, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCy97VbK/AMUa6caG7pjPjeIxH076y7GENyiPHzxp++FQIgPAITnjsrFqRtjfEqR/YrByJah0nfsoKPWD/uAqxh5A4="}], "size": 174962}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557604310, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.3.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-LXZAExpepJew0Gp8ZkJ+xDZaTQjLHv48h0p0Vw2VMFQ8A+RKrAvpFuPVCVwKJCr5SE+zvaG+Etg56qXvTDIedw==", "shasum": "de131a9fcbe1fa0854e9cbf4c55bed3b35bcff43", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.3.0.tgz", "fileCount": 4, "unpackedSize": 463376, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICWuBCzd+v2uRJ18GFIacYY6SfGmalm2zC6BCsB6YFjbAiA9QMDNZcaLGCqHdJK7EnMaRM8694k6wBI3NVmyWO3LWQ=="}], "size": 174953}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850813121, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.4.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-oyN+uA9xcTDo/45bwsd6TFHa7Lc7hKujyMlvwrCLvSckvWogndCEoVYFNfZ6JJ2KNL/6fFiGPcbjp8jJmEh5Ng==", "shasum": "27ffd5ca5f510ecd638f9ad22e2e813049db54e7", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.4.0.tgz", "fileCount": 4, "unpackedSize": 463376, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGi3MPYtSPjGW3LNJambD7w+9OUg52MBXD0xtQN9fubQIhAMZFixkMZJ3eejsWtwkii5X37W60YfsDUHRSc4xPZ9yG"}], "size": 174953}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360250556, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.4.1", "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "directories": {}, "dist": {"integrity": "sha512-p4Xb7JGq3MLgAfYhslU2SjoV9G0kI0Xry0kuxeG/41UfpjHGOhv7UoUDAz/jb1u2elbhazy4rRBL8PegPJFBhA==", "shasum": "bd39bc71015f08a4a31a47cd89c236b9d6a7f635", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.4.1.tgz", "fileCount": 4, "unpackedSize": 463376, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBE/YvcdlkMUe0jGZEcrEn414o8myoXkmq/pYB3ceyepAiAcv8lSX9zC0pp/78BT2mvskD5UmLPRL8uDFnUcLvmKLQ=="}], "size": 174953}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702674519, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.4.2-alpha.0", "directories": {}, "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "dist": {"integrity": "sha512-gXqEAoLG9bBCbQNUgqjSOxHcjpmCZmYT9M8UvrdTMgMYgXgiWcR8igKlPRd40mCIRZSkMpN2ScSy2WjQ0bQZnQ==", "shasum": "39afdf201d1b4d59824e3caf1684e8888ac6429c", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 471616, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGBJgKrabXEhmIOdfMKNRg50yRXeNCNc3qsg2+shxQcZAiEAltCF9ts2ygnxzDPV6HcfNd0lOtkrkRwHmVCKMRAPhFI="}], "size": 181274}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259257244, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-linux-arm64-musl", "version": "2.5.0", "directories": {}, "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "dist": {"integrity": "sha512-S1qARKOphxfiBEkwLUbHjCY9BWPdWnW9j7f7Hb2jPplu8UZ3nes7zpPOW9bkLbHRvWM0WDTsjdOTUgW0xLBN1Q==", "shasum": "dcb8ff01077cdf59a18d9e0a4dff7a0cfe5fd732", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.0.tgz", "fileCount": 4, "unpackedSize": 471608, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDX/pKQYwwALeDbD8ATHsXmUmOWSBngDR3j0sevAhznXAiEAvYlL29a+Jy1KHLIX6S1GANdHHNTCTFaMMSN7GzFO8gQ="}], "size": 181337}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701343636, "_source_registry_name": "default"}}, "_source_registry_name": "default"}