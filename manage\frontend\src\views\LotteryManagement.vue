<template>
  <div class="lottery-management-container">
    <div class="page-header">
      <h2>抽奖活动管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="showAddLotteryDialog">
          <el-icon><Plus /></el-icon>
          新增活动
        </el-button>
        <el-button @click="router.push('/')" type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <el-card class="lottery-table">
      <el-table
        v-loading="loading"
        :data="lotteryActivities"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="title" label="活动标题" min-width="180">
          <template #default="scope">
            <div class="title-cell">
              <el-icon><Promotion /></el-icon>
              <span>{{ scope.row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="活动图片" width="120" align="center">
          <template #default="scope">
            <el-image 
              :src="scope.row.image_url" 
              :preview-src-list="[scope.row.image_url]"
              style="width: 60px; height: 60px; border-radius: 4px;"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="结束时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.end_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getLotteryStatusType(scope.row)" size="small">
              {{ getLotteryStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                type="primary"
                link
                @click="handleViewPrizes(scope.row)"
              >
                奖品管理
              </el-button>
              <el-button
                type="warning"
                link
                @click="handleEditLottery(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleDeleteLottery(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 添加分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="10"
          :total="total"
          :pager-count="7"
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          background
        />
      </div>
    </el-card>

    <!-- 新增/编辑抽奖活动对话框 -->
    <el-dialog
      v-model="lotteryDialogVisible"
      :title="isEditMode ? '编辑抽奖活动' : '新增抽奖活动'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="lotteryFormRef"
        :model="lotteryForm"
        :rules="lotteryRules"
        label-width="100px"
      >
        <el-form-item label="活动标题" prop="title">
          <el-input v-model="lotteryForm.title" placeholder="请输入活动标题" />
        </el-form-item>
        
        <el-form-item label="活动图片" prop="image_url">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="lotteryForm.image_url" :src="lotteryForm.image_url" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议上传比例1:1的图片，大小不超过2MB</div>
        </el-form-item>
        
        <el-form-item label="活动时间" prop="time_range">
          <el-date-picker
            v-model="lotteryForm.time_range"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="活动描述" prop="description">
          <el-input 
            v-model="lotteryForm.description" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入活动描述"
            resize="none"
          />
        </el-form-item>
        
        <el-form-item label="活动状态" prop="status">
          <el-select v-model="lotteryForm.status" placeholder="请选择活动状态" style="width: 100%">
            <el-option label="草稿" :value="0" />
            <el-option label="已发布" :value="1" />
            <el-option label="已结束" :value="2" />
          </el-select>
        </el-form-item>

        <el-divider content-position="left">保底设置</el-divider>
        
        <el-form-item label="启用保底" prop="enable_guarantee">
          <el-switch v-model="lotteryForm.enable_guarantee" />
        </el-form-item>
        
        <template v-if="lotteryForm.enable_guarantee">
          <el-form-item label="保底抽数" prop="guarantee_draws">
            <el-input-number 
              v-model="lotteryForm.guarantee_draws" 
              :min="1" 
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">用户抽奖达到此次数后，必定获得保底奖品</div>
          </el-form-item>
          
          <el-form-item label="保底奖品" prop="guarantee_prize">
            <el-select 
              v-model="lotteryForm.guarantee_prize"
              placeholder="请选择保底奖品"
              filterable
              style="width: 100%"
            >
              <el-option 
                v-for="prize in lotteryPrizes" 
                :key="prize.id" 
                :label="prize.name" 
                :value="prize.id"
              />
            </el-select>
            <div class="form-tip" v-if="!lotteryPrizes.length">请先添加奖品，再设置保底奖品</div>
            <div class="form-tip" v-else>保底奖品应选择活动中较为稀有的奖品</div>
          </el-form-item>
        </template>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="lotteryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveLottery" :loading="saveLoading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 奖品管理对话框 -->
    <el-dialog
      v-model="prizesDialogVisible"
      title="奖品管理"
      width="900px"
      :close-on-click-modal="false"
      top="5vh"
    >
      <div class="dialog-header" v-if="currentLottery">
        <h3 class="dialog-title">{{ currentLottery.title }} - 奖品列表</h3>
        <div class="header-buttons">
          <el-button type="primary" class="header-button" @click="showImportPrizeDialog">
            <el-icon><Upload /></el-icon>
            Excel导入
          </el-button>
          <el-button type="success" class="header-button" @click="showAddPrizeDialog">
            <el-icon><Plus /></el-icon>
            新增奖品
          </el-button>
        </div>
      </div>
      
      <el-table
        v-loading="prizesLoading"
        :data="prizes"
        border
        style="width: 100%; margin-top: 20px;"
        max-height="450px"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="name" label="奖品名称" min-width="150" />
        <el-table-column label="奖品图片" width="100" align="center">
          <template #default="scope">
            <el-image 
              :src="scope.row.image_url" 
              :preview-src-list="[scope.row.image_url]"
              style="width: 50px; height: 50px; border-radius: 4px;"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="probability" label="概率(%)" width="120" align="center">
          <template #default="scope">
            {{ (scope.row.probability * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                type="primary"
                link
                @click="handleViewPrizeDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                type="warning"
                link
                @click="handleEditPrize(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleDeletePrize(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="probability-summary" v-if="prizes.length > 0">
        <p>当前概率总和: {{ getTotalProbability() }}%</p>
        <p v-if="getTotalProbability() !== 100" class="warning">
          <el-icon><Warning /></el-icon>
          概率总和应为100%
        </p>
        <p v-if="currentLottery && currentLottery.enable_guarantee" class="guarantee-info">
          <el-icon><InfoFilled /></el-icon>
          已开启保底: {{ currentLottery.guarantee_draws }}抽必得
          <span v-if="currentLottery.guarantee_prize">
            {{ getGuaranteePrizeName(currentLottery.guarantee_prize) }}
          </span>
        </p>
      </div>
      
      <div class="dialog-divider"></div>
      
      <div v-if="prizes.length === 0" class="empty-data">
        <el-empty description="暂无奖品数据" />
      </div>
    </el-dialog>
    
    <!-- 奖品详情对话框 -->
    <el-dialog
      v-model="prizeDetailVisible"
      title="奖品详情"
      width="600px"
    >
      <div v-if="currentPrize" class="prize-detail">
        <div class="detail-item">
          <span class="label">奖品名称：</span>
          <span>{{ currentPrize.name }}</span>
        </div>
        <div class="detail-item">
          <span class="label">奖品图片：</span>
          <el-image 
            :src="currentPrize.image_url" 
            :preview-src-list="[currentPrize.image_url]"
            style="width: 80px; height: 80px; border-radius: 4px;"
            fit="cover"
          />
        </div>
        <div class="detail-item">
          <span class="label">中奖概率：</span>
          <span>{{ (currentPrize.probability * 100).toFixed(2) }}%</span>
        </div>
        <div class="detail-item">
          <span class="label">奖品说明：</span>
          <div class="content">{{ currentPrize.description || '无' }}</div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 新增/编辑奖品对话框 -->
    <el-dialog
      v-model="prizeDialogVisible"
      :title="isPrizeEditMode ? '编辑奖品' : '新增奖品'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="prizeFormRef"
        :model="prizeForm"
        :rules="prizeRules"
        label-width="100px"
      >
        <el-form-item label="奖品名称" prop="name">
          <el-input v-model="prizeForm.name" placeholder="请输入奖品名称" />
        </el-form-item>
        
        <el-form-item label="奖品图片" prop="image_url">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handlePrizeImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="prizeForm.image_url" :src="prizeForm.image_url" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="奖品概率" prop="probability">
          <el-input-number 
            v-model="prizeForm.probability" 
            :precision="2" 
            :step="0.01" 
            :min="0" 
            :max="100"
            style="width: 100%"
          />
          <div class="form-tip">输入0-100之间的数值，表示百分比概率</div>
        </el-form-item>
        
        <el-form-item label="奖品说明" prop="description">
          <el-input 
            v-model="prizeForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入奖品说明"
            resize="none"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="prizeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSavePrize" :loading="prizeSaveLoading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="Excel导入奖品"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="import-container">
        <el-upload
          class="upload-excel"
          action="/api/lottery/prizes/import"
          :headers="{ 'Content-Type': 'multipart/form-data' }"
          :data="{ lottery_id: currentLottery?.id }"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeExcelUpload"
          :show-file-list="false"
          accept=".xlsx,.xls"
        >
          <el-button type="primary" :loading="importLoading">
            <el-icon><Upload /></el-icon>
            点击上传Excel文件
          </el-button>
        </el-upload>
        
        <div class="import-tips">
          <h4>导入说明：</h4>
          <p>1. 请下载Excel模板，按格式填写后上传</p>
          <p>2. Excel中必须包含"奖品名称"、"奖品概率(%)"和"奖品说明"列</p>
          <p>3. 所有奖品概率之和应等于100%</p>
          <p>4. 上传成功后，系统会自动校验数据并导入</p>
        </div>
        
        <div class="template-download">
          <el-button type="info" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
        </div>
        
        <el-divider />
        
        <div v-if="importResult.show" class="import-result">
          <el-alert
            :title="importResult.success ? '导入成功' : '导入失败'"
            :type="importResult.success ? 'success' : 'error'"
            :description="importResult.message"
            :closable="false"
            show-icon
          />
          
          <div v-if="importResult.success" class="result-summary">
            <p>成功导入: {{ importResult.total }} 条记录</p>
          </div>
          
          <div v-if="importResult.errors?.length" class="result-errors">
            <p>错误记录:</p>
            <ul>
              <li v-for="(error, index) in importResult.errors" :key="index">
                第{{ error.row }}行: {{ error.message }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Back, 
  Promotion, 
  Picture, 
  Warning,
  InfoFilled,
  Upload,
  Download
} from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const lotteryActivities = ref([])
const lotteryDialogVisible = ref(false)
const prizesDialogVisible = ref(false)
const prizeDialogVisible = ref(false)
const prizeDetailVisible = ref(false)
const saveLoading = ref(false)
const prizesLoading = ref(false)
const prizeSaveLoading = ref(false)
const isEditMode = ref(false)
const isPrizeEditMode = ref(false)
const currentLottery = ref(null)
const currentPrize = ref(null)
const prizes = ref([])
const lotteryFormRef = ref(null)
const prizeFormRef = ref(null)
const importDialogVisible = ref(false)
const importLoading = ref(false)
const importResult = ref({
  show: false,
  success: false,
  message: '',
  total: 0,
  errors: []
})

// 抽奖活动表单
const lotteryForm = ref({
  id: null,
  title: '',
  image_url: '',
  time_range: [],
  description: '',
  status: 0,
  enable_guarantee: false,
  guarantee_draws: 1,
  guarantee_prize: null
})

// 奖品表单
const prizeForm = ref({
  id: null,
  lottery_id: null,
  name: '',
  image_url: '',
  probability: 0,
  description: '',
  is_unlimited: true // 默认设置为不限量
})

// 表单验证规则
const lotteryRules = {
  title: [
    { required: true, message: '请输入活动标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  image_url: [
    { required: true, message: '请上传活动图片', trigger: 'change' }
  ],
  time_range: [
    { required: true, message: '请选择活动时间范围', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ]
}

// 奖品表单验证规则
const prizeRules = {
  name: [
    { required: true, message: '请输入奖品名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  image_url: [
    { required: true, message: '请上传奖品图片', trigger: 'change' }
  ],
  probability: [
    { required: true, message: '请输入奖品概率', trigger: 'blur' }
  ]
}

// 添加分页相关的响应式变量
const currentPage = ref(1)
const total = ref(0)

// 加载抽奖活动列表
const fetchLotteryActivities = async () => {
  try {
    loading.value = true
    // 模拟API调用，实际项目中应该替换为真实的API调用
    // const response = await getLotteryActivities({
    //   page: currentPage.value,
    //   page_size: 10
    // })
    // if (response.success) {
    //   lotteryActivities.value = response.data.results
    //   total.value = response.data.count
    // }
    
    // 模拟数据
    setTimeout(() => {
      lotteryActivities.value = [
        {
          id: 1,
          title: '国庆节抽奖活动',
          image_url: 'https://via.placeholder.com/200x200',
          start_time: '2023-10-01 00:00:00',
          end_time: '2023-10-07 23:59:59',
          description: '国庆七天乐，每天登录抽好礼',
          status: 1,
          enable_guarantee: true,
          guarantee_draws: 50,
          guarantee_prize: 1 // 指向奖品ID
        },
        {
          id: 2,
          title: '圣诞特别活动',
          image_url: 'https://via.placeholder.com/200x200',
          start_time: '2023-12-24 00:00:00',
          end_time: '2023-12-26 23:59:59',
          description: '圣诞节特别活动，赢取限定赛车和装饰',
          status: 0,
          enable_guarantee: false,
          guarantee_draws: 0,
          guarantee_prize: null
        }
      ]
      // 模拟总数据量
      total.value = 15
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('Failed to fetch lottery activities:', error)
    ElMessage.error('获取抽奖活动列表失败')
    loading.value = false
  }
}

/**
 * 处理页码变化
 * @param {number} val - 新的页码
 */
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchLotteryActivities()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取活动状态标签类型
const getLotteryStatusType = (lottery) => {
  const now = new Date()
  const startTime = new Date(lottery.start_time)
  const endTime = new Date(lottery.end_time)
  
  if (lottery.status === 0) return 'info'
  if (now < startTime) return 'warning'
  if (now > endTime) return 'danger'
  return 'success'
}

// 获取活动状态文本
const getLotteryStatusText = (lottery) => {
  const now = new Date()
  const startTime = new Date(lottery.start_time)
  const endTime = new Date(lottery.end_time)
  
  if (lottery.status === 0) return '草稿'
  if (now < startTime) return '未开始'
  if (now > endTime) return '已结束'
  return '进行中'
}

// 显示新增抽奖活动对话框
const showAddLotteryDialog = () => {
  isEditMode.value = false
  lotteryForm.value = {
    id: null,
    title: '',
    image_url: '',
    time_range: [],
    description: '',
    status: 0,
    enable_guarantee: false,
    guarantee_draws: 1,
    guarantee_prize: null
  }
  lotteryDialogVisible.value = true
}

// 处理编辑抽奖活动
const handleEditLottery = (lottery) => {
  isEditMode.value = true
  lotteryForm.value = {
    id: lottery.id,
    title: lottery.title,
    image_url: lottery.image_url,
    time_range: [lottery.start_time, lottery.end_time],
    description: lottery.description,
    status: lottery.status,
    enable_guarantee: lottery.enable_guarantee,
    guarantee_draws: lottery.guarantee_draws,
    guarantee_prize: lottery.guarantee_prize
  }
  lotteryDialogVisible.value = true
}

// 处理删除抽奖活动
const handleDeleteLottery = async (lottery) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除抽奖活动 "${lottery.title}" 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用，实际项目中应该替换为真实的API调用
    // await deleteLotteryActivity(lottery.id)
    
    ElMessage.success('抽奖活动删除成功')
    
    // 如果当前页只有一条数据，且不是第一页，则跳转到上一页
    if (lotteryActivities.value.length === 1 && currentPage.value > 1) {
      currentPage.value--
    }
    
    fetchLotteryActivities()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete lottery activity:', error)
      ElMessage.error('删除抽奖活动失败')
    }
  }
}

// 处理保存抽奖活动
const handleSaveLottery = async () => {
  if (!lotteryFormRef.value) return
  
  await lotteryFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saveLoading.value = true
        
        const lotteryData = {
          id: lotteryForm.value.id,
          title: lotteryForm.value.title,
          image_url: lotteryForm.value.image_url,
          start_time: lotteryForm.value.time_range[0],
          end_time: lotteryForm.value.time_range[1],
          description: lotteryForm.value.description,
          status: lotteryForm.value.status,
          enable_guarantee: lotteryForm.value.enable_guarantee,
          guarantee_draws: lotteryForm.value.guarantee_draws,
          guarantee_prize: lotteryForm.value.guarantee_prize
        }
        
        // 模拟API调用，实际项目中应该替换为真实的API调用
        // if (isEditMode.value) {
        //   await updateLotteryActivity(lotteryData)
        // } else {
        //   await createLotteryActivity(lotteryData)
        // }
        
        ElMessage.success(`抽奖活动${isEditMode.value ? '更新' : '创建'}成功`)
        lotteryDialogVisible.value = false
        fetchLotteryActivities()
      } catch (error) {
        console.error(`Failed to ${isEditMode.value ? 'update' : 'create'} lottery activity:`, error)
        ElMessage.error(`${isEditMode.value ? '更新' : '创建'}抽奖活动失败`)
      } finally {
        saveLoading.value = false
      }
    }
  })
}

// 处理图片上传前的验证
const beforeImageUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isJpgOrPng) {
    ElMessage.error('只能上传JPG或PNG格式的图片!')
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
  }
  
  return isJpgOrPng && isLt2M
}

// 处理活动图片上传成功
const handleImageSuccess = (response) => {
  // 实际项目中，这里应该从response中获取图片URL
  // lotteryForm.value.image_url = response.data.url
  
  // 模拟上传成功
  lotteryForm.value.image_url = 'https://via.placeholder.com/200x200'
}

// 处理奖品图片上传成功
const handlePrizeImageSuccess = (response) => {
  // 实际项目中，这里应该从response中获取图片URL
  // prizeForm.value.image_url = response.data.url
  
  // 模拟上传成功
  prizeForm.value.image_url = 'https://via.placeholder.com/200x200'
}

// 查看活动奖品
const handleViewPrizes = (lottery) => {
  currentLottery.value = lottery
  fetchPrizes(lottery.id)
  prizesDialogVisible.value = true
}

// 加载奖品列表
const fetchPrizes = async (lotteryId) => {
  try {
    prizesLoading.value = true
    // 模拟API调用，实际项目中应该替换为真实的API调用
    // const response = await getLotteryPrizes(lotteryId)
    // if (response.success) {
    //   prizes.value = response.data
    // }
    
    // 模拟数据
    setTimeout(() => {
      prizes.value = [
        {
          id: 1,
          lottery_id: lotteryId,
          name: '一等奖：永久赛车',
          image_url: 'https://via.placeholder.com/100x100',
          probability: 0.01,
          description: '随机获得一辆永久赛车',
          is_unlimited: true
        },
        {
          id: 2,
          lottery_id: lotteryId,
          name: '二等奖：7天赛车',
          image_url: 'https://via.placeholder.com/100x100',
          probability: 0.05,
          description: '随机获得一辆7天赛车',
          is_unlimited: true
        },
        {
          id: 3,
          lottery_id: lotteryId,
          name: '三等奖：礼包',
          image_url: 'https://via.placeholder.com/100x100',
          probability: 0.2,
          description: '随机礼包，包含强化材料和金币',
          is_unlimited: true
        },
        {
          id: 4,
          lottery_id: lotteryId,
          name: '谢谢参与',
          image_url: 'https://via.placeholder.com/100x100',
          probability: 0.74,
          description: '很遗憾，没有中奖',
          is_unlimited: true
        }
      ]
      prizesLoading.value = false
    }, 500)
  } catch (error) {
    console.error('Failed to fetch prizes:', error)
    ElMessage.error('获取奖品列表失败')
    prizesLoading.value = false
  }
}

// 显示新增奖品对话框
const showAddPrizeDialog = () => {
  isPrizeEditMode.value = false
  prizeForm.value = {
    id: null,
    lottery_id: currentLottery.value.id,
    name: '',
    image_url: '',
    probability: 0,
    description: '',
    is_unlimited: true
  }
  prizeDialogVisible.value = true
}

// 处理编辑奖品
const handleEditPrize = (prize) => {
  isPrizeEditMode.value = true
  prizeForm.value = {
    id: prize.id,
    lottery_id: prize.lottery_id,
    name: prize.name,
    image_url: prize.image_url,
    probability: prize.probability * 100, // 转换为百分比显示
    description: prize.description,
    is_unlimited: prize.is_unlimited
  }
  prizeDialogVisible.value = true
}

// 处理删除奖品
const handleDeletePrize = async (prize) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除奖品 "${prize.name}" 吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用，实际项目中应该替换为真实的API调用
    // await deletePrize(prize.id)
    
    ElMessage.success('奖品删除成功')
    fetchPrizes(currentLottery.value.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete prize:', error)
      ElMessage.error('删除奖品失败')
    }
  }
}

// 处理保存奖品
const handleSavePrize = async () => {
  if (!prizeFormRef.value) return
  
  await prizeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        prizeSaveLoading.value = true
        
        const prizeData = {
          id: prizeForm.value.id,
          lottery_id: prizeForm.value.lottery_id,
          name: prizeForm.value.name,
          image_url: prizeForm.value.image_url,
          probability: prizeForm.value.probability / 100, // 转换为小数存储
          description: prizeForm.value.description,
          is_unlimited: prizeForm.value.is_unlimited
        }
        
        // 模拟API调用，实际项目中应该替换为真实的API调用
        // if (isPrizeEditMode.value) {
        //   await updatePrize(prizeData)
        // } else {
        //   await createPrize(prizeData)
        // }
        
        ElMessage.success(`奖品${isPrizeEditMode.value ? '更新' : '创建'}成功`)
        prizeDialogVisible.value = false
        fetchPrizes(currentLottery.value.id)
      } catch (error) {
        console.error(`Failed to ${isPrizeEditMode.value ? 'update' : 'create'} prize:`, error)
        ElMessage.error(`${isPrizeEditMode.value ? '更新' : '创建'}奖品失败`)
      } finally {
        prizeSaveLoading.value = false
      }
    }
  })
}

// 获取奖品概率总和
const getTotalProbability = () => {
  return prizes.value.reduce((sum, prize) => sum + prize.probability * 100, 0).toFixed(2)
}

// 处理奖品详情
const handleViewPrizeDetail = (prize) => {
  currentPrize.value = prize
  prizeDetailVisible.value = true
}

// 获取当前活动的奖品列表，用于设置保底奖品
const lotteryPrizes = computed(() => {
  if (isEditMode.value && lotteryForm.value.id) {
    // 仅在编辑模式下才有可能获取到该活动已有的奖品
    return prizes.value.filter(prize => prize.lottery_id === lotteryForm.value.id)
  }
  return []
})

// 获取保底奖品名称
const getGuaranteePrizeName = (prizeId) => {
  const prize = prizes.value.find(p => p.id === prizeId)
  return prize ? prize.name : '未知奖品'
}

// 显示Excel导入对话框
const showImportPrizeDialog = () => {
  importDialogVisible.value = true
}

// 处理Excel导入成功
const handleImportSuccess = (response) => {
  importLoading.value = false
  // 实际项目中，这里应该从response中获取导入结果
  // importResult.value = {
  //   show: true,
  //   success: response.success,
  //   message: response.message,
  //   total: response.data.total,
  //   errors: response.data.errors
  // }
  
  // 模拟导入成功
  importResult.value = {
    show: true,
    success: true,
    message: '奖品数据导入成功',
    total: 10,
    errors: []
  }
  
  ElMessage.success('奖品导入成功')
  
  // 刷新奖品列表
  fetchPrizes(currentLottery.value.id)
}

// 处理Excel导入失败
const handleImportError = (error) => {
  importLoading.value = false
  // 实际项目中，这里应该处理导入失败的情况
  // const errorMessage = error.response?.data?.message || '导入失败，请检查文件格式'
  
  // 模拟导入失败
  importResult.value = {
    show: true,
    success: false,
    message: '导入失败，请检查文件格式',
    total: 0,
    errors: [
      { row: 2, message: '概率格式不正确' },
      { row: 5, message: '奖品名称不能为空' }
    ]
  }
  
  ElMessage.error('奖品导入失败')
}

// 处理Excel导入前的验证
const beforeExcelUpload = (file) => {
  importLoading.value = true
  importResult.value.show = false
  
  const isXlsxOrXls = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel'
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isXlsxOrXls) {
    ElMessage.error('只能上传XLSX或XLS格式的文件!')
    importLoading.value = false
    return false
  }
  if (!isLt2M) {
    ElMessage.error('文件大小不能超过2MB!')
    importLoading.value = false
    return false
  }
  
  return true
}

// 下载导入模板
const downloadTemplate = () => {
  // 实际项目中，这里应该生成并下载导入模板
  // 这里只是一个示例，实际实现需要根据项目需求
  const templateBlob = new Blob(['奖品名称,奖品概率(%),奖品说明\n一等奖：永久赛车,0.01,随机获得一辆永久赛车\n二等奖：7天赛车,0.05,随机获得一辆7天赛车\n三等奖：礼包,0.2,随机礼包，包含强化材料和金币\n谢谢参与,0.74,很遗憾，没有中奖'], { type: 'text/csv' })
  const templateUrl = URL.createObjectURL(templateBlob)
  const a = document.createElement('a')
  a.href = templateUrl
  a.download = '奖品导入模板.csv'
  a.click()
  URL.revokeObjectURL(templateUrl)
}

onMounted(() => {
  fetchLotteryActivities()
})
</script>

<style scoped>
.lottery-management-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.lottery-table {
  margin-top: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 6px;
}

.title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.upload-tip,
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.header-button {
  padding: 8px 16px;
}

:deep(.el-button--success) {
  --el-button-hover-bg-color: #67c23a;
  --el-button-hover-border-color: #67c23a;
}

:deep(.el-button--link) {
  height: 28px;
  padding: 0 8px;
}

.probability-summary {
  margin-top: 20px;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border-left: 4px solid #E6A23C;
}

.dialog-divider {
  height: 1px;
  background-color: #EBEEF5;
  margin: 20px 0;
}

.warning {
  color: #E6A23C;
  display: flex;
  align-items: center;
  gap: 5px;
}

.guarantee-info {
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  margin-top: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

.is-admin, .has-permission {
  --el-switch-on-color: #13ce66;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.prize-detail {
  padding: 20px;
}

.detail-item {
  margin-bottom: 16px;
  line-height: 1.5;
}

.detail-item .label {
  color: #606266;
  margin-right: 8px;
  display: inline-block;
  width: 84px;
  font-weight: 500;
}

.detail-item .content {
  margin-top: 8px;
  margin-left: 84px;
  white-space: pre-wrap;
  word-break: break-all;
  color: #303133;
  line-height: 1.6;
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.count-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-text {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.import-container {
  padding: 0 10px;
}

.upload-excel {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.import-tips {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #409EFF;
}

.import-tips h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #409EFF;
}

.import-tips p {
  margin: 5px 0;
  line-height: 1.5;
  font-size: 14px;
  color: #606266;
}

.template-download {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.import-result {
  margin: 20px 0;
}

.result-summary {
  background-color: #f0f9eb;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 15px 0;
}

.result-errors {
  background-color: #fff6f7;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 15px 0;
  color: #F56C6C;
}

.result-errors ul {
  margin: 10px 0;
  padding-left: 20px;
}

.result-errors li {
  margin-bottom: 5px;
}
</style> 