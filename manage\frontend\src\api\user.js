import request from '@/utils/request'

// 获取用户列表
export function getUserList() {
  return request({
    url: '/users/',
    method: 'get'
  })
}

// 更新用户权限
export function updateUserPermission(userId, data) {
  return request({
    url: `/users/${userId}/permission/`,
    method: 'put',
    data
  })
}

// 更新管理员状态
export function updateAdminStatus(userId, data) {
  return request({
    url: `/users/${userId}/admin/`,
    method: 'put',
    data
  })
} 