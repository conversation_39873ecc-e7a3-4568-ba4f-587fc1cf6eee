<template>
  <div id="app">
    <!-- 对于登录页和404页面，使用简单布局 -->
    <template v-if="route.path === '/login' || route.name === 'NotFoundRedirect' || route.path === '/404' || route.meta.is404">
      <router-view></router-view>
    </template>
    <!-- 对于其他页面，使用带导航栏的布局 -->
    <el-container v-else>
      <el-header>
        <div class="header-content">
          <div class="logo">QQ飞车赛车图鉴管理系统</div>
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                <el-avatar :size="32" icon="UserFilled" />
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="password">修改密码</el-dropdown-item>
                  <el-dropdown-item
                    v-if="isAdmin && currentUsername === 'Pikario'"
                    command="sqlExecutor"
                  >SQL执行器</el-dropdown-item>
                  <el-dropdown-item
                    v-if="isAdmin"
                    command="users"
                  >用户管理</el-dropdown-item>
                  <el-dropdown-item
                    v-if="isAdmin"
                    command="feedback"
                  >反馈管理</el-dropdown-item>
                  <el-dropdown-item
                    v-if="isAdmin"
                    command="comments"
                  >评论管理</el-dropdown-item>
                  <el-dropdown-item
                    v-if="isAdmin"
                    command="prize-management"
                  >道具与奖品管理</el-dropdown-item>
                  <!-- <el-dropdown-item
                    v-if="isAdmin"
                    command="vip-management"
                  >VIP管理</el-dropdown-item>-->
                  <!-- <el-dropdown-item
                    v-if="isAdmin"
                    command="car-treasure"
                  >赛车夺宝</el-dropdown-item> -->
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>

      <!-- 添加修改密码对话框 -->
      <el-dialog
        v-model="passwordDialogVisible"
        title="修改密码"
        width="400px"
      >
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="100px"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              show-password
              placeholder="请输入原密码"
            />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              show-password
              placeholder="请输入新密码"
            />
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              show-password
              placeholder="请再次输入新密码"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="passwordDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleUpdatePassword">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { updatePassword } from '@/api/auth'

const router = useRouter()
const route = useRoute()
const passwordFormRef = ref(null)
const passwordDialogVisible = ref(false)

const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能小于8位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!/[A-Z]/.test(value)) {
          callback(new Error('密码必须包含大写字母'))
          return
        }
        if (!/[a-z]/.test(value)) {
          callback(new Error('密码必须包含小写字母'))
          return
        }
        if (!/[0-9]/.test(value)) {
          callback(new Error('密码必须包含数字'))
          return
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
          callback(new Error('密码必须包含特殊字符'))
          return
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const isLoggedIn = computed(() => {
  return localStorage.getItem('token')
})

const isAdmin = computed(() => {
  const token = localStorage.getItem('token')
  if (!token) return false
  const payload = JSON.parse(atob(token.split('.')[1]))
  return payload.is_admin
})

// 获取当前用户ID
const getCurrentUserId = () => {
  const token = localStorage.getItem('token')
  if (!token) return null
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    console.log('Token payload:', payload) // 添加调试信息
    // 尝试多种可能的属性名
    return payload.user_id || payload.id || payload.sub
  } catch (error) {
    console.error('Token解析失败:', error)
    return null
  }
}

// 添加获取当前用户名的计算属性
const currentUsername = computed(() => {
  const token = localStorage.getItem('token')
  if (!token) return null
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.username || null
  } catch (error) {
    console.error('Token解析失败:', error)
    return null
  }
})

/**
 * 处理修改密码
 */
const handleUpdatePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const userId = getCurrentUserId()
        if (!userId) {
          ElMessage.error('获取用户信息失败，请重新登录')
          router.push('/login')
          return
        }

        await updatePassword(userId, {
          old_password: passwordForm.value.oldPassword,
          new_password: passwordForm.value.newPassword
        })

        ElMessage.success('密码修改成功')
        passwordDialogVisible.value = false
        // 清空表单
        passwordForm.value = {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
      } catch (error) {
        ElMessage.error(error.response?.data?.message || '密码修改失败')
      }
    }
  })
}

/**
 * 处理下拉菜单命令
 * @param {string} command - 菜单命令
 */
const handleCommand = (command) => {
  switch (command) {
    case 'password':
      passwordDialogVisible.value = true
      break
    case 'users':
      router.push('/users')
      break
    case 'feedback':
      router.push('/feedback')
      break
    case 'comments':
      router.push('/comments')
      break
    case 'sqlExecutor':
      router.push('/sql-executor')
      break
    case 'lottery':
      router.push('/lottery')
      break
    case 'car-treasure':
      router.push('/car-treasure')
      break
    case 'vip-management':
      router.push('/vip-management')
      break
    case 'prize-management':
      router.push('/prize-management')
      break
    case 'logout':
      localStorage.removeItem('token')
      router.push('/login')
      ElMessage.success('已退出登录')
      break
  }
}

// 注意：主要的路由守卫逻辑已移至router/index.js
// 这里的代码保留仅用于参考，实际上不会执行
// 如需修改路由守卫逻辑，请修改router/index.js文件
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 0;
  padding: 0;
  height: 100vh;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  padding: 0 20px;
}

.header-content {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.header-right {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-main {
  padding: 0;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
  overflow-y: auto;
}
</style>
