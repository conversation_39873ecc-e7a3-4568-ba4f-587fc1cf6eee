<template>
  <div class="sql-executor">
    <div class="page-header">
      <h2>SQL执行器</h2>
      <div class="header-actions">
        <el-button @click="router.push('/')" type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>
    
    <el-card class="box-card">
      <!-- SQL输入区域 -->
      <el-form>
        <!-- 添加常用SQL示例 -->
        <el-form-item>
          <div class="sql-examples">
            <span class="example-label">常用示例：</span>
            <el-dropdown>
              <el-button link>
                查询示例
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleSqlExample('SELECT * FROM cars_car LIMIT 10')">
                    查询前10条数据
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleSqlExample('SELECT DISTINCT level FROM cars_car')">
                    查询所有等级
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleSqlExample('SELECT * FROM cars_car WHERE level = \'S\' LIMIT 5')">
                    查询S级赛车
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleSqlExample('SELECT COUNT(*) as total, level FROM cars_car GROUP BY level')">
                    各等级数量统计
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-dropdown>
              <el-button link>
                高级查询
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleSqlExample('SELECT level, COUNT(*) as count, AVG(CAST(REPLACE(normal_speed, \'km/h\', \'\') AS FLOAT)) as avg_speed\nFROM cars_car\nGROUP BY level\nORDER BY avg_speed DESC')">
                    各等级平均速度
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleSqlExample('SELECT *\nFROM cars_car\nWHERE updated_at >= date(\'now\', \'-7 days\')\nORDER BY updated_at DESC')">
                    最近一周更新
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleSqlExample('SELECT *\nFROM cars_car\nWHERE CAST(REPLACE(normal_speed, \'km/h\', \'\') AS FLOAT) > 221\nORDER BY CAST(REPLACE(normal_speed, \'km/h\', \'\') AS FLOAT) DESC')">
                    高速赛车查询
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-form-item>
        <el-form-item label="SQL语句">
          <Codemirror
            v-model="sqlInput"
            :extensions="extensions"
            :placeholder="'请输入SQL语句...'"
            :indent-with-tab="true"
            :tabSize="2"
            :style="{ width: '100%' }"
            class="sql-editor"
            @change="handleEditorChange"
          />
        </el-form-item>
        <el-form-item>
          <div class="form-actions">
            <el-button type="primary" @click="executeSql" :loading="loading">
              <el-icon><Document /></el-icon>
              执行SQL
            </el-button>
            <el-button @click="clearEditor">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <!-- 结果展示区域 -->
      <el-card v-if="result?.success" class="result-card">
        <template #header>
          <div class="result-header">
            <div class="result-title">
              <el-icon><DataAnalysis /></el-icon>
              <span>查询结果</span>
            </div>
            <div class="result-info" v-if="result?.data?.message">
              <!-- 添加结果消息提示 -->
              <el-tag type="success" effect="plain">
                {{ result.data.message }}
              </el-tag>
            </div>
          </div>
        </template>

        <!-- 查询结果表格 -->
        <div v-if="result?.data?.headers && result?.data?.rows" class="result-content">
          <el-table
            :data="tableData"
            style="width: 100%"
            border
            stripe
            size="small"
            :max-height="tableMaxHeight"
            row-key="key"
            v-loading="loading"
          >
            <el-table-column
              v-for="header in result.data.headers"
              :key="header"
              :prop="header"
              :label="header"
              :min-width="100"
              :width="getColumnWidth(header)"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <template v-if="row[header] === null || row[header] === undefined">
                  -
                </template>
                <template v-else-if="header.includes('_at') && row[header]">
                  {{ new Date(row[header]).toLocaleString() }}
                </template>
                <template v-else>
                  {{ row[header] }}
                </template>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页器 -->
          <el-pagination
            v-if="(result?.data?.total_count || 0) > 0"
            class="pagination"
            :total="result.data.total_count || 0"
            :page-size="pageSize"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, sizes, prev, pager, next"
          />
        </div>

        <!-- 执行日志 -->
        <div class="log-section" v-if="result?.log" style="margin-top: 16px">
          <el-descriptions
            title="执行日志"
            :column="1"
            border
            size="small"
          >
            <el-descriptions-item label="执行时间">
              {{ result.log.timestamp }}
            </el-descriptions-item>
            <el-descriptions-item label="执行用户">
              {{ result.log.user }}
            </el-descriptions-item>
            <el-descriptions-item label="执行耗时">
              {{ result.log.execution_time }}
            </el-descriptions-item>
            <el-descriptions-item label="影响行数">
              {{ result.log.affected_rows }}
            </el-descriptions-item>
            <el-descriptions-item label="执行状态">
              <el-tag :type="result.log.status === 'success' ? 'success' : 'danger'">
                {{ result.log.status }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 错误信息展示 -->
      <div v-if="error" class="error-section">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { defineComponent, ref, shallowRef, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { useRouter } from 'vue-router'
import { Back, Document, Delete, DataAnalysis, ArrowDown } from '@element-plus/icons-vue'
import { Codemirror } from 'vue-codemirror'
import { sql } from '@codemirror/lang-sql'
import { oneDark } from '@codemirror/theme-one-dark'
import { syntaxHighlighting, HighlightStyle } from '@codemirror/language'
import { tags } from '@lezer/highlight'
import { EditorView } from '@codemirror/view'
import { EditorState } from '@codemirror/state'

export default defineComponent({
  name: 'SqlExecutor',
  components: {
    Back,
    Document,
    Delete,
    DataAnalysis,
    ArrowDown,
    Codemirror
  },
  setup() {
    const router = useRouter()
    const sqlInput = ref('')
    const result = ref(null)
    const error = ref(null)
    const loading = ref(false)
    const tableData = ref([])
    const tableMaxHeight = ref(400)
    const currentPage = ref(1)
    const pageSize = ref(10)

    // SQL关键字高亮样式
    const mySqlTheme = HighlightStyle.define([
      { tag: tags.keyword, color: '#ff79c6', fontWeight: 'bold' },
      { tag: tags.operator, color: '#ff79c6' },
      { tag: tags.special(tags.variableName), color: '#bd93f9' },
      { tag: tags.string, color: '#f1fa8c' },
      { tag: tags.number, color: '#bd93f9' },
      { tag: tags.comment, color: '#6272a4', fontStyle: 'italic' },
      { tag: tags.function(tags.variableName), color: '#50fa7b' },
      { tag: tags.definition(tags.propertyName), color: '#66d9ef' },
    ])

    // 处理SQL示例点击
    const handleSqlExample = (sql) => {
      sqlInput.value = sql
    }

    // 处理编辑器内容变化
    const handleEditorChange = (value) => {
      sqlInput.value = value
    }

    // 清空编辑器
    const clearEditor = () => {
      sqlInput.value = ''
    }

    // CodeMirror扩展配置
    const extensions = shallowRef([
      sql(),
      oneDark,
      syntaxHighlighting(mySqlTheme),
      EditorView.lineWrapping,
      EditorState.tabSize.of(2),
      EditorState.phrases.of({
        placeholder: '请输入SQL语句...'
      }),
      EditorView.theme({
        '&': {
          backgroundColor: '#282c34 !important',
          height: '240px !important',
          width: '100% !important'
        },
        '.cm-content': {
          fontFamily: '"JetBrains Mono", monospace',
          fontSize: '14px',
          padding: '8px 0'
        },
        '.cm-line': {
          padding: '0 8px'
        },
        '&.cm-focused': {
          outline: 'none !important'
        },
        '.cm-gutters': {
          backgroundColor: '#21252b !important',
          borderRight: '1px solid #333 !important',
          color: '#676f7d'
        },
        '.cm-activeLineGutter': {
          backgroundColor: '#2c313c !important'
        },
        '.cm-activeLine': {
          backgroundColor: '#2c313c !important'
        },
        '.cm-selectionMatch': {
          backgroundColor: '#3e4451 !important'
        },
        '.cm-cursor': {
          borderLeft: '2px solid #528bff !important'
        },
        '.cm-placeholder': {
          color: '#666 !important'
        }
      })
    ])

    // 处理页码变化
    const handleCurrentChange = (page) => {
      currentPage.value = page
      executeSql()
    }
    
    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1 // 重置到第一页
      executeSql()
    }

    // 执行SQL
    const executeSql = async () => {
      if (!sqlInput.value.trim()) {
        ElMessage.warning('请输入SQL语句')
        return
      }

      loading.value = true
      error.value = null
      result.value = null
      tableData.value = []

      try {
        const response = await request.post('/admin/execute-sql/', {
          sql: sqlInput.value,
          page_size: pageSize.value,
          page_num: currentPage.value
        })
        
        result.value = {
          success: true,
          data: response.data,
          log: response.data.log
        }

        // 格式化表格数据
        if (response.data.rows && response.data.headers) {
          tableData.value = response.data.rows.map((row, index) => {
            const rowData = {}
            response.data.headers.forEach((header, colIndex) => {
              rowData[header] = row[colIndex]
            })
            rowData.key = `row-${index}`
            return rowData
          })
        }

        if (response.data.success) {
          ElMessage.success(response.data.message || '执行成功')
        }
      } catch (err) {
        console.error('API Error:', err)
        error.value = err.response?.data?.error || '执行失败'
        ElMessage.error(error.value)
      } finally {
        loading.value = false
      }
    }

    // 获取列宽度
    const getColumnWidth = (header) => {
      switch(header.toLowerCase()) {
        case 'level':
        case 'status':
          return 100
        case 'id':
        case 'car_id':
          return 120
        case 'created_at':
        case 'updated_at':
          return 180
        default:
          return null
      }
    }

    // 监听窗口大小变化，调整表格高度
    onMounted(() => {
      const updateHeight = () => {
        // 留出一定空间给其他元素
        tableMaxHeight.value = window.innerHeight - 500
      }
      updateHeight()
      window.addEventListener('resize', updateHeight)
      onUnmounted(() => {
        window.removeEventListener('resize', updateHeight)
      })
    })

    return {
      router,
      sqlInput,
      result,
      error,
      loading,
      executeSql,
      handleEditorChange,
      clearEditor,
      extensions,
      handleSqlExample,
      getColumnWidth,
      tableData,
      tableMaxHeight,
      currentPage,
      pageSize,
      handleCurrentChange,
      handleSizeChange,
    }
  }
})
</script>

<style scoped>
.sql-executor {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 6px;
}

.card-header {
  font-weight: bold;
}

.result-section {
  margin-top: 24px;
}

.log-section {
  margin-top: 24px;
}

.error-section {
  margin-top: 24px;
}

.table-section {
  margin-top: 15px;
  margin-bottom: 15px;
}

.pagination {
  margin-top: 15px;
  text-align: right;
}

/* 添加表格单元格样式 */
:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8px;
}

:deep(.el-table) {
  margin-top: 10px;
}

:deep(.el-table__header th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}

.sql-examples {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.example-label {
  margin-right: 10px;
  color: #666;
  font-weight: 500;
}

:deep(.el-button.is-link) {
  padding: 4px 0;
  height: auto;
  font-size: 14px;
  color: #409EFF;
  
  &:hover {
    color: #79bbff;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-descriptions__title) {
  font-size: 16px;
  color: #409EFF;
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-descriptions) {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-alert) {
  margin: 16px 0;
  border-radius: 4px;
}

:deep(.el-pagination) {
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

:deep(.el-pagination .el-select .el-input) {
  width: 120px;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.sql-editor {
  border-radius: 4px;
  overflow: hidden;
  width: 100%;
  border: 1px solid #333;
}

:deep(.cm-editor) {
  min-height: 240px;
  width: 100% !important;
  font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
  font-size: 14px;
  background-color: #282c34;
}

:deep(.cm-editor .cm-content) {
  padding: 8px;
  min-height: 240px;
  white-space: pre-wrap;
  word-break: normal;
  word-wrap: break-word;
}

:deep(.cm-editor .cm-line) {
  padding: 0 8px;
  line-height: 1.6;
}

:deep(.cm-editor.cm-focused) {
  outline: none;
  border-color: #409EFF;
}

:deep(.cm-gutters) {
  border-right: 1px solid #333;
  background-color: #282c34;
  padding: 0 8px;
}

:deep(.cm-activeLineGutter) {
  background-color: #21252b;
}

:deep(.cm-placeholder) {
  color: #666;
  font-style: italic;
}

:deep(.cm-activeLine) {
  background-color: #2c313c;
}

:deep(.cm-selectionMatch) {
  background-color: #3e4451;
}

:deep(.cm-cursor) {
  border-left: 2px solid #528bff;
}

/* 滚动条样式 */
:deep(.cm-scroller::-webkit-scrollbar) {
  width: 12px;
  height: 12px;
}

:deep(.cm-scroller::-webkit-scrollbar-track) {
  background: #21252b;
}

:deep(.cm-scroller::-webkit-scrollbar-thumb) {
  background-color: #4b5363;
  border-radius: 6px;
  border: 3px solid #21252b;
}

:deep(.cm-scroller::-webkit-scrollbar-corner) {
  background-color: #21252b;
}

.form-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-dropdown) {
  margin-right: 16px;
}

:deep(.el-dropdown-menu__item) {
  line-height: 1.5;
  padding: 8px 16px;
  font-size: 14px;
}

:deep(.el-dropdown .el-button) {
  display: flex;
  align-items: center;
}

.result-card {
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #EBEEF5;
}

.result-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-content {
  position: relative;
  margin: -20px -20px;  /* 抵消卡片的内边距 */
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;
  background-color: #fafafa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border: none;
  margin: 0;
}
</style> 