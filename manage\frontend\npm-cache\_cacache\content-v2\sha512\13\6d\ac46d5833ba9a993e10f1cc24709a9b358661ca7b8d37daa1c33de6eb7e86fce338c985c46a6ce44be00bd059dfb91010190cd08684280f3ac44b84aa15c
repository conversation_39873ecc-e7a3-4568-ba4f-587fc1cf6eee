{"dist-tags": {"latest": "6.18.4"}, "modified": "2024-12-17T16:27:13.881Z", "name": "@codemirror/autocomplete", "versions": {"0.19.9": {"name": "@codemirror/autocomplete", "version": "0.19.9", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-Ph1LWHtFFqNUIqEVrws6I263ihe5TH+TRBPwxQ78j7st7Q67FDAmgKX6mNbUPh02dxfqQrc9qxlo5JIqKeiVdg==", "shasum": "28b6600ad617bdc8dfeb0102a1df8cc61883d87c", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.9.tgz", "fileCount": 8, "unpackedSize": 138694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoKXXCRA9TVsSAnZWagAAaIcP/3CW1hxecwUD/s8H7udx\nmzOeaXo6HFA69RhJujLfbtXkV25wp2OXiPtqoav7ipj85q33xXW4RmHPHvh8\ny3SRgSxlZydTzkxBQvAzYhqL0jju/Sk1je8LKvmKvb2y0LRucs2ovrkP34FV\nRPcJYlD1e4wfomCBuMFUf26h7dIpG/uRozO2nvGhpWzazDggIHdJg+OlVLpG\ngfsJH+Ec/EJBPMvu9ljm00puVN+GTCpgh51kAWW9shw/Zk+qKuAchVH1xjl0\nJ8KBeu7UicxCAjPyjfqlntJcFESx8sjwJ4R/YNStiFn751iEVH1mCOeSOUAs\n8VGO86Cc0WisXDWhc/LmZZqD84MzfyILmeTQa5H8X5//yTAJRaVeAt2YF7YH\n9fAg41BxF2PhUpWrIIjrFpCy/Ree8i2KkdFANMeQcEEgRKWzUGQY6wf0eliU\nXMc3UC4QNAcZZJZh/lP7uieqMqeR7UQqwQurl0BzoDLzxcU3kNXBAcBwfmv1\n0+cGOXd0z2YX1eHJ8QXqvKqtlMeXojuYj7cqMFeZxWWCLuwziPdDTZ15obxe\nQ9Fa8x119Q5jF/Enba9GdVTFNS6EFyrhvmZqHobpXssjTY1uIljW60HZJWxV\n2cxecqXtsqVSmRuUpBlbMcgtpn97+2h4FgfxHPKfKNaMST3d6dI8Sb73vyMD\nlNIC\r\n=U527\r\n-----END PGP SIGNATURE-----\r\n", "size": 36273, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.8": {"name": "@codemirror/autocomplete", "version": "0.19.8", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-o4I1pRlFjhBHOYab+QfpKcW0B8FqAH+2pdmCYrkTz3bm1djVwhlMEhv1s/aTKhdjLtkfZFUbdHBi+8xe22wUCA==", "shasum": "a4a886089f0248a0d1ccdc11d9f8560c0b2c2abd", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.8.tgz", "fileCount": 8, "unpackedSize": 138390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlN1GCRA9TVsSAnZWagAAf54P/2OQkjZ8xdGhyqYtetex\nxohkBvEKnt2rh+Vj78kiuwsOvP/iLupM9kLzZNzSwlA447AJJ+bmfIrKvN4G\nuaDChbKOi+nwnZnIKoPZxpVTiHFE64ebCH4dAbXgA00Tu905Tsrw7vfAEL4A\nA688D88n4RE4R1M8dVqObfIcq1Kc9b/2k7EcPYsoH6DnLxc7VwHV5cdByvBx\nzNGROku0E+SHBEl6ximhkVrwyE+/vOPSSS2/zZL3xY8wy2J1uS+G2jwxCWzM\nKxqUiPnYDXU6kk39CR5PyFhBUCtz88u2+sB4IrWq0wSITKwIzA9ERbE1bNDM\nB0hKog6kJaCcYe+3zZ1fJRQAMv0gcd/Ph5kUES23+qE3zKjL9Bxaor3c9LR4\nomvqniU8UUMeQ506l+e60cCKiuw0dDSQEIWiO2nRU5g2QhXXc+H6fDfUVL7+\n5h8QQ+2oPHUBYs4yBm8x/JZvyBR+CrmCbcqpO2urIIqjMhnTRwVG9qk2YX6F\nrf72sZVca+8Sku3o3j42uPK+N7BcPYLebYKqc/eaJucoIIsCPw7uyjkI1Ga9\ntWtIitSFKnv+PXIoB1TFDi6T+J47eTcDoGZHTi4wov1XNWbgjPxGk8msdHAl\nP9nyPvkNVTdDMkXVpkSql3lRQsbCMmH8XuDZdieUJCep+HufFy9nOMzT17e/\ngVy3\r\n=slit\r\n-----END PGP SIGNATURE-----\r\n", "size": 36199, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.7": {"name": "@codemirror/autocomplete", "version": "0.19.7", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-zSTzAPVIAGSyAXxk9E7LAXXBb0MCeUdVEqZlo5URG23yaROZkAMo1yxqKaTU1BpXvckJ3C8YN/9UZjKXI1eM6Q==", "shasum": "ff4e78056eb90f91228082d9e192f7870af4af5c", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.7.tgz", "fileCount": 8, "unpackedSize": 137508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk38kCRA9TVsSAnZWagAAwsgQAJ8aP72k1JoFNEvL1aE+\nwwzmLACofbrkimr0PFPw7D0594SLYz+UTlJPISN8Y85HsSBWO5bwGLGtonx/\nUcuuWKPw27x0k8dWFgVYTs6aM0eqIUo9u2czrqM4kN/c1GStAzhcHF/i8bRN\noMtRq5Lmipxf9hpBx1k2jQO85xUTByXFUGB1tZm29qtQ2h6TDdnVYqU/dOTK\nTQCTlgccebQVv0XdBWdD4MMW4cijeKx/ta+v44cPnHgeI7+wWdlOIOzY/PhH\nXh30CvSsiUbYXQrv4FBY9xBiVpv+nVAS2fwzM2t5DqvvBLhFyDbD0J8CZjZO\n9Ma5qsUWt1h1FImRly3HwKLazaMl+OAW9H8uJ3yHj1Z2yHUwyu/tbQUiNWxH\nB/1YlJui3lnX68uU6ZmTNxLGnjIHPMlddsFCRbRAezMHmFQMA8n4c4s8bsWT\nouOhvjaZyYgl5gBNiiRIYOrLEvw1UjYzOTYRI/Mb9pVro8FBaJlHSIIHh0QM\nPEWjNNQaGHTZKlfLNsJEHel2c7BxgZlrJ0LLrHl+Ljd3ZrS9qKligTdqp1Xd\nn1t9fIPpwLZ3Paygd2dfUtE2YMK+Va9pH9KyHBEML5y8H9ipQAs2nhtbG9/Q\nJdyKUWTNKPhQAF709iz4qdQSQRPc8HfSIeyQcpFdz6U77LHwTdfkE9jS62Ip\n7MJw\r\n=tFk7\r\n-----END PGP SIGNATURE-----\r\n", "size": 35949, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.6": {"name": "@codemirror/autocomplete", "version": "0.19.6", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-hFYpNWq/DHpZTDn51+40YfNXysfX/iUnUzYuXnDVLOYMyxCAC+0vzA6aMHACFp/R2CEpRFfdAsNrQZpFkWVgSg==", "shasum": "8c2ecd6eace3a1196985dbc68a46bb399f70d9df", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.6.tgz", "fileCount": 8, "unpackedSize": 137179, "size": 35857, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.5": {"name": "@codemirror/autocomplete", "version": "0.19.5", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-4ZP9hUaGKw5iSeOMwBv3Qa5fZO8puuna7eDwB8jjRPOtxW4X6e2LY8SmsNSp3ZLXsrhUAOt8urNqv1jBp7SxqA==", "shasum": "7304a08d1efd6ac6a6223f3804cb625ff73df2c1", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.5.tgz", "fileCount": 8, "unpackedSize": 137062, "size": 35824, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/autocomplete", "version": "0.19.4", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "2faff94d5a566864444b22fb765d12a3d328bc02", "size": 35423, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.4.tgz", "integrity": "sha512-<PERSON>uf4xZ9opIpUwMvxFMknC1C202qtTx1q5bS1GuMnTK4lBYoG+tekpAqlLBF3x6fEe2+fw6dRLwYTigtCuS7pQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.3": {"name": "@codemirror/autocomplete", "version": "0.19.3", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "0266ac29617277d2d8cc7dcfb600c1e50c332d35", "size": 35291, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.3.tgz", "integrity": "sha512-5juP6hVrHAogzQ0JUTQuibE8j1seqeGNJ98qLUPuliI6kLBg5INS4qvUI1Brqye+wYPFu7UHqrrn13RLh5YSzw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/autocomplete", "version": "0.19.2", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "f89d3ea340a92032bbd49fac3104d136ddbed9e0", "size": 34333, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.2.tgz", "integrity": "sha512-Nf50tOrEXbE4WpAv9Pt06seSUVRU6XP0fjBAII/skWD+2RDg++i/+jcnAnthtwfZmo/vgNzk5XAPl3KygP+o5g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/autocomplete", "version": "0.19.1", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "030862c6b6ccb4f49f09aed53e8d104a7f9b8465", "size": 33521, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.1.tgz", "integrity": "sha512-xc7BqgTrul+kW9RpM1zB9hmmILF6GOgmz2B6UQkTv3bbH+sQ9LV0edvCJf/QUphep0mpsjLu/e2cAhkVdy6kZw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.0": {"name": "@codemirror/autocomplete", "version": "0.19.0", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.14.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "d53c397cebc083797cb90314dd17b93bd6164ee3", "size": 33497, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.0.tgz", "integrity": "sha512-8idydH6aKhp7OUfDuB9aKPbSsyQWg6eF8NL8nlQphR2jA6YNfrXF7bB+N3C9BfU7DvCsvAvcLwLGfp0iGvm26Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.8": {"name": "@codemirror/autocomplete", "version": "0.18.8", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "e82847071bd28029356055a498492659911c70a2", "size": 33419, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.8.tgz", "integrity": "sha512-Va1Q763Vu/rVmIazru/ZnO2kkWVq6SlmMEjeD0qmxLAypyP6j/QNdpmaPDI1qb/+Mb9VFZBbac6a0aLTTi8qxQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.7": {"name": "@codemirror/autocomplete", "version": "0.18.7", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "5d99cb8c4adf6abbc4b0cad9a14dcd2aae618916", "size": 33041, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.7.tgz", "integrity": "sha512-4x6xH2jDuJzweB0Wdx3VQrFUvZF0V7RAHoomQOCq3NxUOZd/UXcSXrj3LXItFAxi6K4OlTLayFuI3z4k3UKUQQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.6": {"name": "@codemirror/autocomplete", "version": "0.18.6", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "ba185148f0bac0391d26d29cb737f1d45280e42f", "size": 32875, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.6.tgz", "integrity": "sha512-pKKv1caMxgdOx4/oyPN47oRwM8G0w5z2kDPXfSEpOsxPEkYMbkvimoDp8UEfUbyScc7m4OEkfXkiC0A3B0/g4w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.5": {"name": "@codemirror/autocomplete", "version": "0.18.5", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "5c25ddbef858503920fa4912b48bf78be93ee462", "size": 32757, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.5.tgz", "integrity": "sha512-Zp/HMTwvaP4B01HQx+5Ga0xBBLAwNaAlbALip355NPRBkYX9PZheX5b/F5IUAdI6kIrF2eYz1xAOChXgYg9maw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.4": {"name": "@codemirror/autocomplete", "version": "0.18.4", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "e1d67744e9006acc2ee48c875aaa231b6cd14e92", "size": 32593, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.4.tgz", "integrity": "sha512-HgfSGSI44ylJd9Dhb6jSfS7Wm/c+pIQ2IHiE+tVUl1tV0cd1ySeRt5+/r4rE/4FubmkW7n97CnepGJFStj12YA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.3": {"name": "@codemirror/autocomplete", "version": "0.18.3", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "6c75904c1156e4d9a00e56b9a3e559dda6149e1e", "size": 32610, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.3.tgz", "integrity": "sha512-XAilIpYxsessr3Zh39nc5T97Zz9wLMwQTCDlIKapm/VK3JnX1I1jkoe8JqpbyVyabVxGXpB2K88GIVS9X+nLZQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/autocomplete", "version": "0.18.2", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "746d720532dad9448319a90d11d0c91c63cb7a0a", "size": 32641, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.2.tgz", "integrity": "sha512-R6DxgWL9Nz0aNqqKztaBnrPcTqNr/1ORKW0IMFo57KegTtG0Znb+3EfS/xviMjZuQ3K5sne5k7okuujbY8nL5Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/autocomplete", "version": "0.18.1", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "93536d43a782e1cbc6f10b8edb5f91be60a23382", "size": 32457, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.1.tgz", "integrity": "sha512-6Em//s+xt+lkFE3mAsSSIHtukITQNkdCSPg8IDop5kYLPkrhi7IX5NnYqQoPi06sWdUySyF9+h1+zpZQW3BG6Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/autocomplete", "version": "0.18.0", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/tooltip": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "8a3fec5a234650f150846c15f01fc33ff44d1a95", "size": 53560, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.18.0.tgz", "integrity": "sha512-8MzkXNh/Sj2pcCPYWe+1vjQ51rmmGI+Nx95TJ4xJoH9uEMqnFSrOuxr0x52eLUwYIOihXt9OV6vjERr+p2IZzg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.4": {"name": "@codemirror/autocomplete", "version": "0.17.4", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "06e1ff689b07c7fb3a4a9de121e94282b49c86ee", "size": 53476, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.17.4.tgz", "integrity": "sha512-fni2OJC1aStMeQaQQE9lMSJAntWoDeGucLMGbf8XZrTNxZR1IoWKLXBy9YoA0CQfJX/VNnJljNG7WRvwxHmBiQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.3": {"name": "@codemirror/autocomplete", "version": "0.17.3", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "01f012ec57fe6648ae71c08ca12f4770f8e50cd6", "size": 52786, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.17.3.tgz", "integrity": "sha512-VYNMeoUB8xlD7txiBEffFNPKnnQ7GAtkJJRM/ePY5DAkJCrGx8oCb8RzfqqOEM16omUDt0kso8R4zjWMfOHP6Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.2": {"name": "@codemirror/autocomplete", "version": "0.17.2", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "99accb17aecd0ec37e92685f80e8787b9fa37a60", "size": 52006, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.17.2.tgz", "integrity": "sha512-52uHm3KYaZg1J1wKOZhLqb9tMSC0HrE/l4KQrFdBLxJJ+styRy7Sung9iY5ahN90qgEDbk/hsbGFQopoA/Tm8A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/autocomplete", "version": "0.17.1", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "e502afd15150d574beb248a9db23a5edbda2990d", "size": 51964, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.17.1.tgz", "integrity": "sha512-K53TC6eH+nnmVRBoH9vgzOsyzBPlyXyYUzgE3X9SwtKVI5Csi75SEbfsAQEJEwZJbSi3PkSzHR5gPxw2rfSDYQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/autocomplete", "version": "0.17.0", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "1db4b38583bc8ccdb6cbb8c5016199e400b1e390", "size": 38288, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.17.0.tgz", "integrity": "sha512-6hl/x0r+PDy6umyl73q5Go+SzPL+j+OzN7bvxm5w18AyW+2xZylZ2QOtIYhEP4lN/pQJKQxdX4wEjQw7Xx/7kQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.10": {"name": "@codemirror/autocomplete", "version": "0.19.10", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-8mgT+cSVxIoV/AbfJ36+r06xykxpACmXoD8bbTNwnkrttTwLkugxUP2oFAxL7I+TGMwxkX3ZquroyWX0HFO9nQ==", "shasum": "d8f7079da99cf0bfa7f33ffe10a590f2ff226f36", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.10.tgz", "fileCount": 8, "unpackedSize": 139808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1bEFCRA9TVsSAnZWagAAWHsP/2vn1KkySnoYQg1D+R/X\nSDpOGcXSNC9XFUaxz/0J1z+q6JHSAjT1K+lgG9VZQLXaJzXo9c7lRhKGMecp\nSrvt1lFVUf2mgtJwtV2YlirbyWRL7PUAW2lgGTxpzuxt9dsrpPe7hegoIQGa\nHBLisS+OG5J26JHL93xbABOqaV2lGWYtseb87NxNHxg7OSGPYceBb6h943GD\nD9JydCkWfFLDh5GfF09zHQ0f39NqjxAyTg4B3d/jEQPZb+4hcsoJNdcXpyH4\n+5VHUa8BsejYngMBECc7aEK2CRkVHcO5mt8OLgz/Ta/CmbI4jltjfgzjt6Yi\n8xxzxu55OFv4gYNPKs2Pca5LktIjVJndJOHIt8ONos2zn0Eq48FAXMDRkePb\nxFYWoUYNebvMUfa6yWBtYnx7vL5pyk57XEGdmCjFXwhubD+lkeTY16vHpcav\nF+jRJc5nrJtZmRHAINYYWI75fTDt0/mUiF72Bkc+lknVtfM2TyXGTQmNVA8g\n0DVspCCNEsP4d+KMcIRvNdjFLkT8vFE6FOKp7E8JJhDBjf7T0MRxG468R61p\n8A2bZRtm95MrrRgcC/f3c8DUQUzSTAnTA27LAhyJ0d3SE2p97b1t7wxZjOZN\no8pvDccEYba1GckbBmxdhEgbLRxmCvvkrgL1ye4VMzw5cm5R0nbq78ydpDkY\nX6jW\r\n=TYSB\r\n-----END PGP SIGNATURE-----\r\n", "size": 36617}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.11": {"name": "@codemirror/autocomplete", "version": "0.19.11", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-ZZ2KqbJ2S0/5CAZWFk91jrY4jU9vc6iKjCqlpjtTmwP7KN+q/aAVpZxfWk6j/iJaTj51u0lIpuEfADdJQuL6Pg==", "shasum": "066efd8816d3fa12bec19500e6cd06ac3eb914b0", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.11.tgz", "fileCount": 8, "unpackedSize": 139979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3WNVCRA9TVsSAnZWagAA1wIP/3kt8Z+3YCJE9Xrpd9yO\n7axgG4Fetv8aKtQ5OZB5k8qpl4WEB7hsP6c6XO72Nnq64ZNAQD+Bq63bdTVh\nZa5VwJpT+/a1Dk91LBlOl5vs0R/N5vEbRRIH6GRfVx46VKpChlw7b5bttmj3\nCcpU/piFHhbLDWhN/+GD9SQxtZmYOa8QWPNy+AwCe/gex2OBkB3Xm+jZUe0a\nBwL9l8LoaYj9oFYEfp4zXvOL3pQZPytNMmvGrfQ2vy9aCcfFnSSk517Kj0+3\nKHz3u82/2ZolAL0Rbd180RurwVppt0muUJnE0aqdeCAVovHdQqkM+0/aCiZf\n+WarNEdVCLT95gZH9EG5sNBNTxjpokMRCsy9kbRN7NVftQdIr8pDTqI62W+a\n8TdVFYArDdObsgffd0PPoYtli6eb6jurZL82Ec0aYW1NSKtxtdpmPcgENAbk\nZmrsi9F3czBDYhUK5fWsE2usofj7LRrn+5W0hc9C76EvI+48H3yYJ8xjSMWF\n1dJaZcMOarklOP5egAfH38R+udqJjdCW9Em/j9nhYOO1013C6e9BEvaMdDrM\nOj2qbJScrht6PaBE2cjab4PZgvI0p6AQpr2md7MVKmwU+iSsAYfzaIYcSyvo\nDwnWulMWzIptjOs0hPBVIG0WNPM7hgcRPJ9nUHshOfoHpB9GVOrZyIDIumFq\nsVXx\r\n=m44M\r\n-----END PGP SIGNATURE-----\r\n", "size": 36656}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.12": {"name": "@codemirror/autocomplete", "version": "0.19.12", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.12", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-zUQYo5gMdv7vhxlKoAY/vnNCGzlE9AU7+P649v3ovpQpoFdo3U1Nt01EJqFb4Sfaw6l1U/Elc9Iksd1lDy+MVw==", "shasum": "4c9e4487b45e6877807e4f16c1fffd5e7639ae52", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.12.tgz", "fileCount": 8, "unpackedSize": 140170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3Ww6CRA9TVsSAnZWagAAoYkP/A1MUKgZ84a9t/jp7ja+\nl63ttrJeA4EcNlRnvVMokbgxTI5debpCTkuqurp7XtW1Byma034hNveO0XQ5\n5GLgTbPPcGqnDDi3cxxW/K+56NlJaH5VCR0E+240Ccl1oIdQXXRdF2nJDCSt\neBx7wT316AQ0SJkTdDdK2Sm3QvcjcRbadfJ7z0tn9XU1npmpWLcis31asJUN\nLT6NDOexztbjssqunxCWD/T9N57X07Cn64vpKHLaxWmsdJMMUwhJTxF5QXqr\nyTVXq0oA3SxiABsE3PqyNn8cOh6fpmlu3udAsmZunUOlkWu8cSdUaALv7Xzg\niIvqMoIfFzHryvk3Z+3ez2k5/D5kz8qv/PWBj7murDV9/Rz2N6LRb3EC3njA\ndc7Z5pzgf+O0t4cuZUyZgZrU4jZDgr1BXQJtQNIsUm41TVBJYQZTyBnB6SbG\nHgQ+e9FjKP2V6ZNhpKQOSDnPU/s5ym1GVgdGRtqD/OxGKVrCaFGSfoB+AD1+\ndVSwHtN7cjZOvvEv0ea2gXF0mBl294AItDnwiuJRCbON4h7zGB6XRu/Wazv7\nl6f0iQ8ycf9phUwD0kBMvtFgBsCDp6sTGwZsomcYlk6CmPXjYUnrrUAD7Jdw\n91bzm3E32aMFvJdrRdtl+db1dv5PrmFXQOu0rhOyXqrTD8e94kd8HXy3jk0L\nvyBv\r\n=P7Yl\r\n-----END PGP SIGNATURE-----\r\n", "size": 36730}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.13": {"name": "@codemirror/autocomplete", "version": "0.19.13", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.12", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-fI6lgTH2PlyTQqF2eWKDRBRUC+9MqvS2TfvE7aJ4Pu1oHM1w8JI16J9Qvwrd0iabJeF/QodU51Clok98c7fpCQ==", "shasum": "33253750605ffa94a8880636df514fa49821485f", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.13.tgz", "fileCount": 8, "unpackedSize": 141186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD2OZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC8A//SRemH2bQRP4l4GhVMZyNw8KyDnD0mhUI6JreopGcKF/DNW5P\r\nVQ0qP7Mz7mfU4vt2xaPiYEuSHkOq3ziH4kEcurGVKvGcd+/7N5hGeq3CRffL\r\nNwEL0jkCJyLtZ45zWd9nXVnzXW28x9ZIAglN1eZtX58gwnhpLufGo2ynTP3Y\r\nmKkHDdxQ6vD/zEoLKbNNdjTOfSUiiTqVEDQ6YmPigoBsnfdpVBkXH4XsCjGg\r\nVsJe66gya4k/3SDbjKQ3nHjqc5LrE7F7HcZLGnDLg+KNoquzVv6hwk2JHvFg\r\n99155p4AXtIzoaSMphHyC1GXfbZKDmr59JsSkjqdC1hmYo164uGSfUbwbPHk\r\nJacZR1gs9TqUrNwH2SI4SJA/lbCRhLhEIRWEAulpI4yynSEwVUqMWrrUSAyZ\r\nQSGE0EBaYpPkZ+Q+PymlMgnfUM95hxogt9fGySAdJ839Y2To4EWnNMAuNw2u\r\njyCS2Jk1fwFco41zJj8zMCLybQmdBtMLUswit+TVI/6hQw5Xi3LzDtDtgv3v\r\naTeGHI/ntTpl6+/sO3jTeI0pAt6yUTeSrQArLyzqKr5tSrcVFl+QOjlAF8Ry\r\nkwbguXegSJgUva7nO4pjXFEvvLqttaH3kpEZiyGGMRBzyY/lULLIcf4p9BBt\r\nJpwoRAvaW59v0B7beAAr6+GNOWvZmSgfXlE=\r\n=fDa7\r\n-----END PGP SIGNATURE-----\r\n", "size": 37008}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.14": {"name": "@codemirror/autocomplete", "version": "0.19.14", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.12", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-4PqJG7GGTePc+FQF387RFebDV4ERvKj23gQBmzNtu64ZSHlYEGulwP5EIIfulBiaWEmei9TYVaMFmTdNfofpRQ==", "shasum": "5f61b0fced56e7960791063d03b21d11a63f0ec5", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.14.tgz", "fileCount": 8, "unpackedSize": 141334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKbpbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrj+w//eaPU7QIlvxxsANPwj8G66IZbYdLAnS1NHt+W6Ohx8BFREyQb\r\nMZ8R8RPOr7zxBWdxIhplBD1ohxJnQxiDVNVZtBjMYNRb8JZiT8JqYBkUy65i\r\n3P75ugw3LfxzEsGmllA62WNYwEJ1/DiAmOe/cV2X32vqN/6DS/m0fkvtqiPw\r\ngQm+/bQE/BLuSX+1MyAmxtKLGuhNBCQOC1J7H3KoPmK9lzIXNsWMRsJJYJLl\r\ngAAtZtIudbL5zr2qhEDZOYmwhsX1BKje+FlfyknnLifIAnn2La7/dbgJmu3P\r\nnes+2PUvxgg5fPJHSW6fVP+vR7l5B1JvY6VJ/ISLqjj063aRbz4WuvcG8eGI\r\nNCxyQwuLwyiyW00iNb9h1l5xS9Da2umjxQ0EFzUtztJERrhYlRHtBA03kjme\r\nSAgADUiTPTCORSytbpV1HfHOln/eeu9VXZ7rPxS2ftdvsotsLO8pZ6HznTxc\r\nNlf1rtAHJdHBSW1gbktJH1y6xCxnzTJPojYT91dAs6TKOFRWGzdfLOgPnz9i\r\naa+zJqcrn+cZA+bVUP3hPbPkQvYyRS5e0AguWCQ0WCbuIUrlBEvIEd4q9e3w\r\nZEUdp7pbRkWVHQP2dfjcuOikiMJfJHteWyKA6tu7yWI0HKYNm8FBDbtvuATX\r\nE/+RXRxHhMPb+A+IlPIkrEtrb8CpLwtEgzY=\r\n=fIeF\r\n-----END PGP SIGNATURE-----\r\n", "size": 37052}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.15": {"name": "@codemirror/autocomplete", "version": "0.19.15", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/state": "^0.19.4", "@codemirror/text": "^0.19.2", "@codemirror/tooltip": "^0.19.12", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-GQWzvvuXxNUyaEk+5gawbAD8s51/v2Chb++nx0e2eGWrphWk42isBtzOMdc3DxrxrZtPZ55q2ldNp+6G8KJLIQ==", "shasum": "061f09063dc2a68668d85d7ac8430c7bc6df1a82", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.19.15.tgz", "fileCount": 8, "unpackedSize": 144169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOuhXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAVBAAlgET2PyVbkEQ1/rtlLDyOhAAJvhG2Rdijhr+yO6VPZYz7c3g\r\n2zbRqLU2P0FFj9wN14NI8W2vdJZQFF23AAFNATB3s1rbwVEsis2cU31t6SkX\r\nmoV2se/HcMEyvvLxJ2//THWbJnw0RWJE8fR/nIH1DowZrDPtzPS9hIBTgPbG\r\ndvVlwiWkFuD+tv+mspOyc0cJhgqhJWlxTtep2WM0EQGxNM1zoK7Jz/sw0tbH\r\nfON2JgM3HYJzzxzWpSM1rfnX2aveQKBHhY+SX0Ob38lQJLW9d4fi7mgnPAru\r\nl6GScrfyJn5VKF0yG6oKbJIyDTfjkzrfgE20F/0XK0mAityPQn0dywZ854/U\r\nbJi+aToI58QXl7XvB0L8nHqbPgd6e/H21ZNv8JgzI9JxPEPRMEBHO2tHRKJt\r\ntzreBtEkYjO6yO85W9+ZBCTwx5ndBMYyhp8OmoXcVTucdvk6IFB8O1n3cocu\r\nMxJzS56aqBIENp76/vEaBeBXUiGC2YVsgsfHdk536/tcf/NCJ18RwykL5irb\r\nCh8COG+B1FTn8QpGFzgiz6WzFXMwdutfMpP9cpYJlLh0HBJvo2EEb/ROGdqz\r\n78g9hwaG3Bc7IHzKDRTaklC2gEe/D5UIMT0rYycPNeyv9Ygc8oQ+VRMEEhi4\r\nxCTuuY8yk2Gl5HErpLx5ZqiarRAFBoEhpT4=\r\n=zAQT\r\n-----END PGP SIGNATURE-----\r\n", "size": 37598}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/autocomplete", "version": "0.20.0", "dependencies": {"@codemirror/language": "^0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-F6VOM8lImn5ApqxJcaWgdl7hhlw8B5yAfZJGlsQifcpNotkZOMND61mBFZ84OmSLWxtT8/smkSeLvJupKbjP9w==", "shasum": "390cc444ea36474e77117f2153caad84214f0edf", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.20.0.tgz", "fileCount": 8, "unpackedSize": 169760, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICEiBsQPQdM9SBKDRxBvvCE0WIgrdb4wLFrwiIBBv4wcAiBbJKhS1KQCAeiFDLUtZxPpNx2T63ncddh4Q8TPhZutSg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBVMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMYw/6A4905HwhCot6KJza5iq2KbItj88UQu9SW0BveCYF+fKYhMUm\r\nNkAuUSNDMd5Z/v5BtPtAoZkvidjsyq61Iyd6kmlPQu9jeOyVWjW3q3gijGSY\r\niDmmaTauPx5fdCqH4Y+b3AJdl3vxwtSbhuR4SHwML9c7mh2G373s22eYdijN\r\nQwPq3r39XhW33wDE3UBXDLLal/8DZ/oYuokv9t54uWlNok2l0l20tG3itEoM\r\nF2Y3QNyP7T1u5xP5dvGfKZqBb3gt8TZvb1uTPgRv7yA2tq8RL6mal1JjwqYC\r\nDRK5H9PVHeRLTgl2UvB84LBPFjV9XEHHU/oThrnqBV2CbmIsbdmtPZUxPDNo\r\nCqA4lXJfEOXrvJRpeN06NFAj+YizfpkPC4ltxcN+AUpUJX/oQeqB1UcSkkDm\r\nRDb3e7FL8b3AqgQ4/oJ+vDAWz/huXy0jij/Ls4JskViXLnPNfgU9ovMUsvQ/\r\ng0BwJNffcLlvXVy+X89Z7V+MStyTXAnnxvlSnNpKl2NCEIsbndoeY8NTpmOI\r\n9DFrF4UPGn1zTXhpZfWNLW0Jgbz3/0euFHLBUD7l1tO9muXvXRyufzRb/EyM\r\nYTYmNarGuyp6sdl6M0ZLY9piIJOY5i6TTSzv4H/0J2YMIYk/6XShYMTQeCZw\r\npOcNk/OqJH8JmmFxzwoFMaROzA+LrDZvjIs=\r\n=GHEU\r\n-----END PGP SIGNATURE-----\r\n", "size": 44182}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.1": {"name": "@codemirror/autocomplete", "version": "0.20.1", "dependencies": {"@codemirror/language": "^0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-rWDAkE1Qn9O4LsV8tAm/KlzzqdQLaIxqDOLhVYja3rKQIWebD3dixIzg9BAKpjt+0dIaDwtIp3yinD9MefgbQQ==", "shasum": "d9aa5cfc111469f9f014e80d239916bb0c21edf9", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.20.1.tgz", "fileCount": 8, "unpackedSize": 171769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBYrLRsEutT9gc13nLo06Pl3EfI0VgQh1G6uep6TAzyQAiEA2KB7tu3L0IeLBVOdWAI0wtyf9Ba3iIvpfANy1N1J+vg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigfHDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptMBAAhcrMl83DJe0Tr7qzerOs1sylLaW3U3QhZTuX44d+vT6Nehs7\r\nOurulevTnbdb8COjw4mmgBi3c8HhRkr/bkKXSDBIVg2Ci0VH/MGGM+AWCPHH\r\nexzKf+B3/qaSeiUEL+oLgnWMbiIdbzG1aFtLZxs+kW2HZ/wlFyT4hiGoj6GP\r\n2ugxX53pyRfJIWs3NtopypthO0qZ1NeaFgc7sOELumTsqpqts1QFR7AAFzOC\r\nZgp469DRihdn6VC4BawZuP9N2B6COhtL4Xrsc37I7JuzBGy3TD3OPawzoXry\r\nUF/AQV8huBV4jnJ7lbBUGs0BQbXlL2miSIisLyP1TTcMFxfm6AT3TfsZ5pVj\r\nFxC1P4rd3Z/TgVCdJtBmOeWk8EBnml+AciTo0JyKKE1KWnzCaSeoQhOrRmwU\r\nHal/GK+4K6ISkUuHkOvWHxPMCku3yX/Pjgh7OcrNiymL5QpsQLGHndgHY35V\r\ngijrLeLDldeEoQxbL0+4Kz68Vv/mXqs71/9fOm2VECCfSpC+xg5j+gdkOGBc\r\niIZ609p5wS6w2fsELf4RKJGvsB4I4xmuEs7Jyzp8P9As6BUO51qXAuBKgiVS\r\nYdTxDGU3aPOFLwU9RaAMajTkTwnpqZM/RHut1dieFJ2vAtPi8ociyHyClFSB\r\nVxAjyKtUCcC3CgGD9bGCsvHQP44aBOzeMUc=\r\n=aDsx\r\n-----END PGP SIGNATURE-----\r\n", "size": 44601}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.2": {"name": "@codemirror/autocomplete", "version": "0.20.2", "dependencies": {"@codemirror/language": "^0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-UBElAn5HTx9o0XDGyTm5RBJ9gegc16/Lmd9tjphW6nX0NMmnT0Kcd8k6Fn6ok60WyFQinSHPwOHQbdAXuLLjYA==", "shasum": "4fd46dc751a82c1922abcfad9b64b9cb67826998", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.20.2.tgz", "fileCount": 8, "unpackedSize": 172474, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCX3b6PuPNT0wk/EdJE3xLcbnn1+cn0RCNj3MjvLPjPJQIhALcMDpyn2eKfKoIL0EooSUUMNdvY/enaRdx0ww7KzqAJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijMyeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRjw/+Osa2Z6T7zOUR4bmgVWN3MMnx3Aii9k5P8ubm2zuwUUUWhfv/\r\nbanUXjNlDV6RwZpD7DgEevr3iBnrGLBw9T1pgoULvbGpw3ITCAK+PtHhNhDR\r\no76RdvMLk/TwWoRe+Sr+IKQe7nrn1b08nu0xkzEAMgFoOu3LyN7SA4JtHOcj\r\nyq3uRmbLN09gArx4fTx9p5NwdQrabEe10SeADehFuAXiZPb9gRZC0+3AIdE/\r\now8BMgkvuEieVxTpFZAswzu7T2eTYrjHEZdsPd+L24UwIu/YT9eNbtAejEsv\r\nyuCE7d9N6pF6eiFDpAR3xBabKE80WQkwGOWDu3JhqNaPXLLdVx511x50fWsO\r\ngC/2uouQX33xRqsZjjjHRK+FZxhOsbIzOaW84UKKPgkkWnxVVgN5Xn07sX4S\r\nicXaB2s3SdEKXM4iZpHM3VWLFEhhof+O10YwxBVKpa5kerohmqWXbPZJV69b\r\nWd/cDUEjijHjvXwZO9hftRmo6TFexSB4TxbXaVpHPAzYaUyV5cEfhTIW2Vo+\r\nq1l0zF3iMvHfsGZsbNggtnuotOpQUvHkfFepbdLm0dvFeWJeTb2X2Wo6uVjB\r\nB8Tplgsm1kRk6BV1vqkSlQ3ZmuXtog/uhC1dy+9NwfPKRA4wjydLTADYiGxX\r\nbJyNSdG97unfiUlo9JOLF1ZykMwI5NPZD9M=\r\n=6GSv\r\n-----END PGP SIGNATURE-----\r\n", "size": 44787}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.3": {"name": "@codemirror/autocomplete", "version": "0.20.3", "dependencies": {"@codemirror/language": "^0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-lYB+NPGP+LEzAudkWhLfMxhTrxtLILGl938w+RcFrGdrIc54A+UgmCoz+McE3IYRFp4xyQcL4uFJwo+93YdgHw==", "shasum": "affe2d7e2b2e0be42ee1ac5fb74a1c84a6f1bfd7", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-0.20.3.tgz", "fileCount": 8, "unpackedSize": 172955, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC/2c4icsGwUWhmTm4zJSt+No3a0k9Q2XrchG5EK2wG7AiEAt0uGdcgtEFofoGy00nWNUPLXKaLvpN+SVYCF5GwT4Ik="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilJpBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHURAAmbOhHZoFVBavTr54UMPp8O89EXxoL8B5Eu7t78Kc6DQBWlQ5\r\nWcLKa+tRtQNYoXqSxfJLDt7DU0UqKJ4N3WHVqRlj4bzKA0noADrikMTHZo2X\r\nUwgeUL3Qou7TC7SCNz0s5cbM2rrZWkHM/6QtWr04HkdeJXj4krvKc6fgQG7q\r\nb6VyGsZBZo7GEZgUADCuzD4XK7xnwERRX+iFP8XZjkRNFF+cITax2Dvva2zc\r\nYdTtMw0dREWd0Er68YynTo3/BGy5eGWWQ5DYJzRsS29sBAzLU46+MWPMxmyc\r\nMvPNKOOLvEDgO5UZG3Mb03ag9Mz/5cu5CgFPH90kbEVK6YYEN6+gOGdQPCK4\r\nhHcnmdlwT9p2iN1eG4SS8itHe4N6z/b6E1/OdajsimSR81DHAGFfaIGNCFTL\r\nBUK7b/u+oLWyYbz+jhkUcOMuFC0ThOPuXArJuvLUZixbHKESMyXc+QEeVCwp\r\n68jGVKAeQvJZe0udDqdTmZ1DWgq85SAYTmIPaL8K358jQUIOq3aM3xjY0rc0\r\neN+Jskr6R/4feZgQM8yvcQaLI4NA/p0VslnMbWYFh57xNhDH++EbDSo4Y0B3\r\n4vAmkq3HQenzaz8h2OmMSKDjpRwT1hjaXjf7P5xGTePS9iay5uKRNbz+hcMI\r\nvazC3NHpooUqDp2NEfdbMWeNZ7cpvc4sxJ8=\r\n=Df48\r\n-----END PGP SIGNATURE-----\r\n", "size": 44927}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/autocomplete", "version": "6.0.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-53io5DTxNqRm4TVpCsLJuSLPXXKsGdye5TtkC6Ac59cG1WRcY+4n9IfSn/OlINEWd/hthvu0vdoo/TfWoJd9/g==", "shasum": "4a1c977a0481e285be6b3691a779706d91e78472", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.0.0.tgz", "fileCount": 8, "unpackedSize": 173151, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSGNwbX7qYtmXIN+wR0FXa3x0qe1Fr9JsGWd4JEQpTlQIgOpzTxA3FAcqm/FDWzvMAtBiSK6r2IEdGIGrNrBYoYMc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioEreACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLKg/+PLcmScaWQJ2OLjhCvpuBkKhyR9RZcs7FHeqgiVMY3lX8Bnyc\r\nRAsIo05bfGVX7rD6kY/ajOGReuDUw1ugNGHO5GNF8HK+Ktmgsmk0gyMa3ANY\r\nIoLqtasAXgEPsXhPTzYKYs/1q1aSuGuOYS4E+MIISKayLjI2wjBdh/4+vUUG\r\nM/UfnxwpjVo/5ft8XHu7wsHalrFIsbmmTtuFjk1EdP+Zy6smH8eHdgSB8fvH\r\nghVF0RAKwjSGBoGrnEs16CBFeAhWxus45H5qLZF3ppmLWanjhdTrgy6bx1w4\r\nKWK2ibwgdlheF49NNAcSu+T2tflvwSmqYZZ/YunbMiCPaTIxdZwwnkG3Z/W9\r\n+YVnWm6PdZI7umIK83SjIT4OuoS8wFuga4Ap4TWxkNV+mRDGlkvUXxnFCzgx\r\n48stpe5SgoVjnYRPRC4mMegKYCn3boINP7UhST22fSag8/LC50/iIqRY44VB\r\nn+sfn/PydOMure/zeXogJqA7/Xyt9nrfvekG3IgN00uDmsll/C1NHnZEW52N\r\nZ0ssm480a0J1fyTiE4Q1trsjfIYqWJ7HXjJBSoxX7GagD50HZ+uBqJKycdc6\r\nTpC3xc0heFu8GsWmkPEUh80Gh88Na4G59LT3cxlME54soe5yD7LRCKfO5k0t\r\n5J6Sr01bhTOmv0sdjSj3ZE4N7/jz8oiUSmw=\r\n=12KJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 44945}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.1": {"name": "@codemirror/autocomplete", "version": "6.0.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-cD1MxuZq4scpVHecqSnUqQfRYlFDdtu4Y+7G4gF989R4F9nzrcrZ58bVua1/kIh0JminbhwqXeWXI/A5bScXaA==", "shasum": "83c64a47d00897daef055c3963e736d549b73354", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.0.1.tgz", "fileCount": 8, "unpackedSize": 174588, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFvGQ5Upo8jkh8CAiBk4ya+iipgxrzfb7ELzb4A94DeyAiEAlqGm44aAni+Opuap1SjQZvfkJON2U4hDXjOdR3gi9YQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiokPNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmracw//ayTs2VB9N63R4ENuaQW+GLlLync2Nz/RhwKhSR4DXOmKzgrL\r\n8hotN1FNNdQyQ9GQx68WVXoWQ1hLDUwz924e4Av/IJuW9xSX1bCSat1sjaTi\r\nsvB17KaDbftMuGRL35iS/VL71iTbb1VBe7PA2RIgZjsaCLnek3wdgoLCRztU\r\nXieJJ68jACuGrHksN63TzQxwJJV8gvBhuD7zcT6EUwz0QYhrD2C7n8tM4XUl\r\n78/6e23+ZJ6ZhwzD72AqKozvs1Vgjvwm0wadPnxeuu5dvnku18aFkjxywCoi\r\n0YgMKk1UKrLNqXzAgpM6INeQ+q2l8g1pQEPav5LFCh5Drzj6WB4t+cmoztPx\r\nC6NqWIgZOVpOKdBikuRebYHOgKMW97DBmnYNzMVCW8uB9p/S2+m/1bu/G+w2\r\nxm2KyoLSIIH48DB4rIofi/yJhwBsFh4OSiM2dc5RivQJQRlp3NA2dq3QtZIb\r\njjPy2L4gjWQebHZrsJ5HuTIb/2hVrG0Y4/xRqaUrXehKPb/lcGngKblZO3m3\r\nwW2M4F8E2EtXqGbQt6JptHkKWhK/8aqZjok/fentE293Ih282HJREsVVBs10\r\npzAxtPRBU9fRviKoZU1CPzTkMQ8o3CdQZIvQmOvTqKYo0OJFP+05C3gQcQ9c\r\nkRWAdJ+kwMfFIG1rXN/anxiM7hbwlOnXeN0=\r\n=iQqP\r\n-----END PGP SIGNATURE-----\r\n", "size": 45544}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.2": {"name": "@codemirror/autocomplete", "version": "6.0.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-9PDjnllmXan/7Uax87KGORbxerDJ/cu10SB+n4Jz0zXMEvIh3+TGgZxhIvDOtaQ4jDBQEM7kHYW4vLdQB0DGZQ==", "shasum": "119b9d147456418895de6fae09419465b58d7beb", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.0.2.tgz", "fileCount": 8, "unpackedSize": 174893, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtpTsvhvzqC1/6gDTRQCjPoFa0InVORfmz888HOOIDngIhAJIIcy113FmAD6vnpMCVml8XinpJ0ud7pCVmLXYWstbl"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqXhzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZhg//cVoE4ubGvEE3WwGr5r/D/5h2Tmb/nHPskITMHgRTTzkN8N+S\r\nPfYsbIvKd+aLTH7vRFaLrUafNSd4V7pywkZOmLkgtbQ0yhsG1+1ZvjutMtsA\r\n3GCgJwm0EHH1b4sU6navHcxuFdwc4meA3ndtK4dD+qKFiJZV+IY/WTKdO31J\r\nkTOq/qyuBIdbsV+WbYV2zJdeu/h1IapWNaD157/qybz5EZMPHNNmrOuSHMSR\r\n0e4sfsUpu+mUtaHoWpFv5QTTCpc8Ev9YnmYTqU/Dr1rDNeINYobFgce3RbEk\r\n0N8o4aXMgAJaeYLIQK6h5hIHQ9hlZsRDTbbujfl6mMWtf08qt2PAI04oYJRw\r\nBEgGKPPIu+AymV7I0jL//nImwbE51SuuY4a6Ob1FnlRtNf29NEI0Aj1QzDDz\r\nDFX5aoFXKawbf73/dnxZZjfWxMidADwWP322lmobtu/HJfDSGnSqrXpS81ug\r\naOiwhtxLtToG0i5a4QnrDztQjVvA6+4ZXDIJ2BesTSN/TB2sjLVfgxGU3jjq\r\nnn+IR0lGdeZoxoixZhE7UL/0M3s3yrM1TFOKiskg1DS339qP2HqbhAJqQQTY\r\n5gC0VMB31AAbryE36b6A8T47Skmw1hw9h/UHTlWKjyr/KiZP4QwMUm66dGsi\r\nghebi6damqsdO1CbPjoil8nC2CqpUG44neE=\r\n=iYCf\r\n-----END PGP SIGNATURE-----\r\n", "size": 45682}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.3": {"name": "@codemirror/autocomplete", "version": "6.0.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-JTSBDC4tUyR8iRmCwQJaYpTXtOZmRn4gKjw1Fu4xIatFPqTJ7m0QRCdkdbzlvMovzjTiuHp4a8WUEB1c/LtiHg==", "shasum": "a4621a0238db22e5919af2f9b78053e670f8809a", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.0.3.tgz", "fileCount": 8, "unpackedSize": 175801, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD25DDIl8Y6Qx1lIGojjhRxKKxdX2bUjUr8CuY1SPx5wIgVuNRyVAjqy582RGeKvniVrKAIMWheqPZNNffKHfk8RM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwoJzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruRhAAm6wJW5uuMGVbI+IRBwi5UngTQ08oPumNmNDERqM9hsEul2WZ\r\nwEFbdSM9tsjk1XXx8dt36yRPXTbazlgvr4BfBN2EGOQKzXq9x28MMMmYyIzV\r\ns3rtuG71pbPlaTmU73ou6RmmbRfOPN0cK3kxCw6e9eUGghDI4TNxv+0amyLz\r\nUpa/2tEXton2Kd7w8W9fzYVkf7kvrKcc5pWeAJmuQtdrJeZmn5dcs18f+b1M\r\nM4q6dJZSGtwEH3sXJ706ENNGok4um0qBYv+unBeB/aedK3YiaZSQlawI7le6\r\nUDN7ExwVd7WaI9LJmS7+idWJ5qjiV+RYi5F5NFbBivY0dWnvrIlwxcH99YPs\r\nryESrZZFz2IFf5YhI95ae/TqMVniGlMvKYqH2W8fFCvFCRcVJMXL8yimqISF\r\nGLdEEvZ9dKU23y1wSiFWd+sQKEOC3n6nK62pp86pIPHxW73YPE8JfrPybet6\r\n9Nb1sHVtMRb3bHwtYHT8vtNZsr8Z4W1WxT4WG1Xul3kDgY3idPxJwC9xBbcE\r\nloMWguw5Vj/q0HPzGoGl54mcz9PtWysQMQqK3mNYCTQkF5hGZSrPFgnSwvsT\r\nCLQjaVNZKAnHmPokTKmpH5Ujigtdo79SLGevVX8OszscArFnSsl7xX2Li6TC\r\n67Y6GdzKw+ltxFJKCK9NOUjxLJNNARD+eDA=\r\n=wPmF\r\n-----END PGP SIGNATURE-----\r\n", "size": 45823}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.4": {"name": "@codemirror/autocomplete", "version": "6.0.4", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-uP7UodCRykPNwSAN+wYa/AS9gJI/V47echCAXUYgCgBXy3l19nwO7W/d29COtG/dfAsjBOhMDeh3Ms8Y5VZbrA==", "shasum": "90a9c81cfddac528b9e9dc07415a7c6554dbe85c", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.0.4.tgz", "fileCount": 8, "unpackedSize": 175827, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnEs1rnDxqs06D8gYsq4lQx3FTot32/wLHiFoZ2H3r1wIgaVWM8YNcQjJPTy91K0nzIcQhDQMtEtj3hI8ORBcpgMQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixpvmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0fA/+PqAWVNOCCBuR+89peu4NP5K+cux25dmZ5KpXxLvmcxElwXjY\r\n3ySLcpXMZe7OQ8AlePUdJeQglVsGadMptLakRXTFx4OQ5a3OhRDFIsApR5VB\r\niEK7Zu2OdVwW9loMAH37dagY/Wbh5fbL5on+W63LHnXyOGnBJAejr9CWQPu/\r\nB1WgxjRcYEdfk7VfjOzhq/WdepGK3LvDR2uIG2C3FEdvBA9l0o0yearNcy4e\r\nag7HYnQoWf2aJ+NuU/By6g3SWM0n0LJceo3SeNpBxJndbCRvJqyLj18a4SlF\r\nTSLKLpYK0DQSJp26gvBIEJsEH+E3zSykGqqWjwVTfqAhlvF7bxGG1Jsr0PpD\r\nODnEzHN4nn5LLHmCU4E7xbNg513o0zPy63vx1cOLpV7Xm405MbgxPV51BYNF\r\nanD5yU0pdrhepWbVvyVJHQ0bzNqMSMu0Oyd6sN3qWIT1o9p+TjwaO1nqETmY\r\n7rud5lB1gyloAcryU6TPh5vAZ5xHDGCyQZYPNm2HFu5JVcX+N1jN9xVTfglI\r\noYnSpSG2d1nCFDc23O7gQzARqcvHA2aeTlEAZOQGW1F/nhC3zZMyKXmTJO+s\r\nTerG2g9BRomliBz0D7fg1uiXlHeDx0EEbvVz0Xr9FA5yWzaiJPRCHcFuS67b\r\nz1GzddETl2nOZLe4E+egndKDctf7srZGNho=\r\n=yM6t\r\n-----END PGP SIGNATURE-----\r\n", "size": 45816}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/autocomplete", "version": "6.1.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wtO4O5WDyXhhCd4q4utDIDZxnQfmJ++3dGBCG9LMtI79+92OcA1DVk/n7BEupKmjIr8AzvptDz7YQ9ud6OkU+A==", "shasum": "7d3ca6d49e3a71cfd366c0af16172f5c128376eb", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.1.0.tgz", "fileCount": 8, "unpackedSize": 177679, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtC7I+GzR+jNLh6XNPDhF/O1n67Gua5Lsyl3A9c3Ra6gIhAJsIrPlaHb+g9Tl+tNAGkIHIcY6WYqKAMwYbQ87r6ntX"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1t0GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHHg//eOD9z2VXqv0efM+MPWpsR/2nNXMnsPZNRGq9O666/MXdopDK\r\n0BVvRbeoEwMLQJJCexc383upCA6Q3zTOOR7KAWoVeQGpfWEJX/TsIgpP4YGk\r\n92A9MzCswp5AszkhokxvwsuJcXTfRyoq11FrxCK9hC0cZbXSM86ojfBeqD7Z\r\nTDrrB6sJD/rcHfLIM9h0+VdLNTTQUkbajKHh+VhGkXDLr0wDN30U5Ea7f5aw\r\noS5Bh7ZU/N0HyYQ0BTwt4kSe/6f1EQP8EgD/VZXcD4R0oNY39CiuLodOQA3q\r\nnuDI7OzBaDehUMqa6x0wtsn5de80VPXbi+ekE2tEUraKbKSJk25Q9dBnqRmq\r\n5bHkdj7zDSvwzEcSiAvEVk75x00Xjw/y+WQZmnFNeNJT1gNa8g+WI9cnKZDV\r\nM1kPxLd3YTTBwIlRB1EOmmJsJQfJUTWHG8rEUF6Re5/aLWR0Mh9ryPxJEUOT\r\n1hvFAJDqv8ZjeJ46Kvow2C/Y8rmHyXi79HWxTuIQ6jB1BEMHrOaRs/1IBC0C\r\nw6boy8nip3ps38HQ29ouljQnPKyUp8jXlB9XNbag8GCv/hNqgVEGdlhuaCXD\r\nnbvW11ektw6oHXrfktROjIa50QqIu/xfEEH09oxVn7wdq4wD+41S2rKQOeIi\r\ntHFd2tJmeyrIR/fBT0ap8uvYO1TlQQt915s=\r\n=VtNK\r\n-----END PGP SIGNATURE-----\r\n", "size": 46492}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.1": {"name": "@codemirror/autocomplete", "version": "6.1.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Xqkyyrvuk7Ovg3e/XWxlIv+ez4URpTEGwolLYA5r5ROSwsyObsizdLoA13Dh8K5AMQM7z3SB36r0MfprrGWlrw==", "shasum": "27ffeb3e2e8adc9ad7eddcfe537b183e5def2253", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.1.1.tgz", "fileCount": 8, "unpackedSize": 181446, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGabFtmgxcKsQ/45+yKXZ8pru4vDvyRreYQXqrNREwKHAiBsTqRRn/X/pBt8HqzrsKGO/524Y6ky7ObwcS7L2yTnqQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGeNCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+eQ//SYCNhnMhUN574mv1qTqReQt1pazarQsyvjeURLUbo7FWlDea\r\nf4A9BVY+Yp3k3oRHX9gULPjVjbmkIVb5eCjLk30F/B1/lU6gqZZwNCEvK0q1\r\nhRrDTSlyPNncQY4knxRtO0ZLOvFCEK5Y2wDDMZDC/K3Ixzlf5xfNTNyWmgdD\r\nq6Gsd9GzyIPvLXgrh6+KdaQomfGiP3yJz4Ok81fcVccjwq+eu++5Vz/0gnRZ\r\nYzu2rVgnSbdgtrzZsW4qXCsbRbGvEorkCPxyjLVeVzJxPCGRpZpntL9lK7Kn\r\nh7Ks0+7xANpIS8deGBUJrBKkthl5fOYXCE9j1b2A1cCXBk4q49GlDgDAoGVo\r\n2UTjnkhlXWGJGFX6AwE9xOYXMSIB7IE8UbBtW5jxYGaDNU8qxyTASbjvrGks\r\nOfZ87LrAIONFu6M6qnssomTohARBouxLkeKkAcJ8FW/hRrOBAiwW4o5vL+6q\r\nM9N7DXT0WsFXHUIQBgbTe3Yn+mFx/aUKK/Pm2sIdHmiyeTSK6avPfwnVQGRr\r\nqqAEiwyBn1zEoK/Ope0Sf5JEqIhsc1yt12/hvYKj67Zwa8BDFz/MaKHKxBEy\r\ngLBY6M6LFdtkS6X9u9hxaH0iz5VHLpq88e/LbcztLNM8SyxnbPsXq8VAJtzb\r\n/HU8hGPjvB/UcANTXwZwO/XY7hYoApVumd8=\r\n=A+MF\r\n-----END PGP SIGNATURE-----\r\n", "size": 47234}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/autocomplete", "version": "6.2.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-yNCm2CEE4kE4L2Sf7WeyCej1Q3951ccaCWfomrlBkoERKCss+TzuEeqGe5VnAJTEybLy1yzf1BdMUY/988bfpg==", "shasum": "f7205f8281613f77529f07b279ee25e1a5d20124", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.2.0.tgz", "fileCount": 8, "unpackedSize": 182126, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1VsC/Ph4dPMwYbnsPl0eKNUsUOAsAoYrsI4mtznooTgIgGdJltiaW5R3XWAGZlWB2ohFel/sZgTg1v31pzb/RrmE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIM2gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOEA//fbygQRU+hjTcAySxXH8kQVx+0llgNpf4dg50YRsiUtYH3yYj\r\nIFp2iu3JAmauEGg1/RukPurandu7/xE39ZC11tBsBhC3znLtou8iAVuvet2J\r\neaw0YtRlj0OvGlaxKAWjWDEFrob0gD0Bnii+4jnIfnvMlBK1VjKVaqNXHnAz\r\nbzV2UjGXeq52ot85eRQIasWn4fcywtlRYR3NziAHS6eCr3vRxgnTexCJadgd\r\nLLojAGaXaANYQTfeGmBDDbNITaCbedxlouAcGnMytoFD6xiTtkWbTzoYaExj\r\nEcqLNg28ZBdI9ivYTinwvzBLIFl7B+yoVmr4uPNlTo4BxmZK1fyzijINSRZE\r\niFYXXUAhSeXJ9BmjnYcJUcdeLUzucv4SELWOW/O6G3W1x6KkMI601GIeIsvN\r\neZ0F4b+/QAB5dN/EQ9YQnI/RBDOHTvqj+j1za+pcwVA4A3a/QcDqo8vDWkW/\r\nzPLFqgiJ2YOcFbswar97LX2CSPqp6h0SRsGg+2fel/xOXL3FPocqzKGywt5F\r\n3f0rAUJcP54a7Gz99tS+2M0+DEEhYPvofwVwP37bY5bW3V0tlpMs7s313461\r\nVJqKB2+cY0TfcV/OARGZzQH/ytUJ/gUvGV/m+2iOWyHjFavdId5ifRmUJXwk\r\ns0GcuB9GEDvEMzN5z0w4ZxvNDbVSC6T3K5c=\r\n=7j/T\r\n-----END PGP SIGNATURE-----\r\n", "size": 47422}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.0": {"name": "@codemirror/autocomplete", "version": "6.3.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-4jEvh3AjJZTDKazd10J6ZsCIqaYxDMCeua5ouQxY8hlFIml+nr7le0SgBhT3SIytFBmdzPK3AUhXGuW3T79nVg==", "shasum": "217e16bb6ce63374ec7b9d2a01d007ba53ff0aff", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.3.0.tgz", "fileCount": 8, "unpackedSize": 184210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHj8CE3LVxLMmPT5a6wWvqr7Wni4sutTrx1cyXfqyVOWAiEAzdSrO0RbYFvLnhYarImCnori89zZfqdxaW+MPk83TUQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLHloACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoX8BAAld45FoKuP0S9To/M2ZPRqS4dYrswXSUnq7wPjV570temQktD\r\nk4aWcPH0DxCIDsOKsUe4s6TjF4eEdCgs8Yd5f/GQ+PHgf2jf+AfPRNMl8TFX\r\njJCC04lISUpLoz0v1dI8LJbdhAPKKYZR+ptLiveZ40/aAYIDxQIetRW8zd1M\r\nhvEbohsH81DGRcEI6vOSUivfPkdFJ6QJ5dEWW9duXthoRiPvOh89AWuf1yKD\r\n1c2o8VzEkWZh0q+X5r97RltWkkX15xvidM/gOU+qmUPdjMZWL6gw5UjrojSP\r\nSeOG/a0gexk6G7X3uUXYBizop72IHhaHbcIGoa5KHCaEfIRRWc7HVzKQud/q\r\naBsabvEs5E7/K6kfSbF2hsGsNN5aJAgXUgdJTXMh3ZZztnkdlNYmWw5EhK8v\r\nAg29OkXMEosfCRYKVYT/fTHruYdqq7IkGh7kCWF81eo9JV2aylnxkGq+v4QK\r\n5T4WLdmGdorBUyt9TY6c+NEX4npTyzCFn3VnglmW9OZHmVz7WDatNosPn5jT\r\nWOKu+a1pRWiMKKU+Cgvpd0OfFP9tnEJxxwHms89x3u65llAgVvlI7J/7apuG\r\nfBx34o6BauM9DmEjMQAXoUo56hM0maU2tCJt6/d6CaJJ4Dksg2vs4xhMMGEY\r\nduwWml9NEoHmL7rXoMt12MFWNa6IDeHLca4=\r\n=qICF\r\n-----END PGP SIGNATURE-----\r\n", "size": 47952}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.1": {"name": "@codemirror/autocomplete", "version": "6.3.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.5.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-t7oq6gz7fkZsrnGDrtFLfk4l3YivTpq/fqqxtzOAV/YGlr16jQFxIqOpUrjE2Eb914GXOwERfUz+TYOxx4L76w==", "shasum": "1dae99f69fd93b4b6275bb85e1a72f7b903369d7", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.3.1.tgz", "fileCount": 8, "unpackedSize": 187396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICFx8FlM6mE0dOjwYMHvpAEWop/rvDVtv1dITXj7LciwAiA3UiFJPSDezOOFLmK0McXrqzeldDca6vzoRlyKIRLVAQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcl5uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoe/g//Tegq0tQsRcpGKWLkUq8C19etau3ZMwa1CzPBioTF8kZgnYL7\r\nQbigTj7ng+iPTH2SaDRmc5452LBjIKG7gar/qsHzCC5xAvkcm+BH8XB2wAnV\r\nHdaPdsfayHlTv+lJvcz/VDjS81kuW6ntmDaK4osKDeKGsU91w73rEQupxMpJ\r\n/Z9bcLDsxeutso3AWUOHm2bkZbgohWS/iIpYmuR9UDloEjQjHKaTgrOt3aHz\r\ng9WtDRVwpAF2pMKqYVbg5HLumWSjeXb6cW37oV7IgEk3tVEIqF5eJVNrd9k+\r\no42NyceJEQvT5ja4vZzJsYi7Xi4YqPabYFH9BXfhDp8hA/23Y80m/q95kLX4\r\nfQ7WUN1xzVd/Y7+QEZ4/wUF7q0EWOWMXSUp6f/ZgBQ2clqEeW8TjI11790/B\r\nqckquPJrljX9xWxRhSKGDKq7jMqhuqxGLcrHs5sqLjtGrS+BGAlSN0+diEJ5\r\ngguHaz7l+akbYmsij9Rv/1Vz+UsYTjR7y4OMJnVLUjaMCmGguAVTTD7MJUlH\r\nrBaNCHTS92iAmQoliQrv0LUlkdxdh0v9lJkBdSWak0ytJNKXQrLyiw75GrjI\r\nkt/qPkQB7nvKX6qOPIKdJ8IjnL/nAXTK0hPiC+XyC+5DkD4V5UtQFa4374N/\r\npYpdeJT/iFMCg6Ox3rdcbiQlBHAr7p0c0ac=\r\n=OxSF\r\n-----END PGP SIGNATURE-----\r\n", "size": 48695}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.2": {"name": "@codemirror/autocomplete", "version": "6.3.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.5.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-+VzxrHWkuvSSt0fw4I57SULo/NMrLnNgm6JHrkbIYfDw9jZJNTruCwkv32TCqSeC8xIXhYWMuxawwr/xOoHr8w==", "shasum": "cfd5d8eb2348dddf7b63098f9f09ff47b02f57db", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.3.2.tgz", "fileCount": 8, "unpackedSize": 187773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICi6hRuYRqOeJ0sgfwF+Y6N3Cwl1gkl9uqBofF+QbaAMAiEA5iQQ9yvAzoqw76z1lSTXbZvkJ/FFXeyFRp33Y+vju4w="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc7w3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/rA/+LCUuarwgEpwGZYE8TZkOQ/ixTF15mpbaY2uWfMq208ORLy5J\r\n4WEpfYWIjU3ftc7gvV9gM/3Niu8Ei+gF+RX0+wj/UuSc+4zr4nVAIW5Ix/py\r\nkqEL3DazvkyViAQrgxgiZYmRt+2ZHFdXOotxXQJ5bLyOph1lwR5xNO4j27u0\r\nCiVCCfvm4AvQwdsatxG/MZgQW350aZP8ywQU2bPpaK6e8gjzhCv0naQRL88W\r\n9M/4w0Fngr5jpjtsSafRmmDCZNimAh+hLLXKsMo4qy5mUSfyJ0Zz3g1HZYcN\r\neRYzx0LCl8tsLcca8ChcUnKnKuNEYxssNIw4f0YKRrn9N6nT8ICDirdiXd5/\r\nQVCxTnwL2S2xksUsPi3yIlCELRQ0woxDTVgcpL8haXA8p4VlhGDSB3WqlhCC\r\ncROQLE3rG1O5KCHugUyweUPRmDNvOZGdt2n28ER0Nl9EZcnQU9mr79wqe4kd\r\nuV0UAkjvLGfRM7Z7j0l2t59gfXrAmrNzrk4Dz8X6kIdwyMGrT1oE8pNOlgK/\r\nFoRq8PnM1Ka3/lUeE8mfk3Q3FnOO1UEtW7f3LBhdkyRjjHFxYjRHSxPl6HTp\r\n0Rj2tooUmVxC+gGbdWdzj9mtQkggfS5StuQtGbEqyIhhdADsZ36QqOmUDubp\r\n1UL4WiPTc2e33x7Wh8R0BNt1SXyJkR8biDE=\r\n=cWEI\r\n-----END PGP SIGNATURE-----\r\n", "size": 48768}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.3": {"name": "@codemirror/autocomplete", "version": "6.3.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.5.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-8MGqPuKvE0W0XjabDYX3BFJNJRsYFAYpDBol+YCg6o5t2JyoDeoMYrqRMVnJv1B1FBgNAkvdDVrNrVE/7c9qvQ==", "shasum": "f9dba421b9a0d83ecf89a06def37a96e6afb593a", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.3.3.tgz", "fileCount": 8, "unpackedSize": 187920, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/3Pif6zY2R3NZdDJjnrYvZTHOlFNbSIDm1AxqVUh20AiA019W1R5xEGBL/VfrR++d3sjpQ0gwKiTb5kHbdFPi7Mg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjd8f/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop4BAApC8d7UcBCZJyOm4E+Kr6hD1EhcXLghJkhBipf92RE913IkuJ\r\nX+kmEdZ/ahjuOy3sE+El0PSnb/rUR6WgKolfHaAccOy65t1WOGT9QT9mVQyH\r\nOnMUY/Sla09bzMwKheUO4b87WbCoP89ygO6j2OnAXEBQNxY8uXEBblxQ0+aG\r\nqrNiuM8V/40OwVT6sR6mDVtvp7TrvePmzli8awnTplcjFfW2FQzA+Ea4MOEI\r\nVppvNBxJHDX9XlYovwoLJGNVfqdy6mQh42VH+B12Ox0PJTk7y8j8wFPZOBN9\r\np5BN3DEAZYOS3GRIKUO3n6WlCS9LhIVIFu7szBJXnPJbkNm3zYrDlqLz6+PM\r\nUYwFrF2Yk+kP2wE4ZJPSxDCguJVFfPrNBiD46JoSo7tTijeNnMyB+S5sDHL8\r\n+Zx00o4oYuG4iT8UD3pfH7ruzEgbCWkymUvhiopyq7K5kp9ZpRJFBn2TmQXl\r\nhFOQThSBYSDlnfXlAPLbWnB44lDarRJwIAMqTwKFDa2MjQfEcciAuuJCpPfH\r\nn+yFCGKpBXYrcVHoWwtwVlpNPt5Dd9inW6oPafX/Kv07/O9eNIvfFKeOlsHa\r\nY7NYNi/5w6nU45o0Ww7CKb6+cQHKuSp2kOiAPl7ULmN9h9xVPwOoAiJE9UNO\r\nQFLyrf/KEg0YogkamitDsHDXGU/32v9ILaA=\r\n=o6p2\r\n-----END PGP SIGNATURE-----\r\n", "size": 48791}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.4": {"name": "@codemirror/autocomplete", "version": "6.3.4", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-irxKsTSjS0OkfMWWt9YxtNK97++/E+XIHfKnRpSVfZyHzda/amYF0BR+T8mMkrGQWidx2zApxHx08GT13egyQA==", "shasum": "dd22f0a0220718e1f6e3b48319649859b6c929b6", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.3.4.tgz", "fileCount": 8, "unpackedSize": 188108, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmAiabzhKT3JkQuST328xD3uJ9cGGwqJ4Vd3rGuamqQgIgZ8EoXbyO/fnbUkPtrhjYDyUCMJjOYNjD32rRGtr8QQM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjf4aHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGYBAAmuylGfunCW4FM2wplDS4pFpvaaLvsu6Ws17/2xFmPlzGhzWR\r\nUJqZ+TC59ZGcYCyVvpKxqHF+mhbVXtMcz4nlhudt06JGUSy3lYuyX3UzpFRP\r\nPlq0WMxJS8HlwDi3mdDxv05Achxd3pwyMzLoCvALyaTt5gb0xsnJSU86KLXx\r\nv9Jh0ANpi6AzqKhllozPOdwQwB7fb0/I2m1SGgAxhv5q0IXBKjXlNlDdURF6\r\nGAYwtkSIf9d6QBCdnobJTAK+ZlqJzDAIvMYLPS8ti2vNgXXO3rSBfUxNbqVa\r\nQvj4YdFW/p0TK/RXLXqcleNmVmR6yw3CDHh7fXAtN8m0xiPValMkaziMTC8m\r\nVWczm2n1ribncnNXdURiFcohg3akt2E2JGDbGmNjTqKT07T233mvPCGoSzkY\r\nLNpe36QpshLryo4haxs5FoEBr7TtLHOojpaGCejFLfFSBsKOKwQnDY/RUsw1\r\nVbQk0OJOzPQrT2zGfHrbSgTcevmSslnl81iRc4J449SIq3G41FAIMaHiFAg3\r\nW7hvoxp38Ujtwieq3yFG+frmGkH0F96tYXWqnn1d4pE2xN83Wl+35aRNweHF\r\n2RdV/gBTaRqsW2QPVoFgxoHOrEMyzmXaWK+6E37yqj/sSWeyL2eoHTDpaMrV\r\nKMlKUr1TbltbpCFCWldKpiFdH4Pmm7Ccc0c=\r\n=ZXtJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 48828}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.0": {"name": "@codemirror/autocomplete", "version": "6.4.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-HLF2PnZAm1s4kGs30EiqKMgD7XsYaQ0XJnMR0rofEWQ5t5D60SfqpDIkIh1ze5tiEbyUWm8+VJ6W1/erVvBMIA==", "shasum": "76ac9a2a411a4cc6e13103014dba5e0fe601da5a", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.4.0.tgz", "fileCount": 8, "unpackedSize": 189925, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCJ2of7Meh6+bRXT6EfLAxCNplj/4V/MSpbvO5gsg4LgIgCzw949V/Q4D0nC3/GYFE5CauTAb+2YeilCYXU/A/jSs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmi9TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/vQ/8CCnwbuBS8JUNf0xnNeZLs95x9M8S+y4PGrRrkIxXMq2Q5djZ\r\nLmFjnEsbnHeDhVXnBfpZfJQT9lCnwuz/Non5niaD3JyfFt1thR5pD+Dthb/J\r\nYdFOEf3QkWv8jN5v28ntT0rp9+/1K52gHCu3Af+lJiKl8e8MvEbaSWyiRrPv\r\nkQdYu17pGunli5l5J8LOrnEsEOTZc5kT3ZG5YvjMyNqRAKTO6KvUOe1ulrtX\r\nBjvpAtNQXYqavzl/+chlkvfbYwgyWnDiqyw710/jLwWmYQqlEIKkkmz4WzNy\r\ngEK7obofsqm7Q901HitlvkiDRO+qkgDF6hCN1vs7OCsDNd7wy9zAbQ5HYBRy\r\nUqSOdlED1LFB6RJdD5uIIVMjetgT16VGrjzFRcvnlEQlLYPIpHHfN9Luvp7z\r\nsknZwsN6pGD8QhlgIrYoR0ko+sn91QGv73rNQLwxN3YAqKJQZ+n+N2uO+PvZ\r\nrroL5px+oXHfJKNbLCsPYnq9VpK6l7r21DzgLs+1wo0KXgVkfm2kev1C3BjL\r\ng9PCYvGOdanjEjbdEeGJwnpzZgYOCdI8S6MhvRvGDxCUPHpZc00a6Dsuvk8+\r\n3mGgZISKslBJxpis9wcKWVxR9cVmlWMtLFruAQsqZGJ9cxQAUWeGBiaDh50n\r\nGnudtArXcJZk7QwhFLWq5YMr67ntmSc3GxE=\r\n=sBZq\r\n-----END PGP SIGNATURE-----\r\n", "size": 49211}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.1": {"name": "@codemirror/autocomplete", "version": "6.4.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-06yAmj0FjPZzYOpNeugJtG28GNqU2/CPr34m91Q+fKSyTOR6+hDFiatkPcIkxOlU0K5yP7WH6KoLg3fTqIUgaw==", "shasum": "0af405af8b90ab24bcb883d8218bd72e47ec03ec", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.4.1.tgz", "fileCount": 8, "unpackedSize": 191131, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbs3M+Fw1gqBId2sMSsYgqR6QPuBZRmpwfDesC5Bw9UwIgNLqw6L9iXlG85cMhrW1UTaq9+YAIhj9uCxmuU3d/m54="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj61CWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrnmg/+Icam3/VeKWsBjz99yJvi9x76+YfbQPKvGVCYOfNiLLONgoz0\r\nNGnWsF35SBPHj+Fd4xBLOQSxHgmzgG9iTZKbxy1xEUcjNKzv8PhUr8g6XRes\r\nMGrZsA1ZjxbYyiT5B1vvrjWJXZQv+8zoPxspTvBUYnQLoSQxv7u3nwr8zz4K\r\nkUC5RdXVsN1kkcxHOwfedELTqW3Vvx+tyY4lU/U7oI8ZzUwKGORX54D26cQe\r\nvkzugfGveJSDAUP3s77ULJAB0ifjClKtKyoeP7rW7qGRXRI9cNKTrWy+qMJ9\r\nc7JSwelf8cK/FoEemrzk6ouQb/3kC4IHGeBzWJrf+CgpgOc+4rTvWqGp9pSs\r\n18z0ks3pIzK0a1KN4coJK/4iHC27NN4NoBKt10uco11gaiXwPi2JJBoBXOII\r\nl7VZK3HPOEbiFCmA5ClRkJhe1nIRZ77ln18Wc+rRXGeXYHRR8znVkD962iRw\r\nOas9dhKe/cgCgzudcRG8eaFuYSr4GmOClUG6jkscyu+98DGRV0skr2QVQq/k\r\nNf4upkHvYoGeYSwGE1CILIdNOt7raoJXG/xfOHo/AH1OjU0eTTnc0rh952Yr\r\n0zbe+rw8qBK4wQzm8JflgoM4XzOGa4JDCngTvsZdx8/sz4CC7FTnJZxgqvKA\r\n4iixgejQee4CAAudNE1FuT8Nep6p+S9RD6w=\r\n=qIEv\r\n-----END PGP SIGNATURE-----\r\n", "size": 49476}, "_hasShrinkwrap": false, "publish_time": 1676365974510, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.2": {"name": "@codemirror/autocomplete", "version": "6.4.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-8WE2xp+D0MpWEv5lZ6zPW1/tf4AGb358T5GWYiKEuCP8MvFfT3tH2mIF9Y2yr2e3KbHuSvsVhosiEyqCpiJhZQ==", "shasum": "938b25223bd21f97b2a6d85474643355f98b505b", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.4.2.tgz", "fileCount": 8, "unpackedSize": 191412, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHriW6RjCmnHDH0mC8BoleyI9mpZVQKPB4FMSpx89Z46AiByjGuEa0/jZRYk8GZlEF+s/xom84/5KyIt0S2bN5nN6A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7zdJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyLg//RmK4Iq5hGvC+vbj7RKps4tg8aqeAJ3A1ezAL1vyhJo5RdksT\r\nLzLy1dM36yMXyBhHlbkPSU/KGvNGfftUbwDGX3pJNrkEK4AxlTjnE7+DEtkU\r\nBuELeVUrrkAzB6kWId11X26LtioftfbTRMT1tbC7D0WWxab/8Cttiq/hXKUs\r\nZnX8PFeNDKzj4qCEs8yG/PWWuNNKmL30orkkDmknwv1IqrgCFpXsVhfO+p3f\r\nBAMcm8pvZ+yzfdTt5uznXCnb5IRzLE6GjEIPHB1yfjSbPZX2GLIZNIej5otW\r\nVELEmL2WXXUKJSrkRuEFlgyRpVo0CWzfTkQwS121UUOX60sflC9Odrs/G9rC\r\nKztncWujByvkojHamylqd47aoeq+JtnctHss/EZdmiueZ7dTtkRtL17v5rDY\r\nrMZm9n7Mfx6/4Bk5iVtb5nHDfxilpWSIoaNzunAc7hKsT3wuNlI/HmWc9P4k\r\nyxxcZrb5gjP8rpVVsJR1ySY0MMuNtZqYxOtadrsMO5JtdIweh1pVJ9gO7ipf\r\nS543koqsxw5tAOkPMxk9q0iryCuPGpTh2fK1TZ5V1Uh+0Lr0WaHYNNr7NRiu\r\n22MppgAoqCiFy8T3JiU8zqC4Le8ZiWa3yJNZ8UKqF3vekYOk4axNd688PMt4\r\nBqA8oFEVBPSfId14H8+NtBpUoqGIHBFDQGw=\r\n=AIxb\r\n-----END PGP SIGNATURE-----\r\n", "size": 49549}, "_hasShrinkwrap": false, "publish_time": 1676621641411, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.0": {"name": "@codemirror/autocomplete", "version": "6.5.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-GNS3bK/BcFRt8G6xFB2eP4TONBEyAwGTbBQFZZpaUJjJzBe80ZdPEyGJMYNUG3RsYaVOmwI7Ka/sQ4gqJU6TdQ==", "shasum": "1e6c34b624595bf982e8cb18110ae88fce3d6ebd", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.5.0.tgz", "fileCount": 8, "unpackedSize": 197551, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6nEkpNxl0dOV0dZwtBDFhzA5NX3D7LrsKgFHuOgb6WQIgDFgz9k1iNv3WWt+xrSSup5Dhpx8Hi3Gt6BzIeh00Hb4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkN5dOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr19A//ZK+6J8baymiGrb77hKHmTE6i6N4cPGv9jAEf7xcf5ovfWTGe\r\ntkmlPgiI9XdPzKFkPS6J1GK3EqSe7v/jqbqy9cQnugMDWaUaUM7oPp9lmmfr\r\n3AE78PMPkOl3RaGUbCmZfUB1ltY+8A3esdZSDvjYxTPSynK4kPRWm+pvNo9I\r\n7VfotOkfpB+6nkdkmLiEYOxP0TIKc2Mi05Du1Ybrrmyc463QYBhShJvjOaeu\r\ne2r0rWJCRJeyM5pH3FKOdgw0YwR8UXhbl03Njtyc2xapa5n/+TuNVnN2TV8d\r\nS6RvCf9eZ7uQGAFQ0tR/aFIxvkdBcx0J4O6TxbZozvutWVhtNqeSYmEHEd/N\r\nf28F9gusSL+cSZ+RoePeJdxjlKIvR8M9tbxsNrIVc2joFOYBSarrYLaqAaIA\r\nFIWXza4WteuRiSFDARKIf8kFNt41VppPYOa79x9AB4c7NwQiJDJQhYpnbuka\r\nJ+pENcWecMLQxN0FuZJGMfG3YOxXJcP+RbZ82VJJHa+KUedQPJcijQ4qxwAC\r\n35ZT1G350usIAQuAabuJuLy3XuqAsQ3arej65pdHiR0fPuKAfZbGamiqYnZj\r\n2QY9eJ9eSJo5dMru5tyOokKBHAASG7SRKont1SwrZp/mkGtoZqU1gt3SaWyD\r\nMxXNK3alfbiIMvpXy44nLpbBwhMHkuB8qsE=\r\n=BAaJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 51373}, "_hasShrinkwrap": false, "publish_time": 1681364814731, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.1": {"name": "@codemirror/autocomplete", "version": "6.5.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-/Sv9yJmqyILbZ26U4LBHnAtbikuVxWUp+rQ8BXuRGtxZfbfKOY/WPbsUtvSP2h0ZUZMlkxV/hqbKRFzowlA6xw==", "shasum": "539cfff291dbffd3841cba078b222cea28ff7eda", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.5.1.tgz", "fileCount": 8, "unpackedSize": 198762, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwXYnShiEda8uCoXIH+OUyGrvQr+RZcAouePV3um2slgIgDXh+4TcZrwuefDQ8/sv3QJEkEMjsTRVLxhzjoNky2io="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOE/2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT+A/+JSxJ/emwcyMxaM+5ezs1cTJmxxRZtha9Yn3n1HT2dUgX6ftJ\r\nU221Z+u4TfyOUdhk77Zs18Hxr2lNCeoeornbwMdPqVFmKl7pELhg+deo5neb\r\nslqHHBOMpyadeE0KhfJa4B/xQk4Yg5hCzgfUP2jzntaZOKPTU2YjWyV5U6El\r\n+TL9Zis5PVr/R8KShPwyAWVD6LbAo2c+B+caXVwEZ6LnqppZsJWU6U54HdbO\r\nJ3thNYmp6IBq7l9P24fTPx/Wmi+/MbW/tXSEnTObuabD53cqxuAY9DfzYm/Z\r\nelDei7QOA6AfP4dtwp9jQmeT00r735vrlZy1AulZtngIcHd9YzJtORSS8tym\r\n2kIITUpPQ6BBMR+u1653EV+NVtffTgayh+t7P0To0uNtEbpmfwVc70HSHBUR\r\ndttEwI/qQEFnULf4tC+HLMLey28wYxdXf9bGZvQwE3TFqKX+4f0V+z2DalGd\r\nERWfIgoYzDjglAST/Lb1l4nOTD6QlGigUkS/tQTYe2qgeTw2p6qycTPKp/Sn\r\n7J3uXOKrj5+kDxHBf+XO8z+Mqp6Ar0WBqp6FZmvqI5ZG1lXC6296ooFEL7V2\r\nz9J2podd1YHVNuyZAz9pFHUtp4XT70fokk53Z6o5loDu2Ne5VqRDDX6oPPz2\r\nLzJnWovXAWXFqgVse3JCioVbzpJHh5+UAPk=\r\n=iZQA\r\n-----END PGP SIGNATURE-----\r\n", "size": 51594}, "_hasShrinkwrap": false, "publish_time": 1681412086262, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.6.0": {"name": "@codemirror/autocomplete", "version": "6.6.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-SjbgWSwNKbyQOiVXtG8DXG2z29zTbmzpGccxMqakVo+vqK8fx3Ai0Ee7is3JqX6dxJOoK0GfP3LfeUK53Ltv7w==", "shasum": "9c0ea57792b405a391599bd80acae19b8c4c6ff5", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.6.0.tgz", "fileCount": 8, "unpackedSize": 199154, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRMauH3pBywCksawFbYAIr9hIY7qq5yv8338UV4pzVvAIgQ95NFONnkF34vQqF/YUMawp9Q0VKzvv5b64xkYiWoMM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkShCgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxlRAAlL9tLxugC1HAyZWFNIv/X7Kus1/mix8ft65XPPD1LSt+GfFx\r\nE36GwALj6qM4I56uyt4C7KC0+IISn7mkn4L73j6k9eO1Gmy2qRY/rsHcclaV\r\nVlOFIdVrxeIzO/cTGWqjOuLOgxdN6V1LHoI2AKTec5myYRoz3XFGBtY3/w00\r\nkcZuF/hQ82B/Skd40/9av/eZQKCmcFWLR8T0GSw66WgqdStotcQ98IyvptVB\r\nu/qTfG7hqXSN2H+UsjdRxkSO7A7emhqhN/v9jXGcFpaThR5bUQwlqNtNx4OM\r\nrKXbSbC26owFHuH6fLk7Cjj7TxTi52MVT+hfXrfs/wz0iT2ckgQoIH/+3On7\r\nWhw0tcdh6tDy9Yg2b7gcYPtf/rtXEFNSllk9QeeLi9QSY5kjHXL2vLvf3nRi\r\n1ZgTxBUrwU24HYZgOfCMdqIEpzs/a6KPeWnHFROju5ayCimL7OMtNkka5+MU\r\nXs4Jeh5aROmKRQnRtSEAL4xif6TmY/pC4774K3dSrbdzzHk4fyCeBfF41N69\r\nTiRE+f199U5pzvfXJc3sm4jlSZOsS1t6cvxZA56HLl0MPOBIjJIlh04KN1Tw\r\noxztQaUr9VT4JzVafveG9qqazUPZrkseSo5L8srjyk6NdqQYSUtJdg8FvETQ\r\nlVqBv/mofsabuk6T/IXhPxlKSS3Kz7dKco0=\r\n=D/nM\r\n-----END PGP SIGNATURE-----\r\n", "size": 51710}, "_hasShrinkwrap": false, "publish_time": 1682575520819, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.6.1": {"name": "@codemirror/autocomplete", "version": "6.6.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-RpsvnYOopnyNbZg487qoRD5bKg63KMMUVP5d8MQ4Luc7Mb6JBWTORovLi6cTvWaKlbmLW8Zd2dAJkIdrhBsXug==", "shasum": "19e3541f8d17ceeb7e96fcc7506e8da9346b14af", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.6.1.tgz", "fileCount": 8, "unpackedSize": 199697, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+wNqWAG+JvhtCXqsHLGcA5YTJzhm1YnuGz6ih0gZVTwIhAIAnYmEkCqGp3lIaGEI35mgPX+PKs3/JyvXQ0lVf1DVM"}], "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUoDoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkUw//fbXkFI4EZb0MD3FNSzJSfpm3jiRa3MULhpchYyCMzu8/dbRH\r\nTWl1JSpTRILfZHS3mwNLqAgq/+t16gv8rrgu4cnvST7a1yB3MxU0BfleNiON\r\n7oEq/bDX5vMKxU3FHlLl3jHSfaTFd3gLHzfrg2xT2k1419MBQK68yCW+dKNC\r\nlILnloVRyc+YtVQ4cONX93zx9Mk2k2s+rhEjVRO0fgLzVCB+YQtCLPT5HaPH\r\ngx0r8PZqYCMSGJZ8VGC3IafGEl9W+8LAylnay5o1Ipr5K4mcLbPqtM6bYjrh\r\nIwH5P9aD3KZbysdWpgqdaUH6bx6wNTbrYPyliYF8nlnRCt2PGLfMsedatgz/\r\n5ZEFbSMaAFzLGo61O3a/14vSeEWFVo47frwQIbFtp/GPzcNAb+DpVtb3MTy1\r\nH39aMilyIz2GjB7lqiKIN1PA4dIAoTJezEfnBPhIpgBSPBpvFY6VHOMK3pxf\r\n5N2wNZAasVgzMyfw8IjZJ6in4d6sZmLl/MnN5XdxuVE2NcjnppYvxyD+n5MD\r\nx9oBcZs7jL2JLQ0/AFDyJ937CKPAB99HRr40kmqQ1Rmzt//qVui/JDYKoybO\r\nIRXySuP3T7VgTLKueLqb4CL7DLYGqjJFcm7LMzSXzwNCflkXwNa39Q3qVAv1\r\npXj0dBFqn50ziiM9EbMLUeG2zN09yJumhJc=\r\n=hDGr\r\n-----END PGP SIGNATURE-----\r\n", "size": 51839}, "_hasShrinkwrap": false, "publish_time": 1683128552740, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.0": {"name": "@codemirror/autocomplete", "version": "6.7.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-di5NFfxuG3c2dllwb33Zyw6Qsmo/W+cFCO2JzBPZLWlLSkgKQfr+r0Kf3qK1b/TwkQ3MbnBQY8Z01hkSYX6Qfw==", "shasum": "8cc6ec437beb2aafd3186d63cb475c30ba3aa3c3", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.7.0.tgz", "fileCount": 8, "unpackedSize": 201430, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC/AAaad9oi3JxiedlU7gxzppz8LvZ5jypEBec60aFdcAiEAi64DStPRDQI4FvRgcXM5prgY+VL4k6VGoIsstjYAl88="}], "size": 52163}, "_hasShrinkwrap": false, "publish_time": 1683789618584, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.1": {"name": "@codemirror/autocomplete", "version": "6.7.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-hSxf9S0uB+GV+gBsjY1FZNo53e1FFdzPceRfCfD1gWOnV6o21GfB5J5Wg9G/4h76XZMPrF0A6OCK/Rz5+V1egg==", "shasum": "3364799b78dff70fb8f81615536c52ea53ce40b2", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.7.1.tgz", "fileCount": 8, "unpackedSize": 201630, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfKz+sNmb8Xrmlxb+vDpZqcprpZBBErGPdnGBXtKT4jwIgOQLT62eGtqYx9DaXkgmA8JZg+yLJORKyJMAyiLhbaIc="}], "size": 52217}, "_hasShrinkwrap": false, "publish_time": 1683960463293, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.8.0": {"name": "@codemirror/autocomplete", "version": "6.8.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-nTimZYnTYaZ5skAt+zlk8BD41GvjpWgtDni2K+BritA7Ed9A0aJWwo1ohTvwUEfHfhIVtcFSLEddVPkegw8C/Q==", "shasum": "20d46587ffe0e8df24f5a503c13ab994c2c73107", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.8.0.tgz", "fileCount": 9, "unpackedSize": 224930, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbQM3C7MmxrbdSyToejaVibLhVKE9hn5VleVJySXwYHAiAIXpBtWUdAbo0W29VodcrECn4FoIzSaqCNAzqfkneTIQ=="}], "size": 58114}, "_hasShrinkwrap": false, "publish_time": 1686581807351, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.8.1": {"name": "@codemirror/autocomplete", "version": "6.8.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-HpphvDcTdOx+9R3eUw9hZK9JA77jlaBF0kOt2McbyfvY0rX9pnMoO8rkkZc0GzSbzhIY4m5xJ0uHHgjfqHNmXQ==", "shasum": "3f3daa9f591186901db07f58d17256656242e841", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.8.1.tgz", "fileCount": 9, "unpackedSize": 225051, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxxKGP4oSZf24VrVZcoigipFOLdYmC1GJEaeSWGFcjUAiBiy9IzrUbJHHk5/2Yrk2qabfxrRxAcBzU9mOXjhJb9iw=="}], "size": 58337}, "_hasShrinkwrap": false, "publish_time": 1687506072261, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.0": {"name": "@codemirror/autocomplete", "version": "6.9.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.6.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Fbwm0V/Wn3BkEJZRhr0hi5BhCo5a7eBL6LYaliPjOSwCyfOpnjXY59HruSxOUNV+1OYer0Tgx1zRNQttjXyDog==", "shasum": "1a1e63122288b8f8e1e9d7aff2eb39a83e04d8a9", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.9.0.tgz", "fileCount": 9, "unpackedSize": 226835, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+GhD5+MBs/W4gd1l8DKzuO8A8LHvnTIxGKBi/ptwtYgIhAN49IAeN41vb/jQeBw5/Mw2QrOgtkbSSlWY+cHLSUlr9"}], "size": 58732}, "_hasShrinkwrap": false, "publish_time": 1689662231855, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.1": {"name": "@codemirror/autocomplete", "version": "6.9.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-yma56tqD7khIZK4gy4X5lX3/k5ArMiCGat7HEWRF/8L2kqOjVdp2qKZqpcJjwTIjSj6fqKAHqi7IjtH3QFE+Bw==", "shasum": "e0989c6a33a37604b5d2c896dcca7562ae3d7c61", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.9.1.tgz", "fileCount": 9, "unpackedSize": 228703, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsQjrIcsC6uyO25T1kJnMlHcf6vGEo47OET+mo/HYa+wIhANV1eOfwuzpgE28MC1Il1r8UlWd+fR4FfHagRb3hoCQa"}], "size": 59027}, "_hasShrinkwrap": false, "publish_time": 1694669950640, "_source_registry_name": "default"}, "6.9.2": {"name": "@codemirror/autocomplete", "version": "6.9.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-suItGf7PhtfgQMCd8ofYzycdsAHDBB8BkNrmyxeLvptW7yNT6zGT6ZzwhAfmB94TUyAAStrHjaDGC4/foenF2A==", "shasum": "b0f5132647a2f4bd2eb223d682df65f2cc020beb", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.9.2.tgz", "fileCount": 9, "unpackedSize": 228845, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzbnO/uJn8iBxOgZh0pMdjiCI3sMjKpmE/WEVFFrRbMAiAuXLRXPndxkoDoIlBF34WqcQu1cLK0+BugXnPlZVtm7g=="}], "size": 58985}, "_hasShrinkwrap": false, "publish_time": 1696600999668, "_source_registry_name": "default"}, "6.10.0": {"name": "@codemirror/autocomplete", "version": "6.10.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-5mgz9EQ75jhypQKVBBgCXWrDSOgA/gS4fhGUzL6hdR3aQSWGE1GphfrUxbkmtuh+3TERGWY6/NlIWgZ8WyS/4g==", "shasum": "c25b40877ec87e8043912954231053cf05d670a5", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.10.0.tgz", "fileCount": 9, "unpackedSize": 229745, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKpcL1OBs6A+uh4/hN9wShjXyP1ilvMSpkbR9OpkJ4PwIgSXWbSkNUpKc7JC6KzUjO44rcvhpSEqvgAlnSUB2iqNo="}], "size": 59216}, "_hasShrinkwrap": false, "publish_time": 1697015652689, "_source_registry_name": "default"}, "6.10.1": {"name": "@codemirror/autocomplete", "version": "6.10.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wKfeYuf4Dsz0r/jvhIoN9k9KrFAhK8x+YZ0E9HfbgJ0ZJ+AmoNbmjSDv6HQ/tBSjgb3H00wn1XrdmhRkOQxsew==", "shasum": "f96591320ce5205ef1a7c5adb2a73776649bd3be", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.10.1.tgz", "fileCount": 9, "unpackedSize": 230052, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF4Zk8zikrID5jl7kIeJTGs5yGuFSeKI48X3PWyHRCYcAiEAuXR5QcBdxaVaS7JMutbTmT/Jqz3xb4UP03EjkXu6LT0="}], "size": 59294}, "_hasShrinkwrap": false, "publish_time": 1697053314012, "_source_registry_name": "default"}, "6.10.2": {"name": "@codemirror/autocomplete", "version": "6.10.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-3dCL7b0j2GdtZzWN5j7HDpRAJ26ip07R4NGYz7QYthIYMiX8I4E4TNrYcdTayPJGeVQtd/xe7lWU4XL7THFb/w==", "shasum": "d3a7e2a4ddd1e8a2992b1b17d4e7692542c71052", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.10.2.tgz", "fileCount": 9, "unpackedSize": 230088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYZI3VQURbW9jJ6VOYd2rpHbG4tomEvyo6q+IHSx9GJAIhAM0PFUaK7rpNMCv4xVfejqe+0j/MCBmcOWkTenqMnJco"}], "size": 59321}, "_hasShrinkwrap": false, "publish_time": 1697180300587, "_source_registry_name": "default"}, "6.11.0": {"name": "@codemirror/autocomplete", "version": "6.11.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-LCPH3W+hl5vcO7OzEQgX6NpKuKVyiKFLGAy7FXROF6nUpsWUdQEgUb3fe/g7B0E1KZCRFfgzdKASt6Wly2UOBg==", "shasum": "406dee8bf5342dfb48920ad75454d3406ddf9963", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.11.0.tgz", "fileCount": 9, "unpackedSize": 230171, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0HsPi8EVAKrGs7D9fZUKyAaKImmn9+D5z5xlL59j0EgIhANvV5b9xLbMHzDPI5XpNqOu4+vL3hyd/R5UNMIGqTzjB"}], "size": 59398}, "_hasShrinkwrap": false, "publish_time": 1699529813997, "_source_registry_name": "default"}, "6.11.1": {"name": "@codemirror/autocomplete", "version": "6.11.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-L5UInv8Ffd6BPw0P3EF7JLYAMeEbclY7+6Q11REt8vhih8RuLreKtPy/xk8wPxs4EQgYqzI7cdgpiYwWlbS/ow==", "shasum": "c733900eee58ac2de817317b9fd1e91b857c4329", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.11.1.tgz", "fileCount": 9, "unpackedSize": 230320, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICINXNj2SeoEPFYGjnOO0sIEqfJM1a1czoGGBNbBi6XWAiAdv3d7JNkJqpnornQ/9ovq2b+dwx9EyutJ31ttIhEzcA=="}], "size": 59434}, "_hasShrinkwrap": false, "publish_time": 1701078513801, "_source_registry_name": "default"}, "6.12.0": {"name": "@codemirror/autocomplete", "version": "6.12.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-r4IjdYFthwbCQyvqnSlx0WBHRHi8nBvU+WjJxFUij81qsBfhNudf/XKKmmC2j3m0LaOYUQTf3qiEK1J8lO1sdg==", "shasum": "3fa620a8a3f42ded7751749916e8375f6bbbb333", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.12.0.tgz", "fileCount": 9, "unpackedSize": 232466, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfUMVwiYzv5h6F8yRibQRZWBMwfpCbHXf9ntY55ykIlgIhAN/OjKmD+ghtkEdoGIQYLTu+8OtqeSMA6d0tKfafE+6N"}], "size": 60059}, "_hasShrinkwrap": false, "publish_time": 1705083944530, "_source_registry_name": "default"}, "6.13.0": {"name": "@codemirror/autocomplete", "version": "6.13.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-SuDrho1klTINfbcMPnyro1ZxU9xJtwDMtb62R8TjL/tOl71IoOsvBo1a9x+hDvHhIzkTcJHy2VC+rmpGgYkRSw==", "shasum": "fa7df3b2809863df0da4556f72ac4263ea4d7adb", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.13.0.tgz", "fileCount": 9, "unpackedSize": 235173, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEnQQa/BS+SVsA3uBgLa4lNI7eHK3Z3ty/mWq0tOOMAFAiBjq0dt7jEZ8UTFrse4M1ZEbPc8QxNJTE4M+Hlyw+teuw=="}], "size": 60693}, "_hasShrinkwrap": false, "publish_time": 1709188969989, "_source_registry_name": "default"}, "6.14.0": {"name": "@codemirror/autocomplete", "version": "6.14.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Kx9BCSOLKmqNXEvmViuzsBQJ2VEa/wWwOATNpixOa+suttTV3rDnAUtAIt5ObAUFjXvZakWfFfF/EbxELnGLzQ==", "shasum": "297071b51c8849ff35bbdd8c323964e113c10d84", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.14.0.tgz", "fileCount": 9, "unpackedSize": 236755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAYjLddWA9z2UfiGpN0mP3JIZB8low8FwnIQMeBriz08AiEAs1N9XL3JEumhWhMNGt536XCPAj9Sv3NoVGhN+nVd5/c="}], "size": 61047}, "_hasShrinkwrap": false, "publish_time": 1710091942611, "_source_registry_name": "default"}, "6.15.0": {"name": "@codemirror/autocomplete", "version": "6.15.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-G2Zm0mXznxz97JhaaOdoEG2cVupn4JjPaS4AcNvZzhOsnnG9YVN68VzfoUw6dYTsIxT6a/cmoFEN47KAWhXaOg==", "shasum": "37bc320f20cdda332d6bf4d1fc7f300f8fc5f04c", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.15.0.tgz", "fileCount": 9, "unpackedSize": 239279, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIArdgSIsLvyNMRd+kMZCvDwVJh8lUsjKWuKVrE7qX9h3AiEA+IfOtlM9XbUuVepUursJJSln03TuY4JrmM3BuRi/HFw="}], "size": 61518}, "_hasShrinkwrap": false, "publish_time": 1710354455778, "_source_registry_name": "default"}, "6.16.0": {"name": "@codemirror/autocomplete", "version": "6.16.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-P/LeCTtZHRTCU4xQsa89vSKWecYv1ZqwzOd5topheGRf+qtacFgBeIMQi3eL8Kt/BUNvxUWkx+5qP2jlGoARrg==", "shasum": "595eb30099ba91a835ed65ed8ff7497388f604b3", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.16.0.tgz", "fileCount": 9, "unpackedSize": 240475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOKa+67+X0hHI23/ZHodnQX3R7SbB7BhQTSjf+CTbbNgIhAI6W6ia+RvE0G3GZF1AOhu4oLjIdO34yCxn56lDcvwjf"}], "size": 61786}, "_hasShrinkwrap": false, "publish_time": 1712907786349, "_source_registry_name": "default"}, "6.16.1": {"name": "@codemirror/autocomplete", "version": "6.16.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-A8ouvLbTi9rGNhdmqRuj0ohY7pSWwO4iK3DdiIhWvUjI8T9Yiqul6t8z9warW8nFq2fyW1Pr+KZWZhuQL+iOqg==", "shasum": "719c2c7feddfb9938e23b9f71220fc062ba54dac", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.16.1.tgz", "fileCount": 9, "unpackedSize": 240476, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExwkAn33AYXEJNifYlNwj5yL7J4akfHyWlxP6DtD7ghAiBLLhhiklqm/dCB9CQDPDOgqxT93L7v8GT+jPIKGElPvA=="}], "size": 61792}, "_hasShrinkwrap": false, "publish_time": 1716988104907, "_source_registry_name": "default"}, "6.16.2": {"name": "@codemirror/autocomplete", "version": "6.16.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-MjfDrHy0gHKlPWsvSsikhO1+BOh+eBHNgfH1OXs1+DAf30IonQldgMM3kxLDTG9ktE7kDLaA1j/l7KMPA4KNfw==", "shasum": "ac4e191cd599503e45f35e97366b432d30b8f37a", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.16.2.tgz", "fileCount": 9, "unpackedSize": 240757, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCt/7Eia5mqkLNEsgKgFEWox9bHY0JwzI8IbJerGWRaYwIgI5LtOqYFRoSvXa+f1XJiT9x6/roeQsPPfaEYECe1TbY="}], "size": 61882}, "_hasShrinkwrap": false, "publish_time": 1717138621104, "_source_registry_name": "default"}, "6.16.3": {"name": "@codemirror/autocomplete", "version": "6.16.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Vl/tIeRVVUCRDuOG48lttBasNQu8usGgXQawBXI7WJAiUDSFOfzflmEsZFZo48mAvAaa4FZ/4/yLLxFtdJaKYA==", "shasum": "04d5a4e4e44ccae1ba525d47db53a5479bf46338", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.16.3.tgz", "fileCount": 9, "unpackedSize": 241000, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMJUYNtXWfUn2w6C4EJQFO+1o8f2Yu5R5telK2BZU/LgIhAP6MisVPqLBSwsI5T/jpu6z94L0/PXij0ZVyhUen74kB"}], "size": 61963}, "_hasShrinkwrap": false, "publish_time": 1718779884392, "_source_registry_name": "default"}, "6.17.0": {"name": "@codemirror/autocomplete", "version": "6.17.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-fdfj6e6ZxZf8yrkMHUSJJir7OJkHkZKaOZGzLWIYp2PZ3jd+d+UjG8zVPqJF6d3bKxkhvXTPan/UZ1t7Bqm0gA==", "shasum": "24ff5fc37fd91f6439df6f4ff9c8e910cde1b053", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.17.0.tgz", "fileCount": 9, "unpackedSize": 244217, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHsOMsiTNDYzTxc4WaheQiStNDo5bVzeSkdb1tD3wFvwIgaCmooBdMJ2QB6h26e0A+/ACVY/Z3ga+ibFHGzTCPszM="}], "size": 62584}, "_hasShrinkwrap": false, "publish_time": 1720004408333, "_source_registry_name": "default"}, "6.18.0": {"name": "@codemirror/autocomplete", "version": "6.18.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-5DbOvBbY4qW5l57cjDsmmpDh3/TeK1vXfTHa+BUMrRzdWdcxKZ4U4V7vQaTtOpApNU4kLS4FQ6cINtLg245LXA==", "shasum": "5f39b05daca04c95e990b70024144df47b2aa635", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.18.0.tgz", "fileCount": 9, "unpackedSize": 246928, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHyBanTOTe5MCLzWKB9rncv2rRaC2yArhlhVWNTLdeT7AiEAvW8mf5+8RvPlix7v1lOqoQO5mpqciR2l4ug1QRlAVYI="}], "size": 63234}, "_hasShrinkwrap": false, "publish_time": 1722850469879, "_source_registry_name": "default"}, "6.18.1": {"name": "@codemirror/autocomplete", "version": "6.18.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-iWHdj/B1ethnHRTwZj+C1obmmuCzquH29EbcKr0qIjA9NfDeBDJ7vs+WOHsFeLeflE4o+dHfYndJloMKHUkWUA==", "shasum": "3bd8d62c9c9a14d0706ab0a8adac139eaf1a41f1", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.18.1.tgz", "fileCount": 9, "unpackedSize": 247484, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBf6ioxCsH+d64m/9VISzbMjabttdYznPDayUGHKLr/gIhAPCmMg6xQ+889Zpgiqj85NXG7hfhiD8KoDetMKaFGi+Z"}], "size": 63441}, "_hasShrinkwrap": false, "publish_time": 1726321080489, "_source_registry_name": "default"}, "6.18.2": {"name": "@codemirror/autocomplete", "version": "6.18.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wJGylKtMFR/Ds6Gh01+OovXE/pncPiKZNNBKuC39pKnH+XK5d9+WsNqcrdxPjFPFTigRBqse0rfxw9UxrfyhPg==", "shasum": "bf3f15f1bf0fdfa3b4fac560e419adae1ece8a94", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.18.2.tgz", "fileCount": 9, "unpackedSize": 248632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBY9FNh8vObpZgrVyj6ihBdMbyX2GDJJbZZLXYZFeygGAiA2RA7oYbtpE+5KRLIVUrjYca7KW5unfQkhBpmLU/nslg=="}], "size": 63611}, "_hasShrinkwrap": false, "publish_time": 1730302885976, "_source_registry_name": "default"}, "6.18.3": {"name": "@codemirror/autocomplete", "version": "6.18.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "peerDependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-1dNIOmiM0z4BIBwxmxEfA1yoxh1MF/6KPBbh20a5vphGV0ictKlgQsbJs6D6SkR6iJpGbpwRsa6PFMNlg9T9pQ==", "shasum": "f9ea79a2f369662516f71bc0b2f819454d3c8e00", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.18.3.tgz", "fileCount": 9, "unpackedSize": 248354, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFT2MK2UTCpI0rin7neg3NGfVOVkKGC2eqR6/2jHN9DJAiAHc+DSoMnKRCuaakCEABsEC/G7rYS4SbodL0dYRApXlA=="}], "size": 63658}, "_hasShrinkwrap": false, "publish_time": 1731481564781, "_source_registry_name": "default"}, "6.18.4": {"name": "@codemirror/autocomplete", "version": "6.18.4", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-sFAphGQIqyQZfP2ZBsSHV7xQvo9Py0rV0dW7W3IMRdS+zDuNb2l3no78CvUaWKGfzFjI4FTrLdUSj86IGb2hRA==", "shasum": "4394f55d6771727179f2e28a871ef46bbbeb11b1", "tarball": "https://registry.npmmirror.com/@codemirror/autocomplete/-/autocomplete-6.18.4.tgz", "fileCount": 9, "unpackedSize": 248492, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTf3QZb6q2MmKcgJ8n1nRxdWxxozaNbYtDrNkdl7TNkAiBGjm+A62aPbv2MyXQOiEnYMzjocA1pUKQnf6pUINOzXg=="}], "size": 63750}, "_hasShrinkwrap": false, "publish_time": 1734451975142, "_source_registry_name": "default"}}, "_source_registry_name": "default"}