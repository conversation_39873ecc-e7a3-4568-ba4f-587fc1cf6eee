{"dist-tags": {"latest": "5.0.3", "next": "5.0.0-rc.2"}, "modified": "2024-11-19T15:11:35.441Z", "name": "immutable", "versions": {"4.0.0": {"name": "immutable", "version": "4.0.0", "directories": {}, "dist": {"shasum": "b86f78de6adef3608395efb269a91462797e2c23", "size": 134898, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0.tgz", "integrity": "sha512-zIE9hX70qew5qTUjSS7wi1iwj/l7+m54KWU247nhM3v806UdGj1yDndXj+IOYxxtW9zyLI+xqFNZjTuDaLUqFw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.15": {"name": "immutable", "version": "4.0.0-rc.15", "directories": {}, "dist": {"shasum": "c30056f05eaaf5650fd15230586688fdd15c54bc", "size": 134910, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.15.tgz", "integrity": "sha512-v8+A3sNyaieoP9dHegl3tEYnIZa7vqNiSv0U6D7YddiZi34VjKy4GsIxrRHj2d8+CS3MeiVja5QyNe4JO/aEXA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.14": {"name": "immutable", "version": "4.0.0-rc.14", "directories": {}, "dist": {"shasum": "29ba96631ec10867d1348515ac4e6bdba462f071", "size": 163881, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.14.tgz", "integrity": "sha512-pfkvmRKJSoW7JFx0QeYlAmT+kNYvn5j0u7bnpNq4N2RCvHSTlLT208G8jgaquNe+Q8kCPHKOSpxJkyvLDpYq0w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.12": {"name": "immutable", "version": "4.0.0-rc.12", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "^2.26.3", "browserify": "16.2.2", "colors": "1.2.5", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.19.1", "eslint-config-airbnb": "16.1.0", "eslint-config-prettier": "2.9.0", "eslint-plugin-import": "2.12.0", "eslint-plugin-jsx-a11y": "6.0.3", "eslint-plugin-prettier": "2.6.2", "eslint-plugin-react": "7.8.2", "flow-bin": "0.85.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.1.0", "gulp-header": "2.0.5", "gulp-less": "3.5.0", "gulp-size": "3.0.0", "gulp-sourcemaps": "2.6.4", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "23.6.0", "marked": "0.3.19", "microtime": "2.1.8", "mkdirp": "0.5.1", "npm-run-all": "4.1.3", "prettier": "1.14.2", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.59.1", "rollup-plugin-buble": "0.19.2", "rollup-plugin-commonjs": "9.1.3", "rollup-plugin-json": "3.0.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.1", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "3.0.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.1", "vinyl-source-stream": "2.0.0"}, "directories": {}, "dist": {"shasum": "ca59a7e4c19ae8d9bf74a97bdf0f6e2f2a5d0217", "size": 168802, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.12.tgz", "integrity": "sha512-0M2XxkZLx/mi3t8NVwIm1g8nHoEmM9p9UBl/G9k4+hm0kBgOVdMV/B3CY5dQ8qG8qc80NN4gDV4HQv6FTJ5q7A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.11": {"name": "immutable", "version": "4.0.0-rc.11", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "2.24.4", "browserify": "16.2.2", "colors": "1.2.5", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.19.1", "eslint-config-airbnb": "16.1.0", "eslint-config-prettier": "2.9.0", "eslint-plugin-import": "2.12.0", "eslint-plugin-jsx-a11y": "6.0.3", "eslint-plugin-prettier": "2.6.2", "eslint-plugin-react": "7.8.2", "flow-bin": "0.81.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.1.0", "gulp-header": "2.0.5", "gulp-less": "3.5.0", "gulp-size": "3.0.0", "gulp-sourcemaps": "2.6.4", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "23.6.0", "marked": "0.3.19", "microtime": "2.1.8", "mkdirp": "0.5.1", "npm-run-all": "4.1.3", "prettier": "1.14.2", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.59.1", "rollup-plugin-buble": "0.19.2", "rollup-plugin-commonjs": "9.1.3", "rollup-plugin-json": "3.0.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.1", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "3.0.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.1", "vinyl-source-stream": "2.0.0"}, "directories": {}, "dist": {"shasum": "de1ef684501a4da5b535497da7d391bb486faad7", "size": 168848, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.11.tgz", "integrity": "sha512-7WJU1gJAkR/nxb30C4Jl/0N48mAJTZPh5HO1NQzhlMZgMmTOz0b1Ll1Ygh11MC8qmekTFa/hEkSAMl0ISW5tJA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.10": {"name": "immutable", "version": "4.0.0-rc.10", "dependencies": {}, "directories": {}, "dist": {"shasum": "0c97cd272bbae51861cb2311edbbe548b2fc4ef7", "size": 166952, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.10.tgz", "integrity": "sha512-tIdM/FgCQKvsOW29sorYe0VCW+MMyV7Yw/5Mx87GELY5vUiAeZSvl0HKZEBbhX0QnoGiFl8YMO8ZVHN3FXoDnA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.9": {"name": "immutable", "version": "4.0.0-rc.9", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "2.18.13", "browserify": "14.4.0", "colors": "1.1.2", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.8.0", "eslint-config-airbnb": "15.1.0", "eslint-config-prettier": "2.6.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-jsx-a11y": "5.1.1", "eslint-plugin-prettier": "2.3.1", "eslint-plugin-react": "7.4.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.1", "gulp-header": "1.8.9", "gulp-less": "3.3.2", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.6.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.1.1", "prettier": "1.7.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.50.0", "rollup-plugin-buble": "0.16.0", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-json": "2.3.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.0", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "1e6e0094e649013ec3742d2b5aeeca5eeda4f0bf", "size": 178403, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.9.tgz", "integrity": "sha512-uw4u9Jy3G2Y1qkIFtEGy9NgJxFJT1l3HKgeSFHfrvy91T8W54cJoQ+qK3fTwhil8XkEHuc2S+MI+fbD0vKObDA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.8": {"name": "immutable", "version": "4.0.0-rc.8", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "2.18.13", "browserify": "14.4.0", "colors": "1.1.2", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.8.0", "eslint-config-airbnb": "15.1.0", "eslint-config-prettier": "2.6.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-jsx-a11y": "5.1.1", "eslint-plugin-prettier": "2.3.1", "eslint-plugin-react": "7.4.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.1", "gulp-header": "1.8.9", "gulp-less": "3.3.2", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.6.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.1.1", "prettier": "1.7.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.50.0", "rollup-plugin-buble": "0.16.0", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-json": "2.3.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.0", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "ea20062c1f0dc0fb5cc1ac17808ec21935ea2e44", "size": 178282, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.8.tgz", "integrity": "sha512-EHUmY0f1GQ3rudivZp8zWSTGoRvVr+gfJwa6LnKeleUzD9I3J84pZQTSZt3HUe7wxzbJ9MU+yuJkh5HT38/Q4Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.7": {"name": "immutable", "version": "4.0.0-rc.7", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "2.18.13", "browserify": "14.4.0", "colors": "1.1.2", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.8.0", "eslint-config-airbnb": "15.1.0", "eslint-config-prettier": "2.6.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-jsx-a11y": "5.1.1", "eslint-plugin-prettier": "2.3.1", "eslint-plugin-react": "7.4.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.1", "gulp-header": "1.8.9", "gulp-less": "3.3.2", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.6.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.1.1", "prettier": "1.7.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.50.0", "rollup-plugin-buble": "0.16.0", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-json": "2.3.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.0", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "50b4ad60e02ac5c71ebb19ad72577b08ed4ab736", "size": 164770, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.7.tgz", "integrity": "sha512-mAgiQEgKLAPyeeWOQ0GeWYaAj3c/S2qHEjH6az6yQ5z5TWFyqRGyxCzGnQnwd08+aMm8BGo8K4H6n24e65lfQw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.6": {"name": "immutable", "version": "4.0.0-rc.6", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "2.18.13", "browserify": "14.4.0", "colors": "1.1.2", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.8.0", "eslint-config-airbnb": "15.1.0", "eslint-config-prettier": "2.6.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-jsx-a11y": "5.1.1", "eslint-plugin-prettier": "2.3.1", "eslint-plugin-react": "7.4.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.1", "gulp-header": "1.8.9", "gulp-less": "3.3.2", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.6.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.1.1", "prettier": "1.7.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.50.0", "rollup-plugin-buble": "0.16.0", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-json": "2.3.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.0", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "db1688749f2093fa3dc68dfd47ebaa4d81effd72", "size": 164758, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.6.tgz", "integrity": "sha512-oSrzrBiqpbVswyMj9qljjRuMFKIvcVAoo9V8PtvYVN4yMeFT/M/d4jRSnxSUrppH1pM16pIYdEj1FLY50FNngA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.5": {"name": "immutable", "version": "4.0.0-rc.5", "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "browser-sync": "2.18.13", "browserify": "14.4.0", "colors": "1.1.2", "del": "3.0.0", "dtslint": "0.1.2", "eslint": "4.8.0", "eslint-config-airbnb": "15.1.0", "eslint-config-prettier": "2.6.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-jsx-a11y": "5.1.1", "eslint-plugin-prettier": "2.3.1", "eslint-plugin-react": "7.4.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.1", "gulp-header": "1.8.9", "gulp-less": "3.3.2", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.6.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.1.1", "prettier": "1.7.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "0.13.3", "rimraf": "2.6.2", "rollup": "0.50.0", "rollup-plugin-buble": "0.16.0", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-json": "2.3.0", "rollup-plugin-strip-banner": "0.2.0", "run-sequence": "2.2.0", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "5.7.0", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "219d98db7a9a2a69769d48abedaaafba28561fe6", "size": 166600, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.5.tgz", "integrity": "sha512-/PNxdLmKBOOexBF5F9a34tk3nfEOL4QNVQU2/R+TJgIrq908kcYSwCB2W+UycVAbDu6LbpKEIEd+dWy5HYBcpQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.8.2": {"name": "immutable", "version": "3.8.2", "devDependencies": {"acorn": "0.11.x", "babel-eslint": "^4.1.8", "benchmark": "^1.0.0", "bluebird": "3.1.1", "browser-sync": "2.11.0", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.0", "es6-transpiler": "0.7.18", "eslint": "^1.10.3", "estraverse": "1.9.3", "express": "^4.13.4", "fbjs-scripts": "^0.5.0", "grunt": "0.4.5", "grunt-cli": "0.1.13", "grunt-contrib-clean": "0.7.0", "grunt-contrib-copy": "0.8.2", "grunt-contrib-jshint": "0.11.3", "grunt-release": "0.13.0", "gulp": "3.9.0", "gulp-concat": "2.6.0", "gulp-filter": "3.0.1", "gulp-header": "1.7.1", "gulp-jest": "^0.2.1", "gulp-jshint": "^1.8.4", "gulp-less": "3.0.5", "gulp-size": "2.0.0", "gulp-sourcemaps": "1.6.0", "gulp-uglify": "1.5.1", "gulp-util": "3.0.7", "harmonize": "1.4.4", "jasmine-check": "^0.1.2", "jest-cli": "^0.5.10", "jshint-stylish": "^0.4.0", "magic-string": "0.10.2", "marked": "0.3.5", "microtime": "^2.0.0", "node-jsx": "^0.12.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rollup": "0.24.0", "run-sequence": "1.1.5", "through2": "2.0.0", "typescript": "1.7.5", "uglify-js": "2.6.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "c2439951455bb39913daf281376f1530e104adf3", "size": 105602, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.8.2.tgz", "integrity": "sha512-15gZoQ38eYjEjxkorfbcgBKBL6R7T459OuK+CpcWt7O3KF4uPCx2tD0uFETlUDIyo+1789crbMhTvQBSR5yBMg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.4": {"name": "immutable", "version": "4.0.0-rc.4", "devDependencies": {"benchmark": "2.1.3", "browser-sync": "2.18.8", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.2", "dtslint": "0.1.2", "eslint": "3.17.1", "eslint-config-airbnb": "14.1.0", "eslint-config-prettier": "1.5.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "4.0.0", "eslint-plugin-prettier": "2.0.1", "eslint-plugin-react": "6.10.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.0", "gulp-header": "1.8.8", "gulp-less": "3.3.0", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.4.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.0.2", "prettier": "1.7.2", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rimraf": "2.6.1", "rollup": "0.41.5", "rollup-plugin-buble": "0.15.0", "rollup-plugin-commonjs": "7.1.0", "rollup-plugin-strip-banner": "0.1.0", "run-sequence": "1.2.2", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "4.5.1", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "eff99bcea8bd68ce6c7725885e44011225f14fec", "size": 165172, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.4.tgz", "integrity": "sha512-1oi1vdL+bYEsswc5lnBwPJs+W9KiAGP1WzqqyH/Oamm4r6SSi8K67ZduPbwuE+OkijNbJJ9oTklwjEppEdGTiw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.3": {"name": "immutable", "version": "4.0.0-rc.3", "devDependencies": {"benchmark": "2.1.3", "browser-sync": "2.18.8", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.2", "dtslint": "0.1.2", "eslint": "3.17.1", "eslint-config-airbnb": "14.1.0", "eslint-config-prettier": "1.5.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "4.0.0", "eslint-plugin-prettier": "2.0.1", "eslint-plugin-react": "6.10.0", "flow-bin": "0.56.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.0", "gulp-header": "1.8.8", "gulp-less": "3.3.0", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.4.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "21.2.1", "marked": "0.3.6", "microtime": "2.1.6", "mkdirp": "0.5.1", "npm-run-all": "4.0.2", "prettier": "1.7.2", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rimraf": "2.6.1", "rollup": "0.41.5", "rollup-plugin-buble": "0.15.0", "rollup-plugin-commonjs": "7.1.0", "rollup-plugin-strip-banner": "0.1.0", "run-sequence": "1.2.2", "through2": "2.0.3", "transducers-js": "^0.4.174", "tslint": "4.5.1", "typescript": "2.5.3", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "ba2c47077aff68112e7482811225ba8d694593c5", "size": 165054, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.3.tgz", "integrity": "sha512-gMu/tYbNkD2iA7gdXfxmGTbt9QYU2Qwhepu0yTHBljBU4xxOc8yVTcBRpTq0OLuq+JY4+ESwnI1HD/b9Yjy5Mg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.2": {"name": "immutable", "version": "4.0.0-rc.2", "devDependencies": {"benchmark": "2.1.3", "browser-sync": "2.18.8", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.2", "eslint": "3.17.1", "eslint-config-airbnb": "14.1.0", "eslint-config-prettier": "1.5.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "4.0.0", "eslint-plugin-prettier": "2.0.1", "eslint-plugin-react": "6.10.0", "flow-bin": "0.41.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.0", "gulp-header": "1.8.8", "gulp-less": "3.3.0", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.4.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "19.0.2", "marked": "0.3.6", "microtime": "^2.1.2", "mkdirp": "0.5.1", "npm-run-all": "4.0.2", "prettier": "0.22.0", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rimraf": "2.6.1", "rollup": "0.41.5", "rollup-plugin-buble": "0.15.0", "rollup-plugin-commonjs": "7.1.0", "rollup-plugin-strip-banner": "0.1.0", "run-sequence": "1.2.2", "through2": "2.0.3", "tslint": "4.5.1", "typescript": "2.2.1", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "fdd0948aae728fda2306a02f72bb73e1773432d1", "size": 127849, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.2.tgz", "integrity": "sha512-R0u2hXIMFhiTF2D84LhzQU4S4Xeo7GKjIk5OkoeKuOgzmiI/mAlPTqjEc7zZPK/Pq7Uz63oApKa2L/JNzlYlAg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.0.0-rc.1": {"name": "immutable", "version": "4.0.0-rc.1", "devDependencies": {"benchmark": "2.1.3", "browser-sync": "2.18.8", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.2", "eslint": "3.17.1", "eslint-config-airbnb": "14.1.0", "eslint-config-prettier": "1.5.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "4.0.0", "eslint-plugin-prettier": "2.0.1", "eslint-plugin-react": "6.10.0", "flow-bin": "0.41.0", "gulp": "3.9.1", "gulp-concat": "2.6.1", "gulp-filter": "5.0.0", "gulp-header": "1.8.8", "gulp-less": "3.3.0", "gulp-size": "2.1.0", "gulp-sourcemaps": "2.4.1", "gulp-uglify": "2.1.0", "gulp-util": "3.0.8", "jasmine-check": "0.1.5", "jest": "19.0.2", "marked": "0.3.6", "microtime": "^2.1.2", "mkdirp": "0.5.1", "npm-run-all": "4.0.2", "prettier": "0.22.0", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rimraf": "2.6.1", "rollup": "0.41.5", "rollup-plugin-buble": "0.15.0", "rollup-plugin-commonjs": "7.1.0", "rollup-plugin-strip-banner": "0.1.0", "run-sequence": "1.2.2", "through2": "2.0.3", "tslint": "4.5.1", "typescript": "2.2.1", "uglify-js": "2.8.11", "uglify-save-license": "0.4.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "75c5d728ccf1fb22d0a459add6748448ae270acc", "size": 125678, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0-rc.1.tgz", "integrity": "sha512-lZCwlSBwXeer82P0O/wb7Pn+AQMmyTXkuKk72nSUZT3AgvxvFqECwx1/htkJQg4iHxbm3VvI3hniHIqcdjXOnQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.8.1": {"name": "immutable", "version": "3.8.1", "devDependencies": {"acorn": "0.11.x", "babel-eslint": "^4.1.8", "benchmark": "^1.0.0", "bluebird": "3.1.1", "browser-sync": "2.11.0", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.0", "es6-transpiler": "0.7.18", "eslint": "^1.10.3", "estraverse": "1.9.3", "express": "^4.13.4", "fbjs-scripts": "^0.5.0", "grunt": "0.4.5", "grunt-cli": "0.1.13", "grunt-contrib-clean": "0.7.0", "grunt-contrib-copy": "0.8.2", "grunt-contrib-jshint": "0.11.3", "grunt-release": "0.13.0", "gulp": "3.9.0", "gulp-concat": "2.6.0", "gulp-filter": "3.0.1", "gulp-header": "1.7.1", "gulp-jest": "^0.2.1", "gulp-jshint": "^1.8.4", "gulp-less": "3.0.5", "gulp-size": "2.0.0", "gulp-sourcemaps": "1.6.0", "gulp-uglify": "1.5.1", "gulp-util": "3.0.7", "harmonize": "1.4.4", "jasmine-check": "^0.1.2", "jest-cli": "^0.5.10", "jshint-stylish": "^0.4.0", "magic-string": "0.10.2", "marked": "0.3.5", "microtime": "^2.0.0", "node-jsx": "^0.12.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rollup": "0.24.0", "run-sequence": "1.1.5", "through2": "2.0.0", "typescript": "1.7.5", "uglify-js": "2.6.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "200807f11ab0f72710ea485542de088075f68cd2", "size": 107231, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.8.1.tgz", "integrity": "sha512-0R2q5f83L0h+zizu3lAA3ZR/mzEl04U1jVVXIqf2rQbZs9eX5YGtx1EFQuuJJHzVXH10ur6hGKehR8yBOQmZlQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.8.0": {"name": "immutable", "version": "3.8.0", "devDependencies": {"acorn": "0.11.x", "babel-eslint": "^4.1.8", "benchmark": "^1.0.0", "bluebird": "3.1.1", "browser-sync": "2.11.0", "browserify": "^5.11.2", "colors": "1.1.2", "del": "2.2.0", "es6-transpiler": "0.7.18", "eslint": "^1.10.3", "estraverse": "1.9.3", "express": "^4.13.4", "fbjs-scripts": "^0.5.0", "grunt": "0.4.5", "grunt-cli": "0.1.13", "grunt-contrib-clean": "0.7.0", "grunt-contrib-copy": "0.8.2", "grunt-contrib-jshint": "0.11.3", "grunt-jest": "^0.1.0", "grunt-release": "0.13.0", "gulp": "3.9.0", "gulp-concat": "2.6.0", "gulp-filter": "3.0.1", "gulp-header": "1.7.1", "gulp-jest": "^0.2.1", "gulp-jshint": "^1.8.4", "gulp-less": "3.0.5", "gulp-size": "2.0.0", "gulp-sourcemaps": "1.6.0", "gulp-uglify": "1.5.1", "gulp-util": "3.0.7", "harmonize": "1.4.4", "jasmine-check": "^0.1.2", "jest-cli": "^0.4.19", "jshint-stylish": "^0.4.0", "magic-string": "0.10.2", "marked": "0.3.5", "microtime": "^1.2.0", "node-jsx": "^0.12.4", "react": "^0.12.0", "react-router": "^0.11.2", "react-tools": "^0.12.0", "rollup": "0.24.0", "run-sequence": "1.1.5", "through2": "2.0.0", "typescript": "1.7.5", "uglify-js": "2.6.1", "vinyl-buffer": "1.0.0", "vinyl-source-stream": "1.1.0"}, "directories": {}, "dist": {"shasum": "5175514f70cb2a8218b58183d33747134a81c697", "size": 106941, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.8.0.tgz", "integrity": "sha512-/ahL1tzqBxgbNNBe7RuLcCxruwkbSBGoBFtT4lp2SrVqZPTX9ppGlLMOEuacwNYNRMen4VdmruLX9S8umz2Tyg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.6": {"name": "immutable", "version": "3.7.6", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "rollup": "^0.19.2", "typescript": "~1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "13b4d3cb12befa15482a26fe1b2ebae640071e4b", "size": 80317, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.6.tgz", "integrity": "sha512-AizQPcaofEtO11RZhPPHBOJRdo/20MKQF9mBLnVkBoyHi1/zXK8fzVdnEpSV9gxqtnh6Qomfp3F0xT5qP/vThw=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.5": {"name": "immutable", "version": "3.7.5", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "typescript": "~1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "557d03e5c2adb979f4cdee49454434c09c3610e4", "size": 79902, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.5.tgz", "integrity": "sha512-NsjjKWNQifRj2D+kNBADtdxsWdA20oy1RwoWqa2YdsBr3sQnjOn+2kgS/cRiZj9l7lz8cObNpTCUAQ1dZYilBA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.4": {"name": "immutable", "version": "3.7.4", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "typescript": "~1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "40ab3ec87b4ac95e0331a6d359a4b1fa73b2ddf3", "size": 78600, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.4.tgz", "integrity": "sha512-ZiXl/ogqSHw1aqOSgjVHwKVFH72UGh6alU6123LrKfTzjgHlnjOK99gS+AR6KYbO3RSSPgGwIGdFXvhBL+1xOA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.3": {"name": "immutable", "version": "3.7.3", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "typescript": "~1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "14baab98ffe86f83458c3cc08c06cbe318c1ff0e", "size": 78227, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.3.tgz", "integrity": "sha512-MnP9lTXDeqHYgk/sN65MoaNwYle0Hdz94TcADEW3gtyzqYNvcwjF9ugShlPYrh79W40QkvKRYbBOOVQPqGPH6w=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.2": {"name": "immutable", "version": "3.7.2", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "typescript": "~1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "21703ccb6c20bfa08e54aff7f65f86e32cab7f8f", "size": 77491, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.2.tgz", "integrity": "sha512-RLM4CIFiA5HydRa8pDq8awkDQxnvBoj+wDTbWn28U1fqZOcZOSIXW5hnJLO+BrKeEfTAvzNO+nKl1zbV+Cb7wQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.1": {"name": "immutable", "version": "3.7.1", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "typescript": "^1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "04118f278c2297b7a2ee96d8da067bb5dab8ed7f", "size": 77279, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.1.tgz", "integrity": "sha512-rfFGjRp46moEZKH2ZhYhCApwMpwEZlxJSXoukf0ovrsLv4g5p5TCwBaxVOVsezVvlcYiPtfUD8kq3jKaSeXWeg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.7.0": {"name": "immutable", "version": "3.7.0", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "typescript": "^1.4.1", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "3b7d249ee5d4e4d0bc1ccb6bc7ec9dd840bc6c80", "size": 77207, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.7.0.tgz", "integrity": "sha512-j2xzK5C6y1iRa2VwCVyJ9a41zcFxc7Iu3n1LZP8hXZ0YnaneXEQC9GwLhtut00w36E3Nmz/1fXkDpxi70w1l5Q=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.6.4": {"name": "immutable", "version": "3.6.4", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "8e640ec5e015acd30b65aff118f274ff29a28e6b", "size": 76292, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.6.4.tgz", "integrity": "sha512-SGqq/iR3lgzxq6/83hj0WRay93GeJnw8X1j+q6pzYh/8f/jBZ91gqt5YWRryZAmyP4veLCYeHNeJhM09OI7uew=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.6.3": {"name": "immutable", "version": "3.6.3", "devDependencies": {"acorn": "^0.11.0", "benchmark": "^1.0.0", "bluebird": "^2.7.1", "colors": "^1.0.3", "es6-transpiler": "^0.7.18", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "microtime": "^1.2.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "a11473cabfa774adeaef16837b17ae6fa3a32b87", "size": 14987, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.6.3.tgz", "integrity": "sha512-+Gpx1IAKKjnYdq4gHNdnKw2duhUXciKCUBpcisXW254lg/d9pYITSnSe9ouC+4Kej5IDt0ZDTo7UK/w5din0Lg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.6.2": {"name": "immutable", "version": "3.6.2", "devDependencies": {"acorn": "^0.11.0", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "uglify-js": "^2.4.15", "es6-transpiler": "^0.7.18"}, "directories": {}, "dist": {"shasum": "903298d69cf2c83f975b417dad3a2a34967604ae", "size": 75948, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.6.2.tgz", "integrity": "sha512-QGpVZYBcVF3zn6S+X+5WgvDgxWxuVE0PjVCTVQrDj8q0qj962Qd+osxz19YWdjBYPbTd4IgQ82pd4xzwy0H4FA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.6.1": {"name": "immutable", "version": "3.6.1", "devDependencies": {"acorn": "^0.11.0", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "uglify-js": "^2.4.15", "es6-transpiler": "^0.7.18"}, "directories": {}, "dist": {"shasum": "e62090a99092bcc66e3e507e74ba3ccab0c0c159", "size": 75891, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.6.1.tgz", "integrity": "sha512-Rwo1hzM/TnRurgQDu+qNZQjOXRC23YtOkfvVU+sorG0OBzyqzWROe4fAFZiN95j26mEGJvjOA9ykKSw3qPTYgg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.6.0": {"name": "immutable", "version": "3.6.0", "devDependencies": {"acorn": "^0.11.0", "esperanto": "^0.6.0", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "uglify-js": "^2.4.15", "es6-transpiler": "^0.7.18"}, "directories": {}, "dist": {"shasum": "1c03a1454abe51261ad6e04f958047723a7fcf75", "size": 75790, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.6.0.tgz", "integrity": "sha512-LTT1sJum5Aoqw2uskaKxiR5NYSpe/S1Ma2a+ypF9mr5Y93kiT5kXs4FCpeFO4Jj/n2ntIYt8P1tVHAW3GvCk1w=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.5.0": {"name": "immutable", "version": "3.5.0", "devDependencies": {"acorn": "^0.11.0", "esperanto": "^0.5.8", "estraverse": "^1.9.1", "grunt": "^0.4.5", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-jest": "^0.1.0", "grunt-release": "^0.7.0", "jasmine-check": "^0.1.2", "magic-string": "^0.2.6", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "uglify-js": "^2.4.15", "es6-transpiler": "^0.7.18"}, "directories": {}, "dist": {"shasum": "09d665a2ac6d090bf9f32e67229ddba245235d0a", "size": 74750, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.5.0.tgz", "integrity": "sha512-Uw20do6mggZ2ukl2AUg113702NS0bSlnZj/Kbqf6Ue6RKeR3u6xbg1J3E+abhvsNAx4qYYoNwGIN2LsDkxQ+Hg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.4.1": {"name": "immutable", "version": "3.4.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "ecc32cd72e9f7c7639143bbbc793ed9c245afa82", "size": 69010, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.4.1.tgz", "integrity": "sha512-YI24s64qUo0YX8DeHTyM0VFjKyfwANTIrWbj0HraCFbsxy4SFo96f+rfKyTUASs/tCypW0VBuDRqtRfSavGZ0Q=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.4.0": {"name": "immutable", "version": "3.4.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "a77ffb72a89135fb0163431688a9f622f2533e25", "size": 68790, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.4.0.tgz", "integrity": "sha512-PwBvNS8XKMycy15tG22kt1MrrGFt7qVX2kTTARIJ8+wI/RtTxo5hOe2C5Cmk0g4It9Ifczk1I5xbS8ok1rIp0Q=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.3.0": {"name": "immutable", "version": "3.3.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "c3d584dd353750177661174e796e4fa5f584ce8a", "size": 67436, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.3.0.tgz", "integrity": "sha512-dNToJQjqzo7F0VjtkF1P9453jxgXAC6an0CmXUHvZlFGSNefLdC+CDpyOKpZjv4VLcwEtvvonc7nol8VIqxdpw=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.2.1": {"name": "immutable", "version": "3.2.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "85b4aecc000dafacba6497a64c920738a6dfab64", "size": 65688, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.2.1.tgz", "integrity": "sha512-CeYC40KT0wVv87eNzBOUi/UCUZ2MVft31Oi5PXJfBFIG36klmNMatuaJ280X1lPeQq5ryexdpD8utdo5FkEmyA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.2.0": {"name": "immutable", "version": "3.2.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "a80a6b79ddcbc9430e69364a1c332979ab33dd45", "size": 65698, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.2.0.tgz", "integrity": "sha512-fVvACLA3yWbAV0p9SgI3VWHMoEU9F3t3g2r0yyhpib9D60pl+G6ukwsm2ARN5Iwv7uMsSQjdW/MU0elsgzV+uA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.1.0": {"name": "immutable", "version": "3.1.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "14c3f8e8a37d42cb0290a71840e735526cbfb9c0", "size": 65397, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.1.0.tgz", "integrity": "sha512-pX2yCc/IPJBowKi2bJGUbks4c5yDCLsPOOoXOVme119krkz55iTF72m+/Kdol4WRnoSCcowGSzaigeGlQenRbg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.0.3": {"name": "immutable", "version": "3.0.3", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "5ffb1a7aa69f2f229f352ebc3bb7299ebe6d673f", "size": 63764, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.0.3.tgz", "integrity": "sha512-XzGR6ZgjuEI2Xp7fc9rMrk705DnLoeJUYTvdSFzRyJvbHLpYhobwLexCZ2bXTU+EWDW5+ysBKP4STk4ofa0gNA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.0.2": {"name": "immutable", "version": "3.0.2", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "912a831a7ef98945cceaacd7540abcba32621f93", "size": 63413, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.0.2.tgz", "integrity": "sha512-+YyG7nql2Ql+eWdzSaxZMXSpSD3T5S4wACtUdENhOV76pux24WEFQQkc1M1buOge0HyQCZ5NDrvyzUaBxYW+Wg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.0.1": {"name": "immutable", "version": "3.0.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "dc61e3a1a636e9e32177e101c6a7c32e07ca08b3", "size": 63194, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.0.1.tgz", "integrity": "sha512-vRgrzWSAa2/MDAcy7F4U9NVjvlcBmJYXFMUcK7+EqJAa/l2a1/Uaw962hnAPyWm8KQDc2EE8qm2e/tT3vcxqEg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "3.0.0": {"name": "immutable", "version": "3.0.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "9a6766a0e44c5a23d86e487669893b0abb0a7995", "size": 63063, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-3.0.0.tgz", "integrity": "sha512-llBd/4qU107gXTaCVsTiEPiqCUIH1MxpNCSFL5m7ne1nZuNSpII7sqBu7MPPEuiOIIIzWZpn/fDLV2lL8rofPA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.6.2": {"name": "immutable", "version": "2.6.2", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "ea43141a64efed638c5b816fc7147ad8445ebd33", "size": 57315, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.6.2.tgz", "integrity": "sha512-7xnAW8Fm+X4D+Q2OgIz4VXBslTJLmgapdb5Jz37+A6arS39LUk5jWbOJz2pAp2BJTdsKlt3Lxo6a7I/ItonL6A=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.6.1": {"name": "immutable", "version": "2.6.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "94f473e8951e28cf2350a994667e26feeb94a8fd", "size": 57349, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.6.1.tgz", "integrity": "sha512-j6R9cnNwKMKPmlx976qOmv9IFQQDqIu3txHVXJBa8pDlsQJc3w9caPJWrqY5QqavrH5PDFW13pI+kOiIPWSiSQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.6.0": {"name": "immutable", "version": "2.6.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "2f8e1e175446bb40509bc50a59ec1752da5ab14e", "size": 57343, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.6.0.tgz", "integrity": "sha512-85D9V4MH8CHYkAAImT0yJBPpA45jqqTZ0ri7DKfMqj+IrtODuqq4paoqkV1EY7lnhgyOoM6m/4JhAfxkS9fVvw=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.5.1": {"name": "immutable", "version": "2.5.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "9b7bebeac248f165d466991422631fe7ccc81c97", "size": 56490, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.5.1.tgz", "integrity": "sha512-V0qCaHIyxD99XSSUOl9W7v4PGXFS8T5438CYbmZ7KtlioU8GEp/TLFVsqrwSOS9MkppjEE11DdMHxgkpSL9oZw=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.5.0": {"name": "immutable", "version": "2.5.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "90d4d56a4650ba09e80ef3218941cb89c9add17a", "size": 56268, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.5.0.tgz", "integrity": "sha512-kmk9RnxT5Rt/kFbh4zo9qe9Z9kSf32hV0ssi2TCRf23yWqOzEvwbYb+euZoAJtLBUmIGHbL9JwYlAVhescUZ1w=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.3.2": {"name": "immutable", "version": "2.3.2", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "2bbfecb2d283bfd7159cff037fd3ff500a7cc91a", "size": 55517, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.3.2.tgz", "integrity": "sha512-Aep8EvxZQZuFwFefCnevAx7VWqslLEQxiH+qelFi/fB8ZdIgQAOmEjpMHJ46s9cLynKZF5JxnndhTVMqJ4pIVA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.3.1": {"name": "immutable", "version": "2.3.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "371bdd37d53b1d29a6180e521a164558df2782d0", "size": 55493, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.3.1.tgz", "integrity": "sha512-S5rouBZIaTzm74bgrQK79O4CPulMMIVC7J/adwZzhMuOUyA7S/Vj5l4amOxJnEOrxlViCIDFNW+3Oxp5n7p/cw=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.3.0": {"name": "immutable", "version": "2.3.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "2737e0ae520343cbb89bc98a7d76597aff1dfb67", "size": 54021, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.3.0.tgz", "integrity": "sha512-dBRWgDOiDJubryim1zOQu0i7pMNM7JhENDnRGvj0V1m45OS95sEE75+UWiYorZeq34NhdZUufO/WdG/VvRhQNA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.2.3": {"name": "immutable", "version": "2.2.3", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "0e414e6b20f155acc1a6412997066f54509b408e", "size": 51879, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.2.3.tgz", "integrity": "sha512-YrrwWs2ZOnbY0z6zAswebbdl1Fr1ZgYdINrn2lnN6Xs6TYxzuDTyYkZJ2MIIWqSiWh0rHI/14skhdWIiOnAROQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.2.2": {"name": "immutable", "version": "2.2.2", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "f56ae9a44da1ca11420f0dd09095bbbd836c04be", "size": 51846, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.2.2.tgz", "integrity": "sha512-C6TVxM6CoIwXW+P0qGbC3XOBOKVVMXumaABy3IWT7sk/bdum5BrE7BT/XxGZGdrMXsLQlzTUU0wjf7KZZgcyvg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.2.1": {"name": "immutable", "version": "2.2.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "d3a1d79a7970b9bfcbed9bf045c1d66176b57f8c", "size": 50395, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.2.1.tgz", "integrity": "sha512-sO1ml5fMu0pWDan2OMxgmRd+bWne+ok9X7xhdcsD6MnrffPhap3fNq416SE4UVFCZi5MekYqdlHpIIvXqXEFEQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.1.0": {"name": "immutable", "version": "2.1.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "0d9ef6b73493c16e6dba837ba385b359e37630da", "size": 51528, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.1.0.tgz", "integrity": "sha512-XGKHfF+hEY+l44e1X7ZPCfaHMG7oNFK8JH4S7S0PjmOyyyX+85CjJrGjtDgT2FtXm3qMT0r9/cYqWI8VMdNklA=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.17": {"name": "immutable", "version": "2.0.17", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "666b51a106f44c5a857a8f315813f1df6671f030", "size": 51474, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.17.tgz", "integrity": "sha512-AkILQ6z0Kpi5eMpk44OagAgJ4oChLQoCCs+xaCl0JMQ5nAs5Ed1WBkmVhrKqsnVOaavYRXG69YWF2Xq7yi7YGQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.16": {"name": "immutable", "version": "2.0.16", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "d5943643a087faa80d89cc9d8f7aeb305322332e", "size": 50881, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.16.tgz", "integrity": "sha512-FCyY8tuH3Kyqz2cyPFG7PESASsjvzbe2QDTWSCjb+Fa8FzuvbufDTwT2voshfviejLEC89Ky6TavXJKInnNxdQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.15": {"name": "immutable", "version": "2.0.15", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "a678668a5c690a6835f4c79c9c9fdeb04969779b", "size": 48471, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.15.tgz", "integrity": "sha512-lZT84k5/lb9Nr1R1FpBF71Kek357iL0VwyuKUAtA1DHyKnB9hkHgjJ/rofdolPcvikvMbhR496TIEAgGohfOew=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.14": {"name": "immutable", "version": "2.0.14", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "ee075315223c7a444863d2a78ab86c7391aae9ea", "size": 47956, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.14.tgz", "integrity": "sha512-hF5gtNrnYBjhZg/1zyoNav+MD7R03WhsGzOWSvU5Dg7ATOkzXzSv1h9qEoSch/SrDGp4uDPtMyc/pz9TOHmU6Q=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.13": {"name": "immutable", "version": "2.0.13", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "892fffb8a326baa8220d281f8a30644256fd8378", "size": 47998, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.13.tgz", "integrity": "sha512-bTsLjvfCcScO7iPbg87JJKV/3cLfdJ5M6wJfSJR0T0tCZRDGr544iHpIYnKIQSuL5BOuEnaMgI1NufNcc0JzrQ=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.12": {"name": "immutable", "version": "2.0.12", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "3b36ff5fc9fa11e49bce6df38d69b7267f8168b7", "size": 47964, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.12.tgz", "integrity": "sha512-j<PERSON>nieJd4nuzxJ6f3VR14XcTVb8lsATDaM4YeOKwEAVUii9ntnfKbMoNQIng301w0d0Bie2q43oq1JEjapSWvg=="}, "engines": {"node": ">=0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.11": {"name": "immutable", "version": "2.0.11", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "6435420ea32731bd00422ad0453474932c9bf1b0", "size": 46462, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.11.tgz", "integrity": "sha512-goC1uxK9glk4fQqjQGS8bP+zrDN/VL2c23+ALbxVMHKmompn4X/5UeeS4c3dmCHG/9BAc279pR+CxU7trgtTXg=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.10": {"name": "immutable", "version": "2.0.10", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15", "jasmine-check": "^0.1.2"}, "directories": {}, "dist": {"shasum": "c98ccfc3074a2742cf956739ef14e7ac56656365", "size": 45633, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.10.tgz", "integrity": "sha512-9I61eeuoXUMJRVJrOoegb84G8qFsUfsGCS6Qu3vRS5M2QDDCCvoIqrFdYszD3rPlLX6UFgembUVQMDSjxEjSLQ=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.9": {"name": "immutable", "version": "2.0.9", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "279803d586cdbcb54c3399eed5f06a97997f729c", "size": 43858, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.9.tgz", "integrity": "sha512-3tkslItDb2t/RhsmscSVDLnnFZcpFClrt8QsjWgk2KZEqqJxxNGH3jM9pJPLqeir/7s6OZ+DaOvhsPiOHaxGpw=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.8": {"name": "immutable", "version": "2.0.8", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "66abab0b33bb95b62d83beddbc63ed8e26c29023", "size": 43865, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.8.tgz", "integrity": "sha512-pLTQKfcDT9+QEOt292YzRf90aCzlEkVtf1gWg05wiImx823S2MAyE6DxGi0nFHcWaVXURH2p8kQWyKMj4iV+Lg=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.7": {"name": "immutable", "version": "2.0.7", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "9584f8fd1447540d44dd1e9b08ce0dd0a59b7ebe", "size": 43838, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.7.tgz", "integrity": "sha512-2fYPGJ+aJhuOIBD/8EOe1qgmm1495BJjx2fAuoVhoAF9xdsCfULfBbWoEhwPAtIGEApEROwxUelZ8Mh3yRFkqQ=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.6": {"name": "immutable", "version": "2.0.6", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "c0eb8e818f2ef72b4d9de926439de9482d26d9e6", "size": 43857, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.6.tgz", "integrity": "sha512-+OqFLD/gzFFS9dnumE9RWoIavHR8+azK/IpErMJa4f7mCktHjuY0GeqBydgT6Fz4E8IYT55p6WYXtnDrCvoKow=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.5": {"name": "immutable", "version": "2.0.5", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "react-tools": "^0.11.1", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0", "traceur": "0.0.55", "smash": "0.0.12", "uglify-js": "^2.4.15"}, "directories": {}, "dist": {"shasum": "becc5367e8dae0497e679537e96b151c75de6ed1", "size": 43407, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.5.tgz", "integrity": "sha512-aN1LPxzu07l9kNHs6u0dIZOoohUdF4vT2pu1366o5lDNoETcyf5O/UpPEWTt0iwyaILpvGFbBMwfnqHe/K4Cug=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.4": {"name": "immutable", "version": "2.0.4", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "grunt-react": "^0.8.2", "react-tools": "~0.10.0", "ts-compiler": "^2.0.0", "grunt-release": "^0.7.0"}, "directories": {}, "dist": {"shasum": "5002853cc5b0c5be65c011fe552828412da91091", "size": 36402, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.4.tgz", "integrity": "sha512-ASbHrKhEM+hc1mR7NXPflOXcsF0NVDp5gL/qtoAZz/uQXPjgZf23DQpURrxB1OEQn8DoOssomtapg/8MxpA8mQ=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.3": {"name": "immutable", "version": "2.0.3", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "grunt-react": "^0.8.2", "react-tools": "~0.10.0", "ts-compiler": "^2.0.0"}, "directories": {}, "dist": {"shasum": "e15681264e2daef21039d9977d086e294752e7a1", "size": 36083, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.3.tgz", "integrity": "sha512-axJryPwPGprtirqEBR6qgpwPiEeIWF2QlQZi7MFTVOrfceSNSdq279I2+rLz2Cp3fM6A75f8nv1817uk+xSS5g=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.2": {"name": "immutable", "version": "2.0.2", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "grunt-react": "^0.8.2", "react-tools": "~0.10.0", "ts-compiler": "^2.0.0"}, "directories": {}, "dist": {"shasum": "0dd6ab32d5565431e9c94702183f86c9fb78db96", "size": 35652, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.2.tgz", "integrity": "sha512-sQ6RNTDrhEm8ix3LYtZWaexmqQF7FDnvXBEyAsMAnmLMUgjvknFpz7lhLFBsds50GxU6DwT+2j363a1VnACJSA=="}, "engines": {"node": "^0.8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.1": {"name": "immutable", "version": "2.0.1", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "grunt-react": "^0.8.2", "react-tools": "~0.10.0", "ts-compiler": "^2.0.0"}, "directories": {}, "dist": {"shasum": "a2f90cda2e1552c6e2bdf586294c8aa96275650f", "size": 35595, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.1.tgz", "integrity": "sha512-7vPt5UANmu/gPbeIG6hXU4su4ACftrvhCGD6H2QPEZTyT2aUJ9unuRt8NKwbRWuW/IxF8Fz8kR0cfSsJpjUxog=="}, "engines": {"node": "0.8.x || 0.10.x"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "2.0.0": {"name": "immutable", "version": "2.0.0", "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-clean": "^0.5.0", "grunt-jest": "^0.1.0", "grunt-react": "^0.8.2", "react-tools": "~0.10.0", "ts-compiler": "^2.0.0"}, "directories": {}, "dist": {"shasum": "90b0c357e59ae07dc1f19fd347606a3d6a41584f", "size": 33503, "noattachment": false, "tarball": "https://registry.npmmirror.com/immutable/-/immutable-2.0.0.tgz", "integrity": "sha512-t4S6n/Dng8is3NqiRuuK+iOjFK2+rNXeDWGAg0jDGajfZXxJ/X1WglTNCI0PzyvXERrhZyCzLZehLrBKp9WzPQ=="}, "engines": {"node": "0.8.x || 0.10.x"}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "1.4.1": {"name": "immutable", "version": "1.4.1", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.4.1"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1", "browserify": "2.17.2", "uglify-js": "2.3.6"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.4.1.tgz", "shasum": "617aa350483658f9c7e02d395021dbe5447001c7", "size": 11237, "noattachment": false, "integrity": "sha512-jl0f+kPkwfc/piACzSqlH7purruk+JbJtnwQQzjNihp6UAzKzLD/d7wWg43xrJ4QIWZsoVdp4wb+tWyKLLkzIQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.5.0": {"name": "immutable", "version": "1.5.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.4.1"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1", "browserify": "2.17.2", "uglify-js": "2.3.6"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.5.0.tgz", "shasum": "88c71b78c05ae01ae962e66c8695b49598dcf7ec", "size": 11262, "noattachment": false, "integrity": "sha512-yMGOfRgVnIi6cA0NrCSW2dsOAvNuJbcOxpRafqK+TXaJK84LJ/YZ629+ujJEeyn7ocpmy/FJHH03R4828UneDQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.0": {"name": "immutable", "version": "1.4.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.4.1"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1", "browserify": "2.17.2", "uglify-js": "2.3.6"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.4.0.tgz", "shasum": "5a96545c4eff87dd174b90375599790d363014be", "size": 10558, "noattachment": false, "integrity": "sha512-Mov9Zuo6mtbLJ6MNl1jEBsqH8AKfux+Ft5lw6hOPfnIVfgFKPLcK9V4e7rycsnWmj7/TPooTXSQ+J8qlwWo2JQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.3.0": {"name": "immutable", "version": "1.3.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.4.1"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.3.0.tgz", "shasum": "9aa6a35588e992a5106bbc9b9ff02e9dec71760e", "size": 7336, "noattachment": false, "integrity": "sha512-LtY+iQRdNz0O9EWirQTBkgVFPcwicw1A5Cny2UJMpQYIJ6OL7iEkSkieCw6o/c/F9TlSz/Ui3tggvixM3/otqA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.2.0": {"name": "immutable", "version": "1.2.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.4.1"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.2.0.tgz", "shasum": "3c8b4a143798b385d4da3264987a027f178cc504", "size": 6742, "noattachment": false, "integrity": "sha512-WMt9mifv+indGW26rhbeq9XQ/44l6CabvLKYuMM69Ht1oIWpap+lsXXBEaYPDp001ot0uaoYE/2qJpB1HCg7Bg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.1.0": {"name": "immutable", "version": "1.1.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.4.1"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.1.0.tgz", "shasum": "4619f20661bf924760398964ef8388b7ac675743", "size": 6168, "noattachment": false, "integrity": "sha512-JfozmmmtwvLixAs8kc75EFbLzk6ZFIBdnvIPK8RHuqT2tO2YMUEvylMbmixeXOtXeYnFp2KwNQZmxzX1JgECOg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0": {"name": "immutable", "version": "1.0.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.3.2"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-1.0.0.tgz", "shasum": "eb2ca75c3905ca1d7bc35941a4adfe429807f9f4", "size": 4205, "noattachment": false, "integrity": "sha512-xyeQM3nJmAwi0OqVvJi64TEGRGfkIzIZ4RVltFjjucDvJtVKT+lXkY2pl2+1zjOZJwXLmlclZ7mB1EQnKCfVNA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.9.2": {"name": "immutable", "version": "0.9.2", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.3.2"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-0.9.2.tgz", "shasum": "632682adad8663850fa19026407ebf5593a959f8", "size": 4061, "noattachment": false, "integrity": "sha512-9svET9FRFaXCtd8V0GPJb4TdW96fT4tP/XSKBI7cqd5uBq5XtoAgSMIDeIg6LfpZaENOIkPm7dM/Idl6bqEkVg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.9.1": {"name": "immutable", "version": "0.9.1", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.3.2"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-0.9.1.tgz", "shasum": "e58229ecc36889741c8ef227e3795db6bee1eeaa", "size": 4090, "noattachment": false, "integrity": "sha512-vYLwL0Dx5LgKVHQ//n0+TK/kAQDC/R7Mh8uOEBlBu+s2NfAhCGnt+MkPw/9INCw4VcbkWdN06irawWiq608GQw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.9.0": {"name": "immutable", "version": "0.9.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.3.2"}, "devDependencies": {"underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-0.9.0.tgz", "shasum": "c75b333a08c31df2182f1eced7b91ca8ea742e9e", "size": 3967, "noattachment": false, "integrity": "sha512-DLM4EG2/wVorzkS/mqpAdV3hpLpgJ1KzF8spd+EQnwUEFZS/BwmxYIrs+SucRCXvysRu21mdJFGf1uK4lEQfPg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.0": {"name": "immutable", "version": "0.8.0", "dependencies": {"string-hash": "1.1.0", "persistent-hash-trie": "0.2.x"}, "devDependencies": {"benchmark": "1.0.0", "underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-0.8.0.tgz", "shasum": "c975819c9a8c45fae2ffc9c996ebb9d1525301a6", "size": 4693, "noattachment": false, "integrity": "sha512-u0BG4NknbEsK9DeKRS9rqIchdmaPbnnkq4volBjHAHjVZiZdTFa6UqdhKtDuOxLkQsAUSstODHWeFY1cHzhu+g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.0-SNAPSHOT": {"name": "immutable", "version": "0.8.0-SNAPSHOT", "dependencies": {"string-hash": "1.1.0"}, "devDependencies": {"benchmark": "1.0.0", "underscore": "*", "mocha": "1.8.1"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/immutable/-/immutable-0.8.0-SNAPSHOT.tgz", "shasum": "938257a175134d74b369c27be0a8eaafa87ac8cb", "size": 598273, "noattachment": false, "integrity": "sha512-ez7b3z/OmPkpEPlzfRclXAyKu0+NQZhwCDr2TvPEMS4n92ou7+smC/4PdbNnhag0WCvJO1VUmxpeE3N9/Vo4bA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.1.0": {"name": "immutable", "version": "4.1.0", "directories": {}, "dist": {"integrity": "sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==", "shasum": "f795787f0db780183307b9eb2091fcac1f6fafef", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.1.0.tgz", "fileCount": 8, "unpackedSize": 664099, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1jN7tLglkOP8UE3hydht0/PsFpRX+horhUd4D2ZSlXwIhAIK73vxMQGPKYA5NcIuei2A0ny5MeNyL13ZjiCgDuKTE"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii9rRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoE7g/+OlYwzUx0/ucSMbFchXxPFnzeM5eeEz0SLEfaji71WH0I1ICP\r\nAy0zuJSL92bGLmBDbt8zJq2dlptUdVaYogDzwJFajvBXbPuPrcjPaDubcI3D\r\n3VILGrJgFXWzmrDkeF0MBXa2aKu7UXhqLTjpHy0ueaRD7GwI//1oiatmcLBl\r\nPXBuO2xOnIfjqks70+uNaeu1vZdeJ7P0n/JcPLDvP7sSdDbidfo+Akf6UEaT\r\nEnWVF+tVKzECh/01DGHd3GTmgQIiQVEmVK9ETDUHIaodNHkAaElhIoKgQoP6\r\n/zdDPQkj7EeiXoRnNnMNZ0f5ZYXrw6ZplTk9lalwf2Jojm0MOxWAXLcxLy3u\r\nc32kkD6AuNu9uYiyADKDmkpSP7qN9IUOtSJilkkZIAQliXrkJMvBl9u3iiLx\r\nGtCIi8p5RGwwXKr2V8fMPzugWRedKidE7kaf93FVvi6bSTw/Iudpcqq36vZO\r\nHWQC7Zaxw6+WRibLLuztsodwOugGaOc/yVrju3dWnJrvI/x/nSHeA3RRC8JR\r\nPLym6aTGuzNlEZimtzApnI7xHKPcXk/wS9eBcBEw5mqfhdHdriV9hyKwW5RO\r\nQWyDwWTcSK/CXdwz5E0DZiC/yvVXzZMri7L/U0H3/Bav7TOcJpIlWWCV8Pjb\r\nsMojhKa+93/G1MnvuBfkuet9ygCy+0CoH+o=\r\n=tZ7q\r\n-----END PGP SIGNATURE-----\r\n", "size": 134970}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.2.0": {"name": "immutable", "version": "4.2.0", "directories": {}, "dist": {"integrity": "sha512-h4ujZ0OZ3kpvdFcwJAHXEdvawH7J8TYTB62e8xI03OSZhuGpuPY9DPXnonMN8s+uQ56gMUqMK71mXU8ob20xfA==", "shasum": "c91f09108ed7504c2f0faec7222f40178ff97b11", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.2.0.tgz", "fileCount": 8, "unpackedSize": 675079, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCP6i6EzgJWiyNB0/cyDJSQM2EYkH41gMypuWbtnsBRpAIhAMd4EFiUDgbtQAT3MSScj3I2AfsQn05pdlkJTZ1KsRu8"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpHmwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDNQ//Y9JfXjp/r/YHzk6DTrxF286D6U4PBAsOLre6sF5QRIOadGNZ\r\nhuH3ApazPsdjyvhAwjOxgbECq0GEycS/oZ4qWA8JbzmA2z0whRveS6rkHQY8\r\n9LpeTWlXa/6Ixzj8YHu5GfW9A2fUoETTOmd2otAlCxyORBbGKj3pumzkOEnZ\r\nRlXU/O5mCrciD14eyT4Z2Po9zkX+A3oyNEHrfvDfCLpIHYH/7hdciTwID8Tj\r\nivW3/jdDIQp2DfiRZnD91MWaZUFD8q5zIfKqa9mrfmDKeOUsLdIw5sLLpGYw\r\n4SZqrhOSwz49s7hCSITTIfSJeWLX/Xv6dwdNXQgd9jdaP9uaaC435MyFj5lT\r\nvxfZrKpuH7YecOrdhoF3MBTDmRua3HNoKQbU3y+4n+SIyEOoHco79iE3HJti\r\nSna5yuYistvacwT+UsUWgNI7O/v9VVrkIoEwfsYo5XReXzDLfoJ4oefjqu0P\r\na7loqYHKqogW9y9nJpei6JVa2u70moFW52IaFeMNQhUM1P22eq8KpOUylPED\r\niqX40CyfCybqmg4vrdo9Yl83Pifjej5I0flVQ4cr3Al4Jy5tET7TY6qqjXPQ\r\n9MoLS5wkBbjSQMcL2VgkYS2gm7t+4WNUqA3oV3MtuQEHPIhkrn9vS4I3amlQ\r\nVDt/iAMDVg2dLCjr3E8SrGImI05Rq6KiA3Y=\r\n=ZhWT\r\n-----END PGP SIGNATURE-----\r\n", "size": 136834}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.2.1": {"name": "immutable", "version": "4.2.1", "directories": {}, "dist": {"integrity": "sha512-7WYV7Q5BTs0nlQm7tl92rDYYoyELLKHoDMBKhrxEoiV4mrfVdRz8hzPiYOzH7yWjzoVEamxRuAqhxL2PLRwZYQ==", "shasum": "8a4025691018c560a40c67e43d698f816edc44d4", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.2.1.tgz", "fileCount": 8, "unpackedSize": 675522, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8jDBOLV7d6izgvsJjsTTWNFzhIVONWM8tjGNL2aRXuAiEAn52rb/RTdxtNNTo7UigU8OuN+OuLM1hQIAfUZRCexaY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpa8yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroGg//ZyJzdS81Tn05sZbHe90nwxA7jePevrIcyss+MuStSr7/mdeO\r\nE3EhQ5aMribOtx/F0fGgDnIdHtlPjATrQCiPWTCYnHt9UY4YWoLiUkYAQsxY\r\ndqxkPG9/eZvOZEtnaFHy7UAMxZI3/FgcCtSVAgBZUBZVO8QzO+wzpDTwBuQq\r\niGCAYDAv09ri8pGfr9FKRUu+ZaYDYL/SaZwYI/DRasAT1Dxe7X/kIoZgt9T+\r\nOnIuO3tFbt/isFFqLifZixtSJU0kkg6R7rJtzSyAubPLgpWpAxTHXVC4jeIV\r\nLi6HjO/c9h6GefoYSpPqOQOs05vqr5yLGRH60kiGm7hNp54XEf/l/R3zjYLT\r\nbsUsctkx3ad6jlzfSmn8V+91O2YLOJOujyrPoRG4b9mLeTMPLe6EpoSaGmUo\r\nzuSKpi6zZXpxm/F7U9bVqWeLzH+YDOyAqMYX3t2bQWyXk8jl5x/r7ejauo0R\r\ntWunF0oPCN/u34hy6LJ0bzKhmyho5avseeGA6FUrfUR+/r0PhQTIW6/Dcf5H\r\nhmBXRulYUbp1CsgcT7mz0cEzxUDuPCYzPzq/gi4NsuM6+2a/IbIByNr9HFgF\r\n4AWAs+sDby0ZYH5/ENI4D4nrQji8dQTz9gDXUsB6Dsox4sbeyYHbGIqdzgmC\r\nZlVwq68i2rlEVjKeYoR7g3ivZW4p53K11z4=\r\n=VHAv\r\n-----END PGP SIGNATURE-----\r\n", "size": 136942}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.2.2": {"name": "immutable", "version": "4.2.2", "directories": {}, "dist": {"integrity": "sha512-fTMKDwtbvO5tldky9QZ2fMX7slR0mYpY5nbnFWYp0fOzDhHqhgIw9KoYgxLWsoNTS9ZHGauHj18DTyEw6BK3Og==", "shasum": "2da9ff4384a4330c36d4d1bc88e90f9e0b0ccd16", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.2.2.tgz", "fileCount": 8, "unpackedSize": 676750, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGnHrEqMwaQ0d3nnFZ3Wo4iy5FcGSii2m11iBqIcSMsAiEA3mXiSu/gHR9gTQNRf47htskXrMN4Vv6leS7uh3eCi3E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjt/2hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMZw//Yk/2iCHJzFXAywJsCLREULfmh+3ldxKg7PdYHRMxcCTTESkz\r\n6zu5Bjcmd4SsoluGWxfEYj3pZw9eWxJpmBrFLXzB3/r4NivX9Mrr045T/6gV\r\nWF9EBwayQ3B6gZoujOltYmGrpeqfbYW8x44rqvDDrvnmR74i252p0tOp/WCm\r\nK8KSGNDc21cJY0e92Te1vDszz+EpDkfjdKprGDg+ptwCtlbHKtxaE0r9TFnv\r\n7t13Ay08RnUirDZRa2kbiYa4Pt6hxfrA7Beeb0GLTr2z8VxpGjkpwj0Pm9J6\r\nE+D9PLrx/yikT/z3H1m+EMc545Cm34bKTGpmyywRu1gA/vDxyKsA46fNVpBF\r\nGPS8EQsBiGAzm6t6PTw6PU8s+lX00wa3JUfIjMu+Yd6Fk7Pywmta38vgGrey\r\nQ+WWgKkOzQd8aEywYE0mi81fECoW+nhnV6sEqVRFhIp8pvNboPHMN0K+v/UG\r\naRUoad59QTwfSIKNhloqeu0jNCDPIyP+bw7NlKhvMLGJYT537kbtLwQExn1o\r\nYZqYWwUx3Ca8CqPmxuh/dDxbCjpFGbTKrjROMXu0ZhiIUSSA4jsb+2wqYEcw\r\nDrL7DwxJGIhov1yBRJloXNcP8oGL0ts69mzY3nCEtCMs13qX7LhncIO5se+S\r\nZA565It1xAJXS4ZM70jndgb0KfAgj1bS5CI=\r\n=4Z70\r\n-----END PGP SIGNATURE-----\r\n", "size": 136983}, "_hasShrinkwrap": false, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.2.3": {"name": "immutable", "version": "4.2.3", "directories": {}, "dist": {"integrity": "sha512-IHpmvaOIX4VLJwPOuQr1NpeBr2ZG6vpIj3blsLVxXRWJscLioaJRStqC+NcBsLeCDsnGlPpXd5/WZmnE7MbsKA==", "shasum": "a203cdda37a5a30bc351b982a1794c1930198815", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.2.3.tgz", "fileCount": 8, "unpackedSize": 676695, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEaffgjbF33mAoGo+unrd8kQV+Dow84iFp5jPVkEWWtcAiAIRj59oMVP9Fu6PNgvZPS4qfrL4JLpJL+HRrhR+0xJqg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3BkgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVTw/+IBiXOHJo1PpczhfT8H4RxpoM4iDtbUcFDoyL0jpBYyKS8LCo\r\nOluRkiivqkB/Lkp/aaRu1XtkDwBCDVCFAaoxdKjzR3v+FKK1spjbhbJXlaZ/\r\nyzwz6MdHDkSjiY+K1H9YbBp5/zxEJMYNSI/vmGReH5fcWTMCER55jAdZQFtu\r\nWfIBKKwFnfXcGwKnbUIzO6/E+74RdBWcXiETwXpa8+05Mqfdo3A9choesBlK\r\n0LOE2givxGMjLqgNxH3VSVt40hA/FrlWg8rLF4PBlyzJsM6p6CHpGf1vZwTs\r\n8HHRce0JeUfPJISbkqyYPJS25mYop09IZlIwjk0QiFXMfWoUOiy0Ihpabq2y\r\nGkCK4tzSBHoop3/t1/bs2q7UvJ73JyBHhZySVAi2fykBwyJsA6pN9S3mnGBt\r\nk1UE8mQdQf5hTFr2JG67EYjRe/qR0forOgwII7pYksKCIjDyz3FMnIAnkF0C\r\nUjG7t2Wup3HB6fP0658YGiuxVM5gRC6WXZxujpn0LYMr/y0vsUtmXDAIUdWX\r\nPdmHONsSfXpzJYm+n+VyCFGKJYMbkYCMbxa+TZl9Rg84e7/PA7qr2BBr87F1\r\ngaOYhjI6vklGtkrbcgFgsk+1OpOgWM9A2Pw69wmLW4f9kGVT3G+y0Y8yD4kl\r\nPgdWCx9+DbOXwXfp/xqg5jOu5ppVPpJNITA=\r\n=sCcN\r\n-----END PGP SIGNATURE-----\r\n", "size": 136963}, "_hasShrinkwrap": false, "publish_time": 1675368736355, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.2.4": {"name": "immutable", "version": "4.2.4", "directories": {}, "dist": {"integrity": "sha512-WDxL3Hheb1JkRN3sQkyujNlL/xRjAo3rJtaU5xeufUauG66JdMr32bLj4gF+vWl84DIA3Zxw7tiAjneYzRRw+w==", "shasum": "83260d50889526b4b531a5e293709a77f7c55a2a", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.2.4.tgz", "fileCount": 8, "unpackedSize": 677315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEyXr3N4ZUEV3XS8Meog25GqSp1iETZL4ujUX2RbNHPCAiB7QmnyPHbdUytNCCsbuSj1sMM1PJiU3IOMOovdINVq/g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4LgcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGqw//QozpVnxg6iMkEjgRonghJGADi75idyMtOgvskrYhlKp1xWhS\r\nBTNSwL3GP9TbYQsTjP3dWNT9lbOMmhE7Kg57PawCHU6F5T3U4rikhww3pBQz\r\n+E5/U8iPz6yjZcVBaOFRQr0BOZqXUjRVvUK9yQNNWuMomdgu2fX7huAGWJNX\r\nQtkSrk+AJOf7zhZKwii9cMrKOXH3pfGUeITaBdIo+gUvAUZ5jhq9BnMmhsgR\r\nOnYTYJomoTK+9N/qQ5pPnMBJpkXpwjaEPRlbf6M3b1D4yjOWVMaKeL0aISqc\r\n+vsn77/xGvom5J+S1SRqDpYscHEBfF1N5SrWicbcEMQPvzFOQBQ/7dsoizW1\r\nHNEbMZQgHy9JWarBeWwtC9YfhGD6MP42BQ9eNKIg4r+Jha1ZmF3W3qPQb+2P\r\ntQKnK3NSF9hCdDttXXDD/vIurHWK0VmdMfRGB+iQp92S1N2mPMAkOyD/0A36\r\nix+ZvIN+jMo9q1+ekf1k8LvDg59ux2xPF7GnmLlCx9wud36uvqgwPnmo//br\r\nzfuoghTMaEtOhUmxRyrJMP9w5y/Ae0sjKgsaTF4uKXuV8Hh8JwT4sPpn3kiL\r\ns9MEUojaN0DN2HT16kyfwlcgdgBpcPPwmIp9Wi56bwY9LqJaL/vafabtLFml\r\n28u6aLz8HoS/mInSi665bowDulDRNJIg/8g=\r\n=NqK/\r\n-----END PGP SIGNATURE-----\r\n", "size": 137133}, "_hasShrinkwrap": false, "publish_time": 1675671580141, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.3.0": {"name": "immutable", "version": "4.3.0", "directories": {}, "dist": {"integrity": "sha512-0AOCmOip+xgJwEVTQj1EfiDDOkPmuyllDuTuEX+DDXUgapLAsBIfkg3sxCYyCEA8mQqZrrxPUGjcOQ2JS3WLkg==", "shasum": "eb1738f14ffb39fd068b1dbe1296117484dd34be", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.0.tgz", "fileCount": 8, "unpackedSize": 678338, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1Rmg/zIH+ezW2pbX+kUh2/FXssAy75IFijl/C+NJpSQIhAK7/IqM38evPimWUtPUgUva/BOQlMfWGanJT7wWxx/CQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC0pqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqvRAAkR2USX4Ofv06/06JbH8r2bAHQA/jbMQJqYMPAsJNrvqt5WF9\r\nNnmqY/AeqNrHapt1pyD7MfnaXM0ec+VTUkH7e++mHxH6uTkJyfZia9Or22+V\r\npAiaJjWnHkkGxbgy+4x8+heeEqVIQH2QozrjBcEiNGzH2Y80IgC0gA+Btdz8\r\nzC37we9fsMCYBoxp/Ojv4UsiVzcWwMLZcMg/GJg5OH1Jh0Z3OmD1y162/wZl\r\npP6egjD5zTbYdSZmoen4ZKKsUKwXzklC+wrddRs+B58mCurp25MESYKQNndJ\r\n2IO1iWg1/32GGE95bmwiNXONJVfGqkYvKUZxe2ufknjGr9MFWtl6fW99xxu8\r\nP1PwcKrNnk7gC/92xGKRbZTYFquAytcyADUe3Rb8GRFLs1FoRRfJLOwighvX\r\niBpufrB3Xge9r8eKzHrUBNN9GDpiFeDO/mRWy9o7OdVeXJVCoOAnG00dYFWo\r\n2kgpKQ8Ca1Q1yvgq5J+Ot9R2dX95JgFmdDq6OKfzD/GisGXPGIHO/DZYJal3\r\nRyQD2XchEw6UkUY+ri0+gaWh6du5bXn5gbevJtXSwZWvluqgSK7rJw6yDABj\r\n2k1zQpeD6ZNkz1QLnOgaGyLXwbwOg5IZGCVZ+R/dfxy0HYAJIlEQYc83deU0\r\n6NYd1DjfyVJ2pzLBtr4Xm3PkVFdm68ujPqQ=\r\n=Di5r\r\n-----END PGP SIGNATURE-----\r\n", "size": 137576}, "_hasShrinkwrap": false, "publish_time": 1678461546751, "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.3.1": {"name": "immutable", "version": "4.3.1", "directories": {}, "dist": {"integrity": "sha512-lj9cnmB/kVS0QHsJnYKD1uo3o39nrbKxszjnqS9Fr6NB7bZzW45U6WSGBPKXDL/CvDKqDNPA4r3DoDQ8GTxo2A==", "shasum": "17988b356097ab0719e2f741d56f3ec6c317f9dc", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.1.tgz", "fileCount": 8, "unpackedSize": 678805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCB/NxkaY3nI6SBJ206Rk/4dXhqqGmT1408JHwevXCABgIgJQ0K7rmRyfi4HEEgpZHGylvKWwuu2OwE7KSMQysNGjM="}], "size": 137594}, "_hasShrinkwrap": false, "publish_time": 1689108881483, "_source_registry_name": "default", "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.3.2": {"name": "immutable", "version": "4.3.2", "directories": {}, "dist": {"integrity": "sha512-oGXzbEDem9OOpDWZu88jGiYCvIsLHMvGw+8OXlpsvTFvIQplQbjg1B1cvKg8f7Hoch6+NGjpPsH1Fr+Mc2D1aA==", "shasum": "f89d910f8dfb6e15c03b2cae2faaf8c1f66455fe", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.2.tgz", "fileCount": 8, "unpackedSize": 678848, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRx9hhBBXGS8hhzPC6QMGsnkZN5XDC7e78JqyY793cTQIhAL80mdOd3QiQI8Pi19XpMC0f1GBrmD9aT5XN6FRWZXvQ"}], "size": 137598}, "_hasShrinkwrap": false, "publish_time": 1691045772030, "_source_registry_name": "default", "_npmUser": {"name": "leebyron", "email": "<EMAIL>"}}, "4.3.3": {"name": "immutable", "version": "4.3.3", "directories": {}, "dist": {"integrity": "sha512-808ZFYMsIRAjLAu5xkKo0TsbY9LBy9H5MazTKIEHerNkg0ymgilGfBPMR/3G7d/ihGmuK2Hw8S1izY2d3kd3wA==", "shasum": "8934ff6826d996a7642c8dc4b46e694dd19561e3", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.3.tgz", "fileCount": 8, "unpackedSize": 678769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdU3XYpOyK7FkcurLTax6PVKFZVx2i/nsthvaJ06qfHAIhAI43qoEQo2qw/XoTVABYypNI8AQrKVoT8BV6hqYk28gh"}], "size": 137593}, "_hasShrinkwrap": false, "publish_time": 1692784810001, "_source_registry_name": "default"}, "5.0.0-beta.1": {"name": "immutable", "version": "5.0.0-beta.1", "directories": {}, "dist": {"integrity": "sha512-0jJFCr4A1pWd9zsCBjSOn7LRto4WG9WU8zN92lm7b8qLOvyirdWOdmivEdGEI0uG87g9P5WPgVStP5gLY5/qag==", "shasum": "6c5b810b775614331918aec4607f81d6954affc4", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-beta.1.tgz", "fileCount": 83, "unpackedSize": 786136, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCH3Xcm6yQkGtfkbFHHI/gcrlWcn3WRkUXV8EZn4725xMCIQDN0us1XgiVjhHlVK/VARaez70g76CVA236hwlkap9hsw=="}], "size": 145947}, "_hasShrinkwrap": false, "publish_time": 1692893216523, "_source_registry_name": "default"}, "4.3.4": {"name": "immutable", "version": "4.3.4", "directories": {}, "dist": {"integrity": "sha512-fsXeu4J4i6WNWSikpI88v/PcVflZz+6kMhUfIwc5SY+poQRPnaf5V7qds6SUyUN3cVxEzuCab7QIoLOQ+DQ1wA==", "shasum": "2e07b33837b4bb7662f288c244d1ced1ef65a78f", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.4.tgz", "fileCount": 8, "unpackedSize": 679096, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfyZ5OIrD9oSOzyXy9M4G1+D9rmZM7EnRW3HY48oWmnAiATRMq2vdzsEaOzsoZFqpCdeOx3hABO0AxMkfnOJg98nw=="}], "size": 137681}, "_hasShrinkwrap": false, "publish_time": 1692970835499, "_source_registry_name": "default"}, "5.0.0-beta.2": {"name": "immutable", "version": "5.0.0-beta.2", "directories": {}, "dist": {"integrity": "sha512-3v1xnTlom9D2ssrEvcfFHtipjXgmZeNtximyvNyuK2eAWKWWFtfh+9w+VOVujkBSbgpjrYsS55yuWE1E5ES7IA==", "shasum": "b48800bb70471041d39381437163e07597dec226", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-beta.2.tgz", "fileCount": 83, "unpackedSize": 786500, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFw10P0wQe47DSic2NoniFrlHA2vPP2wEKuU13amkA8DAiBhxGZ1mshHUnF/nBZa9Tff3NVbCT+r+yii/igF5KZMaQ=="}], "size": 146033}, "_hasShrinkwrap": false, "publish_time": 1692971626932, "_source_registry_name": "default"}, "5.0.0-beta.3": {"name": "immutable", "version": "5.0.0-beta.3", "directories": {}, "dist": {"integrity": "sha512-isjiI5BcCJ6qONDo+HW1mckw+lReMzo+2FtIqEypN/6pAfs3g5Yfw4uuevA71AoX7NLyytNsFjktBvF2G7bP4A==", "shasum": "ba6e6f8d780bb0809e24fb054914237804ac94f8", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-beta.3.tgz", "fileCount": 8, "unpackedSize": 679125, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG8ZnaH0jzH7cDlGdNf2vgIsfuCqiM/Z5IphFb5YEbk3AiB7EQw2TFn9TcdCGFwCTbEqDDJXNgkNi5OwsMkLTBAXiQ=="}], "size": 137702}, "_hasShrinkwrap": false, "publish_time": 1693225590398, "_source_registry_name": "default"}, "5.0.0-beta.4": {"name": "immutable", "version": "5.0.0-beta.4", "directories": {}, "dist": {"integrity": "sha512-sl9RE3lqd2LoQSESc8VV0k8qE9y57iT7dinq3Q+8mR2dqReHDZlgUrudzmFfZhDXBLXlNJMVWv3SG1YpQIokig==", "shasum": "6c12ac4ad8b16aeec4d064c9d2f4024bb270b7ca", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-beta.4.tgz", "fileCount": 83, "unpackedSize": 786446, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAH70bYy1ZCOL4bEksmp91a4PKtrRj1ZSF5no+UdXAb1AiBw42MV7V0mGhMCqysIxangSxSJYCzgFCCUVSfMQv9qXA=="}], "size": 146009}, "_hasShrinkwrap": false, "publish_time": 1693227203854, "_source_registry_name": "default"}, "4.3.5": {"name": "immutable", "version": "4.3.5", "directories": {}, "dist": {"integrity": "sha512-8eabxkth9gZatlwl5TBuJnCsoTADlL6ftEr7A4qgdaTsPyreilDSnUk57SO+jfKcNtxPa22U5KK6DSeAYhpBJw==", "shasum": "f8b436e66d59f99760dc577f5c99a4fd2a5cc5a0", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.5.tgz", "fileCount": 8, "unpackedSize": 679351, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE5m2L3iaX3qZsuQdHE5ub67plXDy5XAH5fpKwoif/0oAiBtsp1U97cOA2GTBANTASvti7sb3jMlxng3wvHzgG7Ejg=="}], "size": 138107}, "_hasShrinkwrap": false, "publish_time": 1706265284957, "_source_registry_name": "default"}, "5.0.0-beta.5": {"name": "immutable", "version": "5.0.0-beta.5", "directories": {}, "dist": {"integrity": "sha512-1RO6wxfJdh/uyWb2MTn3RuCPXYmpRiAhoKm8vEnA50+2Gy0j++6GBtu5q6sq2d4tpcL+e1sCHzk8NkWnRhT2/Q==", "shasum": "57dcaeadefae61ebc2d3feb6ea5b584a65282b61", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-beta.5.tgz", "fileCount": 83, "unpackedSize": 789763, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxEhxyGvo2I9X41BKDEOldcyR3+++3G7nyeeve7TQ1RAIhANrsbzeUkf9vIAr7M53PaWv+ua1tbyoII3NAzfoCBRHX"}], "size": 147061}, "_hasShrinkwrap": false, "publish_time": 1706299281742, "_source_registry_name": "default"}, "4.3.6": {"name": "immutable", "version": "4.3.6", "directories": {}, "dist": {"integrity": "sha512-Ju0+lEMyzMVZarkTn/gqRpdqd5dOPaz1mCZ0SH3JV6iFw81PldE/PEB1hWVEA288HPt4WXW8O7AWxB10M+03QQ==", "shasum": "6a05f7858213238e587fb83586ffa3b4b27f0447", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.6.tgz", "fileCount": 8, "unpackedSize": 681930, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHMbqIuY0wTDgWXSY+xYmsFgkHhC66iMHBrcYn+GBvG3AiEAulyg3MkfcCPcbVF+p53K1gp8lLvQ5aKsPMN7OCb6QP4="}], "size": 138270}, "_hasShrinkwrap": false, "publish_time": 1715591060238, "_source_registry_name": "default"}, "4.3.7": {"name": "immutable", "version": "4.3.7", "directories": {}, "dist": {"integrity": "sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==", "shasum": "c70145fc90d89fb02021e65c84eb0226e4e5a381", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-4.3.7.tgz", "fileCount": 8, "unpackedSize": 681948, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeJ3Adxy08m770oD9VRyDdBDtSovFjKnR/M00vql95lAIhAKEI2opD1g5ragRgiGj/pvgX7pV5FPVI33PuDEpdcMHc"}], "size": 138278}, "_hasShrinkwrap": false, "publish_time": 1721650664401, "_source_registry_name": "default"}, "5.0.0-rc.1": {"name": "immutable", "version": "5.0.0-rc.1", "directories": {}, "dist": {"integrity": "sha512-sW2Rt1jlDeEBTqHapJp6owkChOUwoHtbXhDHTa1tXJUl3WuLghuHaxjBZrL0Ih76awAdkcThu0m8QIXYU6nlrw==", "shasum": "3011064998a575a70f17bf2ea4bc35394caec993", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-rc.1.tgz", "fileCount": 83, "unpackedSize": 790181, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/immutable@5.0.0-rc.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDubGnpVuJ+McgHOQZeULcgGr9TM64TMb7Aav/UhaO79AiB6Ph/xE912e7vumcsA9ER5I63wtkB2ZrG8et0WNbVDgA=="}], "size": 146945}, "_hasShrinkwrap": false, "publish_time": 1721654408648, "_source_registry_name": "default"}, "5.0.0-rc.2": {"name": "immutable", "version": "5.0.0-rc.2", "directories": {}, "dist": {"shasum": "fd1d20e02798b1b5bcc691951419b1388484bf63", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0-rc.2.tgz", "fileCount": 83, "integrity": "sha512-iqmDkN2+LGR8DT/7ua4foLlf55Y40ZrDC31YJTJX7lv6yMLpU5pE4rOYnt3/T3Mw5tp/xFeMEwtqAURtVDkMQQ==", "signatures": [{"sig": "MEUCIA5zOaksk4LciWuyZCfotVZxY5Gf/vE2hxSQOAMIaGZrAiEAmXkYnhAu10eCzyqFivk8W8+zdSBx/n/VAyGhRFIj95s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/immutable@5.0.0-rc.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 794115, "size": 147512}, "_hasShrinkwrap": false, "publish_time": 1729206992308, "_source_registry_name": "default"}, "5.0.0": {"name": "immutable", "version": "5.0.0", "directories": {}, "dist": {"integrity": "sha512-6ooCHBvtm9B06fFLW2p0VjKVryK20YgWN04Pju2Tq/L6UP4K/vMj4AzsJs9WQy1wiN80oXl3hSS8unYjqA7vOQ==", "shasum": "33d76138ae6e007328842fa7483f1070ba0abc24", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.0.tgz", "fileCount": 83, "unpackedSize": 794104, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/immutable@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAh9bu1vfkehZHyuN10TS2knypWicAeQi/ZMbnXlvrXqAiAcfPebMCobTlscrtEqDnZbw48SYz2mfjRVqB3MCIyP2w=="}], "size": 147491}, "_hasShrinkwrap": false, "publish_time": 1730675122973, "_source_registry_name": "default"}, "5.0.1": {"name": "immutable", "version": "5.0.1", "directories": {}, "dist": {"integrity": "sha512-V75Xv+/HZ/BqzNRVH5UbknAfiuRHtpQL/VgkMM/L4+gFZf/50nO1aNeoMm/ci/1lPpgs2DuXtjg4B+uif4ZHNA==", "shasum": "050878dd2c12e3a58e78ff1a57270bebece2767e", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.1.tgz", "fileCount": 8, "unpackedSize": 686904, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/immutable@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEi6f59qO3soevwkIx9tlkI4Xmke2b5GXxvJq6xQoiSQIgPJc1Y9qwrGQqG2mvUknB9A1Qg35Xj2Y9535Rb6ia4SQ="}], "size": 139157}, "_hasShrinkwrap": false, "publish_time": 1731024525339, "_source_registry_name": "default"}, "5.0.2": {"name": "immutable", "version": "5.0.2", "directories": {}, "dist": {"integrity": "sha512-1NU7hWZDkV7hJ4PJ9dur9gTNQ4ePNPN4k9/0YhwjzykTi/+3Q5pF93YU5QoVj8BuOnhLgaY8gs0U2pj4kSYVcw==", "shasum": "bb8a987349a73efbe6b3b292a9cbaf1b530d296b", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.2.tgz", "fileCount": 8, "unpackedSize": 686904, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/immutable@5.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/Ih5Nfam3B7y7LQfxBBM321UOvGpvs+Ik4ZFPde5BWAiBW5YDPahVBTmQKZIEJQemChi9HYmNPiJXNw1eAZTxK1g=="}], "size": 139157}, "_hasShrinkwrap": false, "publish_time": 1731024924746, "_source_registry_name": "default"}, "5.0.3": {"name": "immutable", "version": "5.0.3", "directories": {}, "dist": {"integrity": "sha512-P8IdPQHq3lA1xVeBRi5VPqUm5HDgKnx0Ru51wZz5mjxHr5n3RWhjIpOFU7ybkUxfB+5IToy+OLaHYDBIWsv+uw==", "shasum": "aa037e2313ea7b5d400cd9298fa14e404c933db1", "tarball": "https://registry.npmmirror.com/immutable/-/immutable-5.0.3.tgz", "fileCount": 8, "unpackedSize": 687034, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/immutable@5.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFF3unA/0U65N66qXXeHm+CYI9fE2f18tFL1bFwQ8auKAiBl+qtXJhYwTSBYw8wXcxyXapZRouzz1BKLDlQ0nIi9+Q=="}], "size": 139193}, "_hasShrinkwrap": false, "publish_time": 1732027704128, "_source_registry_name": "default"}}, "_source_registry_name": "default"}