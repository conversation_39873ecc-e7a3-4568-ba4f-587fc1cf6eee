# 抽奖道具查询功能 API 文档

本文档定义了抽奖道具查询功能的API接口规范，用于前端与后端的交互。

## 基础信息

- **基础URL**: `/api/treasure`
- **数据格式**: JSON
- **时间格式**: ISO 8601 (例如: `2023-08-01T12:00:00Z`)

## 1. 奖品来源(道具)接口

### 1.1 获取奖品来源列表

获取所有奖品来源(道具)的列表，支持分页、搜索和筛选。

#### 请求

```
GET /prize-sources/
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| search | string | 否 | 搜索关键词，会匹配名称和描述 |
| source_type | string | 否 | 来源类型，可选值：`item`(道具)、`mode`(模式) |
| active_only | boolean | 否 | 是否只显示已上架的，默认为false |
| page | integer | 否 | 分页页码，默认为1 |
| page_size | integer | 否 | 每页数量，默认为10 |

#### 响应

```json
{
  "success": true,
  "message": "获取奖品来源列表成功",
  "data": {
    "count": 100,
    "next": "http://example.com/api/treasure/prize-sources/?page=2",
    "previous": null,
    "results": [
      {
        "id": 1,
        "source_code": "SOURCE_001",
        "name": "幸运抽奖",
        "source_type": "item",
        "image_url": "https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/幸运抽奖.png",
        "description": "可以抽取各种奖品的道具",
        "is_active": true,
        "created_at": "2023-08-01T12:00:00Z",
        "updated_at": "2023-08-01T12:00:00Z"
      }
    ]
  }
}
```

### 1.2 获取奖品来源详情

获取特定奖品来源(道具)的详细信息，包括其包含的所有奖品。

#### 请求

```
GET /prize-sources/{id}/
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| id | integer | 是 | 奖品来源ID |

#### 响应

```json
{
  "success": true,
  "message": "获取奖品来源详情成功",
  "data": {
    "id": 1,
    "source_code": "SOURCE_001",
    "name": "幸运抽奖",
    "source_type": "item",
    "source_type_display": "道具",
    "image_url": "https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/幸运抽奖.png",
    "description": "可以抽取各种奖品的道具",
    "is_active": true,
    "prizes": [
      {
        "id": 1,
        "prize_code": "PRIZE_001",
        "name": "S级赛车碎片",
        "quantity": "10个",
        "image_url": "https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/prize/S级赛车碎片.png",
        "rarity": "rare",
        "rarity_display": "稀有",
        "prize_type": "配件",
        "probability": 0.05,
        "description": "用于合成S级赛车的碎片",
        "sources_info": [
          {
            "id": 1,
            "source": 1,
            "source_name": "幸运抽奖",
            "source_type": "item",
            "source_code": "SOURCE_001",
            "probability": 0.05,
            "created_at": "2023-08-01T12:00:00Z"
          }
        ],
        "is_active": true,
        "created_at": "2023-08-01T12:00:00Z",
        "updated_at": "2023-08-01T12:00:00Z"
      }
    ],
    "created_at": "2023-08-01T12:00:00Z",
    "updated_at": "2023-08-01T12:00:00Z"
  }
}
```

### 1.3 获取特定来源的所有奖品

获取特定奖品来源(道具)包含的所有奖品。

#### 请求

```
GET /prize-sources/{id}/prizes/
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| id | integer | 是 | 奖品来源ID |

#### 响应

与1.2获取奖品来源详情的响应相同。

### 1.4 获取来源类型选项

获取所有可用的来源类型选项。

#### 请求

```
GET /prize-sources/source_types/
```

#### 响应

```json
{
  "success": true,
  "message": "获取类型列表成功",
  "data": [
    {
      "value": "item",
      "label": "道具"
    },
    {
      "value": "mode",
      "label": "模式"
    }
  ]
}
```

### 1.5 导入Excel数据

通过上传Excel文件批量导入/更新奖品和道具数据。

#### 请求

```
POST /prize-sources/import_excel/
```

#### 请求参数

使用`multipart/form-data`格式，包含以下字段：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| file | file | 是 | Excel文件(.xlsx或.xls) |

Excel文件格式要求：
- 必须包含的列：`奖品名称`、`所属道具`
- 可选列：`序号`、`数量`、`道具编号`、`道具类型`、`奖品编号`、`奖品类型`

#### 响应

```json
{
  "success": true,
  "message": "导入成功",
  "data": {
    "stats": {
      "sources_created": 5,
      "sources_updated": 3,
      "prizes_created": 20,
      "prizes_updated": 10,
      "errors": []
    }
  }
}
```

## 2. 奖品接口

### 2.1 获取奖品列表

获取所有奖品的列表，支持分页、搜索和筛选。

#### 请求

```
GET /prizes/
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| search | string | 否 | 搜索关键词，会匹配名称和描述 |
| rarity | string | 否 | 稀有度，可选值：`common`(普通)、`rare`(稀有)、`epic`(史诗)、`legendary`(传说) |
| prize_type | string | 否 | 奖品类型，可以是任意文本，例如"赛车"、"配件"、"道具"、"货币"、"装饰"、"动作"等 |
| source_id | integer | 否 | 来源ID，筛选特定来源的奖品 |
| source_type | string | 否 | 来源类型，可选值：`item`(道具)、`mode`(模式) |
| active_only | boolean | 否 | 是否只显示已启用的，默认为false |
| page | integer | 否 | 分页页码，默认为1 |
| page_size | integer | 否 | 每页数量，默认为10 |

#### 响应

```json
{
  "success": true,
  "message": "获取奖品列表成功",
  "data": {
    "count": 100,
    "next": "http://example.com/api/treasure/prizes/?page=2",
    "previous": null,
    "results": [
      {
        "id": 1,
        "prize_code": "PRIZE_001",
        "name": "S级赛车碎片",
        "quantity": "10个",
        "image_url": "https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/prize/S级赛车碎片.png",
        "rarity": "rare",
        "rarity_display": "稀有",
        "prize_type": "配件",
        "probability": 0.05,
        "description": "用于合成S级赛车的碎片",
        "sources_info": [
          {
            "id": 1,
            "source": 1,
            "source_name": "幸运抽奖",
            "source_type": "item",
            "source_code": "SOURCE_001",
            "probability": 0.05,
            "created_at": "2023-08-01T12:00:00Z"
          }
        ],
        "is_active": true,
        "created_at": "2023-08-01T12:00:00Z",
        "updated_at": "2023-08-01T12:00:00Z"
      }
    ]
  }
}
```

### 2.2 获取奖品详情

获取特定奖品的详细信息。

#### 请求

```
GET /prizes/{id}/
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| id | integer | 是 | 奖品ID |

#### 响应

```json
{
  "success": true,
  "message": "获取奖品详情成功",
  "data": {
    "id": 1,
    "prize_code": "PRIZE_001",
    "name": "S级赛车碎片",
    "quantity": "10个",
    "image_url": "https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/prize/S级赛车碎片.png",
    "rarity": "rare",
    "rarity_display": "稀有",
    "prize_type": "配件",
    "probability": 0.05,
    "description": "用于合成S级赛车的碎片",
    "sources_info": [
      {
        "id": 1,
        "source": 1,
        "source_name": "幸运抽奖",
        "source_type": "item",
        "source_code": "SOURCE_001",
        "probability": 0.05,
        "created_at": "2023-08-01T12:00:00Z"
      }
    ],
    "is_active": true,
    "created_at": "2023-08-01T12:00:00Z",
    "updated_at": "2023-08-01T12:00:00Z"
  }
}
```

### 2.3 获取稀有度类型选项

获取所有可用的稀有度类型选项。

#### 请求

```
GET /prizes/rarity_types/
```

#### 响应

```json
{
  "success": true,
  "message": "获取稀有度类型列表成功",
  "data": [
    {
      "value": "common",
      "label": "普通"
    },
    {
      "value": "rare",
      "label": "稀有"
    },
    {
      "value": "epic",
      "label": "史诗"
    },
    {
      "value": "legendary",
      "label": "传说"
    }
  ]
}
```

### 2.4 获取奖品类型选项

获取系统中常用的奖品类型选项。注意：奖品类型可以是任意文本，不限于这些预定义选项。

#### 请求

```
GET /prizes/prize_types/
```

#### 响应

```json
{
  "success": true,
  "message": "获取奖品类型列表成功",
  "data": [
    {
      "value": "赛车",
      "label": "赛车"
    },
    {
      "value": "配件",
      "label": "配件"
    },
    {
      "value": "道具",
      "label": "道具"
    },
    {
      "value": "货币",
      "label": "货币"
    },
    {
      "value": "装饰",
      "label": "装饰"
    },
    {
      "value": "动作",
      "label": "动作"
    },
    {
      "value": "表情",
      "label": "表情"
    },
    {
      "value": "喇叭",
      "label": "喇叭"
    },
    {
      "value": "礼包",
      "label": "礼包"
    },
    {
      "value": "碎片",
      "label": "碎片"
    },
    {
      "value": "其他",
      "label": "其他"
    }
  ]
}
```

## 3. 错误码说明

| 错误码 | HTTP状态码 | 描述 |
|-------|-----------|------|
| 400 | 400 | 请求参数错误 |
| 401 | 401 | 未授权 |
| 403 | 403 | 权限不足 |
| 404 | 404 | 资源不存在 |
| 500 | 500 | 服务器内部错误 |

## 4. 图片URL规则

- 奖品图片URL格式：`https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/prize/{奖品名称}.png`
- 道具图片URL格式：`https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/{道具名称}.png`

## 5. Excel导入说明

### 5.1 Excel文件格式

Excel文件必须包含以下列：
- `奖品名称`：奖品的名称（必填）
- `所属道具`：奖品所属的道具名称（必填）

可选列：
- `序号`：行序号，仅用于标识，不会导入数据库
- `数量`：奖品数量，可以是文本格式（如"1个"、"7天"等），默认为"1"
- `道具编号`：道具的唯一编号，如果为空会自动生成
- `道具类型`：道具的类型，可选值：`item`(道具)、`mode`(模式)，默认为`item`
- `奖品编号`：奖品的唯一编号，如果为空会自动生成
- `奖品类型`：奖品的类型，可以是任意文本，例如"赛车"、"配件"、"道具"、"货币"、"装饰"、"动作"等，默认为"其他"

### 5.2 导入规则

- 如果道具编号已存在，则更新该道具的信息
- 如果奖品编号已存在，则更新该奖品的信息，并添加新的道具关联（一个奖品可以关联多个道具）
- 如果道具编号或奖品编号为空，则自动生成唯一编号
- 图片URL会根据奖品名称和道具名称自动生成
- 所有导入的道具和奖品默认为已上架/启用状态
