{"dist-tags": {"latest": "1.2.3"}, "modified": "2024-10-16T14:35:25.211Z", "name": "@lezer/common", "versions": {"0.15.11": {"name": "@lezer/common", "version": "0.15.11", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-vv0nSdIaVCRcJ8rPuDdsrNVfBOYe/4Szr/LhF929XyDmBndLDuWiCCHooGlGlJfzELyO608AyDhVsuX/ZG36NA==", "shasum": "965b5067036305f12e8a3efc344076850be1d3a8", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.11.tgz", "fileCount": 16, "unpackedSize": 446392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuyDdCRA9TVsSAnZWagAAlpAQAKT46ph1TbtlyG2bCu7P\n48tiUvhaUxd3CNeZ7Y8g8oOtdNHl+IHa8tz5OFgsE0yueRQoODEQUV+9lWau\n81hc0562nzGQ16cDh0C6BhLqgNL3nxgjXlgEQEC8VsaDtyIAmifWv2jO3Pjd\nmVsBho0iSGeDLIabczSR/ajLzcPnD8gP+CGdd+Lg7yB2Jl5DBuCGkGkzPFE3\nOhYo+y4h+yYBagLlmRvexS0R53syBx0kD1RUr/wwoV4akfugLq9mf0G9SpBq\n4lQAPYsn2ZhbI4O15ff3DhmEnqWbKe0OQXJvhd5+eW4BYZ/qVc5sEJN7HbUR\nE9bLwfl7AWhl5V3IAhGfpElyBL9jRRtg5tDmn1Fi+5jeH/bVncF6yDaLOWQl\nLvqzV1T3R1+8llejs/fxQb2/vBoRFfQIIGz+Y1VA1FIhFMEM8GlUx9pofaZN\nHrm2iCaj72T6TMelRNOvSzgCeY8XN05mvljRci6HOGYDzYstVt3m6YZ/F8wC\nlaauDfhlBgWf7EJwJs8AZbch0HXsNh0Cmwh6Cnt/vwZxVyeXIctoTPz/FMP2\nqdn8qKI+vodDLaPV756hvRBPDeFDm4iixLZjyZgFBiWCLvG3Xkp0Prp46hfH\nbpKWHcou5z9mX9Yoxu88imp/Lf+cJPtb4MSA1J9e9erXOD95KVK+Tayu1xkS\nOxA0\r\n=F7J1\r\n-----END PGP SIGNATURE-----\r\n", "size": 104169, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.10": {"name": "@lezer/common", "version": "0.15.10", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-vlr+be73zTDoQBIknBVOh/633tmbQcjxUu9PIeVeYESeBK3V6TuBW96RRFg93Y2cyK9lglz241gOgSn452HFvA==", "shasum": "662da668f46244fb20bfaada67b43b3d0463b344", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.10.tgz", "fileCount": 16, "unpackedSize": 446466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpkrqCRA9TVsSAnZWagAAUVgP+wf0sxCyWwN2iU1yKAnf\nZtJTVcE7oA7WoEcL3ELJ2bxCKDUI7imKJlpM3uLtkjDuRONujucYv2PF38iD\nCtTVCRGp4qykLGbyw/Vuz9kKdnvM+TE8U3M3rxNgfkpDgswwn0e4gQzLggZY\nqykYIJRRWmoFRYqkzj/RsU6IEUhtFak4AtOEqsi5rnSnG8ZjSUMuEyu+XUgJ\nnCl/d4dO/AsQqFt5vU3BDHYdG4uudgpt5w3KO4VhxIaKTg/FRIR0HwaSyRjZ\nPvagVpkCFebP7RcugT/EOeoDJttneKQW/bAq9ZhD1LjCCbf0JT/0tFNdk7eD\ntLV5ZLWoiI35aTcvTKVQtV7dPuSNpbIGXuCdCtw+vQcuAenjgq90wTTP1kie\n2E7xcnsQhnjSl6REond+SNza4U2zZ6MU4SiQpcQdyyRfTqylHB+TPJ5Gb0kz\n1j5qmdKMd0fa+v8zrzsPtQ1ilXsEEK3RYnPRySKcK7P+28+urnILWHJPPBSF\nQCMbHwT856X9ArDOY6mB68rg0DaRWTjkLSF55Tdw5jSdNEfAIM3n9nii9aPx\ni2jolZsx4aHQH0XWvREMvSGM2E2wmt6gzcX400BUjpdZ4byt5I2qVJGmIMkZ\nh8kGlKWnguFYge7IEmxe6kujIhcMLRPb+JynANd6t15UErrUV6VKgWYzaJZM\nJ7e5\r\n=pZzE\r\n-----END PGP SIGNATURE-----\r\n", "size": 104178, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.9": {"name": "@lezer/common", "version": "0.15.9", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-N86tOY/g9pZQuKbmUjL4TQTegm4/34TZXxp1TYYFGdd9Rnhd9RPjQ3zp4q5qp/2qs4xlF4Dheg19K+JqAtvZxg==", "shasum": "0aab74ad14d2690fb09f83d5a05bd6039ad812f3", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.9.tgz", "fileCount": 16, "unpackedSize": 408484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnhKiCRA9TVsSAnZWagAANjoP/2AeV5lyy66zQpyikWQ7\nND4fo9GaPcmIM35QQjMSmVQ6g5KvDMhcYZzu0o9Lx/PQYsKpHmuR/BAZ5t5q\nXLsoWiBe55G67Xf/d9DyBM2VJlpudcKlrnjGmH2DawQD1hwv6Eg/5mvBnRMn\nwHcNA2ZNCoNN+awhhp/hD/8bMp0og4IvqMNY3Ttg9S4/Yq7gkdLmhd3BGkgT\nzFlZ/hIQF6x8L57pQQXGXjd230hsOd2k0XX1SHHU/DSO8FGpIDKclgC2YeLi\nqLqa9vWcJQRkmIyVCdaOJvwuZe08+uQWZmWrEFZf20/sKdxNz3zNvlyr8j0f\nXnMpKMcjGqfZFfi2TNlU2nK9+6cYYs1rHIqY/P40qhUSpuzcjF8zjFUYQVR5\nTYxm9iZaxAmhyqoSaOgpWtlehrYnYpy8JUmtpbr6HFEKdcFolkkF1Xvj2VVY\n5R1xkxU1vfK/TEg4byakwZ70vccAjHA0G3Txdigf2vTTW7OW6GSUC2aQQLPt\ncpN6oG6qEjm18b/NI4+UL7dz8EhDCD8bz+szf0etVFew+KdV2D7QHqJDXRCO\n7B77nRHF4UMqld7FWbsLrlMqvgYiLjoxsNiGmtnS8fdkJcZ8k/45BtkcNvjV\n9JeOixfCEZXg6IclKBYUXR9JPAUHJlH0QICB1JDSz6x0+VMGEuwi1L+nD9bZ\nOzp5\r\n=EG/G\r\n-----END PGP SIGNATURE-----\r\n", "size": 98063, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.8": {"name": "@lezer/common", "version": "0.15.8", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-zpS/xty48huX4uBidupmWDYCRBYpVtoTiFhzYhd6GsQwU67WsdSImdWzZJDrF/DhcQ462wyrZahHlo2grFB5ig==", "shasum": "e9d87b5f05c18feb51b7f04d74b124caea32a94b", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.8.tgz", "fileCount": 16, "unpackedSize": 406766, "size": 97671, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.7": {"name": "@lezer/common", "version": "0.15.7", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "8b445dae9777f689783132cf490770ece3c03d7b", "size": 97650, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.7.tgz", "integrity": "sha512-Rw8TDJnBzZnkyzIXs1Tmmd241FrBLJBj8gkdy3y0joGFb8Z4I/joKEsR+gv1pb13o1TMsZxm3fmP+d/wPt2CTQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.6": {"name": "@lezer/common", "version": "0.15.6", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "b323b92ccf4e09a12c34f22431dfeae1e91708b8", "size": 97247, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.6.tgz", "integrity": "sha512-UtgE/blhWf+qiLK1kK/TqY9mF3SmLCC3xRTVfw456O1NpsM3ozVtvqrGufUoB7hpUGKFBlwEBQ4ryijW6p1Asg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.5": {"name": "@lezer/common", "version": "0.15.5", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "29266bdd335b650e58efeb25c789b32e0dcdddfa", "size": 97242, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.5.tgz", "integrity": "sha512-ufcjclusHXGdhp4gSPbPD7sUd38SgOej7m5tAEuG2tNPzqzV0d1vwwLh57R6IwW79ml2mb3tUjAoDfqI7v1HEw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.4": {"name": "@lezer/common", "version": "0.15.4", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "e2639e3f018d98a2aac2cdae2f14196c478f6112", "size": 96936, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.4.tgz", "integrity": "sha512-+n/fKtcvw2qp/WIaZCR2HNzoB70Pwa9hQ8xFU86sDbZr9PYf2HX0qaG3N9Q/XCQHJeaB+GOh/XY9jHMWw4h6Ow=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.3": {"name": "@lezer/common", "version": "0.15.3", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "2e9ff601656da0f125c883d4130e776582362fa2", "size": 96585, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.3.tgz", "integrity": "sha512-TLDp0BX/XAXhR2tleyWM7TjQ6LXVkkh2PcAHPu+fgi0xcT9JmI56Gh8zgMhoztDJAzH2ItMmufzVPVeBwz9VDw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.2": {"name": "@lezer/common", "version": "0.15.2", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "3f8512670a03a79ee42132c527e89504e2155dd4", "size": 96554, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.2.tgz", "integrity": "sha512-0AQaL8k/bG1eLOSis+Y0r+8qDn2zxZwLbWK2/QiXZcbHg8GAGuRgbINnr3K+QmbUwlzyLa3q1pZ7BVXL2WWLng=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.1": {"name": "@lezer/common", "version": "0.15.1", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "35b4a84a9f4991f90f3cd3b3fd888e1ed2a10195", "size": 96563, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.1.tgz", "integrity": "sha512-S0sun36njrK87siUuTkB6hXr+i7sx6RGIHjp9YO9LtTrw5d25Sk1hOVECnWqgvy4WFwKGssNP1OxVrx3UBBzIw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.0": {"name": "@lezer/common", "version": "0.15.0", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"shasum": "710c749668c7701049d0ec7937d60f667ad5cdc0", "size": 96510, "noattachment": false, "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.0.tgz", "integrity": "sha512-cCMKzpMw9mOicAPqgibLH2//XCvCFjAUar0HDQcf60MMmKE+TxsgV6q5XqTplejzBCr7UigkTgeedZGOLETwuA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.15.12": {"name": "@lezer/common", "version": "0.15.12", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-edfwCxNLnzq5pBA/yaIhwJ3U3Kz8VAUOTRg0hhxaizaI1N+qxV7EXDv/kLCkLeq2RzSFvxexlaj5Mzfn2kY0Ig==", "shasum": "2f21aec551dd5fd7d24eb069f90f54d5bc6ee5e9", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.15.12.tgz", "fileCount": 9, "unpackedSize": 154680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNGJbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/fQ//TSZzvarCvW7Sz1hiEdbVnabu8INo1MHVkkNOfTanYojyn+60\r\nIh/ekcFraI158xO5PGcFjxdNFFaLzz80KvtFYT7aCpTC6d8FSVBf87s3vGrH\r\nXJxUY0Sd2q7o8SCdIWlGY8h0TvRF387SzZUKDY/715dbGD+uzkcHiQNNEwIX\r\nWEWsxywJ+TcMYHpzj8PB5lkUzPJ+9jFupFZ9vRokfQO874gUwRqE4HdtfN1k\r\nr/6hbNc1/KVZ1AVUOj84X0C2jhC18POjrxSkeG4hvCEnXNbKwJ5nDqoWgCe+\r\nyrR15JJbRSkJsDFCJqK88iXbrnLVstdLYKIOHHwAO6IaHVWZ14hrY7K5aAkA\r\nCtWwjGSYSENpi8X+e0jul8RWShxTb+Cz6MOmIXJB1f8l0iSll1t9G9q5Ptp0\r\nBP42vjD8BSUbZRlJ64NhqM0bKuI9zNYavsDhv06rsr7Ci9tIJ0w1pQzB6n+D\r\ndSnrbphUvbmE3zKZY1bCohcYj10Yxs2D1lhj7TJKWG6XqEUMv+rNYcqVmONB\r\nFUuHJz2v+3Z7mmfBtwZL7ve3YC/fOMJyENsU8/GHxe2wiJrVGfSJd5xJjv0o\r\nlwDxw2u5Wk93osxpCOBKEAOWZT6JnWEWgQG6+w0OdxEPip6LL6BQhX/sjlWs\r\nHG2NSJiq9+rfLhDdBjsmF5Dxx30MOEWS1KU=\r\n=QlKj\r\n-----END PGP SIGNATURE-----\r\n", "size": 37826}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.16.0": {"name": "@lezer/common", "version": "0.16.0", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-H6sPCku+asKWYaNjwfQ1Uvcay9UP1Pdzu4qpy8GtRZ0cKT2AAGnj9MQGiRtY18MDntvhYRJxNGv7FNWOSV/e8A==", "shasum": "b6023a0a682b0b7676d0e7f0e0838f71bde39fcd", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.16.0.tgz", "fileCount": 9, "unpackedSize": 166013, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDH48fmN/Lfue74CmsN+QUFkrVA6gsR/Qc+lyyhvEf99gIgEls/5JrG1R68Zif2+IY3kZO2fjdjQQQYg7efBkFIIwY="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAfMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMVQ/+P7vkNPAS7BlKt4DUdFwUNMuGm6Eas5TnL2Y14t7DmpvWp+WA\r\ntFHBUBZU35NPDSc6lkOcfKIGGoBx5JqzTeR+Ldajzpy6WobL2vDGznCwck38\r\nIztttZt+CTX02ojYgeUVHwSxOjzUCE+Rv4QOASIJf8FX5ioson3/fgmX26tU\r\nAIIBBmxUwGM6km/1i7JFcjbJ91PyrKxl6Mza3gx/n1R0kqou2/sNKMcxzC0e\r\n4e9UbpsQLX0pKlOhD4LYAby1CjDyeZncSVXih+kR/45Px5x01tVPasbHVMZp\r\nVeiKkGDv/zfxn4pBJTosq7eZSbMW6Io0QXOgq/IdNu1nTVQYp/ZsiYhHy+Zf\r\ngQc//6s9OFlbaXnFsMbZBlrYEI92o5gRA0TlOOg4Osf3d1zvKoL/gh7EGU8L\r\n3SNqDA5DxZOG7y4M/GCATgEkFkVokK4LlzACXZjs1gemJhOBuQ2TC5qHdKcs\r\nDjyCNz/CKWHtCVETSGCv5AKryrJy4XhHe+07cjcdx4Mfk7OBAs6PdaDXhqdM\r\nC2eJ2AnVkk54jnEvtI/NcLLXMn3xCOjpqAjWII77s1+Jw/nWSQo4Up/h4wm4\r\nbSMgGsM13ve5hr7Je6C/56x1C1BhPhsmKspcOkhpmmsD18ERF4y6zTNKvyE5\r\nTImg8XwG/vCeUXCE5pTElO5HSauNPbV7oYM=\r\n=OWRV\r\n-----END PGP SIGNATURE-----\r\n", "size": 40264}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.16.1": {"name": "@lezer/common", "version": "0.16.1", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-qPmG7YTZ6lATyTOAWf8vXE+iRrt1NJd4cm2nJHK+v7X9TsOF6+HtuU/ctaZy2RCrluxDb89hI6KWQ5LfQGQWuA==", "shasum": "3b98b42fdb11454b89e8a340da10bee1b0f94071", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-0.16.1.tgz", "fileCount": 10, "unpackedSize": 241509, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFmzNFH35sb4nIKuSQOGYh/DY9IcHxOHnzTO8fCEZ7z4AiAyDo3TpAynYWp31RAuvyRU/FEqWZeXfzVoPqGP/Mfi2Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil2YZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK5w/+LWWCKsYL4xP6356poUdEPlN5/6eXGoPgo/te/jmqWywk4L+V\r\nSZ8xiUMy2lWLH2TR3xp5FKEnzirbNMWghf+2tWyH7eSgTpAojHJUzzv1YYVV\r\nXmULGxEiP+d4BMNkhAmG4ntyuCURZ3Kv5IyushOJ6HTI6KkfQBRYMCmdzT49\r\nMWl2r69evv+YCv1goQdFYe+Dxy0n4VSK6f9hKi+U73L8Cg1i4CkJAx0nF5Uj\r\nmHtJ1MDAryl+qsICX4XrTWTfho+ZExgBBhgraqD63IuBKta4JyENYRhLfDXT\r\nPHfkRbxkKnJYNLAECxpV1AV4G9RD2gylg1bEUjqCEgA3fxi8MRnCN/5SZO5W\r\n9WoXRbT+/1Na7251qgoV5WRDmlyTLxaHaKbIQBbgAiSdoOM6PyznyzfrBlQt\r\nXaL+jT/Mr+1KZ1155d9auVzWYEY/mPHd8V6W8G+T6T7kEhLPvho6QZCOSIIX\r\nqoipnvAreW64KeQIdk1H0HdG2blateBTyFIVladpS1EMd7KIlrI3W3Hq0xAc\r\nepflBsbQLDoTkrAIKOzFenkMDiv6/raGXdMiKAAz6VE7E7wL9XS2ZiCse3H4\r\n/2hm3x2NjY6sUmJkQfnQD7j+JQQIctEaYffLJQP0WziG/ME7DSCwBtuk3f5o\r\n3inz64OLXKTxpwhxIAvaHphjyr/m2jX9ssM=\r\n=jh0V\r\n-----END PGP SIGNATURE-----\r\n", "size": 58018}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0": {"name": "@lezer/common", "version": "1.0.0", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-ohydQe+Hb+w4oMDvXzs8uuJd2NoA3D8YDcLiuDsLqH+yflDTPEpgCsWI3/6rH5C3BAedtH1/R51dxENldQceEA==", "shasum": "1c95ae53ec17706aa3cbcc88b52c23f22ed56096", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.0.0.tgz", "fileCount": 10, "unpackedSize": 241524, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiApnUvxFS8SUZmdk0XU826vVxs1SUcM3qTTXDmNRHhAIhAMpScUcqCtfe53IPNf/RmM8Hg02R3haBDlJErMOzhClQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinhEAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop0A/9ED1UMfNuPUwN04t8ZgECGL+kze3z+MCJhvbTeJR4Z/6HoiFV\r\ntiBB/erKobtaEeVVeTgi4UvrGGqkkFtVDOu1llaFGGHPMZ+I96UqBIHPT5Nv\r\neRZwUQKzfuQBdIDeDlFt7yVeX0e1bUbL9MlTJPndZw2qp30YFncphxiq5z7U\r\nixhs0ncAW8m26vOySeTKrfAKt9S0eglEGB1qnD50NtsQluxOIidPhWFL7ht1\r\nwcHLoDJPTse5jIa3a8vvske4pC7om/OP3wNMpIZKNlaweXBNOEUxcEthkA/o\r\n67W9fR9Qub0PiLz31Wfk9ogqnHnoGaGloE9L7IENsSkr0EtT2vbk1Mv9HZTR\r\n5Kc+0kbT5r5FCSEpga3aAXglNVM5Z4i9oDeZgu8rddzD7/9T9r+h1DxNMqQo\r\nrFJydLq/AbD/bGn9lCu9ll3pGqN06W8nnENkli/b/E9oUUVVrPZ8lovhLga0\r\nvhFtlBLu6FUPNWdR3MPNma35NETxqCNvzsYyc9u6nkKqEVILOXfbJwCLp18R\r\nZnxIIPq73Lc91GXCa8CW3J6FNUTb7Cs84z2nnsF2edBMJGNLWp25UMDhD9Ip\r\nxwAYRHP1lXagJX4vN/OHA2005bTEwtz1KSr+9WrU0WyqWvSL4rjQ1DJZM2km\r\nJzI5/9mYj23TOLjib8XXbYqNE8pv4X2NmGM=\r\n=qXDO\r\n-----END PGP SIGNATURE-----\r\n", "size": 57965}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.1": {"name": "@lezer/common", "version": "1.0.1", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-8TR5++Q/F//tpDsLd5zkrvEX5xxeemafEaek7mUp7Y+bI8cKQXdSqhzTOBaOogETcMOVr0pT3BBPXp13477ciw==", "shasum": "d014fda6d582c24336fadf2715e76f02f73c8908", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.0.1.tgz", "fileCount": 9, "unpackedSize": 166564, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEGreO1QY59fVpCOzYpvgu0j6wR07t+OwX2LgDSI+/rTAiEAzj5PAew0IEMSg9RXpPtIwFFj1Yg5QvrTF0vUngk7hcY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDbWSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry+Q/9Eni44fahupCYSUB0w0M5ZK4txP7Vboi9BrlR29LMbdFkEK3z\r\nofE8O/1SsPq8gVGtQz4OHuakpfhf2ZqMdIP/3L1rAQm4q7SjkZ8S1RqSMw3H\r\nRbyfkF19tm5XuuFzedqZfuZgCBUiArRjIPg16MkqKhGGnnZz+XS/Fb+mfHCw\r\nD5QJRpP1Cm0PMtm44FyLH499bsKWbbYgl+caOj0X5V565jtLjNQvBoUu0XGY\r\nc/sr/aB/sTL5Cm8fx4sej1FKtHHP80oZmWbIlWQLM9xEVTZ/rUlBpsOMuAWK\r\nO3wLzxo3HIEAnNjThOPNOxVg2IKhmlMEie/VlwMKbnGIotVkbFOvj2Cpw+6M\r\neY3xeXYPTPc59f/m0ZoZ1oyIL0mBe0Z7SobWFUx8RyjYLSkoqQhGhXHhHYMP\r\nY/TLBPrP+NlkhwYmMHC1IZsj881EFM2bvDRUW4u09UI6FPGhBKebPeXsJFBp\r\nGOK2bp7HshBXnJaqUjm1i43XwWcUKuk72+ug3Uo3hZ25mQoLWhqhft3Wst1X\r\n0+BVJd3xpM6S87clwQVowVOypTE8DiVVH2CGxnfImN2BKFKOs4fqEdicu/Mg\r\nac99ayToHsxxeRGGqWL8Y3mBfogb1YHQiYoEu6ljrwDRBOhGGkLDp5lowYgA\r\n+/6//K4ONekHhH0oUGfAHQF+5oXBRbJzorg=\r\n=jVo3\r\n-----END PGP SIGNATURE-----\r\n", "size": 40305}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.2": {"name": "@lezer/common", "version": "1.0.2", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.34.1", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-SVgiGtMnMnW3ActR8SXgsDhw7a0w0ChHSYAyAUxxrOiJ1OqYWEKk/xJd84tTSPo1mo6DXLObAJALNnd0Hrv7Ng==", "shasum": "8fb9b86bdaa2ece57e7d59e5ffbcb37d71815087", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.0.2.tgz", "fileCount": 9, "unpackedSize": 167196, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHzPkQqaN7LZHmFvsLHS/6AnZTA/zmeOu0ydEmz3KY3eAiAcNajWKdDNYk5z9uIbRkacfaLeed+JB3moHwHQaZnMXg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfgVDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9zBAAgQD7gLaOVAlU2TP2dgOxrE/kO0nlsK5zp+mOQ+H+vxBk6jtf\r\n2bXAAD3k07gZHicrmmWOvsEedTbtBreZXigG8BO26DesAis7QniiFKk9K3mF\r\nK+TCZYU5xT98JCp+fKWk2nu1Y0J7vqoiZW5CSUOmfH+R9iCjby25ncugExNy\r\nFbWyeqxbhwwJhXqJ3sDqrkYgGgrbxMdqOAH8W8/ONw0fZypTW7ZbVoViu4y8\r\n0e0B9AKFrnCB8RMb3710wl/zx3qsZb2WJcAQR7xNw8XPoj+kbu/4cGDX4x57\r\nzeCKAD3Xm8gNlJ3ihNRciogjYtQPR6u/kjT5F2IPN9jLQK2pmktf99O74Wrc\r\n4AiDNOkP44firMRWvZBpiY7I2ilK6n6mtG6eTZWqR6vrhFh3YP77cGlNY+3l\r\nK+poSbT7nNv0zjDK4izsFrRxu7MyVf7RA3CIVNFoQhlR1rCR5k1l6skDAsd+\r\n2XZE12yODjPUJAPnDdsVEe0OAm0f9XE93a6Qv7A2rDrWgpPaV5jjWjQVdiyD\r\n5KHu6+w9IaM/xGY3ijuQxrf2rmpezTqGVA9cyefWJp1gMtveoWsTnShkUBVF\r\n5RZEZAttmcGpRC3dRN3BzQMfzMUUTDDvPjH0sUZ7DQ0SVRqfYR5yYw93d0/e\r\nmUEOpwzCwXQPPydsYbDVQYO1SNsP4HwcTuw=\r\n=V9DA\r\n-----END PGP SIGNATURE-----\r\n", "size": 40427}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.3": {"name": "@lezer/common", "version": "1.0.3", "devDependencies": {"ist": "^1.1.1", "rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.34.1", "typescript": "^4.3.4", "@types/mocha": "^5.2.6", "ts-node": "^10.0.0", "mocha": "^9.0.1"}, "directories": {}, "dist": {"integrity": "sha512-JH4wAXCgUOcCGNekQPLhVeUtIqjH0yPBs7vvUdSjyQama9618IOKFJwkv2kcqdhF0my8hQEgCTEJU0GIgnahvA==", "shasum": "1808f70e2b0a7b1fdcbaf5c074723d2d4ed1e4c5", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.0.3.tgz", "fileCount": 9, "unpackedSize": 167725, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpAL1GoA47edV7YDEahY8OipBMQ6vw7qKk/DhgKdXGtwIhALMtEJnEuDtfL5VoRSPFvjNXvCoKxJ/8poZQLgI9X2zD"}], "size": 40560}, "_hasShrinkwrap": false, "publish_time": 1685691671668, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.4": {"name": "@lezer/common", "version": "1.0.4", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0"}, "directories": {}, "dist": {"integrity": "sha512-lZHlk8p67x4aIDtJl6UQrXSOP6oi7dQR3W/geFVrENdA1JDaAJWldnVqVjPMJupbTKbzDfFcePfKttqVidS/dg==", "shasum": "42b371138af846c1fe8ff5facd4360a670e30b4c", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.0.4.tgz", "fileCount": 7, "unpackedSize": 228492, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFM8yHkk6ARfheYFXSQg38HGZEIDW9d1Wn6lDNvFdiUkAiAzsbU/06KmL6vdU7uZFtDxbrZAu+rKSxSiNjsKs+8m3g=="}], "size": 52771}, "_hasShrinkwrap": false, "publish_time": 1692282105553, "_source_registry_name": "default"}, "1.1.0": {"name": "@lezer/common", "version": "1.1.0", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0"}, "directories": {}, "dist": {"integrity": "sha512-XPIN3cYDXsoJI/oDWoR2tD++juVrhgIago9xyKhZ7IhGlzdDM9QgC8D8saKNCz5pindGcznFr2HBSsEQSWnSjw==", "shasum": "2e5bfe01d7a2ada6056d93c677bba4f1495e098a", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.1.0.tgz", "fileCount": 7, "unpackedSize": 232234, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAMMs/8x+0LRKYAYwbuVxtUffbCIGIpygwDqP80irWnUAiEA5ZDWXrYT3fSDsVeZjswi6pjJwLe7XrZcTPWcQAzT74E="}], "size": 54088}, "_hasShrinkwrap": false, "publish_time": 1695122333606, "_source_registry_name": "default"}, "1.1.1": {"name": "@lezer/common", "version": "1.1.1", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0"}, "directories": {}, "dist": {"integrity": "sha512-aAPB9YbvZHqAW+bIwiuuTDGB4DG0sYNRObGLxud8cW7osw1ZQxfDuTZ8KQiqfZ0QJGcR34CvpTMDXEyo/+Htgg==", "shasum": "4a06a0e1b9214d7eb2ea4a9354d47a63044cee49", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.1.1.tgz", "fileCount": 7, "unpackedSize": 236304, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXrI28xI/YwjIvkRRokYD5L+udA1IHzcaFyQnom5wkaAIgTaei10ny9Sarn5oT8xzeFZnvxKxipZvxJcxkRJA2qeU="}], "size": 54929}, "_hasShrinkwrap": false, "publish_time": 1699616349950, "_source_registry_name": "default"}, "1.1.2": {"name": "@lezer/common", "version": "1.1.2", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0"}, "directories": {}, "dist": {"integrity": "sha512-V+GqBsga5+cQJMfM0GdnHmg4DgWvLzgMWjbldBg0+jC3k9Gu6nJNZDLJxXEBT1Xj8KhRN4jmbC5CY7SIL++sVw==", "shasum": "2fc5cd6788094ffc816b539ab2bc55bafacd2abc", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.1.2.tgz", "fileCount": 7, "unpackedSize": 236088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGrsOh3uHnMIOlhDQqDN3AmchlqvFwWM4kifdxebmWueAiEApa8AiM0Vys2n3hwIxf9MS/s++9oAouV88tZVJKeyxdk="}], "size": 54868}, "_hasShrinkwrap": false, "publish_time": 1701971114089, "_source_registry_name": "default"}, "1.2.0": {"name": "@lezer/common", "version": "1.2.0", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0"}, "directories": {}, "dist": {"integrity": "sha512-Wmvlm4q6tRpwiy20TnB3yyLTZim38Tkc50dPY8biQRwqE+ati/wD84rm3N15hikvdT4uSg9phs9ubjvcLmkpKg==", "shasum": "f10493d12c4a196a02ff5fcf5695a516a4039aae", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.2.0.tgz", "fileCount": 7, "unpackedSize": 238774, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCh3YtTIbKMMK2VwDPYYCxlF+PMsyyibuTF63tUWD1duwIhAMhaKzW3v28EaBIUJps1wHOZ6Kw/BKij2isxYSKut9IW"}], "size": 55895}, "_hasShrinkwrap": false, "publish_time": 1703783899754, "_source_registry_name": "default"}, "1.2.1": {"name": "@lezer/common", "version": "1.2.1", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0"}, "directories": {}, "dist": {"integrity": "sha512-yemX0ZD2xS/73llMZIK6KplkjIjf2EvAHcinDi/TfJ9hS25G0388+ClHt6/3but0oOxinTcQHJLDXh6w1crzFQ==", "shasum": "198b278b7869668e1bebbe687586e12a42731049", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.2.1.tgz", "fileCount": 7, "unpackedSize": 238888, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4AVvgLNMwv8xsHEk/JCvlt/3TPyVli/YM4BUsY634ugIhAO3ZlBViPiOIPntDbycL8kN7Zaz1+KFxyTvd1Mz2EGrl"}], "size": 55928}, "_hasShrinkwrap": false, "publish_time": 1705402243237, "_source_registry_name": "default"}, "1.2.2": {"name": "@lezer/common", "version": "1.2.2", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0", "ts-node": "^10.9.2"}, "directories": {}, "dist": {"integrity": "sha512-Z+R3hN6kXbgBWAuejUNPihylAL1Z5CaFqnIe0nTX8Ej+XlIy3EGtXxn6WtLMO+os2hRkQvm2yvaGMYliUzlJaw==", "shasum": "33cb2de75d72602d3ca905cdf7e32049fbe7402c", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.2.2.tgz", "fileCount": 7, "unpackedSize": 239802, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGu5QTwG+1qTn9mNkc44sNi4h8ZRMYt6opwu9sh+C7x6AiEA4Pgjg+5iwhwn7ZWcUsKYLM169PzSEezJfbid0kvVdWY="}], "size": 56110}, "_hasShrinkwrap": false, "publish_time": 1727873003707, "_source_registry_name": "default"}, "1.2.3": {"name": "@lezer/common", "version": "1.2.3", "devDependencies": {"ist": "^1.1.1", "@marijn/buildtool": "^0.1.5", "@types/mocha": "^5.2.6", "mocha": "^10.2.0", "ts-node": "^10.9.2"}, "directories": {}, "dist": {"integrity": "sha512-w7ojc8ejBqr2REPsWxJjrMFsA/ysDCFICn8zEOR9mrqzOu2amhITYuLD8ag6XZf0CFXDrhKqw7+tW8cX66NaDA==", "shasum": "138fcddab157d83da557554851017c6c1e5667fd", "tarball": "https://registry.npmmirror.com/@lezer/common/-/common-1.2.3.tgz", "fileCount": 7, "unpackedSize": 239806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErvAgUIpGakBxOUhL0EH/oC75L6Kn7/O4SHoTzdrNFOAiAMFLZT/NOylw4QQErZK5CnU3GrYJXxP23IUjPhgDehOA=="}], "size": 56112}, "_hasShrinkwrap": false, "publish_time": 1729080339050, "_source_registry_name": "default"}}, "_source_registry_name": "default"}