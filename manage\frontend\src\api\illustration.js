import request from '@/utils/request'

/**
 * 通用图鉴管理 API
 * @param {string} type - 图鉴类型 ('car'|'pet')
 * @returns {Object} API 方法集合
 */
export function createIllustrationAPI(resource) {
  const baseURL = `/${resource}`
  
  return {
    getList: (params) => {
      return request({
        url: `${baseURL}/`,
        method: 'get',
        params
      })
    },
    
    create: (data) => {
      return request({
        url: `${baseURL}/`,
        method: 'post',
        data
      })
    },
    
    update: (id, data) => {
      return request({
        url: `${baseURL}/${id}/`,
        method: 'put',
        data
      })
    },
    
    delete: (id) => {
      return request({
        url: `${baseURL}/${id}/`,
        method: 'delete'
      })
    },
    
    uploadImage: (id, file) => {
      const formData = new FormData()
      formData.append('image', file)
      return request({
        url: `${baseURL}/${id}/upload/`,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },
    
    import: (file) => {
      const formData = new FormData()
      formData.append('file', file)
      return request({
        url: `${baseURL}/import_excel/`,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },
    
    export: (params) => {
      return request({
        url: `${baseURL}/export/`,
        method: 'get',
        params,
        responseType: 'blob'
      })
    },

    uploadImagesByName: (files) => {
      const formData = new FormData()
      files.forEach(file => {
        formData.append('images[]', file)
      })
      return request.post(`${baseURL}/upload_images_by_name/`, formData)
    }
  }
}