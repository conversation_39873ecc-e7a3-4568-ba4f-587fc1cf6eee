{"name": "axios", "dist-tags": {"next": "1.7.0-beta.2", "latest": "1.9.0", "old-version": "0.30.0"}, "versions": {"0.1.0": {"name": "axios", "version": "0.1.0", "description": "Promise based XHR library", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "854e14f2999c2ef7fab058654fd995dd183688f2", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.1.0.tgz", "integrity": "sha512-hRPotWTy88LEsJ31RWEs2fmU7mV2YJs3Cw7Tk5XkKGtnT5NKOyIvPU+6qTWfwQFusxzChe8ozjay8r56wfpX8w==", "signatures": [{"sig": "MEYCIQC/cOvHsV7UqLAet6WE89O4Ga3AUHgkqqoP0riLs6sgTAIhAIrePavu3Uw0T3vLyYMlfEI9bqENYjPzH5jGK8vYQVJK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.3.3-beta2", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.2.0": {"name": "axios", "version": "0.2.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "315cd618142078fd22f2cea35380caad19e32069", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.2.0.tgz", "integrity": "sha512-ZQb2IDQfop5Asx8PlKvccsSVPD8yFCwYZpXrJCyU+MqL4XgJVjMHkCTNQV/pmB0Wv7l74LUJizSM/SiPz6r9uw==", "signatures": [{"sig": "MEQCIAkrijLTtL7uiw0fQf5GL/y7bJ+3J8Z0zrrzNLC5fTXlAiBd4Nr/EJ2nWfBGWv/9OkrAONoboG5C8t8plIt5LVeGQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.3.3-beta2", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.2.1": {"name": "axios", "version": "0.2.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "67d7695440e031286bad9b9b36ae455067f542b8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.2.1.tgz", "integrity": "sha512-yHXyPcLATAEj2H2jI0uZx1vYhygVKHU9Xuzy2r7wJEDZ2FfN46kaMsGONXyLENmEtL/TGRF3YEFFhrbcxUIj9A==", "signatures": [{"sig": "MEYCIQCKJw9s75TZB00eTDhkRDEnEGI2l5hC1Ux9SP0e/GRfWAIhALTqfM9o+NVbTSrocwYbHUB8Ay/sGoFmiew6rsARayW7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.3.3-beta2", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.2.2": {"name": "axios", "version": "0.2.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "e69c16b591e43c248cbbdd089e0babb2ba820cde", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.2.2.tgz", "integrity": "sha512-JSOdzUJ9DolR83lSZLDS47UcRiKZj8ulqr2YRhTLl/k7bSDsrZsRhREu655SAnp5TLJ7203H7zuSIrJSpXTCTw==", "signatures": [{"sig": "MEUCIQDMm5MBF88wVwKncT2Ald3nuT2Za5XuholbcW/ZGkloogIgetfzVc5IgZzlfSwGxiEJioURMYWd6tlrycWqVJQz4+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.3.3-beta2", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.3.0": {"name": "axios", "version": "0.3.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "07cc545f0d39b6b0d168a3b3a55b49f90a9000c7", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.3.0.tgz", "integrity": "sha512-CKKrIWf6d0cfM0LwhhGMYFtdMhxwN7wb3yja+0mJkZPpvX3EkKXPz6m/8UtyLTEuRqBKc1iskmVhAr5O6MQ9gQ==", "signatures": [{"sig": "MEUCIQCFyA7wiJAAcG/jQRqHgoZKVx8qi2RSzyk7qUqSVcRHbgIgDRyAJ6CIMkk9Zxd9vUAi90kTryujcMkHJyVg3s0WY9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.4.0-beta9", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.3.1": {"name": "axios", "version": "0.3.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "7380abe912433fb47ea582bed582300ffe479564", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.3.1.tgz", "integrity": "sha512-a3XHOBXDE7ISk4x11ejZuWgPONrDrjGfJPSKg3vxgQ0b+8gP46DIUhvC/rvif2414joE3bv7F2LUdfTvTiF6SQ==", "signatures": [{"sig": "MEUCIFIEEg/oMPJxDlgNDGYEsaowuzyj6OPGSW08ve74mtXeAiEA0/3hFdW5DePNHlIsZ9LLwuEIyKKTtLsU9eOSgDn5qNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.4.0-beta9", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.4.0": {"name": "axios", "version": "0.4.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "b5918c5c71aaa809f6183d68822c44fb39b7b338", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.4.0.tgz", "integrity": "sha512-EROfAKeqOK3uc9N+W4nhcpxXYuoNfKsQPh0MECK23eJ789/SFpAIz8Av3YmXKRnNRHaXNTBtv+Li1RP4X4TtTg==", "signatures": [{"sig": "MEUCIHhZ5WSz4CANaNhWqnVGvqL9G+3CsGBm2AJg3kTAPCx7AiEA55/SjAbJ+4DhEt3uxzPu888r2plUv4NZ5kAQ7HpOjM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.4.0-beta9", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.4.1": {"name": "axios", "version": "0.4.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "e6a375377d5abd3c4389039240059e08530e1881", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.4.1.tgz", "integrity": "sha512-q80re0sKoYmQVJsaQ6ryT6vy3NwIyofp4LVlSusFQV3L/7SA0I4gwFQE/T3i3u81xuvqNIkyRbi4Ozr/xCvu5w==", "signatures": [{"sig": "MEQCIFDfA2dMjDF5tbXL7Z3fX58WaIN8sHc6DXFoo+WzvDLOAiBkKHW0RJpsW67SNJPnFP6LVvmeKmF9jHASFSxf0vOKZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.4.0-beta9", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.4.2": {"name": "axios", "version": "0.4.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "d31b5752d4885ba460ef0fd1107447b279e6f66b", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.4.2.tgz", "integrity": "sha512-uWHkoTqft48NouJRcauDHals99zKFZG2dDnuyPT4vf+nm17v43RDIdJi3bjYOzvmyBTMP1NKAgGpBYsPCkhsSA==", "signatures": [{"sig": "MEQCICFnnau3rgWnmr8QD4e6A6lj6bsD50PwGbVZjWf5OeTSAiBeHS4iyG2TXh2237XY4c2yrkzVOI7osxSw9zS7JMAGlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^1.0.0"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.4.0-beta9", "grunt-ts": "^1.12.1", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.5.0": {"name": "axios", "version": "0.5.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "2f369e6309a46b182c38ce683ba4fbc608d5b4ef", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.5.0.tgz", "integrity": "sha512-9UXZYc6sa+vxmA3naww9AWbaxDFKmwfyD74ks35UTIS39VUB/ushQYBnXjhih5M2q1iFSFVmbYXt5E6fQmKRmQ==", "signatures": [{"sig": "MEUCIQDhfxybfXuKCUFbX62eXx0VhXFbwgOepACqWnMDhzDU+AIgBSE3p+cvsYoCX1C290/2l780lePE4RGCdKxbxCdw8bY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^2.0.1"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.21", "webpack": "^1.4.0-beta9", "grunt-ts": "^1.12.1", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.1.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.4", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.5.1": {"name": "axios", "version": "0.5.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "63d83686335dc59a57a413b715999691fd0ea43e", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.5.1.tgz", "integrity": "sha512-+ElGOJqyNHXpQnXSY8vSvQYkdELe3vW+jRvkksYHx240QoySshggLoRrJOeAYPBqEQIlh3efSt3ghq+9AP8z6w==", "signatures": [{"sig": "MEUCIEVEafTcV0WGjM6OH2Z4ezo55FYz2kfshxOrdMDLZo5MAiEAsRZZX+02T3KDPDQrC0Vl6uD5WcdrVlVfPDwK0A7ftRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^2.0.1"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.31", "webpack": "^1.4.0-beta9", "grunt-ts": "^1.12.1", "grunt-karma": "^0.8.3", "grunt-banner": "^0.2.3", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.3.5", "load-grunt-tasks": "^0.6.0", "grunt-update-json": "^0.1.3", "karma-jasmine-ajax": "^0.1.11", "webpack-dev-server": "^1.4.10", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.5.2": {"name": "axios", "version": "0.5.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "eb6009d000d81067cf0568dbac847e105525013a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.5.2.tgz", "integrity": "sha512-g7gJmNPxRiwetBd6E6P9Kr/phWFYe6edLxKnit+Twrd4XTW4YX9eH783641JWjOPQmOPZvw6dqLkwCG4hj3r4A==", "signatures": [{"sig": "MEUCIQCaHgp0UtB4LveIH2i8ruEQP6Owpor32Mv9/aRr9oWEEAIgJ1QWYyyGxP7/zOT8eIoB51ZiSTf3vxk4levvc+9Lj1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^2.0.1"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.31", "webpack": "^1.7.2", "grunt-ts": "^3.0.0", "grunt-karma": "^0.10.1", "grunt-banner": "^0.3.1", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.3.5", "load-grunt-tasks": "^3.1.0", "grunt-update-json": "^0.2.1", "karma-jasmine-ajax": "^0.1.12", "webpack-dev-server": "^1.7.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.5.3": {"name": "axios", "version": "0.5.3", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "a4d153cc753f430a14aad8a00d8b1017eeabf1a5", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.5.3.tgz", "integrity": "sha512-lQ3Mpyr4VFsIWqe9Yj/4LQ3aUoIzsB4E8T78AAnapiFqaIemOiBq0hkt5U+dQ0lyGd8Uw7cbeXCC+0kZI1k+DA==", "signatures": [{"sig": "MEQCICzA6w2nChzOFDGfgqubosC1COOETf70lhaM+vlDZPJ7AiBf4HfijEFZMoIOspzo4+nQvQUs1szh7TrorASgpXel4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^2.0.1"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.31", "webpack": "^1.7.2", "grunt-ts": "^3.0.0", "coveralls": "^2.11.2", "grunt-karma": "^0.10.1", "grunt-banner": "^0.3.1", "grunt-eslint": "^9.0.0", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.3.5", "karma-webpack": "^1.5.0", "karma-coverage": "^0.2.7", "load-grunt-tasks": "^3.1.0", "grunt-update-json": "^0.2.1", "karma-jasmine-ajax": "^0.1.12", "webpack-dev-server": "^1.7.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.5.4": {"name": "axios", "version": "0.5.4", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "e06f852257838133e69094d925ccb419de94fdfb", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.5.4.tgz", "integrity": "sha512-98BQ+iyqX2IoI/P24W5E+1xbUSChUAWJlCU5uLa4aBgOeXYotSn0VcHwY1ETE+jxd7lgzDOCwYB1uYw83ViHKw==", "signatures": [{"sig": "MEQCIEQ7UZW6xwUGy/ctOdxW7muiYhhAP6RBjuAs/yYw5BoSAiBffacJ2cjwbsR3VArE1c8RQpNrqK+wpg2svwXSJBFheA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"es6-promise": "^2.0.1"}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.12.31", "webpack": "^1.7.2", "grunt-ts": "^3.0.0", "coveralls": "^2.11.2", "grunt-karma": "^0.10.1", "grunt-banner": "^0.3.1", "grunt-eslint": "^9.0.0", "grunt-webpack": "^1.0.8", "karma-jasmine": "^0.3.5", "karma-webpack": "^1.5.0", "karma-coverage": "^0.2.7", "load-grunt-tasks": "^3.1.0", "grunt-update-json": "^0.2.1", "karma-jasmine-ajax": "^0.1.12", "webpack-dev-server": "^1.7.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-phantomjs-launcher": "^0.1.4"}, "hasInstallScript": false}, "0.6.0": {"name": "axios", "version": "0.6.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "5f3b9bc5557f9d804547501f1a227eaa3216cad2", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.6.0.tgz", "integrity": "sha512-4dNYK95y3vVytreh3QNNPIYaxbkZV9cXOxsseqznEwWqnHUSz1QSSKONP3Wbxg3wzZcXgwltVv3ca6Veg0eczA==", "signatures": [{"sig": "MEQCIA7lY6u5LcsSsmW91I2dOvsPv1A9b7xjXLD78SZWPB5uAiBIk6asPZI9Fs1YOpNhpK0Lgs/vNbMGMqa8FLp9JAklEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"grunt": "^0.4.5", "karma": "^0.13.8", "webpack": "^1.11.0", "grunt-ts": "^5.0.0-beta.5", "minimist": "^1.1.3", "coveralls": "^2.11.3", "es6-promise": "^3.0.2", "grunt-karma": "^0.12.0", "grunt-banner": "^0.5.0", "grunt-eslint": "^17.1.0", "jasmine-core": "^2.3.4", "grunt-webpack": "^1.0.11", "karma-jasmine": "^0.3.6", "karma-webpack": "^1.7.0", "karma-coverage": "^0.5.0", "load-grunt-tasks": "^3.2.0", "grunt-update-json": "^0.2.1", "karma-jasmine-ajax": "^0.1.12", "webpack-dev-server": "^1.10.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-nodeunit": "^0.4.1", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1"}, "hasInstallScript": false}, "0.7.0": {"name": "axios", "version": "0.7.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "489c269044d5066dfa2c64c749cb131b176f4a7a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.7.0.tgz", "integrity": "sha512-5X9Fbhy/McI95kWcapRjYA9Kg1wpYgLSniWKCdiIXvm94dcHD967lRaUfyG8EsOrF5SvSeYqdWoXbON0Sr1MPA==", "signatures": [{"sig": "MEYCIQC8zSyFfU5KWkpppVYucw96DxsG9CvrMrFkHOwgZkwcaQIhAJz3hU9VZA0j7clwjuI0pN1EHM2ToKlyaqDWSiA+3O5d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.10", "webpack": "1.12.2", "grunt-ts": "5.0.0-beta.5", "minimist": "1.2.0", "coveralls": "2.11.4", "grunt-cli": "0.1.13", "phantomjs": "1.9.18", "es6-promise": "3.0.2", "grunt-karma": "0.12.1", "grunt-banner": "0.5.0", "grunt-eslint": "17.2.0", "jasmine-core": "2.3.4", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.6", "karma-webpack": "1.7.0", "karma-coverage": "0.5.2", "load-grunt-tasks": "3.3.0", "grunt-update-json": "0.2.1", "karma-jasmine-ajax": "0.1.13", "webpack-dev-server": "1.12.0", "grunt-contrib-clean": "0.6.0", "grunt-contrib-watch": "0.6.1", "grunt-contrib-nodeunit": "0.4.1", "karma-sourcemap-loader": "0.3.5", "karma-phantomjs-launcher": "0.2.1"}, "hasInstallScript": false}, "0.8.0": {"name": "axios", "version": "0.8.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "9c3648c395f704742845b7d70e5b7c11f9afd859", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.8.0.tgz", "integrity": "sha512-Yerc9ZI3+OsqkHSdH+YF2RLAC30lQvdLfiVzxyBJ6DeB0GyxzNHlrWNo3juVSMC7Al/s5iwJT2aATOa2lARKuw==", "signatures": [{"sig": "MEQCICNh3OFVlytTWjSOd9gTkAr67vkJRv3Jk/THYBCFUFJXAiApK0skSeMoR/Mt/V+NQ6YKxbnrXepgpgW+eno7+nCEyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.10", "webpack": "1.12.2", "grunt-ts": "5.0.0-beta.5", "minimist": "1.2.0", "coveralls": "2.11.4", "grunt-cli": "0.1.13", "phantomjs": "1.9.18", "es6-promise": "3.0.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.5.0", "grunt-eslint": "17.2.0", "jasmine-core": "2.3.4", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.6", "karma-webpack": "1.7.0", "karma-coverage": "0.5.2", "load-grunt-tasks": "3.3.0", "grunt-update-json": "0.2.1", "karma-jasmine-ajax": "0.1.13", "webpack-dev-server": "1.12.0", "grunt-contrib-clean": "0.6.0", "grunt-contrib-watch": "0.6.1", "grunt-contrib-nodeunit": "0.4.1", "karma-sourcemap-loader": "0.3.5", "karma-phantomjs-launcher": "0.2.1", "istanbul-instrumenter-loader": "^0.1.3"}, "hasInstallScript": false}, "0.8.1": {"name": "axios", "version": "0.8.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "e0eafec0f346139527dc3b79fdcbff8034a24045", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.8.1.tgz", "integrity": "sha512-YnMreaDO4t7xufT7xeGyetgG/UTifuwzJtcCEa3QnV9RHiIkN5Be/euRHdP8g4TFL38aPxfIfpndc68VPwKeYQ==", "signatures": [{"sig": "MEUCIQCy1d0msZBK+ibqIF85MuALb4Shqou//8/mS2rkiW9x5gIgAQOYef/ZczRlbQxkcNfQYriyxI+9oskJAlpVxexj+OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.10", "webpack": "1.12.2", "grunt-ts": "5.0.0-beta.5", "minimist": "1.2.0", "coveralls": "2.11.4", "grunt-cli": "0.1.13", "phantomjs": "1.9.18", "es6-promise": "3.0.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.5.0", "grunt-eslint": "17.2.0", "jasmine-core": "2.3.4", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.6", "karma-webpack": "1.7.0", "karma-coverage": "0.5.2", "load-grunt-tasks": "3.3.0", "grunt-update-json": "0.2.1", "karma-jasmine-ajax": "0.1.13", "webpack-dev-server": "1.12.0", "grunt-contrib-clean": "0.6.0", "grunt-contrib-watch": "0.6.1", "grunt-contrib-nodeunit": "0.4.1", "karma-sourcemap-loader": "0.3.5", "karma-phantomjs-launcher": "0.2.1", "istanbul-instrumenter-loader": "^0.1.3"}, "hasInstallScript": false}, "0.9.0": {"name": "axios", "version": "0.9.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "89544ac5f55bc94f576db4e663d7530cb4f87d14", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.9.0.tgz", "integrity": "sha512-4KrsxDvkqNxUoN8j0+Nbi2AZ74iylHbe7PaHxrntf2+tX283zFC5/24WGTeItjH4jzkhZLBTUxRbicwIzyRk0A==", "signatures": [{"sig": "MEUCIDiPN76+kJSzkE7fGJvjd8vvNIFMpdGLXLDH4E7SC7dSAiEAz4kn3PfysHKL28GvMzgXbj/cGjr1gXiUK8iAwRXCiVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.19", "webpack": "1.12.11", "grunt-ts": "5.3.2", "minimist": "1.2.0", "coveralls": "2.11.6", "grunt-cli": "0.1.13", "phantomjs": "1.9.19", "es6-promise": "3.0.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.6.0", "grunt-eslint": "17.3.1", "jasmine-core": "2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.6", "karma-webpack": "1.7.0", "karma-coverage": "0.5.3", "load-grunt-tasks": "3.4.0", "grunt-update-json": "0.2.2", "karma-jasmine-ajax": "0.1.13", "webpack-dev-server": "1.14.1", "grunt-contrib-clean": "0.7.0", "grunt-contrib-watch": "0.6.1", "grunt-contrib-nodeunit": "0.4.1", "karma-sourcemap-loader": "0.3.7", "karma-phantomjs-launcher": "0.2.3", "istanbul-instrumenter-loader": "^0.1.3"}, "hasInstallScript": false}, "0.9.1": {"name": "axios", "version": "0.9.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "95608b16447ee29b033589854c3fc7ee2c06bf6e", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.9.1.tgz", "integrity": "sha512-AG+MFLQo7wB9RcIos3MvbnGpaHzf3ZgiI9IRlwtepdyysB0KPxKh18Bw6tztd4ZRjpj6wW/PdTolzYfPzspfKA==", "signatures": [{"sig": "MEYCIQCuoMxHcQbY6x4Acjxs+kJaXaX8rEBIgUBCB1/2K91D7QIhAIiUe2QPlD0J1uTYipIqO38jDXZHNaVcS+gO/xDGXKbZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.19", "webpack": "1.12.11", "grunt-ts": "5.3.2", "minimist": "1.2.0", "coveralls": "2.11.6", "grunt-cli": "0.1.13", "phantomjs": "1.9.19", "es6-promise": "3.0.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.6.0", "grunt-eslint": "17.3.1", "jasmine-core": "2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.6", "karma-webpack": "1.7.0", "karma-coverage": "0.5.3", "load-grunt-tasks": "3.4.0", "grunt-update-json": "0.2.2", "karma-jasmine-ajax": "0.1.13", "webpack-dev-server": "1.14.1", "grunt-contrib-clean": "0.7.0", "grunt-contrib-watch": "0.6.1", "grunt-contrib-nodeunit": "0.4.1", "karma-sourcemap-loader": "0.3.7", "karma-phantomjs-launcher": "0.2.3", "istanbul-instrumenter-loader": "^0.1.3"}, "hasInstallScript": false}, "0.10.0": {"name": "axios", "version": "0.10.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "5b0ec0d5fb53e79b98b7bf84c0e9b1cf902fdfc4", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.10.0.tgz", "integrity": "sha512-sO0TPp3bUhC63P9qu7AvQgqvovFdtugBjnIm9OJS/ylRL+ojNo3i4FwdcmUievEr3ZtuIB3AYRDrZfThp1vVkw==", "signatures": [{"sig": "MEUCIQDs8vVkEoStll6OaCjiLCwM0Poso/yQ66UPNmdQtZfcBwIgb32tbf/Nbs+w4cLC1kPXoEF0/nvpfQBQ6toBlsDJkR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.21", "sinon": "1.17.3", "webpack": "1.12.14", "grunt-ts": "5.3.2", "minimist": "1.2.0", "coveralls": "2.11.8", "grunt-cli": "0.1.13", "es6-promise": "3.1.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.7", "karma-webpack": "1.7.0", "karma-coverage": "0.5.4", "load-grunt-tasks": "3.4.1", "karma-jasmine-ajax": "0.1.13", "phantomjs-prebuilt": "2.1.6", "webpack-dev-server": "1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^0.3.0", "karma-sauce-launcher": "^0.3.1", "karma-chrome-launcher": "^0.2.2", "karma-safari-launcher": "^0.1.1", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "0.3.7", "karma-phantomjs-launcher": "1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.11.0": {"name": "axios", "version": "0.11.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "50adc59bd0f11bee89a383b24b2d407648e6d6e8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.11.0.tgz", "integrity": "sha512-ofeYJ1HlpKXE6Oa/hVkcYwDsMBlmTGCZwypNUY3SnxwVe8ZQyd8Gje+6bPKG3NEUtUdUwwhVaeJkWWNW5KlOXw==", "signatures": [{"sig": "MEQCIHPEoHhywOnzt0th3VbxmqCGqcb/NW02QR/bEga60UH9AiA8EDqGUWJ8nc/4P6Gyd1MuahlWP2stB6FiDtNeri/QJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.21", "sinon": "1.17.3", "webpack": "1.12.14", "grunt-ts": "5.3.2", "minimist": "1.2.0", "coveralls": "2.11.8", "grunt-cli": "0.1.13", "es6-promise": "3.1.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.7", "karma-webpack": "1.7.0", "karma-coverage": "0.5.4", "load-grunt-tasks": "3.4.1", "karma-jasmine-ajax": "0.1.13", "phantomjs-prebuilt": "2.1.6", "webpack-dev-server": "1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^0.3.0", "karma-sauce-launcher": "^0.3.1", "karma-chrome-launcher": "^0.2.2", "karma-safari-launcher": "^0.1.1", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "0.3.7", "karma-phantomjs-launcher": "1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.11.1": {"name": "axios", "version": "0.11.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "39cdb65813e2c549d1c2e9c389f7e33aa65cca22", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.11.1.tgz", "integrity": "sha512-qNx0yZwWXV1HbQBNA41uGPr8l6gcZCbDWQwmT1DSCKfr9e39Dd5e4hPLWkF6uMP84n8ckkVPfF6+E92wMzJzkQ==", "signatures": [{"sig": "MEUCIQD/Jb3W6zwNMKgxPsGZo/B3RPA24faxTDTMLUAuT61E7AIgHoFXeWnZ7OGMj/S08u41JeHOOyQ9VUwGxg0jYA1UHMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.21", "sinon": "1.17.3", "webpack": "1.12.14", "grunt-ts": "5.3.2", "minimist": "1.2.0", "coveralls": "2.11.8", "grunt-cli": "0.1.13", "es6-promise": "3.1.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.7", "karma-webpack": "1.7.0", "karma-coverage": "0.5.4", "load-grunt-tasks": "3.4.1", "karma-jasmine-ajax": "0.1.13", "phantomjs-prebuilt": "2.1.6", "webpack-dev-server": "1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^0.3.0", "karma-sauce-launcher": "^0.3.1", "karma-chrome-launcher": "^0.2.2", "karma-safari-launcher": "^0.1.1", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "0.3.7", "karma-phantomjs-launcher": "1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.12.0": {"name": "axios", "version": "0.12.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "b907b0221cc34ec1c9fac18ec7f07ddf95785ba4", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.12.0.tgz", "integrity": "sha512-FyH6bSfRAKChMa6yvHVFcnaBj6zcbKFCZMvNsG+q0r+n2XplEIhxu6JPq73I6wL196aAzUxUYktcayWKAlbiPQ==", "signatures": [{"sig": "MEUCIFugf0p4uEgg4Je9Jh2SkU5IapK29LnYaf4z5dWO+hicAiEAp5GuBmbmumH1MGSWjcV4gw970YY7aMxsHiSHxy7XHaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "0.13.21", "sinon": "1.17.3", "webpack": "1.12.14", "grunt-ts": "5.3.2", "minimist": "1.2.0", "coveralls": "2.11.8", "grunt-cli": "0.1.13", "es6-promise": "3.1.2", "grunt-karma": "0.12.1", "karma-sinon": "1.0.4", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "0.3.7", "karma-webpack": "1.7.0", "karma-coverage": "0.5.4", "load-grunt-tasks": "3.4.1", "url-search-params": "0.5.0", "karma-jasmine-ajax": "0.1.13", "phantomjs-prebuilt": "2.1.6", "webpack-dev-server": "1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^0.3.0", "karma-sauce-launcher": "^0.3.1", "karma-chrome-launcher": "^0.2.2", "karma-safari-launcher": "^0.1.1", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "0.3.7", "karma-phantomjs-launcher": "1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.13.0": {"name": "axios", "version": "0.13.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "b3b5243ef7e67794fc951bef0298d0bab29ffd54", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.13.0.tgz", "integrity": "sha512-C4zDEKQOVsOrkKzLwK8j0gPgTjoHhRgPIh5/2lsixUfwCjh8TyivrmKeRVtWPc01myiF7efEsrHqIUXKBsAnVA==", "signatures": [{"sig": "MEQCIBzKMqrz19m2yDOtitbH8UfoAvWY8rgYNKbS0nJC1mX7AiAFdhqMBNMU5KV+GOwEjD3QZl8q2KhdY1t0ctgw9CvvkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "^0.13.22", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "5.3.2", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "0.1.13", "es6-promise": "^3.2.1", "grunt-karma": "0.12.1", "karma-sinon": "^1.0.5", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "3.4.1", "url-search-params": "^0.5.0", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "karma-chrome-launcher": "^1.0.1", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.13.1": {"name": "axios", "version": "0.13.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "3e67abfe4333bc9d2d5fe6fbd13b4694eafc8df8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.13.1.tgz", "integrity": "sha512-tPsmD7JWiEcoiKNSGDKKOF3w4QMzOlZygUpcT7g/N+7viNpxz8rDizTHvYZPobB7xgJUfSzQWj+ufQC7ISfO1w==", "signatures": [{"sig": "MEUCIQD6ABqSiBaG4XYFP1TUUIIGYVhKMNE7SPA5hLwy/LQShAIgJg/wJ5fdeZYhtfooKiRyqV++Be9CygboNE51okN3wnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "^0.13.22", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "5.3.2", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "0.1.13", "es6-promise": "^3.2.1", "grunt-karma": "0.12.1", "karma-sinon": "^1.0.5", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "1.0.11", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "3.4.1", "url-search-params": "^0.5.0", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "karma-chrome-launcher": "^1.0.1", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.14.0": {"name": "axios", "version": "0.14.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "40f24f2f4e913b9faa43d3a7b2e40ab8729afa90", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.14.0.tgz", "integrity": "sha512-ezl41/VugKTpB5D7g3KjZOHzcAIlKsoDPC5HqxNxemZsLxuMUhP5oQymOD6rCVVyQQP0RuIcuZXl1iMQkWxrhw==", "signatures": [{"sig": "MEUCIQDRt9jbq0nLPg2KUFkUg/SCJOylWIY9UMygd001T/R8UQIgA7kOx0TZlQFEbkxR8nB2UaCAAmLV+/IxLJ4PbR8BU+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "^0.13.22", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "5.3.2", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "0.1.13", "es6-promise": "^3.2.1", "grunt-karma": "0.12.1", "karma-sinon": "^1.0.5", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "^2.4.1", "grunt-typings": "0.1.5", "grunt-webpack": "1.0.11", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "3.4.1", "url-search-params": "^0.5.0", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "karma-chrome-launcher": "^1.0.1", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.15.0": {"name": "axios", "version": "0.15.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "69a4cbe8646866a22f1075048c41724ecef447ce", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.15.0.tgz", "integrity": "sha512-42XIldb+kzlBR3oD8DbEBUuig+XxZFURv7qlecDANa+kabl6tkiJT2rBdR/Phnby+iSbOYbiSRx6j5+ozoOr+Q==", "signatures": [{"sig": "MEUCIQCBWieXtuz6HCen9zu7r9JuHkYto1gNiHmGAhlsETl8wQIgTqnOqprIkzM/dnuOrX2z18BbdiHonJ19nrawQWhgxXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "^0.13.22", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "5.3.2", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "0.1.13", "es6-promise": "^3.2.1", "grunt-karma": "0.12.1", "karma-sinon": "^1.0.5", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "^2.4.1", "grunt-typings": "0.1.5", "grunt-webpack": "1.0.11", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "3.4.1", "url-search-params": "^0.5.0", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "karma-chrome-launcher": "^1.0.1", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.15.1": {"name": "axios", "version": "0.15.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "9395b9ba25005e478dfd7239e8c4345ff10cd85b", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.15.1.tgz", "integrity": "sha512-gkubWPcUBrfRGmANiUcWJnbA7oyhYOkWzuXWzuIB0v/LPaNqlKE1Ke9jr8t1f2+vy0k5zMQzwqehjw07nPvBeg==", "signatures": [{"sig": "MEUCIDUK+aSg60WSYmByUVW6r5aAJmc/JCumMTJivpxJ3/utAiEApu24LTEYr6Jpivsyjc1f0YFIGNYyPh1dpmPNz5dT2zo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "^0.13.22", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "5.3.2", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "0.1.13", "es6-promise": "^3.2.1", "grunt-karma": "0.12.1", "karma-sinon": "^1.0.5", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "^2.4.1", "grunt-typings": "0.1.5", "grunt-webpack": "1.0.11", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "3.4.1", "url-search-params": "^0.5.0", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "karma-chrome-launcher": "^1.0.1", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.15.2": {"name": "axios", "version": "0.15.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "496f50980b2ce1ad2e195af93c2d03b4d035e90d", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.15.2.tgz", "integrity": "sha512-RCFd+A1fO/DVvW7NvbMN7qjADYnRMQAl/sEOARHPV7g+vxye4W3WOKQ5eVC2iuFB9H9Il6v9cSVRB0sqfFPUHw==", "signatures": [{"sig": "MEUCICKckteXc4xaV6uDr0sLH1xqz6dCLyiw1EWcDT5Rw1D6AiEAgGutMrNM+dWa7fCU9KgRmgxImQb8nrn7Sr8K/YU2Eps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "0.0.7"}, "devDependencies": {"grunt": "0.4.5", "karma": "^0.13.22", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "5.3.2", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "0.1.13", "es6-promise": "^3.2.1", "grunt-karma": "0.12.1", "karma-sinon": "^1.0.5", "grunt-banner": "0.6.0", "grunt-eslint": "18.0.0", "jasmine-core": "^2.4.1", "grunt-typings": "0.1.5", "grunt-webpack": "1.0.11", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "3.4.1", "url-search-params": "^0.5.0", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "1.0.0", "grunt-contrib-watch": "0.6.1", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.0.0", "karma-chrome-launcher": "^1.0.1", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^0.2.0"}, "hasInstallScript": false}, "0.15.3": {"name": "axios", "version": "0.15.3", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "2c9d638b2e191a08ea1d6cc988eadd6ba5bdc053", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.15.3.tgz", "integrity": "sha512-w3/VNaraEcDri16lbemQWQGKfaFk9O0IZkzKlLeF5r6WWDv9TkcXkP+MWkRK8FbxwfozY/liI+qtvhV295t3HQ==", "signatures": [{"sig": "MEUCIQDp4lWfSUdVB6FFbexeG6IvQwy0Sre4hs0XMMfZNAzHzQIgAk9eJ4wNKRHKeq/5tG/6skH4UMSoFQtX0oevhIbwLgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "1.0.0"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-typings": "^0.1.5", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^1.0.0"}, "hasInstallScript": false}, "0.16.0": {"name": "axios", "version": "0.16.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "6ed9771d815f429e7510f2838262957c4953d3b6", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.16.0.tgz", "integrity": "sha512-JMN6DA1EOVdjjCqiP5Ff/kByHDcMv6WtYBOf6pX1AhBA7g5wpzBf5dt3H1/30YRogmhk+hcF9OfpeI/TkPIFGw==", "signatures": [{"sig": "MEQCIHqxReBzftbs8SJXY3ElqY/HTe0pVCacL2OhkGgs7s8oAiB4IIu5jN7fDzCNNMoglWEeU1STk6pYEMd3dOtZHPhWJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "1.0.0"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^1.0.0"}, "hasInstallScript": false}, "0.16.1": {"name": "axios", "version": "0.16.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "c0b6d26600842384b8f509e57111f0d2df8223ca", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.16.1.tgz", "integrity": "sha512-dm8XNlNAD6yRqbxfzK3OBtNs/953XqC6sns+c2jtk8Q1u24ZYPVfQUwtAFJ/PBBSDmafcCg5C3DCJjReOFFRZQ==", "signatures": [{"sig": "MEUCIQCvSr4/LOAdVS8NvD6IAGcUY2JMLpZltyRHnrzmbwO2tQIgc/zDn95uAj1UvdOmJAnOfFZRWlyA8D3zsVYYAZ/V9zQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"follow-redirects": "^1.2.3"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^1.0.0"}, "hasInstallScript": false}, "0.16.2": {"name": "axios", "version": "0.16.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "ba4f92f17167dfbab40983785454b9ac149c3c6d", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.16.2.tgz", "integrity": "sha512-IMYFDrcVbUksQhsMYtWCM6KdNaDpr1NY56dpzaIgj92ecPVI29bf2sOgAf8aGTiq8UoixJD61Pj0Ahej5DPv7w==", "signatures": [{"sig": "MEUCIDUKZPV87aenpMfLRXZryztKVvIBw6XM7Q4CIpfTH0x8AiEAwEPHZ0KWGtL+hFtHDe+/ObQtgVXM0/Q4txM4DGtbmng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"is-buffer": "^1.1.5", "follow-redirects": "^1.2.3"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^1.0.0"}, "hasInstallScript": false}, "0.17.0": {"name": "axios", "version": "0.17.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "7da747916db803f761651d6091d708789b953c6a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.17.0.tgz", "integrity": "sha512-n3PCq2tdo7y4/zEj7JkxoBWopOSIR/m6hjKaaXAgbZhCK1HcbEPkYlbtyyA1hfry/neSHAo7LRrXSystUGrXXg==", "signatures": [{"sig": "MEYCIQD427125dvXs6pNDuyI1JJ/So22PvED885WpbPfhDTI3QIhAJth82czYcIN2jHTWZazEmgfO7OPY9PO0tyvx4U8d2kX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"is-buffer": "^1.1.5", "follow-redirects": "^1.2.3"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "bundlesize": "^0.5.7", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^1.0.0"}, "hasInstallScript": false}, "0.17.1": {"name": "axios", "version": "0.17.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "2d8e3e5d0bdbd7327f91bc814f5c57660f81824d", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.17.1.tgz", "integrity": "sha512-mZzWRyJeJ0rtK7e1/6iYBUzmeXjzei+1h1IvbedyU0sB52++tU5AU6r6TLXpwNVR0ebXIpvTVW+9CpWNyc1n8w==", "signatures": [{"sig": "MEUCIAry6m0nY1dCj8zANWP6U2v8A5kl2rdqJ2wBoN3Fqw/6AiEAyQASvWyvHkYm2tuoyguzLPOqqpFrKlAfUohjilJQncE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "directories": {}, "dependencies": {"is-buffer": "^1.1.5", "follow-redirects": "^1.2.5"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "bundlesize": "^0.5.7", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "istanbul-instrumenter-loader": "^1.0.0"}, "hasInstallScript": false}, "0.18.0": {"name": "axios", "version": "0.18.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "32d53e4851efdc0a11993b6cd000789d70c05102", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.18.0.tgz", "fileCount": 40, "integrity": "sha512-14hgP2oTu6SPu+26Ofye6Se8u5Mmjc07a0ACHTJ5POKFU1Mtxz2IxSvaWy1O+QnbSa8XHy1gYz2E1l+G26XJdA==", "signatures": [{"sig": "MEYCIQC4CNSUQ4q5fU8CNyCcN5jjsj0JYN6orkJfJnaVw6avtgIhAIfVJGNMrNwXRm4nK31JU9vE0Ajow3mfrugePyijXvrS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 307840}, "directories": {}, "dependencies": {"is-buffer": "^1.1.5", "follow-redirects": "^1.3.0"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "bundlesize": "^0.5.7", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.19.0-beta.1": {"name": "axios", "version": "0.19.0-beta.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Beta version should not be used anymore", "dist": {"shasum": "3d6a9ee75885d1fd39e108df9a4fb2e48e1af1e8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.19.0-beta.1.tgz", "fileCount": 40, "integrity": "sha512-Dizm4IyB5T9OrREhPgbqUSofTOjhNJoc+CLjUtyH8SQUyFfik777lLjhl9cVQ4oo3bykkPAN20rxmY1o5w0jrw==", "signatures": [{"sig": "MEYCIQD/psvvYz2bVCH5oPxL78U6+0oW0NFDYcj8F91wmeRm3QIhAOkQumtgsvJ6BjKLeJ86ggs6kpVptjkWIj6GpXp0ArOh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbIuJCRA9TVsSAnZWagAAnvUP/jEw0BG15xpUqzV1Z3IQ\nk9UMtbNc9ZXMtIgjjMqIEic1WCr5H9GZ62FVF1pOTX5YlaCWgaYqzOTCvj5w\no+hgxMgMcyUIx6QsdhL6JjMstI/XWigVPnKUTqYhEtFJZML2c4g1hwxXJci7\nJeTcREn7u+WnRFM1Qf88q4Dqn355rHNuJwFq3Nvqo8SEQfbDOwSafGXuniXQ\nlnFjlGT3cmUK08TBeBQGq9zRsSP3BBXFuIKLwVHyPfFf0uufWuylnU1kjf5s\nbyzarKlK1jguIa6r/jkVpP66uxgLWdN04Cr0N7sWs0fy68C3dRpp1Gm/6Oak\nvp2lLePxPekzpAV9vAxwNfhoLLrV3c/Q7ZncODQLKyA4D9Byg3pvYHySpH1+\nH3AgZZt50pkUSVuJ9pqrWqz0g/AL5loMlhmm9mz8wvOewjA/9MMnUk0pHF3e\nLxwnLn3ZXk8mcsg70kp81hZFG14XQYasF7lnn3wyem3YKqkQsuV4f9iYvVoT\nNdyNeiJpDkRUK7dW3GUUhfH0MxTxBUO2bcUGQEo8EKFrPgrPAemTMSr7lhCZ\n5W5UFLuztilsEIGoY62Qr2N2/ACie0a2KYaFrEHoUobGVw8RFBJeRttZ3crq\nyR3rLdVPQelsZJeOmtHTg8TTlld+pzAnBdwmgCRS5vdjIXa9COo68kJThtkY\ni1G7\r\n=Fgzx\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"is-buffer": "^2.0.2", "follow-redirects": "^1.4.1"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.19.0": {"name": "axios", "version": "0.19.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "8e09bff3d9122e133f7b8101c8fbdd00ed3d2ab8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.19.0.tgz", "fileCount": 40, "integrity": "sha512-1uvKqKQta3KBxIz14F2v06AEHZ/dIoeKfbTRkK1E5oqjDnuEerLmYTgJB5AiQZHJcljpg1TuRzdjDR06qNk0DQ==", "signatures": [{"sig": "MEUCIQCvZo4iZzYOqR9jDd5QpDbqU3L8vZ5BUsvA0GpJC6LRWAIgU8w6KLCbdgZ+5nAkse57rLFESKjO9+mvL6LXw7KM1oQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8AEdCRA9TVsSAnZWagAA7oAQAIoxS4ZIzihXpGVDroAw\nl3qo8ZsDey8n+C3rEr0z11GX7/JY3bxbnyyLrj62v0zJ+zg2m/1G+qJD9wYG\nUo84LVDlNlRoOzGvlAxt0VIjLoQhiMEcARAGOb3cQLH5Cj7olIa6RIP6E3Bw\nsfM1TSUEXZ5Qpc4HKRrIMtJ8ZHZ+ml0/YhzZBYYDXHCX9SCM8Sceb/nunSOI\n9Ui5Ulrturl9lk3POfBj6VnpKljZ02EKmifVHGjAAqsPFo5QSi9CYYLSc6Mo\nlWy/cE4P56uwshwEobi3RKiymQ0FOOOj5FqHiNSCA6tP/IVTPdThlMLBv4ru\n9WoP7N+LRm5MKbyD9+QctOvZNwACY/sY0xMIZeBAxj5sIyigiCLxn/KUk0tt\nJ3wPXc4cqRyqIppGVnEnJE5QKfre12Ebnqslhai7YW2MbWjjB2vXK4wWwn1m\nNU+I5H39vFYLvsDGuGuPNr5+0KtyrIlCDi0nm8GTa283QuebaccGvQzuGlm/\nukoVzkPp24akPjHOUyhpc+2WIQA/mMC1yUD1S9thQ6InmetMAj/7XE5SZAY4\nkVBzRr25/LwhY2ZAAtX9gjjJsNQ9agw7kPe7Lkt0+1Flob9gH9GJftbEEyWe\nnW/Ol48L6F+c/NK3B9hGHEqyJWc5mBnfDMOvbYnrzNNPBvlz3j/WOCvKQwj5\nW2Db\r\n=eIur\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"is-buffer": "^2.0.2", "follow-redirects": "1.5.10"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.18.1": {"name": "axios", "version": "0.18.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "ff3f0de2e7b5d180e757ad98000f1081b87bcea3", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.18.1.tgz", "fileCount": 39, "integrity": "sha512-0BfJq4NSfQXd+SkFdrvFbG7addhYSBA2mQwISr46pD6E5iqkWg02RAs8vyTT/j0RTnoYmeXauBuSv1qKwR179g==", "signatures": [{"sig": "MEUCIQDi7yC9PDuskFqy/4M+5lXN3sLBXIOOzyZSD+BRkUBy6wIgBoTIYX54wEAZhIksPDQq7QvZW4GOj4SlSPAI/iJSsuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8crsCRA9TVsSAnZWagAAMs0QAKSsAzOc8kUxnYJ6E0Pd\nRbKe8SPjO6U/loW+Y5YTIxs81QhTqTq+fFxA8RAnQAB6/DRXrpjPFbqv63iQ\niygsQod95GRUa4MwJnL4a6EEM4nB00yaExxElTmiTh2B3KgESY9WST+CHK/V\nvJ1BIAQO5nh1wgPeKbH7+O0sm5RUk7axt8977YP6KtsrGEjvE4nigcYB1c37\ngpjvQqhg/0Ofpxpt9PBHpYlhKnmam9+/UzOZcQmnKTs/04MilB8iVk0oQleU\nyn/nzhm1kCo3eH9/eFKX9FGiEjnbNwC7NpS6R2/7f+U1JyJYLSFaE5HdCq4F\nin/jIh+4TbTplWcPm43nd180Ffm1ciCSu0RZfwTHOnS/6P68mte6NS81RgTq\nZxdCObWSU+iPELSrwI/R518If7A2iYtNETj7R2Lu7YOV0u7egbIcPvJJPbIy\naB+kk+Kk7+HcHgV+FIdjXQ9YI9Ix+tnXxBuZW4msk0gAuOZR31brXbtOi0CA\nL3XHvWUaYcUcBKHljC5ChC0+Ddo2kzIQrJU62+vcRiNEP3xvfQoeo/iv7E2p\nBU0FQ1zrbDi/iicyE23vSm8fR0R6+TY8lV4AhhBLlrviHHzRTH0CdP3XPn3x\nzL389p2SX3QRkpJ9fGVW28VaNCczBDGCYvl5fcBHD0As/FUqFkEVRubwWPwC\nDsfK\r\n=qJdD\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"is-buffer": "^2.0.2", "follow-redirects": "1.5.10"}, "devDependencies": {"grunt": "^1.0.1", "karma": "^1.3.0", "sinon": "^1.17.4", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.3", "minimist": "^1.2.0", "coveralls": "^2.11.9", "grunt-cli": "^1.2.0", "bundlesize": "^0.5.7", "typescript": "^2.0.3", "es6-promise": "^4.0.5", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^19.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.0.2", "karma-webpack": "^1.7.0", "karma-coverage": "^1.0.0", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.6.1", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.1.0", "karma-chrome-launcher": "^2.0.0", "karma-safari-launcher": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.19.1": {"name": "axios", "version": "0.19.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "8a6a04eed23dfe72747e1dd43c604b8f1677b5aa", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.19.1.tgz", "fileCount": 42, "integrity": "sha512-Yl+7nfreYKaLRvAvjNPkvfjnQHJM1yLBY3zhqAwcJSwR/6ETkanUgylgtIvkvz0xJ+p/vZuNw8X7Hnb7Whsbpw==", "signatures": [{"sig": "MEYCIQD8HrteMDSsZmPugSpRP9eGMBDCYHwkDUMuvyDW7+B1VgIhAKCVFpsE941+OiKuhpWsjiIALhX66Eel03MLdnp/BMPq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFL6cCRA9TVsSAnZWagAAj0kP/0NPs4Nmu741N8YePwEr\nFEdiHcOB58DTnV08vCfDEbFhrXOjtx2zAaTtuhfXkCvYUOfRCkUXf2E64rpx\npvAhUurGuOun9kO+BCzja1nBoNWxPQfTY+1P7ebEZm403+ZkwvojEt0Sxj5x\nX9zGuvNrwScK3rbUFwZ91h3Kw7Sk4vxFFTBy5aupb2mZr/LAeD+IKQzqbDFq\nlkwQAwcUjeNer4VqRprm/CX/AxFrljxIwvI/1W2ksPiUM/D3AtjQKFAqCPBD\na1BvKKXOU2fMychwrhahJ6/X2sCVr0n3M8189lOiBEv1QIePk37fHJo3wffV\ngZrBoJ4UsCrQBzjshJVdTJXteSpGxQcv16H/IXDX+zW596KQsJ8Amd4pA2iL\nkCAIcKhjxZLcTAZhRGYieI9xtwUt1Kj5b+ALmQY1+fbKOICBO4a+GELeOaAe\n1RCU/uzgPMhA+GiwK0W8gBcPH7gk8u0bGQiNNUka50H9GRlaqgN0Ex4JhMDH\nVyhdhGnvttYVJV2myWLEjCVRiev91f6SBpBjcXlzoHQP0gyzu/Zx8knokVEt\n+P7yzj0u4j90A8fvd9oRxkDfGLsc0qXFEZyIPpvmXP1Rs0VLRflX7X4C2qQw\nooSGYN9bBajw33dkOGHzOiyZ07oIEWdNyeEqQk3mc6aWUusfVIiawrqp7Rm3\nkYmc\r\n=MDJG\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "1.5.10"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.19.2": {"name": "axios", "version": "0.19.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.19.2.tgz", "fileCount": 41, "integrity": "sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==", "signatures": [{"sig": "MEUCIEufIpsxjAPjJpaKHcod+3guWOnQ8lDIP8D9ft+O5BdLAiEAoPwOvjmHRfHEUvI9FcAkidOlirSdvaWizcRP3W7RXJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 346079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ87RCRA9TVsSAnZWagAAlxEP/0UTSKptwwa4wfGLs1uv\nf8oE91diogXtmbmh6cXER9KuNFYnVGcLKVYvBd6m92nTnmyx679aTzvWS65m\n6tzM6ba57JzLyQrd++3s+SUtbifso+ymuApkUVqNlHV74nmaK2G2u0mse7wT\niOOQ7hfFieiDKgMdzdH6P8HCGBnwArRfGaVfzQ2iySHmeB3lwX+4HdxAVIbg\nrSveu71YBlF5CyXzKhJgNF9EccyObd1PFiO4jW7K1KL1/yBD5SS8+yfMdr3o\n4dPLw5JCSHUGkB//I//TN/TiQCZ77GAkQyYcKTPEJXfApRhqPnlki5AtWmWV\nYfPhweKWL4PwUp63oKSq96VPpbh/FL5ZQ42o9jc72gWSnTXYGEXfckeXFBdG\nzavhLSogmWpHrG1afJAdL8/ENCQH3WtXxVeTqbcKZlHg6Bk8PQU7n4T1V7fk\nGvXL0O3jrT7rGQIT9+UX8g3GR//srhzC8L/jG+IV1E1Ett/o7w11sK/SyWj9\nOzJgb4YopF5ilVOTQs+yqXbKNltWpxf3/IdbehP6wB3uG+yZ5o9Z2Wn4AoUb\n8h9cFposAXAFsXxxdxo/SRdlMvE/znVqYHwdu/Rg/EdiE/YFY5RPumTOhEQk\nPHc2KRuuky7m/w8rgxRirc/f2h/3QXyUd8wHfTlLjmcCVrzt3Dj8A2N7zN7b\n5mGo\r\n=I3Vi\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "1.5.10"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.20.0-0": {"name": "axios", "version": "0.20.0-0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Pre-release version should not be used anymore", "dist": {"shasum": "cbb49efab92d14be740a04c0f1d0c68031773f36", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.20.0-0.tgz", "fileCount": 41, "integrity": "sha512-FdJxKL7EntcZprZvis5kDQ13aXVG1Fn022tG45wvP7/ILZ074pedaFLkI4uQgfRCrxwkzXeLnL3GOvXo6gqitg==", "signatures": [{"sig": "MEQCIGeikpUXPgkNCd1Kq7a6muBmACEPhzZ6tuuDgwRu8w+eAiBBaTkaXjTdmZQJr8XuTwNTQBWsDNXfUoNi4mWFDgi1wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDynCCRA9TVsSAnZWagAATxMP+wX5O4SFkWG+CkIPu5sC\nlSuxdfIEWP0WI42nrJYsTC/ei0qWyOKDbLrNCjijWBq9odKS/BQlMEzFEJsk\nLpOZ2XewWM16si8x8vLbZwBOQ1FBj+kf2+3zQdFgHpr/1p43xTnqb1wun4Y8\nWWCI+hx5cmf0P4Gfq0Bd8HOlCcVbZkuAI6kogTQQEMRufMUolALUGwMxA8w6\no32tWjJ5KBM87JNTRODfnCzDfn0xTnyo5Vb0dsLIAmgwnYUiOjSmWRJrqEVO\nHBYAYBqdpbDGAspqHuDdtkv1bdmkMezZgcIEfwFuCQzWebi49O6JidUVcdwO\npnTJ7eKdh84f3mc3OxbHDirSpDW8PKE9ac2OVU4aHS8oMd1eZFb6bxos1nB6\nIQI7P3stwElVy2a37akeT369AdaQd9+pbYtJOld3IdeWehsatth3zMhS8uOw\ndK7+7C8kkezN3QWTBvO9XJMccnk6SZJpa1BdzS8LeQfOXSudjoBFrIkvB7HJ\nEU4oVthUwX8+jV+xlC5BHewux9sY1hvozLWDep7PwKK5HZsTcdzM9mRM8vHq\nLp4apr0onv83OkSl5yHTjyeOQXN2c738fj6IxHtL5jwEZgenn7HlNyGZ2b7Q\ntQ5AO1Q6S1lMoHFk6BZAf1l9GgLcmyB6HvfZN4aYBA7Gd+tfRus2QcmJeQEZ\nT/p7\r\n=V/hP\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.10.0"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.20.0": {"name": "axios", "version": "0.20.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "057ba30f04884694993a8cd07fa394cff11c50bd", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.20.0.tgz", "fileCount": 41, "integrity": "sha512-ANA4rr2BDcmmAQLOKft2fufrtuvlqR+cXNNinUmvfeSNCOF98PZL+7M/v1zIdGo7OLjEA9J2gXJL+j4zGsl0bA==", "signatures": [{"sig": "MEQCIFUUssniNdRMxcC1lYqmzcoirqSe/af1xXbLVvz/RGBkAiBHR5HbcAwVIYHKsgsCPfFjVfAmOA5qg/VLZa0ZHUSJEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPzuxCRA9TVsSAnZWagAAiLYQAJoLATxRuWzdKlZtnw1E\nSA7BPTDfIOYrvkDIHgOGz8xK8LngQ4rSL3QqydhzljdVR90l+t5d9UR5AOZV\nbYDeFbhahdClrbF+nNwHsaNkdUuKPB68mxKrD9AL4jAExdna3jdFc4wCQ6MH\nWQsqHrNFKt4zGqPRHhdbMp6qdPN7SPLjtRzm46rs2QDaR5p9iXPJBv1a6VbE\n1CNv/PNTBBMOmTB8M9Hb/gOK9kJdQHx96rqk7vW15xGpVv/qbq7bStijbN0i\nHEqDtyNyVOJr6AWsZM0e9qfPrDYmVCg0eYcBne1/w9GABkQajb5iiPFNZiCZ\nqV9LfBTWGFvLFgfm8ay50cjGd0uhZHo0Nmst6nITzr6uvREwtSNlf3wtI219\nt7j+v2nkmdZHf5n+hne4Q5piypTAf/9y4IFg9Qx6+uTg22okaTlw+YY5ljg+\n7yMAmuX/BGDD1quO6RhjQHrA94J4aIjvcV9+8bkuJlN2dbWGNiNaPqKDZLiw\nFf04w3aTNlO3v/v431TYDzhgsuZ+H9lWdxuFj4BAYh2J0BP1ouKEcjZdor5f\nwpGuWNvgKRjRV+GsXrq9Z+a5Rs6QVjFHuVNoW5mskWe6EdUFe+wxud1Ptgbm\n1jTWQM8OH7TYdWovl6HFdF5Yqm01f38FItNb/Vm1o8JyJbqnD6mUa0PKMXVy\nYmUd\r\n=1JD0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.10.0"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.0": {"name": "axios", "version": "0.21.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dist": {"shasum": "26df088803a2350dff2c27f96fef99fe49442aca", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.21.0.tgz", "fileCount": 41, "integrity": "sha512-fmkJBknJKoZwem3/IKSSLpkdNXZeBu5Q7GA/aRsr2btgrptmSCxi2oFjZHqGdK9DoTil9PIHlPIZw2EcRJXRvw==", "signatures": [{"sig": "MEUCIQDICg+jVI4IyW4UEIOw35HS/3ENV5DhSQFbtOCGkhSFEAIgPc1qBgdL4ty+NLg5Ke+DDS1c29ee1oh3RmnEGq/e41s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkwRiCRA9TVsSAnZWagAAA1kP/3iQ8l7JI/H59ER8u8oL\nzvtAFmTeQhvEhgcnHHhKqgJLl1fWjYPbzTvVPV8MhKoIh8gxX2HRGeeeZGYS\nuyhraDCmbmoiGpv695/3eqXHkmRz0rVEAB6Gn3TJ/vwbvOtPGdsdZ0COYwkK\nJSggexEczWg3IqIQJxy4vLlsHNQ543pieUr6bkVsw8xpURHZqhGOvxHI0q7y\nSn1bPZYKhyZ0DKGrbNklsB0oIVUDFdTVBSgWK+GRhLiIa8IpsUaWhxLnklfr\n6jMmb3lOATgQaanKUsDWMzJdSbld1AoiVBbI+QdVpWcM+1YFuF+DSvWyBQSJ\nVXIcsdTc80JkxY2GIblO7smTdKMRLGZluviT9OCID0rrm/+GTVB+0Z4PwHLz\noABvD0vDW4PN2LLHTewPb54OYdXmqUzDRCU42QaLB0CbL4DRIiuCfgSFC+Uv\nvmzVnWhQ+V9vbnVDMtVNj+Dv/IYEnR5i4Pg3iOVe1qWrHguHP7KAsp5zjllL\nnez3c2NnV8XFF0v3epdOXc789We6F71qwZcvavUFqqQw8r/T77SZIHEmpHRO\nFgkVtZGPuND/d8HVkjsfmvntRCr5inYjktd0rsoofGICd9vx42Lqi+rLc9WD\n4AES1kq73BcIxc56Z0SLXveyt/dRP6HaSpuHO8MoKGg2FyNQKaH5mGvmVtGF\nCIBe\r\n=e7hh\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.10.0"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.1": {"name": "axios", "version": "0.21.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "22563481962f4d6bde9a76d516ef0e5d3c09b2b8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.21.1.tgz", "fileCount": 42, "integrity": "sha512-dKQiRHxGD9PPRIUNIWvZhPTPpl1rf/OxTYKsqKUDjBwYylTvV7SjSHJb9ratfyzM6wCdLCOYLzs73qpg5c4iGA==", "signatures": [{"sig": "MEQCIE4dDyvNb6NBXyCl/1Wm4d2cke9px81jizcW4HKGTuKPAiAQH5pVWQDMziWdHyyHsI/WeZGah043DNJH8OaMYezepA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 371182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4XP2CRA9TVsSAnZWagAAs+8QAJQ8FvmiRX9DHil+8R3s\nC48k6BAZ+7DN0jUPLaiFF+F6G2vHFw6slqNQYsx+RMToOHhaFzQayUk68hHG\n+HPoLsBqS9xtX/egRKuxcAqxuZ+3+K/j0XK7JPNPkH2s8BWlaphDiGerb8tA\nrTzvcdwIzLWn09fOP6vaAaCAZFxIE8iWj/ZggATt5IQ5bgXVUrCcxIUKW6g0\ncM+KTRDTKHCNH7MW5wc7IHSHUPsVrhCvL+Kj5AZF85nXVnmdoeVHGwbP22vV\nGqfKMpMJPTV2WD391ZshYbBY0xN+f1aa+/Pp5GU4HWO+ezOWSYk0IFKDMgGc\n5HAPn5P4k2wdgdfmOALY1noMJcHoyvnJFQwd1/GRU9yAlNXUf/TKwYeibUEm\nBsT4DmZXmKkYhMUDghqE9hPQ6p39bLlGIqqbgZYmQWhkZqgHn5MY628vNYNq\n1W//eJE0xJ7s1/inyS02WNIUccec7fAnHOBqs6kcAOTUZFkcW4Tpu19HZQ3V\nvP6GBBIXjghAyGwGPsbMxiThJERgYIG1LAGxl32ZLtOjmRqQYmoqldxGeesc\nYlvvRg02iI+DkMcNtHYJYoGD6fUU/8oshVZIRxOHKwyhgUttJvHkLEQLa34+\ni8XvDPGYt4Ejt+uw8bQG6KZb+v/Z9mMwv/c3WSJAwcuDU0Zy+LqVLnVfKFSU\nDh+E\r\n=cry7\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.10.0"}, "devDependencies": {"grunt": "^1.0.2", "karma": "^1.3.0", "mocha": "^5.2.0", "sinon": "^4.5.0", "webpack": "^1.13.1", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "bundlesize": "^0.17.0", "typescript": "^2.8.1", "es6-promise": "^4.2.4", "grunt-karma": "^2.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^20.1.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^1.0.18", "karma-jasmine": "^1.1.1", "karma-webpack": "^1.7.0", "karma-coverage": "^1.1.1", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^1.14.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-opera-launcher": "^1.0.0", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "^2.2.0", "karma-safari-launcher": "^1.0.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.2": {"name": "axios", "version": "0.21.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "21297d5084b2aeeb422f5d38e7be4fbb82239017", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.21.2.tgz", "fileCount": 44, "integrity": "sha512-87otirqUw3e8CzHTMO+/9kh/FSgXt/eVDvipijwDtEuwbkySWZ9SBm6VEubmJ/kLKEoLQV/POhxXFb66bfekfg==", "signatures": [{"sig": "MEQCIDVzHUobFs0ylvhBQzM7QmA4iMS1+v8u2QZeCi7TgHC0AiBCOKORGqWcaYcrfW5ecyWoz+OwI8oVjM3RLrJkTRM3TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhM0gKCRA9TVsSAnZWagAAIlAP/iO5yPNLelXZ6JprwoET\n8AD/fvYM1Oj8IuPiQxh6e+NDo8yqqUNT+COTtAguVELg7EJBI0TfuFvciem/\nak41edea+kLPe5Lu5Ik3+F1C80mKH4IoSHz3Mk0CVHhqJ289BkNtS0KijFbe\n49xbKx2dobrNs04jEYzB9PgrkPRU/oytAlmlf5s1yZo53WobBhjt9u43CgYm\nbe7o2z++ekzo5XsdKUY62wX5Vr02c3s+Id0m881301c/GU1a5Rxajj4cO1cj\nw4kUr1ps4Wneb+/c5C00vzOapez2YmnTeiw7P2z8jJuYBipFXNGnWjpLcLp+\nesj4uQCGgvWn0b9/WvnTlS5t++e/euFo8pMtyL2QPxgFVAOBXUyR7HsPB4Hy\nyXicpQv4HfxgjD1xGdZL9v91Y7Qn++uiGsTqMDhG73qC07W7P6cAt7z3FqAl\nQIU932mIs08RN+whYsSAdn/BDDlLrhKgCjpEuvrGJR74ks1BwSGVkfSI2Key\nJ8CHKx7M2pBSghLCWX8/hQ67bBi5yGomFGM2pxRyi2QDLpBP7/TJDj+kRC1J\nRm0tkeTMmrTh1c8woVzU2I/T/ETdbV0NbL2VQC48aG1u4ImhFWGuSkXz+CyV\nZwzPON2bo+yY96NxJU8zZ0LBPZR/DKYioFmX/jby6FCQYr6JPbPQJg2D962A\nOKfS\r\n=ucbP\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.0"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "webpack": "^4.44.2", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.3": {"name": "axios", "version": "0.21.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "f85d9b747f9b66d59ca463605cedf1844872b82e", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.21.3.tgz", "fileCount": 44, "integrity": "sha512-JtoZ3Ndke/+Iwt5n+BgSli/3idTvpt5OjKyoCmz4LX5+lPiY5l7C1colYezhlxThjNa/NhngCUWZSZFypIFuaA==", "signatures": [{"sig": "MEYCIQDT+8JXrUquTqK/gATt3BJD5DOFuonHYuyLVsYkovOWWQIhALVt1iNjVxxU93JKe5No3ZvjTdSbVLgX2+RE5Rw34MMa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhM8OXCRA9TVsSAnZWagAAvh0P/iahUupi7f1kY1cliePC\niO7jDPTOrgzz7UXgG3UY4bST1G7PMai2FeD+0EpflrtWAmMN8bOocg8cIjcp\nOzQ37SxSEWG8D8fT5lKRKh59x2MnX6YAOL0L7eevIYWZZq5jijojrgz/nUfB\nOBPDbCenbiOl4+jg+RicOc2AJSo6f2JKaJv6FYuMU3UJDzTItbpFaEpccMvP\nyeERbvNRCg3ZOLFcfUCS+9s2+w8V0wEqouIVIcPRD3IdOb34kubsrAOQ/hI/\n6GJIppZ3BffUTCEMrOsTeDFC4qwCTBnhk8muNvqqxHiL8BQCt3mOM3IgaHL5\nzxvyS9NB6mTkL11ldMY94T8uM/ny/DhrjVEwx/SRVwKQxEK2S9JYIY0LYa9i\n2AllRHUo7XeCXNTKd9zH9QQhNmlOyKXjuGAhAqCTexfkTBXoR3PkxFIUY44r\n/l4ojYva1PY/ED/oH9ThBXYxkabDi1jF7fbTJCwne8ojdv1Es/VSF04s8aHp\nUqzixpw/XVZUNQiZO6t9mzSvaep74AU/mHyfeNtQxQ+CWbUGcAy1shaKFOy2\ndFI7bfGne53M2oJAEwYWe+OmIzd931Itlho/u3jZOVFieSpljRBwHmTSNcLP\nrkdLp9OonDH7kT1ofatvYmm83Ijv4oIVvSt8GRzB0LEaqKS46NVgvEIoaG7n\n+iPy\r\n=yOG5\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.0"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "webpack": "^4.44.2", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.21.4": {"name": "axios", "version": "0.21.4", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "c67b90dc0568e5c1cf2b0b858c43ba28e2eda575", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.21.4.tgz", "fileCount": 44, "integrity": "sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==", "signatures": [{"sig": "MEUCIGkkAJ1lvuGlmUhSCUk69Ot9u/hhH8bBd+7yA7cyaIIAAiEA2hD60EdaF6ud13eUjHo/zRR0MdZ8boETX3PqRwsrwZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNjVPCRA9TVsSAnZWagAA0foP/3y+vPMPeoBU5dw1GOw+\npuX9t90nj+UaF116uDtZy1nmLl3CI+75T40P6OfbRq8AqTtIZ3GAb1hgFWDD\njftxo/P6kNlQrokmw5Nlpz3orM2Pobmk/TW56vd4CknIBw8PlBW+JQHZpPfY\nKjCg2rCv3XGqcFX8FGxE9sagkaMmomAZSNhw+86IsqWn4PYwL52prAVK1TNF\nBGoJgQCQ7Gh0XGa50SINUuVfnZ/SpYLGMFV+vlZGqVSOCS+Al/SshpldSA0A\nhCf7YUcLgqcej+l+EHFLFqO6iq78VayCTjqgSDM6USC8a17zfAaq/0bJdEht\nM1e3OhtcnUyDC00I5PPGv1PW3N1J8eptu6QyREsPoHRUqVGX0u1eHTB3Mbr2\n8VLwYal453QJ9FN7pW24U7ZCJy953gtJ99DY0KIXcl22+U1X0pNlzYhoghUh\nBpajCP2kDps7SmcRQ0oGCefebuTT+czP7lAzHtF5tAg0FFtMt6X5I8bQahL4\n/22ITU9SdY1+SZRj+oWrj7CJAfjODRd9VmAMbwxgTnStzeWLvU/Cwc1l6euZ\nPh2TnA3smLJ4McjaP9Mj7PAXxGVhmwoMLnAcqpu01L7Dy2C8SdZ+lgy/+1Mz\ncYW4OibWQjCM8/eGmFGS82R2Tg20Mx+aZ09YQo1mjYxZ3SvTmlVh1Ee7Xvc2\nPkX7\r\n=rPmy\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.0"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "webpack": "^4.44.2", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.22.0": {"name": "axios", "version": "0.22.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "bf702c41fb50fbca4539589d839a077117b79b25", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.22.0.tgz", "fileCount": 46, "integrity": "sha512-Z0U3uhqQeg1oNcihswf4ZD57O3NrR1+ZXhxaROaWpDmsDTx7T2HNBV2ulBtie2hwJptu8UvgnJoK+BIqdzh/1w==", "signatures": [{"sig": "MEQCIGFlYrdn5g1KA8xIPGYs4s8TlZ7J23VYm5wOHc0+c/AUAiAj+UDz5m8cgJEB+YJrBAQttaSdSy6+9EDKtvrbJtXmfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385333}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.4"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "webpack": "^4.44.2", "grunt-ts": "^6.0.0-beta.19", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.5.0", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.23.0": {"name": "axios", "version": "0.23.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "b0fa5d0948a8d1d75e3d5635238b6c4625b05149", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.23.0.tgz", "fileCount": 48, "integrity": "sha512-NmvAE4i0YAv5cKq8zlDoPd1VLKAqX5oLuZKs8xkJa4qi6RGn0uhCYFjWtHHC9EM/MwOwYWOs53W+V0aqEXq1sg==", "signatures": [{"sig": "MEQCIAc63y8PI4Y/3ulXiYZO1f8tD/Tk8c/UI8vxlI8cAcNOAiAHCQVI8IxiGKUOnrMgxX9kv4IZ7al4896td4awBr8k+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388462}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.4"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.1.6", "webpack": "^4.44.2", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.5.0", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.24.0": {"name": "axios", "version": "0.24.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "804e6fa1e4b9c5288501dd9dff56a7a0940d20d6", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.24.0.tgz", "fileCount": 48, "integrity": "sha512-Q6cWsys88HoPgAaFAVUb0WpPk0O8iTeisR9IMqy9G8AbO4NlpVknrnQS03zzF9PGAWgO3cgletO3VjV/P7VztA==", "signatures": [{"sig": "MEUCIQDkLH45eGx5Iy1G8pdkvmiME+gZmZEDRsB/NXf4wmuUUAIgBxz9M3wnYYrVrzohuVd08QNnm3eelchxGAm7RPs8E04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388976}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.4"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.1.6", "webpack": "^4.44.2", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.5.0", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.25.0": {"name": "axios", "version": "0.25.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "349cfbb31331a9b4453190791760a8d35b093e0a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.25.0.tgz", "fileCount": 49, "integrity": "sha512-cD8FOb0tRH3uuEe6+evtAbgJtfxr7ly3fQjYcMcuPlgkwVS9xboaVIpcDV+cYQe+yGykgwZCs1pzjntcGa6l5g==", "signatures": [{"sig": "MEUCIQCGIx3XCz3jc6vsm9Fw+X75u5mt5WMNSNdqVWwqEhbt1gIgLgWMLSHFEuT2Qs/0ivq0rCJpUUn4vD6dTxAYLaMwM9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5mi+CRA9TVsSAnZWagAA8WcQAKK7LnQjICSDtbMVtiSM\n4gLQ3lZzWYs/261GMEb6BWRcTtsEepQllxPUstcbtJqsZRyz/GCV6QELAjZU\nvyTWJ8uISgTT55LsfWTr0hMsmW4m5TuUuDBDkSPUPeIZigOF+dSRutD0ZRox\nFyB8OvTri1i5Glhj2jsOP+LttVqL0PJS2aXyskGb5c0B4/mdaPWxv9FC+nRT\ndG1v+cdGT/J+fGV/36bLIYqgu92wW+OKgDdYg9M9+lm/76phiJF1HTS8oDWa\nzFOscb1E9XCzCGOts8BU9ht7KkKUQR9zfbpg26iGap5QWw68Ql9AtaXES17m\nmVJvBZoFWN3WRKXbb2RICEFeMPp7SFviyvKplvngjgVcBUAtJSPmBKCoT4vh\nohCMbhRyk4trOv32XNoiwy1ovytzlJ7JKtF6gye1o6ZOygkr+gLXOkr86tBi\nHX2+9ss/HfQSiaCVj/4p5caE2L62VBUfHHw0xrL974gPDsVzH02d9MTQISlO\nDLaoZZ8uV1P2xSY9WQOn0g6ywcYHSn+mPZeS8wVInAXBc6g7nJYx3AV2Lr20\npiXczmUMQYmkjZt96A/YEfzObYZ7zSerDP4Bxaf/u2RcgU/77BSBfRUJIM0+\n7wfq2pWYmLeSIJvpMsSrKzmL75HHrJMOxaoNtKRoCCFu7SLnkNfgxvVfCrj5\nQKf1\r\n=m+w0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.7"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.1.6", "webpack": "^4.44.2", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.5.0", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.26.0": {"name": "axios", "version": "0.26.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "9a318f1c69ec108f8cd5f3c3d390366635e13928", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.26.0.tgz", "fileCount": 49, "integrity": "sha512-lKoGLMYtHvFrPVt3r+RBMp9nh34N0M8zEfCWqdWZx6phynIEhQqAdydpyBAAG211zlhX9Rgu08cOamy6XjE5Og==", "signatures": [{"sig": "MEUCIENfRK7wO1vD9qAPziH1yxxb2U9Jbs2X3oobyq4Qu1NJAiEAxStH3lVUKROUaQwUol6Tjs0GVvcdyLhcxkX+y48sZfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCRQPCRA9TVsSAnZWagAAIaMP/i/4CN275RHza65MMpux\nBs7jJmMWE6kJrWZi6mqxhoFx4p1qY7tbtucon35PjcCZTLStysahP3Q6D/ur\nfNJRynqnRdwKXBdFmu5YByUthzEhBrXk7oMm5TL1fCR1mPmF8UWHa2tDTCoF\nxGrsgxk8Cxlrh+B6sw8skciaCYFgF0r7XQ7US5ExM5AGy0STyZeMPPHnaDqc\nxTPiN80Djs9S9ufbY1+NTCam3gtu6bpQ0b2yG7pKjc6xDbHQDeqDKTBHsWpl\nnCcODtcaUO/PLALD2LyhRFhekL3Mcg9of9msSSlgnF8DuKeIgNyf579OjLn2\ni5jFy6VjqP6nA9ctfFSxUEvF9yO9RYiTqv91By16wg29Q/68RV+JQz40FywV\nERnrpefOkDpjZfE2p4e1i6OYLLt7oYCVMFFrLRieu7ghafXX2qohycU57IW1\nCtk8ZoczCAe8Yw+Bl+4eTGzRHPfI9j9rfIT/RNMcSNmHqxrTKH5mekbAuoPn\nF6k2frLn/wBwnPJscgYZsWLqAEKcZ37WC1Mly2eauoJjDqq6qbI1/NO3ugI5\nC6vRXLqzhrhvGwHHbODlVDKkKc96GATPrON35XH9WsEkeSEjo1Rklib6Sy5O\nOr/FHHLwJw8LeEfNDqneutSSzmddOBMPwsXFEsqOkh0lBJRvsv43KccFSKg4\n6Ihj\r\n=ynt4\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.8"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.1.6", "webpack": "^4.44.2", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.5.0", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.26.1": {"name": "axios", "version": "0.26.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "1ede41c51fcf51bbbd6fd43669caaa4f0495aaa9", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.26.1.tgz", "fileCount": 50, "integrity": "sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==", "signatures": [{"sig": "MEYCIQD3Gj95LXvibu7OTQGvKA0gaLS6BiQTKe3BHcZHOsfWgwIhAIj7IcvwVeTKOvE0SbQGH09361z9wOfw8BctsS8J//ap", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 398302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKOBGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9UBAAn9gyqbptHclcA2MeIca/N68hX6tbsnA1ZdAlW1GYLVPKTBDV\r\nHjgO6jAxllBoTfq+3JCgkvZ/NFlysf49BH6rdxS76bK2/XA3uVRhIAjAOIOV\r\n69GOoyTnzfye6vprl43XoElBQZc97Zh3qMDkm/J3+q64yQ4PhK6ch7fT67Lt\r\n7h1iTZ5QsBfS3OxStJJNNqwOIuaOWHq/ThgNalIvopzmD5GTysfJurd895CI\r\nzex8X0YCq/n89jetK3zP3BmUUMPyDfWXn4IuahAZPqWQnB6AV+FL+L32DYfx\r\ns4ETzyAanNb447CpajBgyoTYzKRDGjvX90BIde21MZP4TH7Q96JXoZsUpoxB\r\nPJY5DYMeQJvUkJAhVMWDbMnibKGZxvypzvh7FBO2lYiNg10zCcDb9TDcLmrp\r\nfFoKRpvZZjsgUm3JJrr4dklgOdjg9HoFq7AKIg0FC+y0JvjamK6eqJ7tmyJh\r\nv0whYDO+6mSMv/IYZ52XIlv3b6UteDHEr3y0jocwAC+H9F6iMtvwHFjXqdrP\r\n5y7aRubd3vaggXtbdC4KopobtKFVk508yzzq7CQbRhCe0yTYkpmVdqx3Yr7Y\r\n0yopTd/KVxcox882CrsD7r3e/JMD2fyBiPB9km01UiYHXQHj8eUeuPdyneLU\r\nqlkZ4NKhn2dHzgbFx42F68muWERqwv4jHbg=\r\n=o6tS\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"follow-redirects": "^1.14.8"}, "devDependencies": {"grunt": "^1.3.0", "karma": "^6.3.2", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.1.6", "webpack": "^4.44.2", "minimist": "^1.2.0", "coveralls": "^3.0.0", "grunt-cli": "^1.2.0", "typescript": "^4.0.5", "es6-promise": "^4.2.4", "grunt-karma": "^4.0.0", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^23.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^4.0.2", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^3.5.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.0", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.5.0", "istanbul-instrumenter-loader": "^1.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.27.0": {"name": "axios", "version": "0.27.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Formdata complete broken, incorrect build size", "dist": {"shasum": "973ca980dee9077410189340390ea77964af6b29", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.27.0.tgz", "fileCount": 51, "integrity": "sha512-XV/WrPxXfzgZ8j4lcB5i6LyaXmi90yetmV/Fem0kmglGx+mpY06CiweL3YxU6wOTNLmqLUePW4G8h45nGZ/+pA==", "signatures": [{"sig": "MEQCIG9CjACy+E+/i0FUYUF3t8VX2TZdCjS/5OH0EkUctzolAiBXWdA4OL87qi/bWpktlSWjc01fHHbb0I+bQJ1J5ntBmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZs9iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfhA/+LLhomdAUaiJHNE/E1iYgyFwEIDXxjKNgw9DKIP/o6lXkhNAG\r\nUVb2DuN8RzAA88gcW8lR/v1TCCki22c2BH+3u7psXkRu9tqi/cZysuPgCadA\r\nb4KCowXNuO0YKnR8lN48ABE3Fj1DsTsaVNsdHPlqi7ohPt5RSsMvzTg36sbc\r\nwl9JG+4MryTKseqYks5Nj797o4VZIgLymTqYkfW/44qm2Ec9Cca40wRe/Rz/\r\ncpLG2J3uwV44SvAb50e4dDi/rzAjNlQz1pG9MD9XTN7kZ2dMUYhIJtMns4O+\r\n5g8xO2pnHCE+9OQRI8P5nmSFbYEi8p32Uti9b//sLYFEQ8fI2OmXDFeXoy2j\r\nUbz4MhS7DBj0w6fevqF+UKRm8vEKhPmLjZNHnSTTtSpoUk8xSltOIfe0nRDg\r\nqwzf1MyjDNjaptZufC6oeA4bhrj8VUlqtyzA8i9CiINHFZd65zGdSv0AbZuX\r\nGXWvb5NKu7GNxpNq/wJkobtfknAj8tCMYGemtriXAByV+L1ewmvLzl2VKP4p\r\nSJNp8zUk05gk4IR6Qv+2Kzx6cFa4EFVAQR+fvhkSwnsDbKL4jw54CUYShnUW\r\nLSH3nmagqyIh7zmlx9aK3u4MhuuH1/zaxJdWOhyIUrBW+8RGE5Y7wPJSA/1n\r\nAsmmeoXkUAvE6DFiYeNFMm3//L/hpqQP6w4=\r\n=J6B3\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "follow-redirects": "^1.14.9"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.2.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.7.3", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.27.1": {"name": "axios", "version": "0.27.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "Formdata complete broken, incorrect build size", "dist": {"shasum": "badcc8cc38cfa812320221b600776452141ea5d4", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.27.1.tgz", "fileCount": 51, "integrity": "sha512-ePNMai55xo5GsXajb/k756AqZqpqeDaGwGcdvbZLSSELbbYwsIn2jNmGfUPEwd8j/yu4OoMstLLIVa4t0MneEA==", "signatures": [{"sig": "MEQCICWUPEcdnlsV2TkOiTcNam2tUteO3YeMxc+8IbkDjMjGAiAwgXaEeThMHRuXDdTf0JRM7k2DnVf/E7gNzmB9iZrx7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 683820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ6EGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolXg//fc3vCD5X1O/hERF8pw3yQNRW5iQvldFN1m1Dgkd23KaLvkQH\r\nkh+E8tSyzFEjhCm/eCgWEZ+giYMKzCUOxcpcDpCpGddJZErkGyypHfWVhprz\r\nswDjQdFmcnbOkkCh1pRVOPFA1sgS9Z4Jvu33rFljTEmbQOEQh704keD9MFaA\r\nQoZi1/9pU3msU5k3CvjV6EdyN7m3yuZIIykfvqjyH8bJ5zHZCuDLsGXf90uI\r\nBr4mCsVK8qS8m/IpqEDMhNwzw3auBs7gDBnD9d1MsPOyDlYw2mrUO2ujj3GS\r\ntwZ0qgkKHkD8XwihMJDhwrzvm47dXDOrxyxVZR1ytveoqhB8ZJfFnOad5Vfh\r\nNu1Bnn5dM/LSI9sYYc3YJ52+************************************\r\njvEUOxp2xM0tVxuBWnamPp97NQmTCvbNIQhVm8jLCPMpFwm4K79XOL26Anua\r\nQPKuFfuDY4HHJVHTPgeucGPbx7qpz6jHRd87tPtCndD7BDJr7DPX1RjlXdtt\r\nyIvnvNM5l2ZgShMRRvquJlSAy3kEp8HnBqgfdu6tNTJYfBDmoS62577Mq7JR\r\njY9Yu3hCTvGEFyssAyArHSxoMqt984q1n5j0NqzlwnUl6dL/gGcCdRtfGlZU\r\nZQCk6rYMrdsogE2nJbp5VM+HU7qgWJnnF98=\r\n=OrJ8\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "follow-redirects": "^1.14.9"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.2.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.7.3", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.27.2": {"name": "axios", "version": "0.27.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "207658cc8621606e586c85db4b41a750e756d972", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.27.2.tgz", "fileCount": 52, "integrity": "sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==", "signatures": [{"sig": "MEYCIQCHl25WQioU4hNRDD9qyIyzsy2f/fBm2rWMxqhqy5qWsAIhAL0eMIyDnbE11rzH1YpnXSlxcf3UXVHlM5RXMdZEDolm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 445714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRRaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjIQ/9FJLVgfGxTfTJ/L4x/uidfCm8jBng/0PF527CbrbjdyrGGKSQ\r\nTGENpbCFsZj/odWcJ/03Eb2Wi+m1uWlRIPymB1dveT6VX9k0g5WvghhO7phn\r\nijmiWmjsyjf5ZjRzxjlMoGHjOYsBwE0tnSD3cylThabPxVvdgSad0HTSrdZs\r\nhPy6FwWbcoiCbSsiXXfeM5+A771VuYSBYXlgPDfcSViNlr2Zos8nOs868VVY\r\n0T/tc6sDpz1uhhPGUC1H0uhWM1FEfBjQJsbbJdij/gO7XMk4/9llraq02HBx\r\ndKZtWvGVtqApjdZ0j2KBEoiyRPPm4f4x8snHVmd1h4Lnc1+DCzC3elc6uzyb\r\nsoy5o9PgzPjgcI36ND+iCN8i3Q1ti+7l8thDC5v+5yXDf9ZKQvDYtNXI/fA0\r\nwcJx8VpM3YzY4rAf+PKJ0ERq6+HMxEsi+s8JeHcKJ+IbV0sX/SZa7oDGfV2K\r\ngiUqRJJZqwMK5utxZAI5q0UDHWWYortVuAU2ToSG4azdRtX3Ey7wTGE10N1e\r\nARQ6MJRMwMOY3RZutUb+JHfBy1hBmWCVMPzAbYDWSwhUKPhopjjXuozDfUWc\r\nrMKGgz7YXNlwdpIjcVecnD005Snt3WYZ7Wvy7iOfwWpyL0oOCV9X52ca3jyF\r\nWwPYeqylOox44EGovBFBnEmUhl9YD0dkxbM=\r\n=yVKf\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "follow-redirects": "^1.14.9"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "dtslint": "^4.2.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "karma-sauce-launcher": "^4.3.6", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "abortcontroller-polyfill": "^1.7.3", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.0-alpha.1": {"name": "axios", "version": "1.0.0-alpha.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "This version is deprecated. Please upgrade to > 1.0.0", "dist": {"shasum": "ce69c17ca7605d01787ca754dd906e6fccdf71ee", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.0.0-alpha.1.tgz", "fileCount": 69, "integrity": "sha512-p+meG161943WT+K7sJYquHR46xxi/z0tk7vnSmEf/LrfEAyiP+0uTMMYk1OEo1IRF18oGRhnFxN1y8fLcXaTMw==", "signatures": [{"sig": "MEUCIQCyYSzNq5/qzQ33FiO7n/D+gOW9JGyh5d2VC9SO6Ar6VAIgA5N89+Uhzj0bUPv7LcAMD8FnAO0Wy3xUsRojpAxNgkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 795119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilmtNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpG9Q/8D6Tj4+hFYMTOALmy/QCcVw8/sZcoZVtuYqaQMA0aTKJtLxVd\r\nrB8352ZzXUGJQnyNfgSSS0vbES+vsNlxcWfSPcCDwiV3NmLiWvESvi1TI/d1\r\nxRJde2/hFJkfF3ZE+Z66W/zDtSTjnrygYnb3tTnGgPwv8z6Eyys9+8u+I1kF\r\npzcwmB0XWGOiz6wNNU7YdvEpu8aBwx/4KCz3phSTwcAZJuhgeKY8xD8YNNOE\r\n3QIGtbIPYzI4c4D8n+54a0xe7q0AJg9qVgtXUDRdt4cOINhYefm1JagDv+A2\r\n+Zd9/x+o0EwJivfKVOduNLOiwvP+SrWDfh1RabsnJS0mqxvGJoHc1QEoqscj\r\nz6LrUwqHpyvMYd160Iy7BH5FlibZp9775/sP+Yr49UF8oNF7W1dQ2HoPKj0N\r\nwPPPdZrFgXyo16pV/1ZtMms/HObQJkc1T0jGwM4f4Oax8j2y1qxoc4aKkneI\r\nGSxZhjbs455lFac5/+vsMe5P1tKHcPJKC+4GcVifWajNbfEhk1dszp5LxPve\r\nNJ15DtpISWm1CxxfDOLWbUpUf5GwvnBOT9XGuun1EUpGixBfD9t5Po77/UpC\r\nYF3ec8AoebyFlZXIpov+eyNJeY9OUWSzPsk4DR+oDZerIdtXZ6mDxPUk0w+L\r\nZNu4JZYNP7+t1tjk5bVsT011s0Y0yRZTzt0=\r\n=L+fL\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "grunt-shell": "^3.0.1", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "@rollup/plugin-json": "^4.1.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "@rollup/plugin-babel": "^5.3.0", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.0": {"name": "axios", "version": "1.0.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "16ded6096c1d37650db9f6a8d48a2f7c1bb58622", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.0.0.tgz", "fileCount": 77, "integrity": "sha512-SsHsGFN1qNPFT5QhSoSD37SHDfGyLSW5AESmyLk2JeCMHv5g0I9g0Hz/zQHx2KNe0jGXh2q2hAm7OdkXm360CA==", "signatures": [{"sig": "MEQCIGEECbdjC36/6LNiyDl041nRhyXdvV45wK66nxeZt4APAiBP+oZZSTya113XI2srDeFv2GG4RWmc8jo8N2eIxVXQtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1297906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPIheACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVjg//a+KXivRTzYBhpessl7y39k5VRYPiiG+D7dVs4mAbZsoCH/L1\r\nfZWj3/dfs4+z//ckOs4Hz6odpQ7ae6RmbwbiGreY1KOKZ8Cq/FbCC1LeqsoX\r\nq2wMn4HEQg7+PwoBAmR2jSRLqYF5h3BRueoi7zq3s2ow/wFwKJjTpXi7pBs6\r\nBPsLNiz8n9WSkXtYgP0LxFnsXRcILxd+IFnknT4Ozq/WUbqF6Lcyujw7lvKo\r\n71kSHgH4MErUCRsyosjWmJGja0EXvvgDK6881tmlsfOozm1vO55u9kZCqB/K\r\n53k7B+uX6JUZS3TW70OjMrBNOMKAfPgDnQxRMdBXRQ81QSf30LiTgQ/4bJQb\r\nYPD5YaV70crSLfKt8RN5UOHKq+er9xU4mb1/2P5qE+HVPMVGH+bAFfe64o0n\r\nxvtk5QyisfGjQKe4NraQUi/IOXGq4qfE7zTaomKpP6VpbDQIsR1wNXdvOD6e\r\nQ57nTXIfuTe9jZat8BmRHfwQ6F1TZTOWqxVtS6NoaDpVpQYN6eSOqC5XXay4\r\n1zzd1EHw/X1mhJ5gwXbXlsWVOZQeXxowVseKCgRea6AjlDU1Bo0W5Zdcr9W4\r\nspAU9Vq3pyN4ZkoVoEdVDw2Ho29dYEhW08qYz2wEcX/lMEfrHvmjOrr1RRQG\r\nXWq5ttiAq9HZrk9kOLJmuM7xBsPiQpCkWPs=\r\n=8OKn\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.0": {"name": "axios", "version": "1.1.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "94d25e6524743c7fc33954dd536687bbb957793a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.1.0.tgz", "fileCount": 77, "integrity": "sha512-hsJgcqz4JY7f+HZ4cWTrPZ6tZNCNFPTRx1MjRqu/hbpgpHdSCUpLVuplc+jE/h7dOvyANtw/ERA3HC2Rz/QoMg==", "signatures": [{"sig": "MEUCIQCuw2HRU74YLiGOyLfU+/LJ4wDYxqKgLFjbeJ5nWRllCAIgdyrVpXY4cZVwOQzXF2vRJ4/C5XNhI0UeAWVicKnqw8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1301740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPypZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHCA/+PtCaKTTtsRvZsbmKKjYxB8/IbWpNyDyptasycTD7Zo2PgeQh\r\ncP097GpCG3LoBeQYPmAf22RO7UdDRzjKba5mtvS7LrIFc9NE2xBKK73vYoKp\r\nnUKReYBiaucjz9MGC8eeM2nrVZHcrrwaT7W6j88adpRrvtTLq8aFGADNjntJ\r\nLvqpJzS00XtHTmAbSaQPkES4hnB+LzdV88lsnvz/p6XLzmr+8H9vkKDYzK8s\r\nacF6V18nVTGxAx2VslBOeXtoChU2zKgT19At6SRiBd0bRtM6o6EUxvB8puZV\r\njYHURM1SBGlE2uTGswDxJvr+ymwrboxU31wXiL9vC4ZOHM6wxRCVZQ9wOW+n\r\n+luNDZnK/3zCdlB7XIjthLGEFfr4HwPKtQ56TDpbxX7gmiMHnMwKVtXTIj1R\r\nAaPnKBc7zJGB9SfelwDkaEueaYxRCVmmtabwcG/ubVxeb4gcPKYE7GIUYTSP\r\nzn4G8n/nvG1++yhJRb5SrTFby1xthQLw7NWdTZ8z3BNjP5V8b5W8qXit0Vig\r\nCb5aJby/0iOSMpc34d5M91dINI+9Qc7JMBLUWhURE73x+wZc5PpJXCQIUedz\r\ncg3X0CcNjz206rXu9au5u+QqSvaN+wrg3o8l5+L37s/Ex1NhNUm8LLWjJAKp\r\nIiLs/Uc+WI5Qz714oLjikmdqtDWJU9kNYGQ=\r\n=hWk2\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.1": {"name": "axios", "version": "1.1.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "ce2fd17b4517150e0ff475922a70453d5e3f7f37", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.1.1.tgz", "fileCount": 77, "integrity": "sha512-hUo0TDluM7WPGeM2834mualoT0vGt9vcA+02dgfCiSpSPGdKbMhNbnTUwDf5hs6e4oc8yO7jGI33sSKtW7FRDQ==", "signatures": [{"sig": "MEUCIGrzq8z41QLGJQ07dlC1Enpa7y+PHx5bJZ1+d1FJAOpTAiEAgCFuONOFplxdljqtp5D/3bDD5yJZGHOIYekcrNz3VNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1300723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP+4UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc9g/+OR+A6txhgVfZN3e4+K94QgBD0e/kRYzRvFZWRF7bgNVxKvhc\r\naSR1/D2aI6rG6wv3F4wPRjdRzaPkDayo/FxMMXD1ymefNsPzJJbJNQvId0ko\r\nqCfmCFks39JWdXarhhXg4fgNAXdy7dHw2uaAAx7pExc9z+5NtvrlrwjHjein\r\nc/1wIXCT0E+MR1ie1njaPNzv2WfkrHoa+qOF3wSiObsjhotZ/PtWraEA1VOp\r\n7Yrqc3vWzlq59nmvSNyechgTEoZ4KFwUIPXxbF34T9GL0Pv95h1qMmeva4ct\r\nCrJpTomk27jIc4qtkGLODj93yaN2Hod1tKktiUXsUR6CgFgq37Dc0AA/UN2y\r\nolkMYJcgF7wBJD2ceUe8aVAQ43TkCCGnqdMw8ndigFxBU8GId8sWdia3QjT5\r\n8phbiPTIM/C8BrpGIXnensfrpgk4MEUkfsCXszH4NCpGrP4qdARrSDdqYyAh\r\nB0kNZf8WDYQ0WnMIliYjf7lNrawsBZomh/QToJuvZ7jYn0akBFRyzj2iMrC5\r\nA9GmJw7oKOlNa8KSV4HQ51hPLvRSGBxhwU2rqd99J380nqmmG11uHxS1POdT\r\nRL1l8c4QbZzafVUo0bvevyRXUH1Ct5FyXCC0Xp61Ec419zcVx4xO+jW/qB8S\r\nNlEAy4Tgf/ybdc12t8duKrJc3Lh5g6aFZmg=\r\n=V7fu\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.2": {"name": "axios", "version": "1.1.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "8b6f6c540abf44ab98d9904e8daf55351ca4a331", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.1.2.tgz", "fileCount": 77, "integrity": "sha512-bznQyETwElsXl2RK7HLLwb5GPpOLlycxHCtrpDR/4RqqBzjARaOTo3jz4IgtntWUYee7Ne4S8UHd92VCuzPaWA==", "signatures": [{"sig": "MEQCIHcJ4tncF7MMWGLFikqS0SvdEddaGdnIPpm/bVXEGNI3AiBNkjkFO+cOXjtl+JQ76n36bzY0CRwGVWndfa8fTizUww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1300731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP/wWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrR0g//fQSLvHb4JJqfaAJ4vSySosnDkjWzHaV6gIi5XbuPFa0N2JkA\r\nai1FYjHbCbuqSG7SBtTkEyQvr5hd2DuR02db+6PMSIeS+/+ZUDNgoOG0GugX\r\nMz4J0SZhWRKjUOiN7O+zQjpovbXr7wGLv9iv+2G3qVrBKvg6Tr6EtUQEU8r0\r\neBWf5K3qdvdUXs+Ufz3HdfOuG1qhclRtyq2ID/PfvcALaxNv44D2h+agUCPu\r\nNRAkARphV7jQ+tWwwho1tBb8tBZuyIYau1FJeKwEJxtHiH0h0CDO8UqWUQp3\r\nB2elYQrjXMByoI3q+jvNdJT2kAQ+9/tDNvGOaD8G0eI0RvcYWSyEDC9xhMwy\r\ndO7XwOVAo34XvIp7WT5o81zomGRHSnBm7bKn/kJtn9whZbYYwvrdK/Z3L7Bo\r\n0dNcW5jt8bn33wypPco5Zta8lBcj/YI5hY/gwgZlRwdKZm2x24KD58zFTwHp\r\nyCjG2ayN9KI5eCX2JpqPiXk+/KVBE+AvWJ9WqgP4pmhBUR1PMv0l16uPwWBC\r\n7gkSjFAL2zgcoetb39SkNKL8tLvit/AfP0uBOzKZjNUIX+FpJ5SkaFO/OJ13\r\nabpbllyP9rBMYjyJyuXKolQtiykT0VQYAZRot6m7bN9Q2ny96fhO40cyh6Ou\r\ngJtNg/jC8wsN8dW2BCK+1wz8OOHIqTSCCnQ=\r\n=Z4mA\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.3": {"name": "axios", "version": "1.1.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "8274250dada2edf53814ed7db644b9c2866c1e35", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.1.3.tgz", "fileCount": 77, "integrity": "sha512-00tXVRwKx/FZr/IDVFt4C+f9FYairX517WoGCL6dpOntqLkZofjhu43F/Xl44UOpqa+9sLFDrG/XAnFsUYgkDA==", "signatures": [{"sig": "MEQCIDNJymBuI9hmBrcB7Ps+Tm81OO6f8cLSMpvnSk8qkLtvAiAu8wsBO/rcAljZoSYV747Vwd3GAYPl7l2YRM1LwXp3yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1311679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSri+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHWhAAkMekwxjDWQgBn0OCeFRdRlpG/agFWpZ+6W5ovFjU1C3GNBNd\r\ncDh2zmiCPuuQKlnPkOoMa3PczJzl06fgLpfY3kxESRMyrngsoo07rMstYJ9z\r\nBls/Aqj3EFwTy8suzlGKbvK70HOsUgJLcfXx4h+jEGBfcvH1tGj1lXDuxbNh\r\nq0wQPMHefC4RUwI4yK5/wa81H1ATLVRy/A5xXGeRvxwkjNIB52Th+6weO7yZ\r\nlrYsyoIWO4GCgnZvYjSqhKXfXSBi/TcoB30zw94r7Ykl0qo8GwrLJ/adFawX\r\nBs9HtdCFMfQ03XJadwL8lCPKp/ujCt8+URcBhYJjDZAvBqkxTphuxsDA80QH\r\nAa3xuGwV9ogx70M7R0QnfQBF5P12AD8ikO0Sl2duJB54voUlat/sxNXPhD1D\r\nEauRZklim/sPL006M9rpbzVWcNizDQnhNqwkRBQsf2BD1kko8Zg0c85KEIGJ\r\n8MtcC8bf0/1hiEotZG9Xg4ewXjQYSiyY8dMc2yeT3rbc/uoPJfZCYeWIKbdF\r\nMvuBWKnuUB943VvczQtDDtXbQTY7mBL9ay1JvXGO1r29Q9hQj8yQsOo3xWAm\r\nZ53KSzp79HG4fbKpVn9VD3cjFsKkjZz9K3z3ZikKGB17Uw/kLiIoJGrUsgij\r\n9qNbDF8cmtfsSzVkSzx+zbZCqZ3fmOnL17M=\r\n=Kgzp\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.6.3", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.0-alpha.1": {"name": "axios", "version": "1.2.0-alpha.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "This version is deprecated. Please upgrade to > 1.2.0", "dist": {"shasum": "67ade13f4d84c26ca555e552f15e3f6fba849e0d", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.0-alpha.1.tgz", "fileCount": 80, "integrity": "sha512-qt/7xkSQNBRKP26mt28cmSI1Y3jVtrQzu7oLjIyUHEdjpVeg100luMJrRpBlKlCmMd233Peu00mOkNC1OwXOrw==", "signatures": [{"sig": "MEUCIQCFjuhNT2JqUJeaNPp2AG/Q4CweWHcTR+wLUc7r2ESqDAIgYpqhu9eTuq9K4qeaJZ6zxg6hAokgjYdAt5bSBdrVhm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1610647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbUuiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr41g/+KPt+FsK1k5Esjk+O+u11kNUAVeJdXSyYsislqrPYREv81NSZ\r\nGJVw3naWjooFhASZh2gFleUaHdB/b+d5T1o6KOupTUeTxznHHKWfVO2de9gu\r\nyAj1X4MHMKgqGdUeM4eZF35g4XYvyQaGJD/bEimrSVo4bQjcUgUWVSz1/CAu\r\nTboPgpztELmkF9hzniQcWVArnAxLmCPjjxnMBmXfIubfjgZDgk93KqN8XhUV\r\nH2Aq/U/KUucl7ocbThDfIpBThhjVlK70jGA2429iXwvnYL5ysMjCIN7hL40m\r\nCgYEsZ2EsylLXpZFDGtEIBrD2ZlhebbUZCxiLZbdBZfk+962x3R+Q68qQkjV\r\nEuKro6H0CK1lI9vp3eg2MIcRhr3JW/1vOkG/Qn6kKt+6V87rvL5kKqwTN1/o\r\no4Auo/sKbmVVXhMFk2GIeOxruh6e8fYipblmoNNcT+aN22O1jYTh0RnYUM0c\r\nDvJu9dZlZmrGe/cvHJvyqS2BaFn8OhP7RQ9WIopt+J+6XAoiZoxIbdC9J7xe\r\nFYl06cdzmDyM12cQofQfDHARwlodOLxJLYCZfP3khkoqeo6V3nloHgh4TL7p\r\nwRReeKDShxFa5Z9AkW9lEWVoU9Cuu3nbm64z1bIxJ4lGp00X58Ui1p9hl+s1\r\ntl7qDmz31jilv7mOhU7GyQy47/CDXKVGZMQ=\r\n=V8Jt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.0": {"name": "axios", "version": "1.2.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "1cb65bd75162c70e9f8d118a905126c4a201d383", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.0.tgz", "fileCount": 80, "integrity": "sha512-zT7wZyNYu3N5Bu0wuZ6QccIf93Qk1eV8LOewxgjOZFd2DenOs98cJ7+Y6703d0wkaXGY6/nZd4EweJaHz9uzQw==", "signatures": [{"sig": "MEUCIBqmRD0ZBL9MuoTuMt21E/4Gya0cXbLRajTb56aTE8kwAiEAwQs1AY+ZUZpXrHPdfcuQ3PCvrHl7C/dbnWXbmc6J6f8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1632778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfR2aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8SQ//cY3pm9VN8v/5Q7uimYOVF8fZ7/hj1Nh2WRe4QbwbCirGfFLe\r\nKkxPaVZzi2OMRBAe1f4qqci/zTfaLxQ03PeCrJneObme7oQAkNQ0KTTTHTFm\r\nRoeyOSnZj+ApUqdadyJwEfFFWhEcwYyyOKmFtWPYYOLUQu2SCVy1iQeo+tUM\r\nyYEeMNM5VeaYtOUc7/fmjZBbD5w+0kxxKcRt6X0veSfonhQs+rzb4KZ<PERSON>ir<PERSON>\r\nY20stqoCCk0gudK4bNLHx4xQ9UNr8k6tVIwgWEMg1ZN+zDfEVS090isyonYA\r\ntHZKX2LoLUwSCASQA1WxTxDGCMrNWQIR2MMItm+0yimxwE3HARXq7sSKUnYj\r\nSc2oDdHBq9BCH8L6coCXPeMFplhUqzrLzRXfyI02/JOvmpbif36ppkVBkL/o\r\nfZrv9QOogbDIJvkWGksQuRhmhe2NAiatDt5gDN7+grzWIlmER4+8R0tdYq8D\r\nmlnDPJRy7ThbfsXh07FxlWjLjjwZ/pBZUZEqhWp3ZhgDkBOefo94PVmlXiB9\r\nrl+P21duQQxs1a2eugShSH9zdqxuFl3YW62drFfRHqy4FgUOYbL6RZyL556P\r\n/nHHbu+tt0JHtmWiREnxc4ZXDJXiYjYzcn/1bmc1GMFLArUlEFDJQiEgbbOh\r\nRkfv+Uz1O4cBOKuSkNplMnBACKmuuCWQNIc=\r\n=4fy0\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.1": {"name": "axios", "version": "1.2.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "44cf04a3c9f0c2252ebd85975361c026cb9f864a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.1.tgz", "fileCount": 80, "integrity": "sha512-I88cFiGu9ryt/tfVEi4kX2SITsvDddTajXTOFmt2uK1ZVA8LytjtdeyefdQWEf5PU8w+4SSJDoYnggflB5tW4A==", "signatures": [{"sig": "MEQCIALa8nImOsyGJHimHc1XQTytmmZ7Y7ui61K+FC7gHgqQAiBLAAVzO9xrK5QJgwPNPvw3js05jjQtTJy7fquzsIhXYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1644553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjkjfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1Og//ewglZj+Wx+Ohb3O1c7oYDNsnR3TJlgnu1Cj/iotQ0rdTne9c\r\nzoJIG18FvNaeeqex13LL2+Pi129JkGRmipzvwxPd+gJphlIieouL2ZqZn86i\r\nNuzr/qojup7+3L5Cst1knrtWofHlaeE/aTGSorYwF0CLqPdBuGQoGhzF4YDK\r\nP3YGX47Kc3J+cuhrZN1v+D2F+AGM304qhUt+RTkNWualnvOp0jppRGsh1p4/\r\nEMpEib+McYa1jgS7X4nV28CdziPmfFGbEI3wnt6l0XuF1Mn8Y9Kkvp+qQfj2\r\nBE+DV0jem04fRHNrLx0pFsZ7kLzEYWvIaHhhGnLAjR1v17bGz5M2Jg7aU6eK\r\nNO+0hgg1lZPVygZ29Vmt4/0pwmk1qSmQznl2V/HV9hPHUUN8c7UsgMrsNVEE\r\nIynGnrDvnYM05c/oaGYmmqvOwHfk1E9sYUqmCFZhVYdC9zbP4wRJKEua8It0\r\nS/D0EYvVsdFKRb90vLCbBYK3vKCa6CbX5tJEL39rR6ZMtsUILEFlwqqHexwA\r\nXo2Gmjo1ohB4wW225Zz4mK9aIk5kfMlxUdYFiqYVJ6XUYUId846J84cHZ14E\r\nVs3Xj54eY0QlXvcSSckdOsFUKU2Id1R3FkDB6F9ehKDMIqqTGvU2JYMU84Ak\r\nQJmNpxDA0oDRMP1E7qoK9XUCkSgPJpD8LxU=\r\n=b7GK\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.2": {"name": "axios", "version": "1.2.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "72681724c6e6a43a9fea860fc558127dbe32f9f1", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.2.tgz", "fileCount": 76, "integrity": "sha512-bz/J4gS2S3I7mpN/YZfGFTqhXTYzRho8Ay38w2otuuDR322KzFIWm/4W2K6gIwvWaws5n+mnb7D1lN9uD+QH6Q==", "signatures": [{"sig": "MEQCIHTg6RwBEkY7T6mPv9ib7YAz8JEMaunVWXVEeuF3KohJAiBOZaJQQSuDyHdh0vUZXgogBQlu+QLhum7OqBe1+snbHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1679761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrTXyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxnhAAk0AvPrTuZvdb83NIcRhvc5t9Cjho97wjMkjeZj6Z8kNiaRX7\r\n9MM2fu289/H6GrGLFYzSo5LQF+l2zMnvd/OVOjwZECe+Iuq/n4V2IiCaxYrn\r\njztXYRd0jOngZoM7elEYxjwUXRKYJPoh4zs9Drq2Df5MQgsjawsYkgwfAb+4\r\nZjn1X+A4oZ59EgCLDBpmQFrHxjSGe0N88a8MoFhRmhJ73YKOpyONGfgCvwmZ\r\nSimmifFwgcD3zV9akXeaWgEAUr/5pbmBoYUtKS0VOmLHP693LnY6xpoC3AMP\r\nx0uWhAWw000OMq0I0p8yDTKcMVcF/TogXDwb5Q5bDJqaZWznYQhFGyYcg3ki\r\naPkuWwm0qTwTicvzPCHvALwbJRkqlmWHrnDd9oHMlpBtiAshmizGiLSqoAFK\r\nj995F0ZU4h4iQRgTDrfE9MZnYBxjT7XUs+QLO2OZ9Q+znuKzAK0WnfJ7LsPT\r\nkf3BjRPduk1Sp2Gwy+1uKi8RDIW9NUcQV/Hyaa2sxqXmlpUZGqt1sacVtI47\r\neWSLkaPN/PMFSurt2tbnvBxZn+VriTyOmDkOYtPHzdtUajChrboLkPFloyJa\r\nYJ3HYYB286b9Y0IZYw0ipm3sXWk74DCm8a56NC1O3U8k3xgL2QSxz84/VnHY\r\nAzVow19Ucdvn74GCbuA+tChg/F2Kw6tS0S0=\r\n=qYIM\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.3": {"name": "axios", "version": "1.2.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "31a3d824c0ebf754a004b585e5f04a5f87e6c4ff", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.3.tgz", "fileCount": 76, "integrity": "sha512-pdDkMYJeuXLZ6Xj/Q5J3Phpe+jbGdsSzlQaFVkMQzRUL05+6+tetX8TV3p4HrU4kzuO9bt+io/yGQxuyxA/xcw==", "signatures": [{"sig": "MEUCIG/IoitUsKNkZatUX+tKfF4r1CTQAZ05WdoLs8Wh7xESAiEA4LJ5f/vf/GoH+6B+clGHmGNHz2mLcSO9TyOvEEhs67M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1680874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxuFXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0+BAAlJxDP+Gp7eb73gGbwPpKgbWARAYD3wsJg6NKRFTn1yGzzcHC\r\nkKFkl7cw5iPWqXX3bXQWedgCboriF/g3dpKqjA7lCeuAU/2FlZCivBoJ5ddo\r\ndsS8J8rGebxz0gzCmH9XL9EYijTEiM44+IDgJgDIcMhmEo0j/uXIzvwdmYtL\r\nDoqbzdxTf2NOjtmDLyfSFOyyXGUWijyoAVRVB4HynmabfDsCtaycKX+uUjMF\r\nHjJyZxyPN0dv0Be0LIXRxhLRBSA4ju9lnoByds3kKiRx7Q1uDydtpJliF8jD\r\ni+oU4GtFYF9G84+EvvQuZwv41Bn4r3sAeegieaHkv/BfpU6I5BILcLRI9e/B\r\nb2A3kBUEidhDq6DF7KYPlQyM/2AWNPuAc+iq1HVLX25j4bJ0txkVdqixNuGg\r\ny6PXvsI3rTKdfdT/uC5TGUVWgS0qR5dvjml48W2P3B9DDTC+FFx7Cxjcjj1P\r\nNHZUqeAk9LJB9po1mTYVx9/5Ry7CDBcCDkx+AgF9Rnpqi+q7AiVbZJm53aYd\r\n2YELpZ95y0VuN6SMVRAiS+pJlyq60GgUvupVK0mznXlY1FyQ4vps1Zs52d0R\r\nFjygHxYPgy+xqvHTJg15BII9Xg94x9eLe6nQLFGNAYz2JxCDdpoQvmdyjscF\r\nbGTS3c76zrY6JAKnuZAt/7N9G1tfNMnto08=\r\n=yBRt\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.4": {"name": "axios", "version": "1.2.4", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "6555dd955d2efa9b8f4cb4cb0b3371b7b243537a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.4.tgz", "fileCount": 75, "integrity": "sha512-lIQuCfBJvZB/Bv7+RWUqEJqNShGOVpk9v7P0ZWx5Ip0qY6u7JBAU6dzQPMLasU9vHL2uD8av/1FDJXj7n6c39w==", "signatures": [{"sig": "MEUCIQCC/Vw56xvWQDjL1wORS3sGICPhTwF3wsmBTRBPq3LOAAIga4RzKzst2Z03oiBmt0tsL0zduhbEDZOcS2D3ONCedjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1676740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0BO2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+CA/6AqxQ48Xms18aKDZb7y8ss8Z6EkMaU93D5ZIp0TiKFho6U7iJ\r\nSVlX7/TPWy08PLZcFgHi1NbsovQsOp40tB6bN7EpldEOgTX7XIpPXJQOHgkn\r\nlHUB8NKg+334VyvVx30EQIvmZs6yPOAzenwFman2rpy7S/CQqDHzxSBj7iTC\r\nijLM03sJA1LiV3CNop93dMn0BQmqggsJ5M6pglsMkB9TvjUBom54CyF95x8e\r\nvv5Lel+JkAGMswA051lpXEZXyJPJOysB/qnXZmQnDQRlsrjaI3LUQwhzoffn\r\n4RIpHRgEOBC8fHma5fOf5I/+CaIPncLYkTUc/YCmubycrrR4s0tt5APo5iIt\r\noeCRV4XFpqRxdFuYvGbqk1eUYKkKgWC0KqRGwDrOfzwlF29yda3CikJMeliP\r\nxTXDUEfk3xDHUriGa6iEVNZn4Lho/0VunzPW3NVIalB9Vuw3ozhjOGcCQyjM\r\n2DZfxfjHT9C6/XsoE6gm7aE7xuL5mA0OkRz0QRrzg5NiexC89Z58xRYSjJAr\r\nwxw10wDBeq5QSYat1ENeZ9b2bvwwhPBeqc6aapXzSUiD9ch3ZLg1Sa2QpXU5\r\n2SOqBdPtDk//JBCEG2c0PN/wl4HEsBb3mlrCkvSpGhSjegyjz77yrd749CZc\r\nhWJ7ndOLCqQFDt950y5cBvd1V6Q+DTABZdc=\r\n=99j+\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.5": {"name": "axios", "version": "1.2.5", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "858c129d286e7ee3b1e970961cde410ba3ea3740", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.5.tgz", "fileCount": 75, "integrity": "sha512-9pU/8mmjSSOb4CXVsvGIevN+MlO/t9OWtKadTaLuN85Gge3HGorUckgp8A/2FH4V4hJ7JuQ3LIeI7KAV9ITZrQ==", "signatures": [{"sig": "MEUCIFrfmPVQMDFrkHE8iike9G+qyI2kEaBDsEDBG9mWtNLtAiEAq+L5PUg9kVsUjeRufjpgRW0C6j3AV17iq0Njr/vuJXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1679167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pb9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMGxAAh1OdRSz1A4u2qGgmghVk3yagKRICG42EdBI+Kr9SIkDkZFm4\r\nAfu9RpouUiwjWudkl7ulTIyvltGZ4Bfyfpp+MOR1jF67evIzPLCS3EamtP3R\r\nqIItK7fbEhPNw3AfSekni/4yiz89GVwnx4wxowrk+3x5CnRx8Fz0bngUWYBD\r\nFw+8rhAb9+7iXpOniwGJ1YNvrFsmCrS2SuDkMKd4/dLXMFWWcPCADuA3S6Zu\r\n7ftne2xrJtpdWP9nLTT5DAh5KQW2HDXQVp5uHo4eTPoTtZOR4y/hDEq+7dyC\r\nMBDzFeLkiukP5HHTDnWAYS/04ayR+stBA9x/3yJIJaAMfY+4cphz6N2HfuAX\r\n20A9X7/a1MpyW4yqpbZCDYvoQiWQ0z2Ndy07kT4qhfTDYl/7OC3iV4VldIpS\r\nTacvXqkwf12DUDkkj6DRBDylGns6X6T6NbC0UmFbCgLAetAl9qViT2pmuzjT\r\n5JiG+UW/BRFQHLl3j7jz8MQbLAoL2FhUCg4I3MZRq1MAeO/Fgb63nENsrPtO\r\nIa88kN5h6XPYxlv3f11yzbtDFbiGZ7w2wuVgETDUev9fSBESzmKbxSV8kwBX\r\n1hHpXU2XckY7NKXgKswBt2FRlt3fO3M2ZwadabrA4GfB/fQc3aJVIjJ0YZHg\r\nLXqdJSWlr1eQge6H8GgftiNOgiLMEgYAw1I=\r\n=80Dd\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.6": {"name": "axios", "version": "1.2.6", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "eacb6d065baa11bad5959e7ffa0cb6745c65f392", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.2.6.tgz", "fileCount": 75, "integrity": "sha512-rC/7F08XxZwjMV4iuWv+JpD3E0Ksqg9nac4IIg6RwNuF0JTeWoCo/mBNG54+tNhhI11G3/VDRbdDQTs9hGp4pQ==", "signatures": [{"sig": "MEUCIDr7W66tERawtie20BnnhG47mSxuhczZTRePqK8CbG6NAiEAzULPBYSQDPPUsopxGQv5kaem6ET8nVjS883nue7Xnus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1680691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1VAlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR/w//ZGAFEgl29QNj14lTHA6ZfmqGK1V9fU5roDlW+zhIDaDgeQnn\r\nL/di9EaBOrwQB1+HdkT2B39m/whMXf53HN3zYzlAKJ5tDQJZWsBGwNxqRMUs\r\ndx9sF2hdhYLHz7OrVMNOXP4k1A4fic+Vh1uLU6iRZnCmPyThq1F9FZQqB1aP\r\nutkqoVSlCS+SiBfrLrzsgNb2YqAjGJ/yJYBus9V9k9HZ87xVun4nwTU4ouuh\r\nJosvURut7eBFL9R2zjhK5/eOtCp26d52fTZtMPWmlxD5Z1viU1TJFxp6Zzam\r\nvb1dizUpkWa0+19EbAYACf0Rl2m8KI6u6Rl6anQ1OymJBIU54vprI+rmVHWg\r\nzAOA4Y9P2tuGvDbcojGXlArz5Gr3fCEAdU5odIkh/cvTnBBUwJKmZfEqrypJ\r\nCxR2NPttPGYInqv4SGTqz4TlaqihKrDMuNEvm11SHyZfxL+9HwMqln2/3a7i\r\noWOKJ9yomiqqGAom4Dfwal8MK0H6Ci/g9jle0P/bpbh1YVrBuHEoLsvYV7aR\r\nQ75yC+mieGbLERGXImstg2XmND7PqWl7R8XBSxJmPy9ViOTtmYmbQT+QGaDX\r\nyol2/gXwpUzgbUYQ0idfJRMmumg6xsgxbyb+dGrUC6LD3W45psWHNwj3NEJq\r\nIaTM1Xkbuc3UdV5Z774zRoWU8NydcI7ZDWE=\r\n=lMBZ\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.0": {"name": "axios", "version": "1.3.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "4cb0d72213989dec08d4b10129e42063bcb3dc78", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.0.tgz", "fileCount": 78, "integrity": "sha512-oCye5nHhTypzkdLIvF9SaHfr8UAquqCn1KY3j8vsrjeol8yohAdGxIpRPbF1bOLsx33HOAatdfMX1yzsj2cHwg==", "signatures": [{"sig": "MEYCIQCWPXi3hCBwJRp5ys8bXF7YPO7u/v2AiEf2tqufcnUFZAIhAOp2O2NcNXwWiuiQ2sAOwX4sV5rvYjq9xQ86ARK+RJWS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1716407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2UgTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxPw/+OvthZp84qCeOhtCgZyMyTJ15/Vu+jewj4lr+x+sevuKfHeTI\r\n179XVSd5OVz/SAA0c4nVdefsqUS3fhzeCQH3fUUUdXiWgCzHyRu1DFq3jKTC\r\naW1JS2eeSNXl7PscFIxiha6bkTMgpR9oHnLAk2uzLwRbw2e9/S7y6bx13i7L\r\n5ApUMP1zITerki48ZqVxqDjR45o08uNcd9adFrM35hJTSZWuEEKV25acfEKH\r\n7K8u9qfJSEKz4aPm81O1zSL7aXWyVfv/ZfOa/z5Ln8bnEURzC7Sc7MO5U/6B\r\nJfJawD66ecuAIX9cfgXR/gOpEt5giJ5kUKE4Z2wIfMjMLmvSmA1BwRhZCzwx\r\nk1aTDZ226gfucElcedhoc+9j5kQJRm0ASpykMAwRR9CvJx/JaAlyLdhuA4Ku\r\n8r1B3PyEehgtNYKexT2HrgDGhXGZi5Gwek7hApY5faL7ac3LL3gEpjNhKkxr\r\nDMBR/QpL7bHaSp+RwmAMY6RP3kjs1OQBu9w0HZ4UIMp9/1uEJgPDBTtYfnel\r\nR7OjdLeEknh9VPSvwIWyGT4gnZyWgc9kzT2FXqECTwjhBBNAS77lLW0i7hba\r\numU6ssX7AnPmpgIwOz0k9M9ZHRZR3djwxy6XDnc4GnD8Ivj/zrHu9rjzkwni\r\nU0qkM3uOlEHoR7OVvHtBN9ApOzBfRJhuhRc=\r\n=7BYn\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.1": {"name": "axios", "version": "1.3.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "80bf6c8dbb46e6db1fa8fe9ab114c1ca7405c2ee", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.1.tgz", "fileCount": 78, "integrity": "sha512-78pWJsQTceInlyaeBQeYZ/QgZeWS8hGeKiIJiDKQe3hEyBb7sEMq0K4gjx+Va6WHTYO4zI/RRl8qGRzn0YMadA==", "signatures": [{"sig": "MEUCIDj5XPXVyZN4V0wVwjCgjhkdQBT9bUDzRrBqi/J6AxxcAiEArcXeJNOvibkr3WQsG4KlMOecPK3ar6THF9st6jkAdDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1718219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2vY0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/pw/8D8d37AXG/+1jbM5MiisMMmuSVqSjhLdgCR0UJ3nUUaE9nC/H\r\neHBnMSgaHQnOnZ6Rt5FGpcwYgP28zyeRVITH8g6kfqJ1TC+Cstszmfp3bSl9\r\nEth0F7aIT/hVVoNpuzDY5nDRvadn43XRfXnkrawjIda1UP1JPnZO/j1Z4Net\r\nJB+E/zKe5qlqnujYGZe2hYum4xeDUdvXi+VZgQfTllmtArG/8ZtviJMej9uU\r\nRFlPiwh0M6qD1MQhpCYUSoHSP47Onvmm8hQOXdBquKLT0lG/BT+jta355DrR\r\nsCdzxzOkG6AP7F3rb2wSjd54mtEe46ebdT+rJjU1hUWDA4uZFbCsZa/xTHy+\r\nrVL5CkeHlBazSlh/4pZUEX8Jp5Gw2sgxdYF3jQ3eV046aChP7Bp8BqF127A3\r\neU8cipz+b6LeVuWgFOYdGFAZlDs3AmqPvmpRTzei7kKSUXQh44IJCpSYuy90\r\nfu7Z2Nrc7VPilmhmHtIhOXLKin89zjo8GDB4k/dqEgkiL+0hVin5QLtqLnoy\r\nd93qFJvcilI3RRGnld88tqLvNd9zuxgs5BKDRyOJ+8YSfMLP8PtGzQ4c+xGH\r\nuVzjUlk9D7J9OSWeG848je5hvgifHiiT/16JEiTJ8RQ/EBGHCGlIxztZl10A\r\netO1VaT4GmbbsT8+bOuqc7CLy8QO5K3j/5w=\r\n=INoT\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.2": {"name": "axios", "version": "1.3.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "7ac517f0fa3ec46e0e636223fd973713a09c72b3", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.2.tgz", "fileCount": 78, "integrity": "sha512-1M3O703bYqYuPhbHeya5bnhpYVsDDRyQSabNja04mZtboLNSuZ4YrltestrLXfHgmzua4TpUqRiVKbiQuo2epw==", "signatures": [{"sig": "MEUCIGQLexzf+wShODXg2mIavTw4MBQSBqDBAwzVkCCsIQP/AiEAsFW/Yn5J++NOzlG1tjZ56DS4Oh/JRveQO+++rP8To5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1719158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3U4oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpo9hAAjNVOzyXAfGyx1ZX21pdum/aUI9Q7AQwN4YS0NiGr9OGXrvTB\r\ni5NaB34F4OXWtQ6hEpRxvJ+Zb+OZRVULp0rowrFWJbfnq/s5EFe89L3Witnf\r\n2H6TeMMONVvOGSL/Bn3qhtv8sCmG6UhGGCvYANJIstySedc4WdDigRs487Tl\r\nnxkkM2oArsxi7UQ7mVZRAgL7dwSuekWbDYSXxRTGmJ2+OXAP1DBtotPoB1mn\r\nMws6atVhfslhAD/+xinOOJlN0FcBwTj6oB9d6MYjUgZLnCNdCigSC4GCBKY6\r\nTg8Mo7Nrut4cQwD+L0Yzlm4CjFfbBDIAEsp4nXaNE8F2orcDQMnZcEdBiVXG\r\nTPC/mlRUfJp5d38WnNhTreNscIFI+AUribnQMOCYna8BFYzzuG0v6iJu42RL\r\nPPaWgga6u+PZ6rozvGEgQC5bfUTx586+kYdPZ/ttlvMo/0mmROqMzmkPupPr\r\nPsdNNLkxyqvJazzHOyA7Q8EcWesmhq7jXJnuCyVplvULmvAPYvY5yT0dOLLS\r\nlDuVcefr53aS8mFYK3jTv7a4POJSyLdKLfFAHG0l6L1X2TgCnc8zQWvkc1TG\r\nrx/Yu36MtkDSEOx49MYCft7cE7yhYish6XUKN1nv3j9s8NBP1OX96cf8NEnW\r\nZZPndiY3kLx0TAcrMwdc9z96I2RN6E+LMXo=\r\n=CXLr\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.3": {"name": "axios", "version": "1.3.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "e7011384ba839b885007c9c9fae1ff23dceb295b", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.3.tgz", "fileCount": 78, "integrity": "sha512-eYq77dYIFS77AQlhzEL937yUBSepBfPIe8FcgEDN35vMNZKMrs81pgnyrQpwfy4NF4b4XWX1Zgx7yX+25w8QJA==", "signatures": [{"sig": "MEQCIHyDiGw3Gxt9BI87BDJbAhd7Hg86kBiSQSpS0bmOTNhDAiBMjhzlRSpseXIGAl4+N+J/ppl/V7lEVO3ag43MC0paAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1722523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6oW4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVmRAAg0YlvTWIHVeUnloznejF6Znvi79DGJgqSRI7WHrr84tUxrpp\r\nCtWfLHTJ9+B0ORgqY9Wfld04ZodIRsR04wdTL7nf0hEbptmK2lhHVbzIbg5p\r\n3WLaZPnIuIa8NqTlZJQQ5Xnwwlt1leZhwPhFuBjZ3eonfXhIRi/Xa5aNyW/7\r\n6kBsCDOzQD6Eu9DFFINb1DfIvZnR0S47670ljASE4K0fYxMmIddLLcLMrRfD\r\n8CRxNHPP7Ls6PPLd4btwAMWu0PUmRKSRJ6+b+N9VwIOT5VQObMsSTy0G3uhj\r\nKEDKBZUP2vnWO1GYYXGteDK7H5/zgAzb+X2Hrh2+5URJItDfkj2jHpGa1uH9\r\nUAb9m44j0vPgNe+Ap0NsEnRgMvaPNOmxdyA4DUgYYaew6xsrBOHgGdCjLtMK\r\nGH8INAfTTmUVL3JPJQFWnj6Xv9maLd8kg48/RzhEj8OREBcOkeG1uuUxTKSf\r\n4ozoynH5LA+KNz0EJpQDg4MEMAdm45vNitXIeuf3Tv3qRqM6JC1QZ96x/VRd\r\nWIm6Y3oV/o47j1kwwlgGFlaBIKoukknA2R9k8zMF74H03HMdtXLOrBEWeV2J\r\n81nJDW2btymk3ERbLQpgtWqFHvP0VJmh32aEB9rFoI+r40JYnwU0ETIvM1QL\r\nz9PdO04avBIKN4Tm5S672drY5QsD9wsdNTU=\r\n=9P70\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.4": {"name": "axios", "version": "1.3.4", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "f5760cefd9cfb51fd2481acf88c05f67c4523024", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.4.tgz", "fileCount": 79, "integrity": "sha512-toYm+Bsyl6VC5wSkfkbbNB6ROv7KY93PEBBL6xyDczaIHasAiv4wPqQ/c4RjoQzipxRD2W5g21cOqQulZ7rHwQ==", "signatures": [{"sig": "MEUCIQDdGUt3fhi69x/8YjbifEvAs6id8Q+lP/9MUzUwRF9m5wIgGYW2WBQJ3jzgFwy71s4PaL6CWKQulN1EwznlKIvZy38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1725978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9oPPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqx2BAAkD8JAkJQ4cxTHcq47meU4cLUk0eVpUucLfrGQcNFKcwxjXSX\r\nWrz0zqhretQ60YnUoORODVKl+tlD6EHTGDEpM6CJLP3YwAHYuXLiZkYQvBa1\r\nRm8MmDZgCyNTiA5AtH5szW/RM6NojxV5StRWPBFnRlkzT47uTLkM9XVRBO2X\r\n+Iwodv2VkNx3rG+oWDscIQP98ZRRW/ZpjS3HdSIRKHZEaLe7veMVMRrzVE+8\r\ndknaZRNKKQuErmpLiODkpOysjBhzcm71g+w2Ri483y1/JSFEW/N4+w9tQglY\r\nLyDLKd3I3u7xquSzD1oOVy2LJiMG61DpTBywXW5TCbTnyvkIoUss3p6M/B5R\r\nyHxAbdDEQy8wVpIirsreNbz1AU4MzqfbehHXdVPslto9yugwhGBp/K1dlPUN\r\nIMWdD5XosfiPHxAyYI9PhFwq00/gp4w6u0uLd9Qm9d/59+X0vqp0i2OlKSZf\r\no3LqfRBjcIldw3bvrPrDwOsNhQEvqNHspJ3XKn6ZCxGNOThF7Js1e7Iqa0FI\r\nkx85GXiERGhatwmJy9w3SUV9K2D2y2HLFNXHcFDRnljO2I/8sAw3Ujzl6fZj\r\np/k+oL/6pV8Yz0ew8tSpNDWwQ+WUJxarLGVZ0Px8i0CpLUXtH+Q1CZS9BxPv\r\nzI0zxjW9Qer7mWZpHSgOkNoBEcQHDi07Vlw=\r\n=QzfB\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.5": {"name": "axios", "version": "1.3.5", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "e07209b39a0d11848e3e341fa087acd71dadc542", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.5.tgz", "fileCount": 79, "integrity": "sha512-glL/PvG/E+xCWwV8S6nCHcrfg1exGx7vxyUIivIA1iL7BIh6bePylCfVHwp6k13ao7SATxB6imau2kqY+I67kw==", "signatures": [{"sig": "MEQCIGdEyOOIwBVBewXZxOiTtkUFKDW8ZmtP+jPesQ4WGN9wAiAIV3Rq8dJwhaapgkfgfrDVjVCZzZ4q8mhJvelDyX+83Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1729516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLbfXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBQA//RGaZcaC8TX+6a6hGKMg4GsPttJEgXEQ2cUiZ+tDSvD7/Nr0N\r\nwiFOwdJ1ZL29w3lNiFS3/UUlqzp9am/OkLrDAnXUI+4fm73jO+h88hThbk1D\r\n4QzaI8SItELqkSJznYkA9u3Fd89tvFnOOM+dCLh9cQvDRjYuURQJITXVUxeN\r\nvISJYKw+Znj6GW0+jHUlcFFyBnQANWWoHb/N5N5pX5Q8kp258/O/4UFBKRcj\r\nd9f2+VZc/KU+Sp88MaerdN9znqxyoG2eeftAlyc4QsnJo/fp/rLSde6cyMaY\r\nPMoFMM0TeFmmOVaDPwnS0EuPmMdBUcdw1CGB/69lBypV+IvRAvtj9vgaCjML\r\nrGzBNAekuqFkrohKXaYu+OsY3ium7vRbayiglMNMdpVd0Nm9imzEv9qaIIJP\r\nJPxQcVbp6PuEkTGKimAIcd9Vtoi8lLdShbIABaCSHygkPVElZRhkTcKj7ZNJ\r\nAC4HmwvhDHxwWG6QF2nP2vqdo5o8Ok34+nNntCm4XWmdPCHSW0fAfEaEsgIM\r\nw6eWeIQ2Z+lsh9Sh5nfbWF0H1Fsh/XlhTZPhbZ0LdNoyFXUa11lSc3WJ0Pk4\r\nUAhFYhZwCIN79JH4jitaIOYGdDi8bbEuB39xWNI9GZyVbvxVjHsioH2Q0Tig\r\nsFx+l3ib6kg20QSf7XMF8DyyWbXV2kK7PDg=\r\n=ctXG\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.6": {"name": "axios", "version": "1.3.6", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "1ace9a9fb994314b5f6327960918406fa92c6646", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.3.6.tgz", "fileCount": 79, "integrity": "sha512-PEcdkk7JcdPiMDkvM4K6ZBRYq9keuVJsToxm2zQIM70Qqo2WHTdJZMXcG9X+RmRp2VPNUQC8W1RAGbgt6b1yMg==", "signatures": [{"sig": "MEUCIQCav9+/zKeuzTN0qZlydahFUvsJS5iKvpK7F8L9yZ0eJQIgD0R/nlnLkjiLtwlSqXHgB3Vh8FoqKtUh1ImddHH7eZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1732124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQENRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp8xAAijz/HSUYeaWdhtr49x1N5DcqaOhLa1Iz5+yRgHuQiLPHaPxs\r\nDo8B9ulwvcXMUBDntlGqTxxrO+bo8wqDeVs0jotyLjxCmW/Dj8Iaxlrr3JVN\r\nXHsDRXj8RiGPGyXKKkK/2M/wQbqP8tWBaahI9T7Vc2A97apyu+iES2BDQ7e+\r\nfXSLV61+H/p+es0dwdXfCp8UYTqy60ctBwTWgcBpF3kPOImoKrLoBYk9giBP\r\npoocQdV12vELWXOSj+2giIhjmyRB5fN8/o9kC7WOQKesaWOV1W1pDxJAN08M\r\n9AkUauSr+n88ap9hVN5DsLWEpuMUx+Zqmnv0qOPZ+i96q2O3SZanDj9ukyNC\r\nE4LNDriHsc8xcEQRbA+1VFdiXCQm8OVAhBaZytwfyhBwR48ARyDg2qwY7Fpu\r\nLb9eh12MZXa+qc3tFY2Koa8ttnysWLTNOcFA+HiWTPBXNpM3uMs3NXfj7Ms8\r\nqNvAZ0OzR+BVNNpweZzia/QneSRh7WFa+F8HOOOnckYLsUL8264hyaWo5HNk\r\nOYsUiHWTbkMQFM+aWps8x4sM/eNmxjxELxQgV7eVDQlMQdjss+oN3JmkXoy7\r\nRDaHKpIzq6wai/Ok3p0h7FvM/aLYd9D8K+3Uuqn9iyayRCuitINM8E49OYC3\r\ndZNGpEcxZQ7Dgjz3SbzJnE2hGnUwwmG7oYI=\r\n=JmrB\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.4.0": {"name": "axios", "version": "1.4.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "38a7bf1224cd308de271146038b551d725f0be1f", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.4.0.tgz", "fileCount": 80, "integrity": "sha512-S4XCWMEmzvo64T9GfvQDOXgYRDJ/wsSZc7Jvdgx5u1sd0JwsuPLqb3SYmusag+edF6ziyMensPVqLTSc1PiSEA==", "signatures": [{"sig": "MEYCIQCgTzRr/ERpzlEqrlmwJKEgGJnWJnbFq8egGVGBH0ahSwIhAJbxoPCIOH0dEVVkLAyrYHcUlNOma3dAmegL6LEMCNKj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1744876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSv/QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWCw//bGowXu9gYn9RLjkTyDCeRpXf8urdh8A4XEsoNijp290rXdta\r\ntUFS5t35riwwy6tbbl6HAWGB5XkrBxzZQNDPRt2Zj+ZKVsilzjnQiVi7sLjz\r\nsC+S8eNuCXgzT9+GCqliEG67norJ8O/iC7Ejtu1vo6RFxlLPCoEXqA4fUenb\r\nFMfzJI3V4OydtCT9LFyUx0TXM82M/o3KBBuLaTVyUMG3BRro+2TWEX1YkQVy\r\nOLTBMicHy81Wtu+RHeuxEDcvNALvX9q3yEMjOZdlr5Z3n6Q3hXrWWco5tbbC\r\nkscRj6QBfD9sncBwxNjA6aJ9Ye1pnfAKnqwiuUrblbOVogEnjFQ7cDWPlWPm\r\nVZcKTUetZI4sIkWPSosEC5tU/GE5Gm4NGMUuejqlTpbPclPTEVbc4B2Rj6Vl\r\nVf558StOGe+crkg83BJKNEKF1vfOXjxJ3lbwccgMlDoIpifHyI/f76iN31Sv\r\ngyryO5hA9udhMTjRWP45jCi65ogvzNXFQa41E48Ii0olqD2xtqX/sCKn9a0F\r\ncxaY7j3xnIzoj8zlgglLjV3jzuDcOGAlsiCZBJZX6C0HsRpvnP+tZcAV6dlg\r\nO1GbIHaGyIwTRgDV/dxdmYSXXCuCNGyClfsnTxiiSK4kTteBJ/uQS6dr7I9O\r\nHzzHVPQL6bxyvx/4/fxRoExG7jiT12sIu5s=\r\n=X1R/\r\n-----END PGP SIGNATURE-----\r\n"}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.5.0": {"name": "axios", "version": "1.5.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "f02e4af823e2e46a9768cfc74691fdd0517ea267", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.5.0.tgz", "fileCount": 80, "integrity": "sha512-D4DdjDo5CY50Qms0qGQTTw6Q44jl7zRwY7bthds06pUGfChBCTcQs+N743eFWGEd6pRTMd6A+I87aWyFV5wiZQ==", "signatures": [{"sig": "MEYCIQCi9Py9XNF/6HCVX45kk4PFNpi5QyKeJxuDGkrhjIEQ/gIhANa25QQnObS1u/EXcURKvd0uzMJgX8/ea45Cp5U0SqwE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1752636}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.5.1": {"name": "axios", "version": "1.5.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "11fbaa11fc35f431193a9564109c88c1f27b585f", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.5.1.tgz", "fileCount": 80, "integrity": "sha512-Q28iYCWzNHjAm+yEAot5QaAMxhMghWLFVf7rRdwhUI+c2jix2DUXjAHXVi+s1ibs3mjPO/cCgbA++3BjD0vP/A==", "signatures": [{"sig": "MEYCIQDw/1qBDn6muYdNOnUuZcQubzbeWvYK2is47N4W2Y11jwIhAPWspTwUJHB7tjXotTRqf2lG1TaKsU3ZyRaPlbv8Bpd2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1769525}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.0": {"name": "axios", "version": "1.6.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "f1e5292f26b2fd5c2e66876adc5b06cdbd7d2102", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.0.tgz", "fileCount": 80, "integrity": "sha512-EZ1DYihju9pwVB+jg67ogm+Tmqc6JmhamRN6I4Zt8DfZu5lbcQGw3ozH9lFejSJgs/ibaef3A9PMXPLeefFGJg==", "signatures": [{"sig": "MEQCIE7T2XJHHp5uQyn4q/PCgUY+znlXOhB0T3DUBuB5JH1oAiBAFxtKYPOXrrFV2JMC1DY72p6ABeFW/AZ96gWlWVMNlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1785881}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.1": {"name": "axios", "version": "1.6.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "76550d644bf0a2d469a01f9244db6753208397d7", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.1.tgz", "fileCount": 81, "integrity": "sha512-vfBmhDpKafglh0EldBEbVuoe7DyAavGSLWhuSm5ZSEKQnHhBf0xAAwybbNH1IkrJNGnS/VG4I5yxig1pCEXE4g==", "signatures": [{"sig": "MEUCIQD6YBI9oBGxkwGqrHNGsqAObspt/HyOkJOTj9f5wiyu0gIgXh7f358iZCzh+Lf1bqfL0Qatl/eBS/zq4Wk0urwgz38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1797707}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.2": {"name": "axios", "version": "1.6.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "de67d42c755b571d3e698df1b6504cde9b0ee9f2", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.2.tgz", "fileCount": 81, "integrity": "sha512-7i24Ri4pmDRfJTR7LDBhsOTtcm+9kjX5WiY1X3wIisx6G9So3pfMkEiU7emUBe46oceVImccTEM3k6C5dbVW8A==", "signatures": [{"sig": "MEQCIAuObdAtQkDyGUFn7P/AHT8gCtJ6wOHeLEuLXKNilydxAiBUc03VL2no1an3m8T5uzKJ7tlqBswoy3Zyevi4UCvdwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1797704}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.3": {"name": "axios", "version": "1.6.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "7f50f23b3aa246eff43c54834272346c396613f4", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.3.tgz", "fileCount": 81, "integrity": "sha512-fWyNdeawGam70jXSVlKl+SUNVcL6j6W79CuSIPfi6HnDUmSCH6gyUys/HrqHeA/wU0Az41rRgean494d0Jb+ww==", "signatures": [{"sig": "MEYCIQCi9KWvTyfe/CGVqLbZ5AHtGUjCDXNkWKqUH0A0vN6IfQIhAPtDGmZNppqYp7RYZ02Eqdt3pVYU9q7RMxUwKHC7Cc19", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1798922}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.4": {"name": "axios", "version": "1.6.4", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "184ee1f63d412caffcf30d2c50982253c3ee86e0", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.4.tgz", "fileCount": 81, "integrity": "sha512-heJnIs6N4aa1eSthhN9M5ioILu8Wi8vmQW9iHQ9NUvfkJb0lEEDUiIdQNAuBtfUt3FxReaKdpQA5DbmMOqzF/A==", "signatures": [{"sig": "MEQCIC/fW8Cqf1tcsgqXP4pPWi6xY25rpusOx1ZTVlXcP8klAiAHhvH88hoS+kG3KyVKNdpDjS/eyhBt6PthTvwQQSliNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1802694}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.4"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.5": {"name": "axios", "version": "1.6.5", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "2c090da14aeeab3770ad30c3a1461bc970fb0cd8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.5.tgz", "fileCount": 81, "integrity": "sha512-Ii012v05KEVuUoFWmMW/UQv9aRIc3ZwkWDcM+h5Il8izZCtRVpDUfwpoFf7eOtajT3QiGR4yDUx7lPqHJULgbg==", "signatures": [{"sig": "MEQCIF/vpisa8rpe/teYRdtZV87O9h38B+GGrhCRqlTdVVpmAiBYQumbcr9KioxK+a7M3CxSoGXsO3hhXaytJn0pPidiKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1804160}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.4"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.6": {"name": "axios", "version": "1.6.6", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "878db45401d91fe9e53aed8ac962ed93bde8dd1c", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.6.tgz", "fileCount": 81, "integrity": "sha512-XZLZDFfXKM9U/Y/B4nNynfCRUqNyVZ4sBC/n9GDRCkq9vd2mIvKjKKsbIh1WPmHmNbg6ND7cTBY3Y2+u1G3/2Q==", "signatures": [{"sig": "MEUCIQCMlM0vYTqCTy/5LgxGUqy6dPiUEicAeZNt3NeGz5pQDwIgGq7IuwF7RMkDLEHefOglhm7QygdivJep0lzuwRrb/Bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1840271}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.4"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.7": {"name": "axios", "version": "1.6.7", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "7b48c2e27c96f9c68a2f8f31e2ab19f59b06b0a7", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.7.tgz", "fileCount": 81, "integrity": "sha512-/hDJGff6/c7u0hDkvkGxR/oy6CbCs8ziCsC7SqmhjfozqiJGc8Z11wrv9z9lYfY4K8l+H9TpjcMDX0xOZmx+RA==", "signatures": [{"sig": "MEUCIHapqs6xNy72K08cW7+NOvAfleO3306skvTD/LADl4t5AiEAvpnzO/HOYKU4zGc273hPq3EekM1F6J+fnzP9hSWM/1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1841701}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.4"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.2.0", "husky": "^8.0.2", "karma": "^6.3.17", "mocha": "^10.0.0", "sinon": "^4.5.0", "eslint": "^8.17.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.7", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.0.1", "get-stream": "^3.0.0", "handlebars": "^4.7.7", "release-it": "^15.5.1", "typescript": "^4.8.4", "@babel/core": "^7.18.2", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.4.1", "pretty-bytes": "^6.0.0", "formdata-node": "^5.0.0", "karma-jasmine": "^1.1.1", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.3.0", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.18.2", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.3.0", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.28.0": {"name": "axios", "version": "0.28.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "801a4d991d0404961bccef46800e1170f8278c89", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.28.0.tgz", "fileCount": 70, "integrity": "sha512-Tu7NYoGY4Yoc7I+Npf9HhUMtEEpV7ZiLH9yndTCoNhcpBH0kwcvFbzYN9/u5QKI5A6uefjsNNWaz5olJVYS62Q==", "signatures": [{"sig": "MEYCIQDf9ojq/RJHlrNxwB+0EOb0VO4pOImSw3pDyrl+L79RbAIhAI0Y1ySpPPg7Vxg5wuMLyCBBKpJFjJfFG1z7gzwGSRrB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@0.28.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 878279}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "grunt-shell": "^3.0.1", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "@rollup/plugin-json": "^4.1.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "@rollup/plugin-babel": "^5.3.0", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.8": {"name": "axios", "version": "1.6.8", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "66d294951f5d988a00e87a0ffb955316a619ea66", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.6.8.tgz", "fileCount": 81, "integrity": "sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==", "signatures": [{"sig": "MEUCIBJijeDGRwcdlZaz5W2MV1XWBxratH2nt8rVssKS7uuzAiEA8roD9XOEEDqsS4SHz0RNDXnREuABBdxoNcDpDXkRUBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.6.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1843235}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.28.1": {"name": "axios", "version": "0.28.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "2a7bcd34a3837b71ee1a5ca3762214b86b703e70", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.28.1.tgz", "fileCount": 70, "integrity": "sha512-iUcGA5a7p0mVb4Gm/sy+FSECNkPFT4y7wt6OM/CDpO/OnNCvSs3PoMG8ibrC9jRoGYU0gUK5pXVC4NPXq6lHRQ==", "signatures": [{"sig": "MEYCIQD9w2oE0Ja3ZmM1Femglvfo7586kcSN9OuQ1MNK0spZ9QIhAKor3hUkI19TeaZGmntoENJFoePjMVcDW35KIQvvye/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@0.28.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 882525}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.0"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "grunt-shell": "^3.0.1", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "@rollup/plugin-json": "^4.1.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "@rollup/plugin-babel": "^5.3.0", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.0-beta.0": {"name": "axios", "version": "1.7.0-beta.0", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "This version is deprecated. Please upgrade to > 1.7.0", "dist": {"shasum": "2ce152e8f545886aa5a139e54ca7b4d145317b92", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.0-beta.0.tgz", "fileCount": 86, "integrity": "sha512-OBG6+lUDkEqUAOR/JyB4MEIc1bMR4M5Lp/SpD4y3iOUafXCJlftz1GEQ09K9qUmUsySFLfT+8mjZU3Mkmog7fg==", "signatures": [{"sig": "MEQCIGpLkp2BKyj/1Zom5ZEvfn6wy5p3goW7JCXICEDDLLY6AiBkwI490aD06dLTd9/6uW/S1FvKfw/lfqtAXWCgzbamsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.0-beta.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2046161}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.0-beta.1": {"name": "axios", "version": "1.7.0-beta.1", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "This version is deprecated. Please upgrade to > 1.7.0", "dist": {"shasum": "c96fc08a8ec47c4840ee86d82c764824c791d0af", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.0-beta.1.tgz", "fileCount": 86, "integrity": "sha512-ddULIFs1eLuvZ9WOw0XJoIobF6Fxr3eO80xkdp5ab9zw8FnrTU1sTJL8zDowgS8kZcOEpRIokP3xWckK88Q0mA==", "signatures": [{"sig": "MEYCIQDhDiDZsuyi7XiwRwZAwcPxLi0+a5m0NBmw44lDeahvGAIhAM5VV7eV6XuFzgrY0GSXCdOLQB2nRYPePbx2dokvpnwS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2059844}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.0-beta.2": {"name": "axios", "version": "1.7.0-beta.2", "description": "Promise based HTTP client for the browser and node.js", "deprecated": "This version is deprecated. Please upgrade to > 1.7.0", "dist": {"shasum": "27c26a53b397a16cd99db28968cc032ceccf7d70", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.0-beta.2.tgz", "fileCount": 86, "integrity": "sha512-AM2JFzxTjPe8/bO677z1F1w9H53OMCuoWoCUDoXunfmjihXAR47GFof1DGzmfK2TGTiWbbUBOXQN3Ohqf5TzHQ==", "signatures": [{"sig": "MEYCIQCp/81vgvJf2kITJV6bDKYT6ELhxI1rUHK6w9XYxmH17AIhAJWaC6Zc6L7C65AthDv2VRQrYNDUO8exMTKGd+CZJ2S2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2064637}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.0": {"name": "axios", "version": "1.7.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "b48f715325457ededfcb3f0a44a3fb9d5742acb6", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.0.tgz", "fileCount": 86, "integrity": "sha512-IiB0wQeKyPRdsFVhBgIo31FbzOyf2M6wYl7/NVutFwFBRMiAbjNiydJIHKeLmPugF4kJLfA1uWZ82Is2QzqqFA==", "signatures": [{"sig": "MEUCIQDXMY2Nas2Bz5+KhPRTt/pCjDMIJ0jtu0dipDpPICeZ7QIgGlICNrYeTj5uMysBqiSD7aLE8a6aNJ15AW1L1UfOn0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2067880}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.1": {"name": "axios", "version": "1.7.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "522145622a09dfaf49359837db9649ff245a35b9", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.1.tgz", "fileCount": 86, "integrity": "sha512-+LV37nQcd1EpFalkXksWNBiA17NZ5m5/WspmHGmZmdx1qBOg/VNq/c4eRJiA9VQQHBOs+N0ZhhdU10h2TyNK7Q==", "signatures": [{"sig": "MEQCICKuUx0iu0Ky32ObPOFrlqCJJLbSYBNSqShugC97a6MJAiBLtmdcqqE6A8KpF5pUp8PWPSwotS7uad7ucJlCIJecrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2075224}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.2": {"name": "axios", "version": "1.7.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "b625db8a7051fbea61c35a3cbb3a1daa7b9c7621", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.2.tgz", "fileCount": 86, "integrity": "sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==", "signatures": [{"sig": "MEQCIAH6pHuEIoKh5PjU9rNrxctAv8sEn2wTixkXAm6h6O6HAiBDhsN3Ad/vlzeaUGAkmLFLXsuE61IJK8hPWc4+8WcJuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2076796}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.3": {"name": "axios", "version": "1.7.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "a1125f2faf702bc8e8f2104ec3a76fab40257d85", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.3.tgz", "fileCount": 86, "integrity": "sha512-Ar7ND9pU99eJ9GpoGQKhKf58GpUOgnzuaB7ueNQ5BMi0p+LZ5oaEnfF999fAArcTIBwXTCHAmGcHOZJaWPq9Nw==", "signatures": [{"sig": "MEQCIH2CWlvE7WLm6xTAqj2P3T3rHYSDaW+bahRhcU0fqs7bAiBj6X1XbZGfroVnmleBD4LjX7N8geL/PXquGRa6TA8TIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2116388}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.4": {"name": "axios", "version": "1.7.4", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "4c8ded1b43683c8dd362973c393f3ede24052aa2", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.4.tgz", "fileCount": 86, "integrity": "sha512-DukmaFRnY6AzAALSH4J2M3k6PkaC+MfaAGdEERRWcC9q3/TWQwLpHR8ZRLKTdQ3aBDL64EdluRDjJqKw+BPZEw==", "signatures": [{"sig": "MEUCIQDpsadVnjBgvDIQWLvyALNjereTU8Fx6XLrz7KoxWsb7QIgFVMh9sbYgqHTIXa0sQnIXmy2NJwitlnmelfGO2UnRsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2117448}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.5": {"name": "axios", "version": "1.7.5", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "21eed340eb5daf47d29b6e002424b3e88c8c54b1", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.5.tgz", "fileCount": 86, "integrity": "sha512-fZu86yCo+svH3uqJ/yTdQ0QHpQu5oL+/QE+QPSv6BZSkDAoky9vytxp7u5qk83OJFS3kEBcesWni9WTZAv3tSw==", "signatures": [{"sig": "MEQCIBuZiYxHkzTQVkc4CmKHJ3kkN6eTuVk3QMCfbAq0ZuLHAiBbsk8misnumsDlgFF7Utn8tbiiDWu+d1JJ1qAXhXrflw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2124343}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.6": {"name": "axios", "version": "1.7.6", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "34f338182f6802fd3824a6511d6ddf99dd5ae0b5", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.6.tgz", "fileCount": 86, "integrity": "sha512-Ekur6XDwhnJ5RgOCaxFnXyqlPALI3rVeukZMwOdfghW7/wGz784BYKiQq+QD8NPcr91KRo30KfHOchyijwWw7g==", "signatures": [{"sig": "MEUCIDpISkq+ceWfY2VWWlQEogr1ODc4qXKlnyJHWcFQztmiAiEA6ikMfzxvgiUbKoIAqVolLOrhmFKD7O4Wgle6lZGZyc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2133026}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.7": {"name": "axios", "version": "1.7.7", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "2f554296f9892a72ac8d8e4c5b79c14a91d0a47f", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.7.tgz", "fileCount": 86, "integrity": "sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==", "signatures": [{"sig": "MEUCIBesf1VS7HIX3EWqPvh1xsuGYdkA51FVuXcFSJ8yQNeWAiEA7uGXS0z1muoi0di1tzA5084qhW1e15f/Wa/O0Il9NHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2141088}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.29.0": {"name": "axios", "version": "0.29.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "5eed1a0bc4c0ffe060624eb7900aff66b7881eeb", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.29.0.tgz", "fileCount": 70, "integrity": "sha512-Kjsq1xisgO5DjjNQwZFsy0gpcU1P2j36dZeQDXVhpIU26GVgkDUnROaHLSuluhMqtDE7aKA2hbKXG5yu5DN8Tg==", "signatures": [{"sig": "MEYCIQCjkFpz8IC3nXE/Z1sZiwBi/qSiXQTOBV0rfBaq0w2hkAIhAPHdzzJ9xSp29lVXF2wTww0cEzkDcTuzFAi9xURU+/Z0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 864467}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.4"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "grunt-shell": "^3.0.1", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "@rollup/plugin-json": "^4.1.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "@rollup/plugin-babel": "^5.3.0", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.8": {"name": "axios", "version": "1.7.8", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "1997b1496b394c21953e68c14aaa51b7b5de3d6e", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.8.tgz", "fileCount": 86, "integrity": "sha512-Uu0wb7KNqK2t5K+YQyVCLM76prD5sRFjKHbJYCP1J7JFGEQ6nN7HWn9+04LAeiJ3ji54lgS/gZCH1oxyrf1SPw==", "signatures": [{"sig": "MEQCIEbaIO2efTLvZx9xIl/cuZ8aswhV1YwMjHtWGKFgPqo0AiAPhFueaYpI7Y9ClC3s/aYKWrcAy5An+pBBohEZDQIvnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2114636}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.9": {"name": "axios", "version": "1.7.9", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "d7d071380c132a24accda1b2cfc1535b79ec650a", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.7.9.tgz", "fileCount": 86, "integrity": "sha512-LhLcE7Hbiryz8oMDdDptSrWowmB4Bl6RCt6sIJKpRB4XtVf0iEgewX3au/pJqm+Py1kCASkb/FFKjxQaLtxJvw==", "signatures": [{"sig": "MEUCIQDrQISggShxuxZRP3Jdm+AgIS9wLRp+GdvpfeSrYlB7AQIgYlnma23bNLD1Y+ZAoH8rqe7pePOUEg/UrDG3veD1Dbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.7.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2131753}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.0": {"name": "axios", "version": "1.8.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "f68607ed4caf0f741ef084949ebb6f3d973c6837", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.8.0.tgz", "fileCount": 85, "integrity": "sha512-AS3x1klSQ4VeoeAaOyITVjomjITxIRgpKVgAXhE8QFJiNiYliuU+cW0p7XpqvlwNKmiwE57aNEyhPtsfAxERqg==", "signatures": [{"sig": "MEUCIQDxK5vFqVAR5dQVPN84242fydit5fIJ9ZFEpU120532XwIgcVvFacUYD82RBWev3DqPihXEVtKlvHGfpoHUz3wNl70=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.8.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2149568}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.1": {"name": "axios", "version": "1.8.1", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "7c118d2146e9ebac512b7d1128771cdd738d11e3", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.8.1.tgz", "fileCount": 85, "integrity": "sha512-NN+fvwH/kV01dYUQ3PTOZns4LWtWhOFCAhQ/pHb88WQ1hNe5V/dvFwc4VJcDL11LT9xSX0QtsR8sWUuyOuOq7g==", "signatures": [{"sig": "MEUCIDkvam099ssJ6Oh6ps/CMKCn1GSuruMmlzxbKHYjFoZoAiEApw8BJfQcOH33lS+ZaitMOZb4EqIs9Sm4GiquJMkSCHY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.8.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2140813}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.2": {"name": "axios", "version": "1.8.2", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "fabe06e241dfe83071d4edfbcaa7b1c3a40f7979", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.8.2.tgz", "fileCount": 85, "integrity": "sha512-ls4GYBm5aig9vWx8AWDSGLpnpDQRtWAfrjU+EuytuODrFBkqesN2RkOQCBzrA1RQNHw1SmRMSDDDSwzNAYQ6Rg==", "signatures": [{"sig": "MEQCIHrL+Pa8riWC7CnG/rmSUbn4hK/M3gxvtlTyxcbuBDTaAiBVeo6InOY1DoLsOK6SVX6trqjKyR9ejAXaGg/X5SnMmQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.8.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2140651}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.3": {"name": "axios", "version": "1.8.3", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "9ebccd71c98651d547162a018a1a95a4b4ed4de8", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.8.3.tgz", "fileCount": 85, "integrity": "sha512-iP4DebzoNlP/YN2dpwCgb8zoCmhtkajzS48JvwmkSkXvPI3DHc7m+XYL5tGnSlJtR6nImXZmdCuN5aP8dh1d8A==", "signatures": [{"sig": "MEQCIHz0Xc3lNUPqZst93j7x9+dEbvgZSdvnJYVWpmhVO/DNAiBHsaf3fZPbTZ7z8kCrA1wSn1f3QQmQONBydiHl7JS3dg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.8.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2142312}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.4": {"name": "axios", "version": "1.8.4", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "78990bb4bc63d2cae072952d374835950a82f447", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.8.4.tgz", "fileCount": 85, "integrity": "sha512-eBSYY4Y68NNlHbHBMdeDmKNtDgXWhQsJcGqzO3iLUM0GraQFSS9cVgPX5I9b3lbdFKyYoAEGAZF1DwhTaljNAw==", "signatures": [{"sig": "MEUCIQDBrAHF/J/CrYGFgxsH1lF3ahQHuJRPW/pzzF7C3UcBkQIgZ8lItbWa0l/KaER276nk9cTLRUMwmelqiI8JxrjYWts=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.8.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2142875}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.6"}, "devDependencies": {"gulp": "^4.0.2", "chalk": "^5.3.0", "husky": "^8.0.3", "karma": "^6.3.17", "mocha": "^10.3.0", "sinon": "^4.5.0", "eslint": "^8.56.0", "multer": "^1.4.4", "rollup": "^2.79.1", "dtslint": "^4.2.1", "express": "^4.18.2", "dev-null": "^0.1.1", "fs-extra": "^10.1.0", "memoizee": "^0.4.15", "minimist": "^1.2.8", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "gzip-size": "^7.0.0", "formidable": "^2.1.2", "get-stream": "^3.0.0", "handlebars": "^4.7.8", "release-it": "^15.11.0", "typescript": "^4.9.5", "@babel/core": "^7.23.9", "body-parser": "^1.20.2", "es6-promise": "^4.2.8", "karma-sinon": "^1.0.5", "jasmine-core": "^2.99.1", "pretty-bytes": "^6.1.1", "formdata-node": "^5.0.1", "karma-jasmine": "^1.1.2", "auto-changelog": "^2.4.0", "@commitlint/cli": "^17.8.1", "stream-throttle": "^0.1.3", "@babel/preset-env": "^7.23.9", "karma-jasmine-ajax": "^0.1.13", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^5.3.1", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "string-replace-async": "^3.0.2", "karma-chrome-launcher": "^3.2.0", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.5", "karma-rollup-preprocessor": "^7.0.8", "rollup-plugin-bundle-size": "^1.0.3", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-auto-external": "^2.0.0", "istanbul-instrumenter-loader": "^3.0.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "0.30.0": {"name": "axios", "version": "0.30.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"shasum": "026ae2c0ae6ac35d564056690683fb77c991d0d3", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-0.30.0.tgz", "fileCount": 70, "integrity": "sha512-Z4F3LjCgfjZz8BMYalWdMgAQUnEtKDmpwNHjh/C8pQZWde32TF64cqnSeyL3xD/aTIASRU30RHTNzRiV/NpGMg==", "signatures": [{"sig": "MEUCIEEeL44IrAymHjdxYqwxAyrSeVAGRabRVsriMk9gYI3cAiEA3i5LULyHki5rnJQakRZ8FZ5q+3nL9r28MlEq8mWhMIc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 869071}, "directories": {}, "dependencies": {"form-data": "^4.0.0", "proxy-from-env": "^1.1.0", "follow-redirects": "^1.15.4"}, "devDependencies": {"grunt": "^1.4.1", "karma": "^6.3.17", "mocha": "^8.2.1", "sinon": "^4.5.0", "multer": "^1.4.4", "rollup": "^2.67.0", "dtslint": "^4.2.1", "express": "^4.18.1", "webpack": "^4.44.2", "minimist": "^1.2.6", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "grunt-cli": "^1.4.3", "formidable": "^2.0.1", "typescript": "^4.6.3", "body-parser": "^1.20.0", "es6-promise": "^4.2.8", "grunt-karma": "^4.0.2", "grunt-shell": "^3.0.1", "karma-sinon": "^1.0.5", "grunt-banner": "^0.6.0", "grunt-eslint": "^24.0.0", "jasmine-core": "^2.4.1", "grunt-webpack": "^5.0.0", "karma-jasmine": "^1.1.1", "karma-webpack": "^4.0.2", "grunt-mocha-test": "^0.13.3", "load-grunt-tasks": "^5.1.0", "url-search-params": "^0.10.0", "karma-jasmine-ajax": "^0.1.13", "webpack-dev-server": "^3.11.0", "@rollup/plugin-json": "^4.1.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "@rollup/plugin-babel": "^5.3.0", "karma-sauce-launcher": "^4.3.6", "rollup-plugin-terser": "^7.0.2", "karma-chrome-launcher": "^3.1.1", "karma-safari-launcher": "^1.0.0", "terser-webpack-plugin": "^4.2.3", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "@rollup/plugin-commonjs": "^15.1.0", "abortcontroller-polyfill": "^1.7.3", "@rollup/plugin-multi-entry": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "istanbul-instrumenter-loader": "^3.0.1"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.9.0": {"name": "axios", "version": "1.9.0", "description": "Promise based HTTP client for the browser and node.js", "dist": {"integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==", "shasum": "25534e3b72b54540077d33046f77e3b8d7081901", "tarball": "http://mirrors.cloud.tencent.com/npm/axios/-/axios-1.9.0.tgz", "fileCount": 85, "unpackedSize": 2155396, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/axios@1.9.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEurJnFRCxeuO06GPHds3I6ZgaTB3KvTzplq8EQKJ766AiEApY+WbfeXSD1mxXQQ1QGIUm7CkdMAII8AbkA97BUGbpY="}]}, "directories": {}, "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@release-it/conventional-changelog": "^5.1.1", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "abortcontroller-polyfill": "^1.7.5", "auto-changelog": "^2.4.0", "body-parser": "^1.20.2", "chalk": "^5.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "dev-null": "^0.1.1", "dtslint": "^4.2.1", "es6-promise": "^4.2.8", "eslint": "^8.56.0", "express": "^4.18.2", "formdata-node": "^5.0.1", "formidable": "^2.1.2", "fs-extra": "^10.1.0", "get-stream": "^3.0.0", "gulp": "^4.0.2", "gzip-size": "^7.0.0", "handlebars": "^4.7.8", "husky": "^8.0.3", "istanbul-instrumenter-loader": "^3.0.1", "jasmine-core": "^2.99.1", "karma": "^6.3.17", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^1.1.2", "karma-jasmine-ajax": "^0.1.13", "karma-rollup-preprocessor": "^7.0.8", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.8", "memoizee": "^0.4.15", "minimist": "^1.2.8", "mocha": "^10.3.0", "multer": "^1.4.4", "pretty-bytes": "^6.1.1", "release-it": "^15.11.0", "rollup": "^2.79.1", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-bundle-size": "^1.0.3", "rollup-plugin-terser": "^7.0.2", "sinon": "^4.5.0", "stream-throttle": "^0.1.3", "string-replace-async": "^3.0.2", "terser-webpack-plugin": "^4.2.3", "typescript": "^4.9.5", "@rollup/plugin-alias": "^5.1.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2025-04-24T20:19:00.154Z", "time": {"created": "2014-08-29T23:08:36.810Z", "modified": "2025-04-24T20:19:00.154Z", "0.1.0": "2014-08-29T23:08:36.810Z", "0.2.0": "2014-09-12T20:06:33.167Z", "0.2.1": "2014-09-12T22:57:28.872Z", "0.2.2": "2014-09-15T03:30:45.994Z", "0.3.0": "2014-09-16T18:20:18.668Z", "0.3.1": "2014-09-17T00:31:29.538Z", "0.4.0": "2014-10-05T23:55:03.069Z", "0.4.1": "2014-10-15T18:19:42.549Z", "0.4.2": "2014-12-11T07:14:52.563Z", "0.5.0": "2015-01-23T10:15:47.657Z", "0.5.1": "2015-03-10T20:47:18.932Z", "0.5.2": "2015-03-13T23:14:22.809Z", "0.5.3": "2015-04-08T03:01:17.936Z", "0.5.4": "2015-04-08T18:49:41.745Z", "0.6.0": "2015-09-21T20:20:20.241Z", "0.7.0": "2015-09-29T06:36:55.850Z", "0.8.0": "2015-12-11T19:09:50.663Z", "0.8.1": "2015-12-15T03:44:16.229Z", "0.9.0": "2016-01-18T18:19:24.356Z", "0.9.1": "2016-01-24T22:19:01.689Z", "0.10.0": "2016-04-21T04:52:22.553Z", "0.11.0": "2016-04-27T04:19:52.831Z", "0.11.1": "2016-05-17T15:59:30.342Z", "0.12.0": "2016-06-01T05:22:58.397Z", "0.13.0": "2016-07-13T19:42:43.558Z", "0.13.1": "2016-07-16T17:13:24.859Z", "0.14.0": "2016-08-27T18:30:22.182Z", "0.15.0": "2016-10-11T04:40:41.633Z", "0.15.1": "2016-10-15T06:39:23.761Z", "0.15.2": "2016-10-18T01:33:20.989Z", "0.15.3": "2016-11-27T21:59:11.250Z", "0.16.0": "2017-04-01T02:31:09.040Z", "0.16.1": "2017-04-08T18:51:59.217Z", "0.16.2": "2017-06-03T19:29:23.765Z", "0.17.0": "2017-10-21T18:01:30.232Z", "0.17.1": "2017-11-11T23:24:40.635Z", "0.18.0": "2018-02-19T23:28:54.151Z", "0.19.0-beta.1": "2018-08-09T18:44:25.247Z", "0.19.0": "2019-05-30T16:13:16.930Z", "0.18.1": "2019-06-01T00:46:36.277Z", "0.19.1": "2020-01-07T17:23:39.782Z", "0.19.2": "2020-01-22T04:25:53.354Z", "0.20.0-0": "2020-07-15T16:07:30.516Z", "0.20.0": "2020-08-21T03:12:48.792Z", "0.21.0": "2020-10-23T16:27:13.883Z", "0.21.1": "2020-12-22T04:20:06.101Z", "0.21.2": "2021-09-04T10:18:50.516Z", "0.21.3": "2021-09-04T19:05:59.101Z", "0.21.4": "2021-09-06T15:35:43.073Z", "0.22.0": "2021-10-01T05:54:01.140Z", "0.23.0": "2021-10-12T15:37:08.278Z", "0.24.0": "2021-10-25T17:51:25.987Z", "0.25.0": "2022-01-18T07:14:06.173Z", "0.26.0": "2022-02-13T14:22:07.992Z", "0.26.1": "2022-03-09T17:13:42.282Z", "0.27.0": "2022-04-25T16:42:10.109Z", "0.27.1": "2022-04-26T07:36:38.268Z", "0.27.2": "2022-04-27T10:00:58.685Z", "1.0.0-alpha.1": "2022-05-31T19:23:57.168Z", "1.0.0": "2022-10-04T19:24:14.051Z", "1.1.0": "2022-10-06T19:19:53.272Z", "1.1.1": "2022-10-07T09:15:00.061Z", "1.1.2": "2022-10-07T10:14:46.118Z", "1.1.3": "2022-10-15T13:42:22.106Z", "1.2.0-alpha.1": "2022-11-10T19:06:10.040Z", "1.2.0": "2022-11-22T19:06:02.018Z", "1.2.1": "2022-12-05T19:39:11.122Z", "1.2.2": "2022-12-29T06:38:42.365Z", "1.2.3": "2023-01-17T17:56:39.473Z", "1.2.4": "2023-01-24T17:21:58.022Z", "1.2.5": "2023-01-26T15:06:37.154Z", "1.2.6": "2023-01-28T16:41:09.114Z", "1.3.0": "2023-01-31T16:55:47.652Z", "1.3.1": "2023-02-01T23:31:00.077Z", "1.3.2": "2023-02-03T18:10:48.275Z", "1.3.3": "2023-02-13T18:47:20.873Z", "1.3.4": "2023-02-22T21:06:23.063Z", "1.3.5": "2023-04-05T18:03:03.807Z", "1.3.6": "2023-04-19T19:38:57.160Z", "1.4.0": "2023-04-27T23:05:52.716Z", "1.5.0": "2023-08-26T19:10:50.109Z", "1.5.1": "2023-09-26T18:22:12.460Z", "1.6.0": "2023-10-26T21:15:55.685Z", "1.6.1": "2023-11-08T15:09:28.450Z", "1.6.2": "2023-11-14T20:36:10.212Z", "1.6.3": "2023-12-26T23:16:17.756Z", "1.6.4": "2024-01-03T22:10:55.605Z", "1.6.5": "2024-01-05T19:52:15.051Z", "1.6.6": "2024-01-24T23:12:19.622Z", "1.6.7": "2024-01-25T19:58:51.338Z", "0.28.0": "2024-02-12T18:38:24.432Z", "1.6.8": "2024-03-15T16:32:47.800Z", "0.28.1": "2024-03-28T17:36:11.007Z", "1.7.0-beta.0": "2024-04-28T19:50:54.466Z", "1.7.0-beta.1": "2024-05-07T18:37:51.489Z", "1.7.0-beta.2": "2024-05-19T18:01:23.901Z", "1.7.0": "2024-05-19T20:25:03.615Z", "1.7.1": "2024-05-20T13:32:52.757Z", "1.7.2": "2024-05-21T16:58:04.163Z", "1.7.3": "2024-08-01T16:16:13.901Z", "1.7.4": "2024-08-13T19:33:11.369Z", "1.7.5": "2024-08-23T13:32:37.145Z", "1.7.6": "2024-08-30T19:56:49.611Z", "1.7.7": "2024-08-31T22:02:08.862Z", "0.29.0": "2024-11-21T13:08:09.760Z", "1.7.8": "2024-11-25T21:13:58.456Z", "1.7.9": "2024-12-04T07:38:16.833Z", "1.8.0": "2025-02-26T06:01:14.385Z", "1.8.1": "2025-02-26T09:07:00.236Z", "1.8.2": "2025-03-07T07:41:11.719Z", "1.8.3": "2025-03-12T07:24:04.212Z", "1.8.4": "2025-03-19T19:27:47.752Z", "0.30.0": "2025-03-26T17:55:30.023Z", "1.9.0": "2025-04-24T20:18:59.701Z"}}