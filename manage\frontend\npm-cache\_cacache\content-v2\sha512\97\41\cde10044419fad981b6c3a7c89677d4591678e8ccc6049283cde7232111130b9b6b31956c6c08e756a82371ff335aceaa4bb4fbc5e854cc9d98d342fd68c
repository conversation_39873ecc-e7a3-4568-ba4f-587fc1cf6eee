{"dist-tags": {"latest": "6.8.0"}, "modified": "2025-01-08T16:27:51.504Z", "name": "@codemirror/commands", "versions": {"0.19.6": {"name": "@codemirror/commands", "version": "0.19.6", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.22", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"integrity": "sha512-Mjc3ZfTifOn0h5499xI3MfCVIZvO2I0ochgzxfRtPOFwfXX/k7HTgnK0/KzuGDINyxUVeDaFCkf53TyyWjdxMQ==", "shasum": "1568cf2c45a05864c1a4229575c5ac367ebbad9f", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.6.tgz", "fileCount": 8, "unpackedSize": 122861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsxh2CRA9TVsSAnZWagAAIiIP/1v2IeQa+lX3aKDxQZWo\nZ8fiMNA3wgfNknTPbUpYWE9gHdsAWHWX04TBUSeEvpkW/+ttsG31AZnyq3IC\nTqqKEMtkct63L0bYH8XijUc3GkjLjljPcf7a1AU81NOnwWxapvPnqRQiVjJ+\nzpsGSkAlazJVugwkjwnSWlupJGn9NxW74SP9s9mtrL5tPvpOcYAebfz2/0Eb\nKKQRvOz/Fwungs0+DKr0bt0U1tHuBD32w37eJMKJvP9dcALqZ1HHRU6o0pET\n4kuEaZe3kd6ubarVsdXVc5EdOmZHBfAdPLXhjVH7SqhEJYHZigNaZ5PoVlTq\nz5T/jjzbd1LVvDkEa3BEUPTSChFe55rNkd1KM5VvqPKUukzJpBH00muQUCqE\nDsJ/4vhUjFrR2aZ+IwU3PofaDxqBxIVjMY5XhDKZK0pr+mplroiI2SVNFA49\ngCBDg3kZdx+dSeBB1lW3KMINJBixKe4R2Fsnbk0B6ptvFi1uF5cmVrpGWnmq\nDnzUiT+QWSp3WcgzYWnRlduu/L3F+slH8hbhWd69IsbpX40QNJXucnaoldTB\n0Fy9ORI/P0cWee2RmaoeOYVSuNJ34MJw310GLiVyFXU29LHx48qtmskocXP8\n46HJGl9czVdfZdYZGCqIxFWccP7WHQHKL6VXCg8UoKTAxk6ME1wZdC567UnF\neaVF\r\n=gaXr\r\n-----END PGP SIGNATURE-----\r\n", "size": 22754, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.5": {"name": "@codemirror/commands", "version": "0.19.5", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"shasum": "2607b5c12c5c96df2cabce2e43f6285c07cfaf11", "size": 22543, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.5.tgz", "integrity": "sha512-8PZOtx7d/GbKhFYA88zs2wINDtaUgj3pEjLYScKTd/Vsyw8qOp86tJQQNnMFTRZj/ISQl9Lbg3aAmHvroMqspw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/commands", "version": "0.19.4", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"shasum": "455ce92fb058c05736a3722385fa1dbf7503d549", "size": 22227, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.4.tgz", "integrity": "sha512-EW6ffAIdu7aGWWwEG4xRcKLR+1RXH7hJqMt5mbrxDSc2xAQBgqxIPFglcMeSkC7qqkwPuqv3xavVh1+0gQ0piQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.3": {"name": "@codemirror/commands", "version": "0.19.3", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"shasum": "855a7ef9e0a93b3f25f7d0f65aa2ea0c3f7691af", "size": 22103, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.3.tgz", "integrity": "sha512-eR1zSjBfIQCyZvk9qY1/7OPk7bRON3n6pC0KXQv4hcqE1XG9iiGBwXLtGFoeX+kzWvFljRe6gLAGGaKmCfJfRA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/commands", "version": "0.19.2", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"shasum": "4bb4e7f90bf7e16f59dd0a13fbdc1cad0ec48d78", "size": 21816, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.2.tgz", "integrity": "sha512-gYjrobG5GIjImtwKo0hzSsHoZUdYODRpNc0hF9sMHXagh5uy9yuJbsoR6RBS0W4ol1BefJNI0Q5L4US/feqcSA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/commands", "version": "0.19.1", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"shasum": "dc1f1b8fc06bc92915356726d99aafea2c4a1882", "size": 20874, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.1.tgz", "integrity": "sha512-6c9MlBW3UI13rCEqZkhD4etzWpxQKqeYKVn7CygsQ3LRtVprhfQxRts/K4vOcefC/Qq0VeRzYQt6crUPsOpaVA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.0": {"name": "@codemirror/commands", "version": "0.19.0", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.14.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"shasum": "08e778a69d02c9a00274fbeba715c7021a77ab64", "size": 20797, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.0.tgz", "integrity": "sha512-uDzoNANi1sw39bff3S/xQ53FyGSrsFTG6gv9gpzAPHbCsgIlm4m5TFCOPkkqnTsoc5dezSTriYEqsscD4qYzfg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.3": {"name": "@codemirror/commands", "version": "0.18.3", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/matchbrackets": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.18.0"}, "directories": {}, "dist": {"shasum": "56283873fae9dd0c937da7dd0af5eadc9b378e9c", "size": 20885, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.18.3.tgz", "integrity": "sha512-nHYDG13qOirioXTAKmjl10W2L0eZ1ftvmTwvUTNY27UWVBPFSpk5zDXP3WqJ0mgMhQ4AOFLJaTjJEO3hmPComg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/commands", "version": "0.18.2", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/matchbrackets": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.18.0"}, "directories": {}, "dist": {"shasum": "a90067b1e3127ffe2c1be2daa68c0f4aeda59308", "size": 20525, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.18.2.tgz", "integrity": "sha512-NeIpsQe5yUIhG7sD1HPaw/9TO5V7miMKwGwhT/0tkgkmgnMtJcgnguM1gjaUlaZ09BBJO6s61D8JHNDUvBI6tA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/commands", "version": "0.18.1", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/matchbrackets": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@codemirror/lang-javascript": "^0.18.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "2866a15936b7ab3ff30112eb1887a0e01fef6205", "size": 20434, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.18.1.tgz", "integrity": "sha512-JBdGjRTCHi/eVsj5oL1S4kzhepyUVW0EnmOvwh1IYH9iN6FnRkEgaxJBryRgbos7Or8RomOKK01pprGjfqN6oA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/commands", "version": "0.18.0", "dependencies": {"@codemirror/language": "^0.18.0", "@codemirror/matchbrackets": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.18.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "10344d7d7a0fecad826e9853c1e6069d706298c6", "size": 31084, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.18.0.tgz", "integrity": "sha512-4H63B4oqr8dmJ3VOKvMI7xrZIBJjAdtz3Z0V/Jt0HlIYTe76Omy4h9BS3b9EgyNaWjIO/Slz3KQPwihcC8uR5Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.5": {"name": "@codemirror/commands", "version": "0.17.5", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/matchbrackets": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.17.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "0912955a614e554e4fbcb347a785c5c981f12826", "size": 31079, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.17.5.tgz", "integrity": "sha512-9B/Zq6mBkSdf+2xWoOufJutNRhFdsgA9f2w9hfI7ujwFqFLIj7QKuYt+W1LAcJufHYy5BdVCyjgCamx65yb4MA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.4": {"name": "@codemirror/commands", "version": "0.17.4", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/matchbrackets": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.17.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "afca35595cf728eafa280d267285c8985a2f5c8b", "size": 30837, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.17.4.tgz", "integrity": "sha512-YB1Iz5nHVZFJCB/WboNRBS7Bb6f/M/LoJQRm4AY2h1uWmSnxbr02DPX3XpTVhams7x7XNtIFtgk/Q4/wel/JXg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.3": {"name": "@codemirror/commands", "version": "0.17.3", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/matchbrackets": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.17.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "dc33f3371b7d6e421d9adfd9e4c5329fa4eabf95", "size": 30786, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.17.3.tgz", "integrity": "sha512-Y2CF0TAEG356iHQGZx4O9ECbh0j96W3yw/cBUHkFN0nXamwSQehAedl/VXEkVt5uykVYVO+cfE58m4OGyvsrsA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.2": {"name": "@codemirror/commands", "version": "0.17.2", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/matchbrackets": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.17.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "c51f1a4466c842b7876987954e006dd68ee02698", "size": 30631, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.17.2.tgz", "integrity": "sha512-FT2wMrf6Jtrbmb0WqXaAO0B7BvjmzEmsJwZSS46UiC8S5/asXA7hjaFdB9hZUDxtuZ/bsVScvKUVoq7vzXxT7g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/commands", "version": "0.17.1", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/matchbrackets": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.17.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "2e5d3af42afc8f305a272d127ba3a471a79ff21a", "size": 29777, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.17.1.tgz", "integrity": "sha512-y04Rl3kcpnTSzkgnvghjF/9UNjrdVAvT+hxnpuad2OhPcRDEWuFqwwEM7ZpzAkyjFNCG5+KDXuhnERk9cxF6/Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/commands", "version": "0.17.0", "dependencies": {"@codemirror/language": "^0.17.0", "@codemirror/matchbrackets": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@codemirror/lang-javascript": "^0.17.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "620405d84c5dd30b65b5ae30cec0403442eaab2b", "size": 21709, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.17.0.tgz", "integrity": "sha512-y7ajH3fb9xUn5SrnW1pR+zH7qwWD0+UX5cmK8TSvMWQw4riV+2ygS4dO1jZW9m43zUiHbjuWUxWjWOS2DyR11g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.7": {"name": "@codemirror/commands", "version": "0.19.7", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.22", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"integrity": "sha512-Mwh064xnuDbFw+9KJAi2Hmg8Va+YqQzgn6e/94/bSJavY3uuIBPr4vJp6pFEa1qPp40qs5/XJ01ty/0G3uLewA==", "shasum": "b55ccc7f3c1ad4cb0ec422d50c93568dbb05cc55", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.7.tgz", "fileCount": 8, "unpackedSize": 123610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3WOQCRA9TVsSAnZWagAAcMIP/0sGXu0X2MTVdsl6pmF3\n3RsQADJXrW6jcIyUIRGKJu4KjPGtdPNb6OWw18tiYSVC7BgBIvjCgooDs1mh\nPBmgkR1nEC6IePvCP3PvpiYProz4h9p6fp5iWnYXEPxn3dejiidubuIdAjmL\nuR0gT3A0JI1CsP7i4cAfhPuTQ8Q+WRyIys4H+Su0MYTqt4BqXlkb5I+sH3Ko\nEw0+8KFG5XDCgP/HNaNbvtEmRLFaIsMmtfFWQDjBNbICROc0njTdCGggVSov\n97iTX5qj4+qbeBvTUbnwAmIDMyPKaJEZassOpC3ApFKpE0iG2V07+l0fiIsM\niIuK1yMKOExGe1mU+/y4t9N4Dc7A91LznCLSqlieeMJrt1f+vfRmQJGiX8lh\nfPK1GMrQSv2tx1hf+dmJ/RYz7Vuwwy+V95V8EX1YM/FCDyskk5ewQJNlzFAX\n0lIAVX8JtMz2CoaAQPI7mYPywkyW+6mHTpHVAfIEJ+T7OOZIkpUjnqTkTJvD\nIYZA+WRWi+u1FG5qKmKv0TnkynjtnwrTpEfOlIH76pHmeiDjZZ0NRt+B4f48\n+xYcQWLB4r2gWwr47AJHIYDJZffRI/nk0p4DaQiBooa0d+OWkVgrZ+kja4Oj\nruDaRFm4IvXHv4AfLAFQGzW3i3wQJkT2vEU/r3chKflVGo/n9L78IgkIByke\nEWkk\r\n=NGkp\r\n-----END PGP SIGNATURE-----\r\n", "size": 22968}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.8": {"name": "@codemirror/commands", "version": "0.19.8", "dependencies": {"@codemirror/language": "^0.19.0", "@codemirror/matchbrackets": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.6", "@codemirror/view": "^0.19.22", "@lezer/common": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.19.0"}, "directories": {}, "dist": {"integrity": "sha512-65LIMSGUGGpY3oH6mzV46YWRrgao6NmfJ+AuC7jNz3K5NPnH6GCV1H5I6SwOFyVbkiygGyd0EFwrWqywTBD1aw==", "shasum": "1f99c1a8bf200d17c4d6997379099459f3678107", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.19.8.tgz", "fileCount": 8, "unpackedSize": 123866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8a9TCRA9TVsSAnZWagAA1mQP/1YXPSeAOW2OtpTv1KnI\nQUmG0a8sIUtA4eGDAD9a6+76KSaB1B09m3UOv27ZDKUw2RSsYvo7Ki0yaoR/\nNr86QKKTeX5cMdkPqQlb/WWjSymnL3PHQnmzhKI+FxsRs35DRLZad7Njdi9W\n9NctXaSQM/x4HD37BbfQLU0o7FOzABlOCNYCGvuH+j1rJCll6aT0RRuVMFWg\niH5a2v/TZVVIEfnHbJXVjZUzBJLr80ZYxaDWfr5oThQ9upKefhqOFMtZsL+1\nBKxLC/+nao2RDGhVYuxQV3bgpp8IJ1CpHXFyAJBj9qDs2nfpqSuL60lhZcm2\nzNDX/G8OoaKgb21oMc1sk1KpGeEADV97J5qkcocUIjOk+QPqiEHvMtXGKgYT\nUH2H3F/zl88Q1Qs/JypgkiMJG9Lmt9gYawlLwMiBzXA1ofR9nzSVPl9oYv4q\nxYo8VFBa2QRfzrJqEaeE3/KyPQigwf92JoiflKvGpGfDECSD3BPIg6lM0/us\n8xgNWbUHAB9kYzzqQL4ZIBRWHIsOGjdbnW09APN0IwbvZJOG48DhUeB5swif\nuai540t19SNmppOShCYkMLiamWzialMvBN/Afo9vXa0GXWSeGxpxtyXUR9KW\nvGncUogxqzKSUrkCyteWkStyEUgqxq9s2HvZ+lo99Q/O5d534/TGC18pDt9H\n/E/m\r\n=xqvG\r\n-----END PGP SIGNATURE-----\r\n", "size": 23185}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/commands", "version": "0.20.0", "dependencies": {"@codemirror/language": "^0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^0.20.0"}, "directories": {}, "dist": {"integrity": "sha512-v9L5NNVA+A9R6zaFvaTbxs30kc69F6BkOoiEbeFw4m4I0exmDEKBILN6mK+GksJtvTzGBxvhAPlVFTdQW8GB7Q==", "shasum": "51405d442e6b8687b63e8fa27effc28179917c88", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-0.20.0.tgz", "fileCount": 8, "unpackedSize": 177590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5DFayMsD4bOU35qoGhBrjxfvyH8AZ7AE/M4f3VbkBLgIgQjWGLnua1IYSwBvdMVG9Xab9a3aL0yCP3N/tdjkx/AU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBU2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryeBAAhaU87ntr1kWy2C98dQZ1Mf6oF96+fgf7WCqGkNq+eJElZdMq\r\nXzpSsSIOnX1wBCey4tNWKxBAXpSv2jpDZa/CEutwe1tn0BViEEj9X2UI/aca\r\nZKEB3+rFZ/WX6x5p54YDi7kYEG2vCcno5mcWyI+hfu9ZFRrk5IJAQXpVIMa8\r\nTshMXugAHFChvUN6yB7r1GQ/SUJuo3NUukuOvGiq7f22d2yWQb0LO7V+fj4Q\r\n3+/Em2mMRoi9aMo7RKYs9Sw3hFRK5G5XeXC7LxC2tbVJWEiSTK/lDu6JYDlT\r\nDwMzExvNERZ38PvAfirOq2a9YWvFjA0WMnBoeHfMjPN5L8P/S6GxjRipdSqx\r\nBd4J4AHsn0/lvm9aGiIRnQSFFfux7yVTQRq3pSSFTytNqyhn9XHX4/ujqKL5\r\nzCcFyX+8WZT5HrirxTCUaChIOvRQJPHM0VDdISpJdvyKWMl9+hmtpZACuJ3m\r\ncXY3r7W4WTDCKUlXzgPArUmVweXJxvTE56g+tKpBSpfxL7SNTeub/5YC+vYs\r\nHKn4DM1NOMPYx9bsQcr25nQ9bjD4Gp/6NnHrQqRkIj33mGaHb9FCjqg90gf2\r\nG8IL631sCuQO4Ah0t7yZTczS3RCNt+e4zHbrJqdu7BfX8UfSsZxcwSl+ZrFe\r\nlmqUoR89/Eu1CclpZJ/M1bTCfKCAEu3cpq4=\r\n=c9HG\r\n-----END PGP SIGNATURE-----\r\n", "size": 37292}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/commands", "version": "6.0.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-nVJDPiCQXWXj5AZxqNVXyIM3nOYauF4Dko9NGPSwgVdK+lXWJQhI5LGhS/AvdG5b7u7/pTQBkrQmzkLWRBF62A==", "shasum": "9eaa4d53e9cdb2e13da52c8a03636a9f9ad45d2b", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.0.0.tgz", "fileCount": 8, "unpackedSize": 177814, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqgIdmWK22+GxPKMY5Sl7PO6U9dYyCxNIa5jHe2qbXLAiAE5ZJTW6JteozyrpSm9RxUBE6TbTVTmMNpX2BaKaupIg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpR1hAAhn9sbD+9WX0o3g2fwZ1Jn5054etAbrlSfrUT+4poKWrSdSbs\r\nNCMsIWjr0DamTHE/lw5P0BM7sYHMwNhGCXv8tcf/GYUzrfXWaBASpEHx5r0E\r\nAQl6vG1D9tiVejYuPpK8lN6DZ9VgDtCCvX5qjB/Um4Ygd+UxiyHHlx6AhPbd\r\n2y94BFsfBobc9XKAVjtZxRqE55dUtyogQfn7BAA+xjufHR2+Q/a+31kIZDZW\r\nqM/P44m8bmzb7focQk6mxxQkfh1OTfXh8Z2F0JhnZ0re3y1MjFWQQD0E5jZV\r\nay/Tax2P+bgWrkN5Rk0m3UnE95kfHr45vF3Cinu71RLV1S0SXvfaaydf6Cpm\r\nsPLKgvbe8WDIZAjwaUDb9DMaMvGNZ1FYTdx0Nurk6wDFUBXIFHbJQiYBFqiP\r\n2KzkAGGQNcs74TyhMPz9wBjrPgd+8LawelZHE+eBVO40WRIQBPGCWFAsZ4+y\r\nj2npFWL6mQSu+iHad0I9v2GWDvMXATJ7gVIKZDyK0o/KGXkVNh75YBLCtunq\r\nMyWfqlBiiQh+rwcJSIalDF3PKrUrieo95cn+1pe35RDjwjQnVxxcjx0qrm51\r\nQwRVgk6CwT89dhX0GXoQT4484p352acvhcU4xJ2qd29KAySzc/icWjvGg485\r\nUQxUTNvQbuzSmnjpaq5m3IyoQheu8yquWU8=\r\n=w9pL\r\n-----END PGP SIGNATURE-----\r\n", "size": 37376}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.1": {"name": "@codemirror/commands", "version": "6.0.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-iNHDByicYqQjs0Wo1MKGfqNbMYMyhS9WV6EwMVwsHXImlFemgEUC+c5X22bXKBStN3qnwg4fArNZM+gkv22baQ==", "shasum": "c005dd2dab2f6d90ad00d4a25bfeaaec2393efa6", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.0.1.tgz", "fileCount": 8, "unpackedSize": 178633, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwyNxg/K1C2tboUTMu5Ak6cua6KiwI04xcAYAC1kZtrAIgKXAUFyn3h97C8v64Z7ktpEuenbBumDiUoJRinRf0/yk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivWqcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPmw//RiNM7svPnLhab3n7WvRAyKzDkDYcw8NFossxrL+9WV0/NTLs\r\np+gYqlLqqooagHtJ7h7BeX2YpuWgHmgG6MTTrZ3XitjniwWCkAWdaxo/rz38\r\nclPsd51+LvvJU0RSz4u++vr6PaaQdKAgnzCzgBMU+ruaTzOcrqbfbneeFdsW\r\nZYw7r1GOGsamQsNXUYm8qUTxQhVm8YqjqL15KDxqtwh4n51tlgSwOqrkMMQo\r\nvnKdKRqVNISz4kuwNDdJTdaK3NQcufnAk1YJDDLPs0HI9GRczmCAjDwKq0vg\r\nqEOffWOMFY2Nj6u1H1msFRDlYKLZEJ0LM3lrkK2y3avuPrmxgjV1gsrvJhKm\r\nwD/im2f/NRE73l7oooRRemKQzSv4AJkmjq+YSWaRm69+uLG3giRDwV7ptpWM\r\na2ZfBeNbqZJW5Sh5Hu9TH+oXGgCjW4ifh2KVc/q8Jl5l0NyTdMoOdpYNZUWN\r\n7ktk+/ebMDbXVIJqJbuX5d9gOmbTGctdIys3E+qF6s9NCNndw/adMsau2R4J\r\nlleYdiMxroH7mAZ9KkUhu5a4wnFanxWAGg//KuQINEMDI4TutNN0/EIxhik+\r\ntryDjar6DbUZUHjL3Bl19yZIoJbytdtfPGFvh6fLyhun4wtSn/b5ehs57eMz\r\nML1hBUvYO3BuJIaBuykEfT6Tn6oJYgaR4Sc=\r\n=DObH\r\n-----END PGP SIGNATURE-----\r\n", "size": 37690}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/commands", "version": "6.1.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-qCj2YqmbBjj0P1iumnlL5lBqZvJPzT+t2UvgjcaXErp5ZvMqFRVgQyrEfdXX6SX5UcvcHKBjXqno+MkUp0aYvQ==", "shasum": "c9da851f419f25dae400d7cd94f80b80ef060696", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.1.0.tgz", "fileCount": 8, "unpackedSize": 181527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmhPlNYl9mYWOY8OWX+a8dTG0zlM5jYLB0mJd7ByQQZAIgCz2JEuC959+2QFkLjGlnIOdjICnGC1ny7wqwLkMSz98="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/gpjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4gQ/9FNjS4E/pQhN6WNoM8QXmi7DQGR2TQnBsCxN/DnhTY0TpjB+g\r\n9W4XgrmbOG+mkWjr8pPNf1KEK6myRSID449ng2LMfHCSqNjuvYsmPi8ffbnP\r\nwLC5lWoyWRhc+IYlXEABQGqGVTwMmvDn1Kuwneh3arN1Gq6evuBZBsQ6/ycJ\r\n4tRqs/Ss9XnnYztxQSSTVuuSlqSKvCIPnOqN2ktsnvleIEXMJw6qvF4CDP9y\r\nYd4LJBBy8RrLIU9iS2E9l2jDLKuhbO88pVgBWZQ/mb0lw91iIlOsIzTO+fWU\r\npA/Y6QUM1a2Ln6/wUep4oatSp1SVv6pJK9y08G5cveCdUAOZOw+4Ie0VAH7o\r\nY7X62pj+VYjLtRHusYRb+uP5xK20gNVGO8xwI8o5RBmquj8qdtwo1HnpYg8/\r\nuEjugKTEb1I3N60p4IyLlKIAKe/O0+wIuxCPUnI3HfCgHv6Z47SvxO9JUn+j\r\nSrdrW56wbG75/18QK4I5nG0fDlpJVUwV/8vBbtvxaP5Xk2GgV17+oaHtysfg\r\nD5bTz/AmnU1QhqeWDSOsClKQ8CaAtzPQGWDKcGug96df9d/FkFvQyty1QfzQ\r\nFM15/KCNaZhrVKZoNcsmOAgz4PFvStb7s6EU7dgtFiF3r4kKBzfoavYqVLRz\r\n08Dt2Vec4Ayn6Fpw//DIugZuUwuDxOAt2TY=\r\n=NTtQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 38079}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.1": {"name": "@codemirror/commands", "version": "6.1.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-ibDohwkk7vyu3VsnZNlQhwk0OETBtlkYV+6AHfn5Zgq0sxa+yGVX+apwtC3M4wh6AH7yU5si/NysoECs5EGS3Q==", "shasum": "f92a343f53f4ecff10fc1f4114d0c9e49e7715b7", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.1.1.tgz", "fileCount": 8, "unpackedSize": 182672, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmrFeA5LXt42gcclY4FvtqPTLhD2Y4pcALoq384fzdoQIhAKL/B8AFpm8SqxWMsLUX5JlsoFFHc/2zcDmjC7RoDtZj"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNARiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKkA/9H16XACm1Z62Qok8IeZ4ny/Xg9naj4TxIgBeyXzCGBlh6GnAh\r\n2tBnbEC4m9yB0J21aVWNa6PKJU2r/NqyS/ClV4KCc7u0wwa4Q6pZAEoE5b5v\r\n0FdB8TNWSZZtl6QOfRY5zBZY3TUwTMt2TR/tWfwT2mQK7ixcsSgEgML8c2Pr\r\no+y+wUotkb27ZM7XuSFPd84F0b0guoh7jS7pxPKoCKkbyYxpfPGtkHLjMNtN\r\n6s0VkZnSfc8AbFInQyt70js3lpArVZFU4c9Xaq/OHsUYBlnUVQSiTyOlVQDT\r\nROrhmx5zt1Po9vwEY47XCzBnoxzdTINixZkqqTWYQffkb8D5LL4nKXIN4wR3\r\n2rXiXVZaQngmnR1QzkMB5AFuQlzyz661DsJ15sBQDTek1ioKxa3Po/ZNBahR\r\nrN6p4HtjFWVSl+flOTJ3ctUTn5SUcXI5J9CXQ+oEDD/3d844baAaYeck+adG\r\nHXil8IunWPTu26rs706dcx6aU3RBNMoCgaInNuVnOV7pIfFm2RQUyOUN+pA5\r\nE+CH9Ch4R1BsZh1o9p2/FugOh5sI+mivvuy7tz81/A2x4Fo2BLtxIhi3iVXK\r\nVqLC0iare/nGVEVDhwj6zfYkhsFkKtx1IGb7egxBE6Wz2CAm31G6RbNrFhFk\r\nLvteYwJVe9hZH46UYU9gVE8029Q+BPnyGU0=\r\n=KvXh\r\n-----END PGP SIGNATURE-----\r\n", "size": 38261}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.2": {"name": "@codemirror/commands", "version": "6.1.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-sO3jdX1s0pam6lIdeSJLMN3DQ6mPEbM4yLvyKkdqtmd/UDwhXA5+AwFJ89rRXm6vTeOXBsE5cAmlos/t7MJdgg==", "shasum": "84fb7d170047c3aeb7b0047ace59510bb19208de", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.1.2.tgz", "fileCount": 8, "unpackedSize": 182863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDd3PLQ2IBDp/1a6/eqBz+kYVxrqFA87RJI8q/cBvvSbwIhAKSughH3TU7/IbXvkyGQ7Iqaea+7EJPqi7Re53fbUwwG"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSE+EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpg5A//aWFth3RYgNItYF9jpoYAMHGdnTIK6QhDESKo0WncSVcB14Bw\r\n9IG5fcPg9VDwMaEmSy1arli4+UJB8xZ9pErbemfH6iTPtXNXW9CglEg2b5dt\r\nlu8wqMCeMIiW7SoESnhJB65ljPtjtXLeysBP9BOzDndh5ZNpWfTqokaeCPk0\r\nsXbnrgvoN6xo7JG0I8t+/63C+5wT+C+kLUyg7/fEpcq6hh5sVdppQBr39qsx\r\nv3ehdX33/Up1TKZUgrjujQTrMbNRae7T4AT1cw6+WPsvzOEnP/PnOG3mMks4\r\naijNl0Oxsar4Tp6WZ1sl0NLhUTwHj+p9bkuey4sizN5Z/T5EAT3xYDRFQ3fn\r\nyAzYh46SNjPNr4JpmmjXY86xtILxBTi0WBXPL1dhpk0kuKAUZ35SPVsDwgQO\r\nfJxTMEFpJRzRS4KLck7Q/kvMvejrkM2kjeDjRWoocedCDcr+NOG7cnIrpd72\r\n5/OUH/j4eXhGqZr3S+YeP8/2/LGo6fxlAgAwRQPia1oWPIbEnb1akNYZpMos\r\n+4FHqNGMNXpKnSPcAJFn6D6EBpnzLbDttKLKc5kHHHGCCsB0jkbxPEhwgvlc\r\nN27ChsGf5Xa5Juo7nyz/5Q6SPtIaWfGJ7OsBJZB4bz6vwv/LVRicE1SeD5M6\r\n5TWwkfxZuDPgoHGgmEM+eiQ90Qq9gV6bKpI=\r\n=m87P\r\n-----END PGP SIGNATURE-----\r\n", "size": 38307}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.3": {"name": "@codemirror/commands", "version": "6.1.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wUw1+vb34Ultv0Q9m/OVB7yizGXgtoDbkI5f5ErM8bebwLyUYjicdhJTKhTvPTpgkv8dq/BK0lQ3K5pRf2DAJw==", "shasum": "401d0b6d18e7d5eb9a96f6c8ae4ea56a08e8fd06", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.1.3.tgz", "fileCount": 8, "unpackedSize": 183103, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB7/6YIH3Lh4QkYzFd0jraD8UHkmqMwT5eqQLOOJQvtBAiBg5+2yYLSM1Xyfo/F7ArRpIvSLOO1aFDUhZhhuTOxuqw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqXo0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoikQ/+NoIUZRVNLqMA5yjaLny7zc22OR1zyvWLm64rH7iGj3MxFRHR\r\no6QdcLH6li4k4heTZFDytitIpQ3QRTPO6vux1OIWk7PbCC6M6WM8Ou7WvjXC\r\nH14/TL/hvMhQFcYDBmjyXbvS8DKmCgG/a1nODpvcSOi4I6/99vN4pjkdCkJo\r\nXIeni1+z8pSLaz7wknZVGiDim/c0pUNTt8XgEES6dyRi4B2e95JglzXzg2nc\r\nk1bpygAs0Ny1YE2fR/0uA+wHis9XpwejW+hZiO8uVdE3/5C/hvhYfi2LcGYV\r\n38jAuYyIubK75qgPFelmy9aWK4t6p+JPA0gZbirjLezkqpDWS7dxO+lureEk\r\n/KoG0fFhe5LiBY+4V810+LKlDRZI5UCndXsr5ptms8v8Grbt33TPFK+YApBH\r\nUaClgh44l8l6v6Kdse76JSbopJhgdEHV5e6DGbHg8HvpB5L4D85BD2r2lquQ\r\nrOM306KueLEOsUuGztD7n4xvKy2Tm03jJdd6wPjpq3mL7PzRTy34ngJGRfYA\r\nZubDOzgQ63c6pl091qUSg9Gv3PSbp1cDEkZWXHK/wQ2mvcz1pggPnfhBgfzq\r\nWiSt9r/WdZbewxi2N1BiqxSGJy7dFlLs0hUaCdkg9MKgQeI1AufsI4NeOicM\r\nX8JIrO7yLkfeU9Hc6ZQWydUseB6Czlx41Jg=\r\n=+lGL\r\n-----END PGP SIGNATURE-----\r\n", "size": 38403}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/commands", "version": "6.2.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-+00smmZBradoGFEkRjliN7BjqPh/Hx0KCHWOEibUmflUqZz2RwBTU0MrVovEEHozhx3AUSGcO/rl3/5f9e9Biw==", "shasum": "e575b7542406be8bc61efb56f1827c71587bb5f8", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.2.0.tgz", "fileCount": 8, "unpackedSize": 183976, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFMJJZZmWNg34JfD/vcN3MiWJZaUQN7QUkHjK6KXPMpQIhAK91MJ3ra+KooR+IVM4SMludwT7NicDTNfz6hOM5WtFn"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjx+GQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGZRAAlqR7nak8pZ1lnIWRAnmN/pm5LRuPyWldlMNPUDzg2nmJPKyM\r\napqOGiQV7KxqenRCtohZ8ui2/uoFeBqOzZ50+it4V10bGoHJDiMXYhbiZGZU\r\nv5TjYr32Q/Th2XbfO2eWoumST3sN//L7CGnTlr9mq7bRAdMr9Bv1AGNZoM7i\r\nxHu24gMXXHWm8d2GAHNU3kkZBBboOotvUpRjO24ulAnJWxgK8vWup2yooy7M\r\nt7Yy5b96X/iQp6R1XG2A1+IKx7BS1S/pIebUusGaxNpgy/lgNvjnprkOARHl\r\nUM4J7zJ1pohigDq1s8kxNjJd9JcVkJn0wsvDxpo8H8ujXZSkWs3xbJgzy++z\r\ncnX/FD3TTblxvs1Jr23OiSv2BJyc0ak+K4HUHNYo2SaBPPrJl+UV1W/p5d6x\r\nyFZgBoCUSIy62UNT8kJPV8xHyF4n7OPS1cayTF0w4BtJcOPuZ28r+rz33tH9\r\nR8Dykdk3ux04FlKi+n7hS4vP6S/6rsEv3BICuczGN5LNitSaMyy4E2HKNwkY\r\nb/za2ltvF2jtubg3+kJyx8mOjClnir/GsesvUKEiEYbhf/qs/N8ZOlLxJtR/\r\nC3XquXXjSm1OhSTaSUQQmPGZzTF+zl4g76lziC/WRGoCsxg1E/2VWuQnQ4LU\r\nwJp+gL7CRzZ6tjFn/yVh3a6LHGX6tPkKms4=\r\n=rcAC\r\n-----END PGP SIGNATURE-----\r\n", "size": 38705}, "_hasShrinkwrap": false, "publish_time": 1674043792707, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.1": {"name": "@codemirror/commands", "version": "6.2.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-FFiNKGuHA5O8uC6IJE5apI5rT9gyjlw4whqy4vlcX0wE/myxL6P1s0upwDhY4HtMWLOwzwsp0ap3bjdQhvfDOA==", "shasum": "ab5e979ad1458bbe395bf69ac601f461ac73cf08", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.2.1.tgz", "fileCount": 8, "unpackedSize": 186267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDx/fVrfv76+17PB80c9Bmx9Dlw0a0zptXeEu/BZi0zYgIgaJuDWYkFZkY2e00Rk6k+0U0KwouDBzO7yQ5sw5558TU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7H/SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIZRAAkaNLgXSVqxYLUsQP6SRLGyeguoVNCi30PF0/o3z0kZIqZ6VE\r\nCwqk/h8grYw2JcdEYv/+vVnxEFa47ezNyNl9poexL1KV1Qd14VdxfqlqXhFR\r\nzprc1nhNDrfZ4o6REg+awxc69Z+e+6iTSmWwn5DXjlGnLhjWgMHsAR+pRbLP\r\n3g858sTRAoRQtjJnIYXtVQNrCrh/O4cCv/jQtMbTW6zno31Zh/owtIox2TJs\r\nCPpWj0rUFqb3YrGNUGnD+AP1ZQIrczJDt+k0J+yGq8lf5u00zA6zhcCsUQq8\r\n+w/dB86Ln4nBbnaujfP3d662R6jfFFLgnR3dCxzS903AKHRbRyI0baJgEkGa\r\nO4K/wvGECBccZvfSAfgzS1qqnonfejb0f1snE5bsCK6Z5GKFi+Dzg57FDIuJ\r\n8TvUBCi/2ZXQ6qv9uqOriDA0ixbCPICAH+samLp6TD8e1mCgYeorvS2V08RT\r\nWGv6jmJv7Phe09OJL9ODNVrdzTfQlx1ejIhkVoJDI6A+o8zAbYI7YhL2yi0l\r\n/flVp+LYRTlYvWIwd5ae9IF5qunnEnMPm+Qc6UGI8t1P68JbHE2X9jRV333P\r\npQeZPivkyQ9bFzxEjim5L5WpyGWSZ0FCFacK63bapQWBKlPW1GvDc/tfC0+0\r\nSxdhYlqTJ/CU3Kx1RY6dg6ElMknVKZzuxd8=\r\n=8p/Z\r\n-----END PGP SIGNATURE-----\r\n", "size": 39252}, "_hasShrinkwrap": false, "publish_time": 1676443602703, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.2": {"name": "@codemirror/commands", "version": "6.2.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-s9lPVW7TxXrI/7voZ+HmD/yiAlwAYn9PH5SUVSUhsxXHhv4yl5eZ3KLntSoTynfdgVYM0oIpccQEWRBQgmNZyw==", "shasum": "437d9ba275107dbc629f0bfa3b150e0e43f2a218", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.2.2.tgz", "fileCount": 8, "unpackedSize": 186351, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVdHDcSmy6XWl0Vfcvwb/mz3W/UZmyeKCyyIz+FVCq0AiEArVOTYkxVgK/OLc3c3R99976Np9sBhNPn9caq62tKCg4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC1f0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOvw//WWk8ajZn0+CP2RRkTZo7Tu79sE7yGQVtoXNP8xwgw6Qof/Ex\r\nS8gH4t34Z5LUVPEKpERINqUKNWtlU8SmDIUIX7x2fm7N26fe/xpCJJHKUXxJ\r\nb6nYP6qVpRlI0XQK8cp2TKcLoiiYM4YTsC9z/KV/UeKqCFlqhawS5J5euF8u\r\nygBQNUtB5zHw6eT4yg7qJ3q6vSmiED5zFJBq9ba8bT1UlOYYZrx1oSfBo2HB\r\nUV6XdFfHOXOgzH2obBaLfcV1WCOtKEWsen9wTYgOeT0YDqCpsJYZzb6fIW2H\r\nwv0aK7EFHX3GgNOlHkqcxKCV6VvOjXJUSyl5Crdn25eKFFMewmUYiZEu17M1\r\nRbDnJQ6Xax1EOHsJ4Qnr8VWtcq9RZjIwxH/SGycfYz3MvxDNeBsa4R1hcWcq\r\nt6VFgjFHDFvh9eQmm9mHGO5FwakwJkHbhD08mPpj28bnb2YOk1Bpll78kT02\r\nMcFADViWSDVGBNI9HDIFnH/PLvL1NhM54QaZUGEg6NxpZMfQuAbo7+uEPoqa\r\n/elHeBY3L7zsWms9Gr9w6YUubLMaa/K/OMXTk4f6UiQBPhewg08niV+rUjQa\r\n7mBT0PXU7OFoax+goaBa9nWBFn1Jc5QjI6u4hBZzXlAcNvdq9awYJ19AlGMc\r\nribNaSacYxsym584rGxXnPJY4Lpi6z6RxT8=\r\n=2wtv\r\n-----END PGP SIGNATURE-----\r\n", "size": 39298}, "_hasShrinkwrap": false, "publish_time": 1678465012013, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.3": {"name": "@codemirror/commands", "version": "6.2.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-9uf0g9m2wZyrIim1SavcxMdwsu8wc/y5uSw6JRUBYIGWrN+RY4vSru/BqB+MyNWqx4C2uRhQ/Kh7Pw8lAyT3qQ==", "shasum": "ec476fd588f7a4333f54584d4783dd3862befe3b", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.2.3.tgz", "fileCount": 8, "unpackedSize": 186546, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5tgZclgDSlvDVuUqwAIHLsH+/Q1bMi1tcBA3qVv+e9gIhAKEARSg8dOGri+RcmD2UjwPZBQE5MMs69hPH5mZ7iNj4"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkP5iZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtJQ/+ND5bORd0Y5tC1LtSW+DK/Ne7ier9U1tw19KwXFuugLjw2ns9\r\nQu/SmibgcWhKe19AoHKqalZ5tOymy3eNJNEkAw5aDHnIu+RBLqixW4NbWREv\r\neIM4p+5ley1n7GYoux6S3dYwYq2Pxb97VEzB5mePawgFB4o8gtCV/c/OT+xJ\r\nzkiobhGSmeT6Tnu4KOuTNULmtYr7ye5m++4w0l+cmcw0ocRahM41ECcUs9xa\r\ntGh/vZpP9l8lMRJqT1v4spv2G8A9G8GLxGyFb5vYGhWOkCey9/rlA+9YAMEB\r\n3XfJcyKKyXAXJgfExFHqhuLP2A5JR2fwjqr6xy7032N3RB+jqjzeWjrcTH1K\r\nd79G7GuWIX/IOwdSu5/x4IFuUXg30eYbo3eyvC+qNyur3FP0JJRSjYQXs+Vv\r\ntvOuwXs4Z8Rl4ig3pKaQLou3fvaU43CXc8Bs0GWUm28wNJngmbbjthlc3iaL\r\n1idgzhk56LDzQ6JMyNZwnri1OkzIdsk7tJEDVua2YWTo7CQwe22qNfIeRUov\r\nRKNozbR0RrFz+LgDQl+zTJ2smJ5RF3XfhACd0XOeT19sRFOEG7doR1smUCMN\r\nalVXn+fnOy+YP6FcSwHzeohu4YIZaDoX7lJxhUfWEM+KwEqfKmPdg4IEYg/C\r\n0DwtnD5KhV11SykqMR98nMvz+gZiu2OHwIs=\r\n=mk2n\r\n-----END PGP SIGNATURE-----\r\n", "size": 39332}, "_hasShrinkwrap": false, "publish_time": 1681889433077, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.4": {"name": "@codemirror/commands", "version": "6.2.4", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-42lmDqVH0ttfilLShReLXsDfASKLXzfyC36bzwcqzox9PlHulMcsUOfHXNo2X2aFMVNUoQ7j+d4q5bnfseYoOA==", "shasum": "b8a0e5ce72448c092ba4c4b1d902e6f183948aec", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.2.4.tgz", "fileCount": 8, "unpackedSize": 187021, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGd9H9mAf/qETkEIfBQtaeYUXBBL5IyUxk3thB4bUODAiB1vI8uyvqe9FR6R8RFYW+/wbDgNBHlJSPkgEYErCZO7g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUg0/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpD3Q//SprSzq3d/Juyxkew/MP1tZHB7UvYBjZHUpA/Nj835NNfQrkD\r\n/LDRsifwOo2j26naGQGCqWwXOJRb36h8BPS21HQq6jrX88n4i/W/unelDBTs\r\nbSswwmdNO192kpVGXREwUPsbbXMCjj5DFRHP5BjyEDztJwi8cZkxEp5ywnQz\r\n79ZUO3sLDi0yA2aKw+4AJvZp/cvOLHadUWAGTVLLWuVkhw4QD5pxeGJou6Rz\r\n5+OGLZiFUx7IVFrAR2SoKdKSirsaCLLU0JBm+94sSRdtEbLm5A7AglKiC7Ct\r\nX6je30r4H3+Qx3wuYjwz5vGxTrFOtttyUADT7vunB75TwlDiVj5Be7gY7e8J\r\n4RrFlChBtCywGzgLyhJioLw8mQ5JLAM8MPNDlqu+FQv7n3cYK7UUzpl4qbAU\r\nFnGhCYkWhO/iei45MswIbbG9uH7t0GoEFWc0/P8/AL9/s3FidA9dMbhgQ7XR\r\nkuvoAaVo70CTp1JDPQsdbqLWD0HV2vCQD3Cs9Hm6ukxwm7ldntLlTW8k5VpX\r\n2/T25IIk72Ex60OTP6oVL7ymivhdFuwvUiUcLrph3dBSjyOv73ZR0r1MV+qg\r\n6stEHGA+Tm06bsoAZvfby4sfXm3gAMCwaC4yaFaEu5/tZR4PQdUx4JeM1obh\r\ng+1YIIHZxj2WD+UTSRsNTQNAue0C6zdA9Ho=\r\n=FiOs\r\n-----END PGP SIGNATURE-----\r\n", "size": 39527}, "_hasShrinkwrap": false, "publish_time": 1683098943414, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.5": {"name": "@codemirror/commands", "version": "6.2.5", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-dSi7ow2P2YgPBZflR9AJoaTHvqmeGIgkhignYMd5zK5y6DANTvxKxp6eMEpIDUJkRAaOY/TFZ4jP1ADIO/GLVA==", "shasum": "e889f93f9cc85b32f6b2844d85d08688f695a6b8", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.2.5.tgz", "fileCount": 9, "unpackedSize": 214524, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFi4/acxZxo4dPywvb8QCDZ0J51imQfnkojP61E/k9JDAiAakFSb0TiwvTgUx89cPis7Tu44IpQJC7cV50TMzH90yg=="}], "size": 43658}, "_hasShrinkwrap": false, "publish_time": 1693047872727, "_source_registry_name": "default"}, "6.3.0": {"name": "@codemirror/commands", "version": "6.3.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-tFfcxRIlOWiQDFhjBSWJ10MxcvbCIsRr6V64SgrcaY0MwNk32cUOcCuNlWo8VjV4qRQCgNgUAnIeo0svkk4R5Q==", "shasum": "cb7ad6ddc1e8af3a3c352135bd0348e6950b4e9d", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.3.0.tgz", "fileCount": 9, "unpackedSize": 217935, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEEhKx5RrDeUh3Kf/8B37myaazXODqKBEf9wHe0LQDahAiEAsloheyAM/j1qeZCe9abF2L8DMwhrDZxZsGV9OL9luRI="}], "size": 44264}, "_hasShrinkwrap": false, "publish_time": 1695981849396, "_source_registry_name": "default"}, "6.3.1": {"name": "@codemirror/commands", "version": "6.3.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-mgVour3mg9pnZUp53LzWQLPTRIBI3aZcoMJiJu6R0FadsLrydmMFxq/HMcI1zkElWWXcjaIZXemyog5IMKVIIA==", "shasum": "c8eb8d7121dea3ba487330ac115770bb26643163", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.3.1.tgz", "fileCount": 9, "unpackedSize": 218330, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAKawTfezMZXjziXeGcRsINKv6Y3hosZSJWyY5ZDRcNlAiEAkLIm3rcuQ03x+V2wDzGBvZ0oLI2OxjGQfH54LjY3fwE="}], "size": 44343}, "_hasShrinkwrap": false, "publish_time": 1701078602561, "_source_registry_name": "default"}, "6.3.2": {"name": "@codemirror/commands", "version": "6.3.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-tjoi4MCWDNxgIpoLZ7+tezdS9OEB6pkiDKhfKx9ReJ/XBcs2G2RXIu+/FxXBlWsPTsz6C9q/r4gjzrsxpcnqCQ==", "shasum": "9fa47ccdacbea52fcddc6845089dfbf5be03f126", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.3.2.tgz", "fileCount": 9, "unpackedSize": 218486, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID37twmR1pwkqC/TmqvPWvpc5meiOceIyVoiX2NY41NuAiATXIF0VViCSYC7v040wh/aumm7aWwkgSozbf3QnToJQw=="}], "size": 44697}, "_hasShrinkwrap": false, "publish_time": 1701202401867, "_source_registry_name": "default"}, "6.3.3": {"name": "@codemirror/commands", "version": "6.3.3", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-dO4hcF0fGT9tu1Pj1D2PvGvxjeGkbC6RGcZw6Qs74TH+Ed1gw98jmUgd2axWvIZEqTeTuFrg1lEB1KV6cK9h1A==", "shasum": "03face5bf5f3de0fc4e09b177b3c91eda2ceb7e9", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.3.3.tgz", "fileCount": 9, "unpackedSize": 218672, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9afyPsmRcY+uQMveQezmEqp1AsLI0IqMsrry1dF8lJAiAxGNhSaKO3F6ouaRazMT4FLUHpKJ/a10M5PMDskIp3bg=="}], "size": 44837}, "_hasShrinkwrap": false, "publish_time": 1703784440737, "_source_registry_name": "default"}, "6.4.0": {"name": "@codemirror/commands", "version": "6.4.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-HB3utD5GxCvEhSyj5EuG9KpuQQhFpxalh3lwrspyL/GeSNDe4c6JDxVzL12SJ+7gUknHjZzmq7OPCb9QPgiRmQ==", "shasum": "be4538b98b9f4f6f2f43a178622be6540be5a18a", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.4.0.tgz", "fileCount": 9, "unpackedSize": 220762, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID2x6Plf45aMxKCo/SpXiosWRJAyOHxlVcnBskDqerR+AiBIf/2B9CweBvEH62g8CjySnNOMrWgxTmddvDdSgWbp8A=="}], "size": 45411}, "_hasShrinkwrap": false, "publish_time": 1713368039727, "_source_registry_name": "default"}, "6.5.0": {"name": "@codemirror/commands", "version": "6.5.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-rK+sj4fCAN/QfcY9BEzYMgp4wwL/q5aj/VfNSoH1RWPF9XS/dUwBkvlL3hpWgEjOqlpdN1uLC9UkjJ4tmyjJYg==", "shasum": "e7dfb7918e7af8889d5731ff4c46ffafd7687353", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.5.0.tgz", "fileCount": 9, "unpackedSize": 222481, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICiv4Lci8oicS5kJOebMd7XJUu5bcgQpHXvn3D0khsp0AiBgYKed6tlWvcOqs9lecb+Kze4KGXj+vZ2H5tSwJ+DUhQ=="}], "size": 45635}, "_hasShrinkwrap": false, "publish_time": 1713538708887, "_source_registry_name": "default"}, "6.6.0": {"name": "@codemirror/commands", "version": "6.6.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-qnY+b7j1UNcTS31Eenuc/5YJB6gQOzkUoNmJQc0rznwqSRpeaWWpjkWy2C/MPTcePpsKJEM26hXrOXl1+nceXg==", "shasum": "d308f143fe1b8896ca25fdb855f66acdaf019dd4", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.6.0.tgz", "fileCount": 9, "unpackedSize": 225920, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsB5Wh+p8XpdxCW4TENQRE1N26Xgcvq8nyGCWVsy9ZOwIhAPf7V+FZR+Af3u3NuZT4AFoOHwqa3U7rNtcuXbLGzqtk"}], "size": 46493}, "_hasShrinkwrap": false, "publish_time": 1717511314238, "_source_registry_name": "default"}, "6.6.1": {"name": "@codemirror/commands", "version": "6.6.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-iBfKbyIoXS1FGdsKcZmnrxmbc8VcbMrSgD7AVrsnX+WyAYjmUDWvE93dt5D874qS4CCVu4O1JpbagHdXbbLiOw==", "shasum": "6beaf2f94df1af1e7d4a811dff4fea2ac227b49c", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.6.1.tgz", "fileCount": 9, "unpackedSize": 226193, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLOmIjzb5H1lwM5kUxqimZmmcgxM6u7/SGBV4fAPjUBwIhAOwZe8SLVttCWroANcKlBTKtBRXfl2oBLAiVMR9dZDVg"}], "size": 46532}, "_hasShrinkwrap": false, "publish_time": 1725115548546, "_source_registry_name": "default"}, "6.6.2": {"name": "@codemirror/commands", "version": "6.6.2", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Fq7eWOl1Rcbrfn6jD8FPCj9Auaxdm5nIK5RYOeW7ughnd/rY5AmPg6b+CfsG39ZHdwiwe8lde3q8uR7CF5S0yQ==", "shasum": "a8ddb191e00dcc0efa03ea1ff8dc486f902dab91", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.6.2.tgz", "fileCount": 9, "unpackedSize": 226887, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaI57D3kaNcMBxJkxVw4iDiAb1Z1SC0IPG/Bnrc/T3ZgIhANeUyZlsmN/1qqhxqIxVQcOe+KLFsvs6ugATuJt6zwXA"}], "size": 46667}, "_hasShrinkwrap": false, "publish_time": 1726561005584, "_source_registry_name": "default"}, "6.7.0": {"name": "@codemirror/commands", "version": "6.7.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-+cduIZ2KbesDhbykV02K25A5xIVrquSPz4UxxYBemRlAT2aW8dhwUgLDwej7q/RJUHKk4nALYcR1puecDvbdqw==", "shasum": "a06595c0bd2966951640319a6abd953885d22bab", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.7.0.tgz", "fileCount": 9, "unpackedSize": 231743, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB9V3ZM3/o/rOlnJQ0XT5u3dYG+ExMVTFK1Lb4+Dx0FuAiEAoZV21UHUUvEta4ulKaa6YhuaW2J4HDbhf0ae/4WBF48="}], "size": 47787}, "_hasShrinkwrap": false, "publish_time": 1728294114988, "_source_registry_name": "default"}, "6.7.1": {"name": "@codemirror/commands", "version": "6.7.1", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-llTrboQYw5H4THfhN4U3qCnSZ1SOJ60ohhz+SzU0ADGtwlc533DtklQP0vSFaQuCPDn3BPpOd1GbbnUtwNjsrw==", "shasum": "04561e95bc0779eaa49efd63e916c4efb3bbf6d6", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.7.1.tgz", "fileCount": 9, "unpackedSize": 232181, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtCYZxM2lMkIeoNuU4WqfYITOir0D4KsVo672YaWQdxAiAlfWYx6hLcegQDRT9OWorUPOTbCxHayiX82VUxgbkPsQ=="}], "size": 47858}, "_hasShrinkwrap": false, "publish_time": 1729503222027, "_source_registry_name": "default"}, "6.8.0": {"name": "@codemirror/commands", "version": "6.8.0", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@codemirror/lang-javascript": "^6.0.0"}, "directories": {}, "dist": {"integrity": "sha512-q8VPEFaEP4ikSlt6ZxjB3zW72+7osfAYW9i8Zu943uqbKuz6utc1+F170hyLUCUltXORjQXRyYQNfkckzA/bPQ==", "shasum": "92f200b66f852939bd6ebb90d48c2d9e9c813d64", "tarball": "https://registry.npmmirror.com/@codemirror/commands/-/commands-6.8.0.tgz", "fileCount": 9, "unpackedSize": 235543, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDX2TCIHldCK69S79DGBQcJpSY1kZS4J84fbGEEqDx3CwIhAKTRRh6T+7LNrc/8sE6gxsGfQTuRC2KD+ET4OV2ijY4E"}], "size": 48531}, "_hasShrinkwrap": false, "publish_time": 1736353586777, "_source_registry_name": "default"}}, "_source_registry_name": "default"}