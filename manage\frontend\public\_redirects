# 处理API请求 - 确保API请求能正常传递
/api/* /api/:splat 200

# 处理静态资源
/assets/* /assets/:splat 200
/favicon.ico /favicon.ico 200
/robots.txt /robots.txt 200

# 处理应用路由 - 精确匹配
/login /index.html 200
/404 /index.html 200

# 处理应用路由 - 前缀匹配
/illustration/* /index.html 200
/users/* /index.html 200
/feedback/* /index.html 200
/sql-executor/* /index.html 200
/comments/* /index.html 200
/lottery/* /index.html 200
/car-treasure/* /index.html 200
/vip-management/* /index.html 200
/prize-management/* /index.html 200

# 处理根路径
/ /index.html 200

# 处理所有其他路由为404 - 使用感叹号确保这是强制规则
/* /404.html 404!
