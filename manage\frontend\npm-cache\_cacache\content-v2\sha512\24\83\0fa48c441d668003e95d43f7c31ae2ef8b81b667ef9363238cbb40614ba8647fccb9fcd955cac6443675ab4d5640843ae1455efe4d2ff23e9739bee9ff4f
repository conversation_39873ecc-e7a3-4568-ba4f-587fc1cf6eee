{"dist-tags": {"latest": "8.3.0"}, "modified": "2024-11-29T16:38:16.528Z", "name": "node-addon-api", "versions": {"4.2.0": {"name": "node-addon-api", "version": "4.2.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "117cbb5a959dff0992e1c586ae0393573e4d2a87", "size": 55649, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.2.0.tgz", "integrity": "sha512-eazsqzwG2lskuzBqCGPi7Ac2UgOoMz8JVOXVhTvvPDYhthvNpefx8jWD8Np7Gv+2Sz0FlPWZk0nJV0z598Wn8Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "4.1.0": {"name": "node-addon-api", "version": "4.1.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "f1722f1f60793584632ffffb79e12ca042c48bd0", "size": 55474, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.1.0.tgz", "integrity": "sha512-Zz1o1BDX2VtduiAt6kgiUl8jX1Vm3NMboljFYKQJ6ee8AGfiTvM2mlZFI3xPbqjs80rCQgiVJI/DjQ/1QJ0HwA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "4.0.0": {"name": "node-addon-api", "version": "4.0.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "ac128f43eff7fac4b5f5ef2f39d6d7c2709efead", "size": 52696, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.0.0.tgz", "integrity": "sha512-ALmRVBFzfwldBfk3SbKfl6+PVMXiCPKZBEfsJqB/EjXAMAI+MfFrEHR+GMRBuI162DihZ1QjEZ8ieYKuRCJ8Hg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "3.2.1": {"name": "node-addon-api", "version": "3.2.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "81325e0a2117789c0128dab65e7e38f07ceba161", "size": 67002, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.2.1.tgz", "integrity": "sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "3.2.0": {"name": "node-addon-api", "version": "3.2.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "7028b56a7eb572b73873aed731a7f9c9365f5ee4", "size": 66952, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.2.0.tgz", "integrity": "sha512-kcwSAWhPi4+QzAtsL2+2s/awvDo2GKLsvMCwNRxb5BUshteXU8U97NCyvQDsGKs/m0He9WcG4YWew/BnuLx++w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "3.1.0": {"name": "node-addon-api", "version": "3.1.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "98b21931557466c6729e51cb77cd39c965f42239", "size": 122233, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.1.0.tgz", "integrity": "sha512-flmrDNB06LIl5lywUz7YlNGZH/5p0M7W28k8hzd9Lshtdh1wshD2Y+U4h9LD6KObOy1f+fEVdgprPrEymjM5uw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "3.0.2": {"name": "node-addon-api", "version": "3.0.2", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "fs-extra": "^9.0.1", "bindings": "^1.5.0", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "04bc7b83fd845ba785bb6eae25bc857e1ef75681", "size": 138851, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.0.2.tgz", "integrity": "sha512-+D4s2HCnxPd5PjjI0STKwncjXTUKKqm74MDMz9OPXavjsGmjkvwgLtA5yoxJUdmpj52+2u+RrXgPipahKczMKg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "3.0.1": {"name": "node-addon-api", "version": "3.0.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "990544a2607ec3f538443df4858f8c40089b7783", "size": 134912, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.0.1.tgz", "integrity": "sha512-YUpjl57P55u2yUaKX5Bgy4t5s6SCNYMg+62XNg+k41aYbBL1NgWrZfcgljR5MxDxHDjzl0qHDNtH6SkW4DXNCA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "2.0.2": {"name": "node-addon-api", "version": "2.0.2", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "432cfa82962ce494b132e9d72a15b29f71ff5d32", "size": 150809, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-2.0.2.tgz", "integrity": "sha512-Ntyt4AIXyaLIuMHF6IOoTakB3K+RWxwtsHNRxllEoA6vPwP9o4866g6YWDLUdnucilZhmkxiHwHr11gAENw+QA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "2.0.1": {"name": "node-addon-api", "version": "2.0.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "4fd0931bf6d7e48b219ff3e6abc73cbb0252b7a3", "size": 149933, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-2.0.1.tgz", "integrity": "sha512-2WVfwRfIr1AVn3dRq4yRc2Hn35ND+mPJH6inC6bjpYCZVrpXPB4j3T6i//OGVfqVsR1t/X/axRulDsheq4F0LQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.7.2": {"name": "node-addon-api", "version": "1.7.2", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "3df30b95720b53c24e59948b49532b662444f54d", "size": 143727, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.2.tgz", "integrity": "sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "3.0.0": {"name": "node-addon-api", "version": "3.0.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "812446a1001a54f71663bed188314bba07e09247", "size": 133550, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.0.0.tgz", "integrity": "sha512-sSHCgWfJ+Lui/u+0msF3oyCgvdkhxDbkCS6Q8uiJquzOimkJBvX6hl5aSSA7DR1XbMpdM8r7phjcF63sF4rkKg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "2.0.0": {"name": "node-addon-api", "version": "2.0.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "f9afb8d777a91525244b01775ea0ddbe1125483b", "size": 149990, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-2.0.0.tgz", "integrity": "sha512-ASCL5U13as7HhOExbT6OlWJJUV/lLzL2voOSP1UVehpRD8FbSrSDjfScK/KwAvVTI5AS6r4VwbOMlIqtvRidnA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.7.1": {"name": "node-addon-api", "version": "1.7.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "cf813cd69bb8d9100f6bdca6755fc268f54ac492", "size": 143222, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.1.tgz", "integrity": "sha512-2+DuKodWvwRTrCfKOeR24KIc5unKjOh8mz17NCzVnHWfjAdDqbfbjqh7gUT+BkXBRQM52+xCHciKWonJ3CbJMQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.7.0": {"name": "node-addon-api", "version": "1.7.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "d2897e0a2f35895676133004abbe6a7af6e55f79", "size": 143065, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.0.tgz", "integrity": "sha512-TaiwMuQqmonaIz/dI+a3V2XE67872jC2Z+fOzKuH4piwxGZN48NwVy75hL8shzQL09Nfl/Avk7md7dVcMG0zlA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.6.3": {"name": "node-addon-api", "version": "1.6.3", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "3998d4593e2dca2ea82114670a4eb003386a9fe1", "size": 133826, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.3.tgz", "integrity": "sha512-FXWH6mqjWgU8ewuahp4spec8LkroFZK2NicOv6bNwZC3kcwZUI8LeZdG80UzTSLLhK4T7MsgNwlYDVRlDdfTDg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.6.2": {"name": "node-addon-api", "version": "1.6.2", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "d8aad9781a5cfc4132cc2fecdbdd982534265217", "size": 131613, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.2.tgz", "integrity": "sha512-479Bjw9nTE5DdBSZZWprFryHGjUaQC31y1wHo19We/k0BZlrmhqQitWoUL0cD8+scljCbIUL+E58oRDEakdGGA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.6.1": {"name": "node-addon-api", "version": "1.6.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "a9881c8dbc6400bac6ddedcb96eccf8051678536", "size": 131555, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.1.tgz", "integrity": "sha512-GcLOYrG5/enbqH4SMsqXt6GQUQGGnDnE3FLDZzXYkCgQHuZV5UDFR+EboeY8kpG0avroyOjpFQ2qLEBosFcRIA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.6.0": {"name": "node-addon-api", "version": "1.6.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "bbb1a32395245fd2cddcfed8312b3dbd6511b6c4", "size": 131320, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.0.tgz", "integrity": "sha512-HEUPBHfdH4CLR1Qq4/Ek8GT/qFSvpApjJQmcYdLCL51ADU/Y11kMuFAdIevhNrPh3ylqVGA8k6vI/oi4YUAHbA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.5.0": {"name": "node-addon-api", "version": "1.5.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "55be6b3da36e746f4b1f2af16c2adf67647d1ff8", "size": 130419, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.5.0.tgz", "integrity": "sha512-YsL/8dpBWxCFj3wAVAa/ceN4TlT8lACK8EgpuN0q/4ecflWHDuKpodb+tt7Rx22r/6FJ2f+IT25XSsXnZGwYgA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.4.0": {"name": "node-addon-api", "version": "1.4.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "4c43e4c1ed8fbf3176ce71003f352329adad87eb", "size": 114847, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.4.0.tgz", "integrity": "sha512-agquHPHnxYGox7Rjz2+TZQeOiH8IVbNFSTyTPA+peMUAP6klgrBH5dcwHsNNChQh7l/dtF0JNmZPbCqd5OXOIQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "1.3.0": {"name": "node-addon-api", "version": "1.3.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "9280a6afd4d0bd9401ef3c60b936beeea0a6b3d3", "size": 99476, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.3.0.tgz", "integrity": "sha512-yagD4yKkZLeG4EJkh+8Qbqhqw+owDQ/PowqD8vb5a5rfNXS/PRC21SGyIbUVXfPp/jl4s+jyeZj6xnLnDPLazw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "1.2.0": {"name": "node-addon-api", "version": "1.2.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "2d378bbed60cbb7b9e2c505c6833eed8723f41c4", "size": 93635, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.2.0.tgz", "integrity": "sha512-VDpTV5VK4kCqTfK5I7JAphXrL3txLbfshhjtscdYTuI4PkGvjWBFxNkctYdcRsMwgObyqVcm12tsiKZD76/d8g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "1.1.0": {"name": "node-addon-api", "version": "1.1.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "9f14bf703b4f5cc1bfe6bc4a30d8c8280b2d87b9", "size": 86927, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.1.0.tgz", "integrity": "sha512-WSGZ/xdGcyhNY2neCHS/JKQXdbOU5qZp8EGwxpEWSqOmI9+sCwO93npSH93ipa4z13+KZhGUgA5g/EAKH/F+Wg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "1.0.0": {"name": "node-addon-api", "version": "1.0.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "ce62bc6865f60f8d65c0441f2d1a5bdba5cda590", "size": 82487, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.0.0.tgz", "integrity": "sha512-7hQ0EKaQFt6plfAuTHfG5Vh5kCN+pyEZvjG0Xal3Tzv19spQnhIrh8IP7+UxbvJ0meTbktgrlN+tR5kFr14yZg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "0.6.3": {"name": "node-addon-api", "version": "0.6.3", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"shasum": "3905f0876eaa391d19e0cfdc7f10661e1897a475", "size": 80363, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.6.3.tgz", "integrity": "sha512-FikxTt0OaQ716zlsKKxZoRRyd0aAOJNRKK2ne4dS2R4FoEJXb7HyyxDTnYnLc22u0N2S2wiYuRm2gvehEdn4zQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "0.6.2": {"name": "node-addon-api", "version": "0.6.2", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "54f2719ca4a618b4eed70224181f0b816373325e", "size": 79791, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.6.2.tgz", "integrity": "sha512-bMxUO1Fo50bb3B23G7ivNtwmMZfm262xxrkrDn9OSLZ+3eDatp0/vab5nPintawCnls3gA8LXY2wpSbPMFfyKw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "0.5.1": {"name": "node-addon-api", "version": "0.5.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "b1bf4f6d91e4d10ae5d697fbcc5b0a57886fc6a2", "size": 78775, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.5.1.tgz", "integrity": "sha512-3//80u0PIUHvozitcQ/DBaaS46qa4iO9FNyQEGPybMpRyCVXyKz/I64Eok0pu+vIRriqqHyL4nhlnuTwC0d9ow=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "0.5.0": {"name": "node-addon-api", "version": "0.5.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "f378d18447cc33a7190d689a0e78d8e059c3c0eb", "size": 78778, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.5.0.tgz", "integrity": "sha512-c0IqEJ4gqRk2r2XhZIPRhQ+KjTZERXYrd9VrPs50kPhwjDFOQH6CLwHb69tWJH3+FeQR11FXaN86/OK4MmFwHw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}}, "0.4.0": {"name": "node-addon-api", "version": "0.4.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "15e5256750ca0682c5f4060302f802097f753740", "size": 75090, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.4.0.tgz", "integrity": "sha512-yMDJsQiaA01fhm6MO/tBqLXkXUf1FW40+JQ90Tm+RbKmewVGMZR4jD7ZK99Zse+/YC4723r3aJ6h7wZbKRdH3g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.3.5": {"name": "node-addon-api", "version": "0.3.5", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "67e7a4abba26918561efa383fa5be4c91525c48b", "size": 74911, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.5.tgz", "integrity": "sha512-TKtnrlB7rq/v9m4u3gAHt3O1YREgo4x/pgOucxUWb1bTEvte1+vAY1JXExK6WlXjMOnquX0vzejR3mZMt4AT9w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.3.4": {"name": "node-addon-api", "version": "0.3.4", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "af3259040fc861c1812886ddd149b36d3aa63183", "size": 74500, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.4.tgz", "integrity": "sha512-gZ7oL1NuwRwq01zde4MeRjSGMvxU2TsJ0l6anf2yHMoSc3b43wBaSzGpme/Jbul+CAmQONIIrONVp0cpaJCarg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.3.1": {"name": "node-addon-api", "version": "0.3.1", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "3ac3393e5b2b778549e0d99c1b8a32f79ce05922", "size": 70448, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.1.tgz", "integrity": "sha512-/8kB0PYn5lMHDt8k8OmuUIkP2PoiYbQSqjGZNZDR2iriK4cNHutN56nun7ogtMWXhmU1HMR/SnnDUfSWxRdabg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.3.0": {"name": "node-addon-api", "version": "0.3.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {}, "directories": {}, "dist": {"shasum": "9f78149f519144cb47dde3e658bb395c835d610f", "size": 70677, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.0.tgz", "integrity": "sha512-XTR4rYvYFWIEb91Lo8+Wi+2C2QJADI29cVkxMAP+Tq7DixkGmGv0pOW5nc6oF2QwPrg5+Sfg9CqWUYgxuHY4OA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.0": {"name": "node-addon-api", "version": "0.2.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"node-gyp": "^3.6.0"}, "directories": {}, "dist": {"shasum": "cf0f8ecd37b5c4088b8065c138730ad4f6629264", "size": 66166, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.2.0.tgz", "integrity": "sha512-OprZzZqB2Pn9GSo4FXum3l1b5Q1JNg1D/ABKmxAcBM00wSv5yzV6Gm94iDHmYd11NtFVVFC+BbHpgaIw74lB4g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.0": {"name": "node-addon-api", "version": "0.1.0", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"node-gyp": "^3.6.0"}, "directories": {}, "dist": {"shasum": "f0a473c8e462773ff01a1e2be375178c4de39af1", "size": 36950, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.1.0.tgz", "integrity": "sha512-ysffDGPYHfpaVmMi/QCWmd5vKxadnc0c4Zg9c/ElsE/spGuPHlvvfXkFbspdEenMAEYs3eFkt96eUcycMxQaGA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.3.0": {"name": "node-addon-api", "version": "4.3.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-73sE9+3UaLYYFmDsFZnqCInzPyh3MqIwZO9cw58yIqAZhONrrabrYyYe3TuIqtIiOuTXVhsGau8hcrhhwSsDIQ==", "shasum": "52a1a0b475193e0928e98e0426a0d1254782b77f", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.3.0.tgz", "fileCount": 18, "unpackedSize": 384042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sv8CRA9TVsSAnZWagAA6csP/0+vUmMkOtOapGaIgSqh\nZ/jUf+9H5Cz1pqGUY1dqe6QoHawi/zCaTByPCNnEM5gfIcPAv3GO023RvUYq\n8Nz9ZJ4rrWTMb3QEJlJcLtbOsEAP0as/zG6mNWVvviVAiTpV9JZiaemSusuM\nY3/rH6iCmLFtwWnSzrd8Z3ZhFHExKns8Lq4YTPnIhGlbJjvPlPicTtqGS+ho\nty5N+2ZjVB2JloW1yaFpicWjv/l7+2gzdDAi/rkLygYybijkDHVwyhkeKfR5\n3YtMTzu//fVUR1FGSwiGpZFKUOE+uUF9dnNODCurOoFdpLadXq+4cC1qOZWx\nG8+5PIO0Zmr13tVc8ZNbW/RUn8VA62qLDMy9oTEtlyNzVtX4FNSUv41GvJdY\nLcbahh/Z6vg0vPGWGS25+bAUlBLglvkcX4aJRNbdyAnxu2GBOJYH1n5zdsE/\nOJYeb7PfbaWnxsNNqOOqT7dq9QFYlKHRzxxCJjihZnUGXHHf7GHwnnJGwyL9\nI0OG/wb+wiW12vT8IgU/vZGz5K9ky3m0x6i67SopCOEqdrWubp0p5HAwMXnG\n/hEKsFFfOVeFK2rwOPEIPvdBvCLrLWI1S3gXIkFmmuXG5oO6eDiuM+2aYLsP\n/bl7bWs00i2ZcPXEXuk7tLeeQTA2FTDOOAagjUlk+tNOkNJ+kFcb7PxyiZch\nnI1e\r\n=lQKC\r\n-----END PGP SIGNATURE-----\r\n", "size": 57446}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "5.0.0": {"name": "node-addon-api", "version": "5.0.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-CvkDw2OEnme7ybCykJpVcKH+uAOLV2qLqiyla128dN9TkEWfrYmxG6C2boDe5KcNQqZF3orkqzGgOMvZ/JNekA==", "shasum": "7d7e6f9ef89043befdb20c1989c905ebde18c501", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-5.0.0.tgz", "fileCount": 18, "unpackedSize": 387017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmTr7OsBXjkvX1a1pIoBgUEbOcObR7oRZmoyIJTq4gSQIgEZ85Ny60c7G3g1YZz0Dgk7UE73/MJd1Efq3087px/M4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib7fGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/WA/+PaK+VRE7ssR0weWbl1LJgde0I1WDsbpL19izska3umTPme5G\r\nJEhrMMLq1VGkchBLmwIplAQki6BPApJ1TECfD3zub1+QjxfQIEevKD2W2fRN\r\np2c9486Q2CCHGR/Wpe9TqiHb3NXUfZpe+q/bZ10QlVLRQuJZ7QnVjsPBipkR\r\ncvPrUNc95LZDWIozWnf4UIoeXrJ5HMLvQ4v9S8V5TrluR3IshSdlcxirsogU\r\nIPZNmmd+f9eSvosDluIUIV/OuAcAb3f1r8WNI2r7UmSXbW3zf59nOO780eEj\r\nDN8pb3BjCvbhZOLNSkUZVHtbDInFFqOtrvfJM5Y+DoYLOGuuYHz7yWdG48N0\r\nBLdh6g5XlGyWo/aMPrEyXj0sIIofM196U1hBAhEWn/AgtjE9AH50v+TVv0+N\r\nOat6w6w25AOroQTqGPMOwKzqhK+aEiXsMmRIb50BEhixt1fmSeQ7dWamaWq4\r\n2fL17U0jazPwcHTYvMmpmKkVPm8Fnl6RnSp3MeuQfX0J7G1ydUj0c9vMoDsN\r\nv78ezy9QyCJx/a+noSJlDB0DIdR0cVO5VNNGbAbG9k6o8tgCjNPRvemH6876\r\nUhMFY1h6hi+OOOA5sMqCXmyvFN01ZIS1F8TUAKloVTeCG2PuzPGBPhTulpVu\r\naWL3Y86KFIo+5L0+JZ2GW9qCSnD75wIqYZs=\r\n=/WY+\r\n-----END PGP SIGNATURE-----\r\n", "size": 58013}, "_hasShrinkwrap": false, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "5.1.0": {"name": "node-addon-api", "version": "5.1.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-eh0GgfEkpnoWDq+VY8OyvYhFEzBk6jIYbRKdIlyTiAXIVJ8PyBaKb0rp7oDtoddbdoHWhq8wwr+XZ81F1rpNdA==", "shasum": "49da1ca055e109a23d537e9de43c09cca21eb762", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-5.1.0.tgz", "fileCount": 18, "unpackedSize": 379971, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGKHQYKR8EoyskwDDPfLWnWgIk4BQ16l2/ef5Ovne7aQIhAPyKrR8bi2u/EX6khmyNj3s/Pzbu95hnnXThfnfCVc5c"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwULpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ8A/9E/pwiR1N5T91twxyYGVlvgf0n6SNZeqPDFJwDbZyC1mMfn2S\r\n2dpjMShLAGD7ZmG3NH7RpxqNHc6onnOJNIywIB8MH5lI+cdTMm4bfExJo/cs\r\n0S6DR6F79DElW+K6pQX2z6RrFwgFOZci1XaqF3ouXMKyytjgR4L+V6NFNAv+\r\nurynpdjRrwh88t9HArQz6WRQLrmv5DPcuhWLEGYq9xk+tD0gwMaLABysNxFQ\r\nbzpKPLMDCjwE00cAtkglUzEJaaiLS9i/JHUe+z8hN2Qkcqj5oYtVG2CM0Xj3\r\nSgEMXAjriXKvihExvavb2W8hWcEHGcHHu5dWjO8XoLLRb2NpWGRNUqG/q6ot\r\n93W0wuv2LizPGAz45TyxL0zGlKDTpKSOaiWySwhYOpATjyt1+Ds34fzPsOU9\r\n/oF5cvhie2kt3oj8XHZYGRRiGdnuYWBNLrj4lFuVb7F44w8TRMDlsHTDRZQX\r\nS39bVtG7qyoBZxA1JjzRCg24b4poJ6Bv1iPi5zH5/PcmdiiVoKnd13OdKu+W\r\nt+nHA933ZQmIGcieC6g2TylFWk7fHDMvqIg4oIm8cgwd6h2kVCGzWDcgX+Ty\r\nX8wpzDDYsPuJE1mBvbn8blROmH0F+P2ND5lyFGHsRyABAthuCJfDN4Q8dwml\r\noxaL0MboMKzS3D0SXUX1rXjtdH8l/ySXo8Q=\r\n=I+q+\r\n-----END PGP SIGNATURE-----\r\n", "size": 58175}, "_hasShrinkwrap": false, "publish_time": 1673609961661, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "6.0.0": {"name": "node-addon-api", "version": "6.0.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-GyHvgPvUXBvAkXa0YvYnhilSB1A+FRYMpIVggKzPZqdaZfevZOuzfWzyvgzOwRLHBeo/MMswmJFsrNF4Nw1pmA==", "shasum": "cfb3574e6df708ff71a30db6c4762d9e06e11c27", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-6.0.0.tgz", "fileCount": 18, "unpackedSize": 379710, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/kSRgJ1SrAfUbdHkOhW25MkzDS0oBWmM3cLIlukmx/gIgeelRJg4C23MHRWmHEm3ladBYAk35593nWmHpdlsfpz8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5n1zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAPw/7BUC0mETAmvJUxEVjvJAqqr7mUYTX1RlgVwtKMEtWDwmJcGUe\r\nY4K6eSp10AX6QvIWwURVhzY//ULQMqK/lX7njPuUyxSMpp0Wh4TCx16EhCL/\r\nLhn5KGEcsOe3OaH+2aq21NBP2tTMbPE8mQja8VHb9LyuZgQE3XOcxXIfTGBu\r\nclSkK8LNcOvCh3Z+pP7doCOXBm/5BgvaymUwQ8q0+hAedJj5KXhZBaG6rGkp\r\npz/11Y/qWT+1C+NigojYl41DQlWT8+BL3z20CbiGqfWthb7zRVoiHlHI1A+o\r\n6hAWM+PSFmzHlXP+30NWKBr8+J8B5J4jDq7cOurAlfvghwXdKWcKXy5i6Pdw\r\nYPSgd2XgFHH8oYBQTNfGbkyH1CnNK8iwpJBcRAFH+iG6IylyeRaz+7AjJyIU\r\nWyAxLCIYJQe4vFJqdzHTn6P665eOq9ipK4h8wDjdjJHVfWMSVg976BOGehtg\r\nIivih065Jun4ZNK9He3j1HcanlNWXteXmaHvztL95PtGG6JCSq7ITnH/Ax80\r\n19P6H0EEjElaw8ZFTJZJGtN1Jfwca5NJCiOBMdrNtNQpTN1j/BcV20I78OOA\r\n9Z6JT3v/hIhx/np9Y6Ri4EG9DtlOPomzP323Ayw7pFZjMFqXQtom7TuMw6t+\r\n86GQ0nycaFuSH17F1kwy8Ptc99xkyIqp9A8=\r\n=O8mV\r\n-----END PGP SIGNATURE-----\r\n", "size": 58157}, "_hasShrinkwrap": false, "publish_time": 1676049779896, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "6.1.0": {"name": "node-addon-api", "version": "6.1.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==", "shasum": "ac8470034e58e67d0c6f1204a18ae6995d9c0d76", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-6.1.0.tgz", "fileCount": 18, "unpackedSize": 393771, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCepwXNKYPG1xYGIJPpVCIhf4Zf8QotuNkY7DIeYPhFaQIhALIJjWmIp0YD/jysdP9Qj9yyUURKJ5yaT69BqvJ1b4IV"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQUvSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreWBAAgtePyzNCext5Hp+0q3oiMEUBtG5EHvOR1KGo8Mfo6VdNs2io\r\n/rtsPuPPp5867qy+Vj+30y8UPcBdVVIzd6tFs7W653Dr7RWDkymq+GOwDVUw\r\njXC2P5y6/w6PtHWdjEUcSe8A5Oc/gZX5onioOKwxJq9Mfil/ngUaOgR1Hq1r\r\nTQPrhi8wDpgcy92VDYEFduLZPTMSu01dWkzFuPVy41GS3lQjSB2SW6xwECll\r\n5ThpFuirFC4nvhMlBjHcoWlUYJXjII27iNIjvU72EyfCkQeWd58vvzBaT7v0\r\nwrGUqQoUt1gxuayYjytQ5JwCudz8pGIALw2uEAK3xCVs0mJPX2Jwyd1/ocAo\r\n2bNIDCOZrIq+uDQ/naHYqMuNv3Nu/hDM32J1sCxh0Aq0rQvR3GNUYsYvJqTH\r\nUdGQ+dpOE8K4sOpCQ7HAu5WTfcAByEhqmyVQXs3Rn0oyt4ikebttW6jujc2z\r\nkKu4rydf9dEL7OXkOBtAjM03pSY/JJZX7J6IJFacxRJnTNapNviZBYtrzmqL\r\n7KZgVT2zCKNyhPkwUpYIOvZjRSh05FsKhnnv9KkM2sZNJyZvfyrs+Alk8yBd\r\n5wGGjXR8ikzSqsmxTv/bwmKIBM6TROsRe2s1oW62ay0GA53FJJ2XQRdOdTzR\r\nWdDKgeFY+wiLv0ABXyr3zBQo6r4s+xZaRgs=\r\n=O4QY\r\n-----END PGP SIGNATURE-----\r\n", "size": 59623}, "_hasShrinkwrap": false, "publish_time": 1682000850305, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}}, "7.0.0": {"name": "node-addon-api", "version": "7.0.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-vgbBJTS4m5/KkE16t5Ly0WW9hz46swAstv0hYYwMtbG7AznRhNyfLRe8HZAiWIpcHzoO7HxhLuBQj9rJ/Ho0ZA==", "shasum": "8136add2f510997b3b94814f4af1cce0b0e3962e", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.0.0.tgz", "fileCount": 18, "unpackedSize": 394960, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVQCkEY5AOexkrn1XlJwa6qtfPtdIMNWCVhn4XWhkvBgIhAI7SPi0z2r74j/sM2Yu6WXSacMjaeIndKhQ/7l8baLwV"}], "size": 59884}, "_hasShrinkwrap": false, "publish_time": 1686837348692, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}}, "7.1.0": {"name": "node-addon-api", "version": "7.1.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-mNcltoe1R8o7STTegSOHdnJNN7s5EUvhoS7ShnTHDyOSd+8H+UdWODq6qSv67PjC8Zc5JRT8+oLAMCr0SIXw7g==", "shasum": "71f609369379c08e251c558527a107107b5e0fdb", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.1.0.tgz", "fileCount": 19, "unpackedSize": 396366, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0OtSxTKEQBE8iO0FUCCRS4E2NqBSSgMgCpIQoI3OHgAiEA3lZ7t/VZJl+x1M/++iLHBPWd3G4eGxkihSuvsOb+jew="}], "size": 60055}, "engines": {"node": "^16 || ^18 || >= 20"}, "_hasShrinkwrap": false, "publish_time": 1705653985251, "_source_registry_name": "default"}, "8.0.0": {"name": "node-addon-api", "version": "8.0.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "dist": {"integrity": "sha512-ipO7rsHEBqa9STO5C5T10fj732ml+5kLN1cAG8/jdHd56ldQeGj3Q7+scUS+VHK/qy1zLEwC4wMK5+yM0btPvw==", "shasum": "5453b7ad59dd040d12e0f1a97a6fa1c765c5c9d2", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.0.0.tgz", "fileCount": 19, "unpackedSize": 387097, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNFKrMIWHvXgjGAd9zAmgvVh9wM6vMlU9rJ1m6rSSApAiBkvsMgjix15XaejKZ8gVBECfjMAIh4n4FY/wneT0iSfA=="}], "size": 57115}, "engines": {"node": "^18 || ^20 || >= 21"}, "_hasShrinkwrap": false, "publish_time": 1709622148228, "_source_registry_name": "default"}, "8.1.0": {"name": "node-addon-api", "version": "8.1.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "dist": {"integrity": "sha512-yBY+qqWSv3dWKGODD6OGE6GnTX7Q2r+4+DfpqxHSHh8x0B4EKP9+wVGLS6U/AM1vxSNNmUEuIV5EGhYwPpfOwQ==", "shasum": "55a573685dd4bd053f189cffa4e6332d2b1f1645", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.1.0.tgz", "fileCount": 19, "unpackedSize": 391869, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7o+YSYz7Nj0bFdstH1ALmtUb9NYLQ1x/TyggYiwbMEQIgVQlk21YbROhD7cM0l7ufFIy0e07LiRyDO1I6XsqOA7A="}], "size": 58296}, "engines": {"node": "^18 || ^20 || >= 21"}, "_hasShrinkwrap": false, "publish_time": 1720429074301, "_source_registry_name": "default"}, "7.1.1": {"name": "node-addon-api", "version": "7.1.1", "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "directories": {}, "dist": {"shasum": "1aba6693b0f255258a049d621329329322aad558", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.1.1.tgz", "fileCount": 19, "integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==", "signatures": [{"sig": "MEUCIEXMQqSmOJMzuSxQGyLOE1l0PNqOV5eFnpxIY/DmOnkaAiEA+B6rRlodLNAxw3pcP+UXGdVZrKMHZnizga+70Z2z28I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396312, "size": 60031}, "_hasShrinkwrap": false, "publish_time": 1720779307595, "_source_registry_name": "default"}, "8.2.0": {"name": "node-addon-api", "version": "8.2.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "dist": {"integrity": "sha512-qnyuI2ROiCkye42n9Tj5aX1ns7rzj6n7zW1XReSnLSL9v/vbLeR6fJq6PU27YU/ICfYw6W7Ouk/N7cysWu/hlw==", "shasum": "ad92cacecc86834304053fd0089f718b72ff4e65", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.2.0.tgz", "fileCount": 19, "unpackedSize": 400952, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUr5oFs9TT/ieYjOqqDAdSRA4pLrzz6C2uoHYpnvbeNAiBI3bhqf/yfe2KyzUpqPHQM3dsO1kNVuH37gCyesrHRlw=="}], "size": 59775}, "engines": {"node": "^18 || ^20 || >= 21"}, "_hasShrinkwrap": false, "publish_time": 1727706616924, "_source_registry_name": "default"}, "8.2.1": {"name": "node-addon-api", "version": "8.2.1", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "dist": {"integrity": "sha512-vmEOvxwiH8tlOcv4SyE8RH34rI5/nWVaigUeAUPawC6f0+HoDthwI0vkMu4tbtsZrXq6QXFfrkhjofzKEs5tpA==", "shasum": "43a993f110b88e22ba48bcd65e16b92165a6b002", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.2.1.tgz", "fileCount": 19, "unpackedSize": 402038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfvwrjh9pz8BZLCZ8XCWeM0THiQGS1DtQkgZKsi+/23QIhALB8lg9ngWe0dzFauiSnqh60pOi5+iA1gH1ntphzizfx"}], "size": 59975}, "engines": {"node": "^18 || ^20 || >= 21"}, "_hasShrinkwrap": false, "publish_time": 1728659244071, "_source_registry_name": "default"}, "8.2.2": {"name": "node-addon-api", "version": "8.2.2", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "dist": {"integrity": "sha512-9emqXAKhVoNrQ792nLI/wpzPpJ/bj/YXxW0CvAau1+RdGBcCRF1Dmz7719zgVsQNrzHl9Tzn3ImZ4qWFarWL0A==", "shasum": "3658f78d04d260aa95931d3bbc45f22ce433b821", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.2.2.tgz", "fileCount": 19, "unpackedSize": 402588, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZtkwrazen7HDzwEReYhJ61TGdX3CnR7vQqYkNx0+HwgIgK1Cm8jUOcTjKFDYuffVmVg8U011TKb9g7oA9sjK5cHA="}], "size": 60125}, "engines": {"node": "^18 || ^20 || >= 21"}, "_hasShrinkwrap": false, "publish_time": 1731072259573, "_source_registry_name": "default"}, "8.3.0": {"name": "node-addon-api", "version": "8.3.0", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^9.13.0", "fs-extra": "^11.1.1", "neostandard": "^0.11.7", "pre-commit": "^1.2.2", "semver": "^7.6.0"}, "directories": {}, "dist": {"integrity": "sha512-8VOpLHFrOQlAH+qA0ZzuGRlALRA6/LVh8QJldbrC4DY0hXoMP0l4Acq8TzFC018HztWiRqyCEj2aTWY2UvnJUg==", "shasum": "ec3763f18befc1cdf66d11e157ce44d5eddc0603", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.3.0.tgz", "fileCount": 18, "unpackedSize": 403298, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQu9cjCj+utwmBSGDUVR0AMcuy9SsrVUVd+DKOeJ/jkgIhANXVNV2V8le1jot7Xt1Yu0ElsQULDEKJif3HMK5VGrM6"}], "size": 60028}, "engines": {"node": "^18 || ^20 || >= 21"}, "_hasShrinkwrap": false, "publish_time": 1732897180909, "_source_registry_name": "default"}}, "_source_registry_name": "default"}