import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig(({ mode }) => ({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    host: true,
    fs: {
      strict: true
    }
  },
  css: {
    devSourcemap: true
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    // 确保根目录的404.html文件被复制到dist目录
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html'),
        // 添加404.html作为入口点，确保它被复制到dist目录
        notFound: path.resolve(__dirname, '404.html')
      }
    }
  },
  define: {
    __API_URL__: JSON.stringify(process.env.VITE_API_URL)
  }
}))
