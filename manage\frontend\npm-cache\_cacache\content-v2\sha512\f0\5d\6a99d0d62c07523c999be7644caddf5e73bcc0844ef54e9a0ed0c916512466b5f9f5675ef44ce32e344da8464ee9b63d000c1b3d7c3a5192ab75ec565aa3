{"dist-tags": {"edit-context": "6.26.4-edit-context.1", "latest": "6.36.2"}, "modified": "2025-01-09T17:34:01.121Z", "name": "@codemirror/view", "versions": {"0.19.42": {"name": "@codemirror/view", "version": "0.19.42", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-sGpuHYesqNThkAdJHTf4BO0hBeYnAHwamnCGkM6a2G/W5svRJGsFb5Vk/LQPQurDKK9V5fBTRqXH8nKGrIszng==", "shasum": "5170a2f81bb781a1fe9e33f3ab13954ca25a7b18", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.42.tgz", "fileCount": 8, "unpackedSize": 698376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/nSjCRA9TVsSAnZWagAAiKEP+gKBrWVe5IXrQqt0Xcqv\nxlxXoZgUWa9o6Szr0EaVhezf/zhxb2WWd+sjgH+HYl9V/9nC+lvAnL+CJgw2\nWxtq9oMKE7/rf3A+joOPjDdITl/wl56Z29B6QETcOQpR7RurQNX5QKe33cab\nXQ+c750piv38acBrsNj0xHyFXsA4OpFQeyaFV8IUM6eX8kJdSFfakiyHTZ9c\nggcYZnk14EUF83eGI25V/26xaqiNhKM0sK2zKFiOgG9TyZOUKidEUciPxy1B\nDRhAxg9xsV+kMSYm9dHE9nni+cdXJ0SiF4+Eo0YpsWf3dyAbwdGV8a++XYBG\n78chdpX+k8Jwx1jAln1M9A49vfHyOsXbvJpWfejGtk+akdm9n1j2kLivRe0f\nkFjopZj/RZTrl3NeMs2O3oY4OGMYd52byiBt8hqnVYjAe0nV+BsSAPnFu5tv\nZk2dwJGk1ZGDKErSavRAp4sJ1EN/v+eOikvztkunJyzrvdSK48Z4iaGM6eK0\nwX/KsitiHWRG/Ict8p7PNOnWMa7CxWTxZPnN9B7OyHryagthF5+m/lLjWJKq\nDN/qsRCmgr37HcmgoJecZeMY6FDffVQV3VnroG3RggPNHNP7VjTqPgkECDA9\nfnVMLry6PggGNiWUwo2aXBj2JZR90jD7mmfbmWriDpj1G63zEO9dTmKeUxXs\ntNLj\r\n=sjre\r\n-----END PGP SIGNATURE-----\r\n", "size": 176899}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.41": {"name": "@codemirror/view", "version": "0.19.41", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-JvtZmKZUWce6s3LAU2IhQ44LCSU8YzjxvAe1mVg+HliPqH+ARUO/f5lTkq0+ZdUa00D6SlNyslHsMJEhfWNl2w==", "shasum": "1afc984f7de9931f407d47c027df6f3a94625994", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.41.tgz", "fileCount": 8, "unpackedSize": 698130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/S8ACRA9TVsSAnZWagAAmZUP/2ZG/627zvb4iYvvfdT4\nDfiNbsRPcIQ5llQdYisXgFs8QvKV6c03Mina0WxRFGQ4Mvuq5938f0IYFyAt\nWDJinCEUj50kEWNVoS7F4a2KTXjnvH/8l9MYsT9CVzMPdL9QamUhHQn0fi05\nf+gkcOETXsje2lWXPVRago+nZ56yz0+qnwSzp/nETAL88T6VmlAWwAvD9JY5\nC7hh0RsF20HtYXGSXl+DRlVCWQmpzSVE0vsKwd6rmEyJrIbtm/nLUMR08Yut\nHa3axhBl9dyDx9wUPhj5ztnsMyxNJHz/5s6qghhqQdX7DClAIS26IVK6M6IA\nF8A17dKDlM/ivCx3R8YHgxJChEg8+q2QgBiY3w2PU2aMKOyPczuLkFge3Ahf\nQUZNSE7CJH2Gz/8XSVc2MiwIuX8LI3IIQ6R8Zj87Xw6sfFTGO+o5kZ41aiOO\nyp25eSFHRXOMo7xM0wuNI7vIgiBvYqHkzo/GMmyiSKKJw07mYCuc56A7MVY8\nXhXs6acMhMWz4vqYCuJUPcoW8Z+09tIHrUcUZCoTnaH+OVOJ6t+GvdCZd6hl\n9ctYo5xumx13b17mDt8zG+9VqkrOsnfKLJseAhF7ObUYPPGJ0S4dhJskLsJk\nbM18DYH6IZGNnst8AUQoAboTCxcqiDlESmiSaUT9wK3h4Bt0Rtu6lwI0E/RT\n3Z79\r\n=kolo\r\n-----END PGP SIGNATURE-----\r\n", "size": 176840}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.32": {"name": "@codemirror/view", "version": "0.19.32", "dependencies": {"@codemirror/rangeset": "^0.19.4", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Lt+QA+aP2gXe/8IcPT3HXuPpt/dZa3NqHQDQAYo9uXXLXAKn3BbjdIu/4+9a7oWPFWJTo8cKMbfGdaCDGl6njg==", "shasum": "029b37b8cb3d723452c3da55d47f94b1ca2cc472", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.32.tgz", "fileCount": 8, "unpackedSize": 673989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuaq2CRA9TVsSAnZWagAAXZgQAJ1sXrjcnK4ktTP35tra\nNrj2MITkcU7lqBHt6sJtq5q2AcsWY1z0AK4uIMBFgFcV/794RvyAg25VxdXX\nFgqaN16VtSj6Zm45gANAaQvOUrigewqYfZcdy2UwFuRsqhW92R8fPCU1KmiH\nKeo+gyBSLbvWFKSlcXZJonLyur5EFD2WN5NbhDffO2ocQwHvJnH3X5v1MwIh\nHmzi1I15TCwc7rkXBx4lz5K6RfnAkGFULDN2G99mvC7k1myimXgce41FSxph\nXDCPD92V20crA/kdjLrRx4niekUwYTMPRurHof3NsccVnYSDqsgcr+SJAbvZ\n1WFXIITN6IMH25xfJMvPmeLnttS03xPh3K9zH8XM1y5B2HEZ0PSiabatH9r0\nSqOhCjC8Dst68JWLei2VN9TZIeg8GCrep/AUAQ655CplxWl0EJiWDYecttOf\nnpLUx0HDRYIesdofEkNP3kXbWNScXE1RUbJk3cpDG6i1iko2GPrfsGt5kX7L\nypigDBmK85BKgIUW6pRlWYRZte7PVZU/v78NvSOJdTPdwgeUqrP6vFFZ3Ydn\nDbjMtEvrqrqy7YXtALDK9wu+sqAE7ku/6hBCQLUAqap5StkPysCQGBw3Ui1S\nozwrx1KFdDmgGP+JNpeiYUvjzw7QtUDYKrfwNrX+PVAj18YhhYomSEuVCoCQ\ncIWt\r\n=yaaS\r\n-----END PGP SIGNATURE-----\r\n", "size": 171392, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.31": {"name": "@codemirror/view", "version": "0.19.31", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-40JVMHBO6NpCHidjGw37HL7Yx1mRXnlSj6n3sjgD8h94KMDlvA8hnSg0a756ZHiUjrfDSpknSwjTx2PJFiV87w==", "shasum": "c64a3413c1151a11d9ea12519a9dea8e1c31bb06", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.31.tgz", "fileCount": 8, "unpackedSize": 672754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtyC9CRA9TVsSAnZWagAA6bYQAJtHdBhBelmv/7/wHOg4\n6ELOaOszYVNarHZnax51wIMiyBSn18svr33KhzV3jlS8/Ecovwj7bS2rffOl\nSKBk1koqa0JGiV7rin6JZZ0ZS6BSO9Dwr7a/cM/x1+Dn4ZFtc8CNiakPACnI\njb2MMrTUJm7XlfaUfm4UTEgquSCTukfLrGXLm5ceS4WohH5mx2Q15D1zEXNP\n4RGhWAV6DCpAjKRv1VsxUH2Xg3a1pk4Jx6fdrH4zn1llRnd/stCrxJUHFYY+\naJiO3QbaGkRaZ0aja1cW7SnLVjygaZcBLEZmRVngtiKrn7mltXmopq+4Upep\n2gJn+4hW959AnmiTRMXJXDuyc7ONq7xgQ1xkvFbalJjwON7fEnfsSnNV2yF2\nlVmtrQ0jUxCsvCofLyzP5j07HyvJ+2h/StHpk9KoIHPu4Ez1+LZpkKZeaDKU\nOdc8uypXEvlFjw7pj75x58N4c38wjHSVrDrZ/S0wh9zYvp4iNQbqXSC0vHr6\nWBLy2fUc7/YGzQwkJl30edmLX68yOWOa5GovZogr7ZlTjhENtcb+ORd//KbJ\nkeegEZNH88l/wLULqo30JzA2wWUqZBR2e/kprmZWP87c0DUTvKtvDanJRYzt\nB3oTzk+Cu/umRL2ZVznWhkuI0IcD+GjiiHZ/bJOIqt+uZiL4AR8tONy69UJG\nWWra\r\n=jySb\r\n-----END PGP SIGNATURE-----\r\n", "size": 170998, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.30": {"name": "@codemirror/view", "version": "0.19.30", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-gGO/O6J7mSvTw/k8Z+fe/zM6RgPTfsHvApeVfH0GhXtZ4rhPQlwdJSo6oCPnkaq0GbUfOO+IQ+l1QxSUT9uRDg==", "shasum": "47190cdbb393a365dc084ece9363f280282209e3", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.30.tgz", "fileCount": 8, "unpackedSize": 666352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtw5+CRA9TVsSAnZWagAAJ0MP/003u1tI5ySsdQ0mt3nL\n/fCF1JbLvPrmSqMT4Zl+rdQI6FpDJkOP7AXT8ajpDos/W6EBjWg02n+MARgI\nXJzjul5aLBQH3UA3t+32N72Y74fU5yo3gSQxXzeWBfoOYABnZC0B6z+uo84n\nbUzKnqYqum7fYzsAZ9qGOgn2snKHKq1it2UzmZscQJkwHKXTe5vsBm05EiJq\nX/38ByplGZV5IF3djKigb39wwyHJxinyZS25LBoxlGt3GYry3dUb2D5gkTwy\ntNzTcrEykBK75NR06KGm3XSLzf+3MHNbLrzvXFsm/JEa7Lr31E6nG+M3XWHz\nBwC90fLpmdlgL85jQ6DGtWybOTJb8IazFOVUFk9rBWBrl/RjnNP5CiueGiBE\nn/oV9AW9tmKsCyh8TcXvYBODLkOGpHONJ/EvvXIbxFBot5JzQy4lmtipxJEY\n4ZP++1VZh/9i+YNo5CCE9pib70g+p1phj99u1lKfp6G/LGWOqGLnZXZ20qpM\nEK/D43fAaDW2hhRwxnw1RytdrpzVYcJAM6p/cDB+v3ZhQ09T56esz1BmI8Xk\nr8i0BxEIxHHOBlRdh65dtPretR5yNfPxIDtTO2lV6xG7FnYVGS3tHswa4IVx\nlJnM3fH3XnXdoIRvcDC2VFYmm+hVzrzqWxDGFf+bZ8FvHOaT1VP0XPwss2M+\nim6a\r\n=SaV1\r\n-----END PGP SIGNATURE-----\r\n", "size": 169745, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.29": {"name": "@codemirror/view", "version": "0.19.29", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-rET2Ogs0M8NsH29t5R+bC/zevfHrjs1aaMV23mdbOtrebxwvANic1s4PtNofAU2dFiuK3x2vdTHPVtVg4dokBA==", "shasum": "0e2063c107fa0704b212d5f339f18831a6135738", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.29.tgz", "fileCount": 8, "unpackedSize": 664102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsicyCRA9TVsSAnZWagAAfwUP+wRDrcVXNxDkNSoJ55H9\nhA7PwMelktEjOeRRRdEt6kL880YaZvbeHyjRyGnTINGiNyh+yzVsMMCOqM0y\nfVRWoekK56Gc/92YWzJ+qwMPiRaseT9cFeaGWPMED1CDJo89hbugvVbnjNHs\nzCdoVOPCZQBHANhAtcrz03kysNKwlcrooQj8g36Eya0OduLMXwczVO91IgMZ\nd+ny37X4CoBRGLSnJfTsVCUN7mO8UucSOCLNb80TBJQ82CqNUhAW6/FZtYtL\nXWDa3HU4WboBD+N6utKh9IjSv6vRgSJYEr10gpQfi54yxy4VLjXq/KfsGcbN\n+Hu3bsSKld5oF2rfcWkMbYEPtVnZDNED6qglt2uF0bt9+Wuc2Bw7vhSSH7fs\nE1Yuk1wdmcCYDwlozYtOjIWyt08s7MGwg8YXY4+29sneE9GdbY35vMKAe9Yk\n290l0Ie15afwdvq0hs/UplJQ82jEtgPQE2N7c6MnmmoHaqgJgZoeXi00NEw4\nBq/a3MNCcPBBXy+Vvl/c3BTvqMOlAkcBCuOBGn7bwe4/8uJyl/SwMsMg8wRz\naOv+DiVCzRCZ6jhzgFpkGBU+We1QqbmMGynGLWMMSp3HqAAvDKbeRWLgM1kk\njj3ARTvS2ImPZYK549O9iqBQm3ryWDZtgoIym4fyBJ5w5IjEsorinuRw0rgY\nK/Rp\r\n=5WVR\r\n-----END PGP SIGNATURE-----\r\n", "size": 169125, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.28": {"name": "@codemirror/view", "version": "0.19.28", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-GsUC+or1uJAhy6QaRDaAK7MwQ4JqZsjDv3A3zzWhELP5I/V+qNBY5RmaMNwsM5kNJOKrGf9pVigJocFI5kiPzg==", "shasum": "fe1a499658e39f56e50fcf8981399841241b4f36", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.28.tgz", "fileCount": 8, "unpackedSize": 661464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsOm6CRA9TVsSAnZWagAAtMYQAJMvFwLng4Iq768d8X7S\naM0mpIPddsHhf7Ad/AFf7Em5/vivRbHRUquV3giJ1+zK7erNkudaoqDw2qzf\nQRxFAGdknGO3L1flYTYedKz0zT/hR8Tav5U0hU5oPjSuHHUo92bVbsvCsMuT\ndbPlWrgpwDbhxSPm8weCCl2DUIf9Xz+LzUUz2h0wlOVO5RyZhB1E0oQZbYhv\n5Yn4TvlCK+uX5jpeTNqspMMu7nQ4WP97D8MAO5utTh0Og+b2Kaieg3VSEJgg\nbAtY58tFN9JeO9n0zmTukcqfIr/huYDoXCB+G7+0DPSnlVh6ZfTbfoKRvtD4\n/vX9FaLobs6hIPQx2L+D3lNKCFw/fY3ByaedqBZESEQgXZZ77CDrKH2IVZDp\nV5k5RzA6t9aXoxQbPCrnSH6ypdxuVDxi9WLpJM9AeHKUTc37h8dxP8Uj4Qzf\npaJGntg1rRZnImm2g/UxWhwqfkm6XSKeEH9j5gby5XfTI9VqibIO0jljsKMa\nRrmNZbloceCoJBc7fQBSxuMCnMTQRZyWrTRz6ns/sPg/dIm2zGOmzUm06617\nydXAlYOXGlg5oWJzCVR2mL2ZH3U7Uo9IHIn55xuaJ7wYQqHWZ0NTFUN1cZT8\nKDfsFYTMKdV/DZQKHd4mVBggCjKGvFh3J7VSKmxOe3+moFyCPbsFTs0EeSW9\nVj6I\r\n=8M2V\r\n-----END PGP SIGNATURE-----\r\n", "size": 168387, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.27": {"name": "@codemirror/view", "version": "0.19.27", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Uz/LecEf7CyvMWaQBlKtbJCYn0hRnEZ2yYvuZVy9YMhmvGmES6ec7FaKw7lDFFOMLwLbBThc9kfw4DCHreHN1w==", "shasum": "76e5dc19ecb4ce53e9fef1d29245040d7ff64183", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.27.tgz", "fileCount": 8, "unpackedSize": 657698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrcgUCRA9TVsSAnZWagAA9i0P/2BjUJITrqDCP+B6aFsM\n0+OjHLZjAv7LrG2zF2ekF8iiEQVL82gp4J+DrZQbsX2MBCEe9PAPhNt1rdVg\n15twjgK4LH/hgZB2tPPMD7hdmb26LeHk9ow/Ih1SJ7rAUtoIs/SKSxNSSovo\nSurWS/qltJPRbJOLOYnNWpcrRK76euOmV6qZc3XaY1ef4xdRKp0eDlQOY5BF\nSlCFXlr6Y+/0s7xp2ijVigHCEZV1hx1Z3XhtFHoIB+QLqdZIud3TbiNPu1b/\n5wNn3GFT/hZ2uToMP9MfnCK4UIbvQeWX+NUEjDbvVgwg3Tf1aiLD4u4YNCY9\nHMneBeBAsmVSe6tx2J8oDusNkBdoWT2cGP37D3NR5Pi00W43sYmu53wJqCYc\nfZtg/WKuaAh9tvc/Yc/czeNONcJQbWKte1Nscl2hwrPbOuTcKREFlDlaS3G5\nB6glUXMRm2JYxk/JrptvvcmS819jE1i/Q2nuwXXlTHwB1inlDd7IRsXi+hnl\n6nmwZTYyFxIohREkZsU1uiPuHawMYNwja8zJ+wMMVQNOXwg5VCSgPflOXTBs\nGtXtYetHbSlTSkdVHlTukJ6EgSFn9EpBQXESEeeusZ45j3L43q0S9Y5IHLUP\n8rGLXn7vTe99QPi+vdFSumIwvZg1CBYyz2fTaCmos2nzYHlbxZg6IHXmx5dV\nL8Dy\r\n=RykN\r\n-----END PGP SIGNATURE-----\r\n", "size": 167369, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.26": {"name": "@codemirror/view", "version": "0.19.26", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-7QfXtFLDqXY2TfdxPCQ/NvXjINGaqXQ6SAHKQmxZ+jDcTCWmhFcxaAkrDneqcfGmtp72tUPOXG9PiwCbRWgRLw==", "shasum": "38ec9b9e0f04006d9089332b4cde2642fb4910dd", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.26.tgz", "fileCount": 8, "unpackedSize": 657167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqdorCRA9TVsSAnZWagAA/lgP/ipFX/HfJu8tsAMoqhYY\njpCmuxwm+L/qU7Ym8AcNNRp9hb6466BOILojis6N5mWPg7iBhwD4GGqrxYQD\nB6cv2JfnBHH4JvWgG3se27Xv1QBEb7uyp1f2nPfLY1Bp5jPnu+H9/FM8KGJM\nt8CuGi+BV47CRLx0Kw9j+xQRdhPyLP9WjLQdPmLLy17cXiNk1Tojx/DAGXhR\n2jBMwpwqQfcDjz4+eEPZ5s+L/Gs58W47JJ7dcOhYPVvkusyQwyViK01rOJE4\nXueEMltIx+5U2myksH1C87RZ1EbIEJx6AKQmZiSgmb8TNWCiFYGR4MCHkY/C\nnH08gLA4YuvDBY+1bctk/l02wvLN416BRtmhKSojvDzsa1QYBx8vLm/27IgV\nGLpAYyB1vCqYdFuyPW1a27HLER5+EhAtsM4gdUPJYxLuitupH7uWfCAEowK0\niOZKzP0OpB7VvQpDrFE3aJkccaBZLCM7zJlpZY3EC3vKBqmww5v/1iy87NR6\n6bmt9gRNzmfqcZVHQEjoa5QyVntpujoqcDfkdKLWJDFCzYU4sNSKy5UalC3N\nip/wC7oM5IuMl7hEXytxS1vuFFmxJ+NLjw9Rc/KNEQ8qOymzvdHX9Zv3J0JE\nNkmf5QOHD65NsMi0k12ftmi0IlssiEGpjgPB/wx09Q0UNv0SiANPGk5RSc+U\nPiWm\r\n=xxUP\r\n-----END PGP SIGNATURE-----\r\n", "size": 167256, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.25": {"name": "@codemirror/view", "version": "0.19.25", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-tJ4AnDeSyAIwUsm+lU4VRH9apMMYXcKNYM28bObTKQWV9qjTbra1UZXvQfq7/0yfp3Oituai5f90bFhakCKsyA==", "shasum": "9876e9be943499268f5ca2fc6aa60f30a1356ff3", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.25.tgz", "fileCount": 8, "unpackedSize": 660601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqMyhCRA9TVsSAnZWagAAlgwQAItauksByUuc0m0l8qF4\nX5PgBl1HR4Uu4eHecxW3uG/w2pxQJnbGCMml5d24e+sCFHAyhWLIMEdZlrrY\nu55oxFtjEr5O1IH01J0v4ZXlfkbw4qAjPILmYyubwqaEfp0ppgtg8iu1awiS\nmbVdDxai+CncoB95lLwyUq1k5rmYMEUebOpOM9A/FS0LhjuEre98+12J6WSI\n4l/LtBVUBZ6vzQAcPjPyzWqO5xAXrZjdVUwdhlZ+6RlibsAnJBee6E7ASQ97\nfUirm7Bofa4RWRLOFWcLu5slMeabztA1wtMYsnf2ZL2L8ojzS1mpHLZE2ytY\nHIHL+uV3uq+vNYs5CvsgManoi4EsnsW/TBZicmw+OfznLVQhzZCPRxF4nbs9\n5kMi7iUnzXLEseHyvWZg06625Ni7zp3gwe+ZtfmCSrVWbL0JcOO6HeP0EDW/\nUGHf7yXJH0soEPdMllBhdWmWVKxcJZNO+laE6pBSlOwmPW104p4cqLwlqo9p\nilSG3Z6b2YTRC4U4pXHMTqfL6gBq8CF6kGLygVB7Nasv0aMdLayYXycPzz0Q\nkgTId9K6XDF8/631xVr+VDV1yCLLOrp7aZYIlpT+bmnrr6GfN3rxMNNi2toK\nVze8DhLLvRnddY/pndNiA3rJNywZskeSfzAEBwBC7yOpam640B3lzRMkKy4G\nHMyI\r\n=tUgT\r\n-----END PGP SIGNATURE-----\r\n", "size": 168199, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.24": {"name": "@codemirror/view", "version": "0.19.24", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-sAWBsJK0JWka+8xgkPqCefDTUZUEcyzXixoqnHG5V81o9owL1dKAWZ+VGcJP7CvAC3L4c2BMJyQu//WMIt2kdA==", "shasum": "20d48220daf50fd301daad79cff7a52e964ff052", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.24.tgz", "fileCount": 8, "unpackedSize": 658611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp0zzCRA9TVsSAnZWagAAzkkQAIjLW2NdGEgsUo+AOKBI\nJI7zY0WqcjTOgagDtEtVRbvxucdYhKTJaqK0VN78h5lClxtjfufDxBlKzfuN\n3euYGFR3rIaiYCW9V8LgYyfZCnVZV0gE29BPKxcEOgoX/Ifg9gEO5oyv5eQy\nJkVU8M7QAk14hcbNGiN6oTGQkmyOTUFqmnq/Blz6CX8uMWOuhGgYwJ2MtSCX\ns5upYmvBsMFFnkmvfjxRgjZa8rNH439c+Cj9SQ0F+DhqiF5ydhB5a4WrNUQE\nLI3JdGyiQ5ECpdXRy95ff0EGpRkWQ/d0ASjz+5NVU8ImMnaJqQp8PPzY2sGM\n9JMVhUEj2bvoxDqNcbSOmm/7DFtYNpdaEr/JzvsXMH5bCJLvjNWEKNzF0Nr7\nZ33g+EGAUIg6Kww7rQy188xozZxhdVkN6oN3v5XYPS4B8ZFrqamnd5mi7ow+\nZlEVV840FhJHnNHmmavx95SZtEYmzyooxygM+/N0/isClH/PzWl3xWcjUnj2\ndNHUEPekPgeMOSWXXL7jYjHqsrwDNaEd87ID2ZHxWxbZbTbyOottDEqNNlOE\nWpdC6Rb6fBXRFHCjaJBwWmi3m7OU698Qnlcg/Lza/c3ShIw8P1CEasAt98HC\n1sMqZbsFdD2z8WuFM1yjGdTIvRETMBrk8/QWS6JdGYmYiRnl5XzZ6m57vlCz\nhrYE\r\n=vTXr\r\n-----END PGP SIGNATURE-----\r\n", "size": 167607, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.23": {"name": "@codemirror/view", "version": "0.19.23", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-CE6wjyo6gczS9+/PVgJuFA7+Z4IYQoqwZWeDU1epFb+oN83BGyDZqna9ICnkz0geVlKqCUcbGk6PES4Eyt8/oA==", "shasum": "a7481052dec6c13fe30255153dc905a3023a8b94", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.23.tgz", "fileCount": 8, "unpackedSize": 658454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhplgkCRA9TVsSAnZWagAAa18P/jm27rLO2MF0PE0UC/kl\ntneJ5yZpBsMHXA9jfF9GxyAx3dGyHJ9ifi0mWcphVX4wgdlKzY0JbczMrdNB\nKBC2qXXRA6I2HAoYG0eRYJ5i9xFcw72Ba2TmvVksMXG0v4JQg4gUnZ8cusbA\n4NA4+jX2qu2R6EOVJ0A6FisGaII4N2e4YwizhZQqs0zexuf6F4cEFsWoXYHm\nJhJvzP0P43VFZKt3BNbIFkUbzK3ENMAAm9lPNKSMz01enhK5SguIVfAm4zyd\nVzoncMGKS2GETsCKQClpvH/kQ8uTR+7xveGFScMNmSNuDWXfqLOp6x8s5rPj\nJOR1KgrTy3LLN2TYnzMr3XWLFkZvG2de0TpQtCrbJF6NPd7TT6e/xR2k+pYd\nI56U/fNtIcYJmRjyTwCvg0FO1Z5NlWOmPdUZKDICxUJtmKRc8qkQUnQJGtyN\nov1PGqMf7P/xiBSmflzlDt1ydh16ekf9QGUpTBcaxP1wstjBflsNqt1OVM5J\n4AiQJKE2NB7anwG6XP1crVvsIZLrdNnKGWC8Io6UhZimx6wINfwFpvxGRof2\nnHjmRQ6CzXGlbPXROVzm8TbGwy0qDtavmoTpCHg10uCx360Fr0WJdI5EL375\nAw9Ek+ccF/Wu2B9OBIrx9v6qwbJQUuGOwdVfOw79ftxGybTM30Z/y01rEd/L\nqvTB\r\n=hSFi\r\n-----END PGP SIGNATURE-----\r\n", "size": 167575, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.22": {"name": "@codemirror/view", "version": "0.19.22", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-As+zQHQvq/UuilT3kt/kzBfxwKP+6CAcLg7D+KsJbgEmFAGWBy+rIG44w7nKQzKYF48EoKnfSjMHhJOUMqvgJg==", "shasum": "e6216b5e525ddaaa8ff9be1752421d1791eae497", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.22.tgz", "fileCount": 8, "unpackedSize": 657398, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhplAKCRA9TVsSAnZWagAAD18P/2nLEeH6DZAqAH7hOMym\nRchjwh19f2MhYZGLnGPlnYkCoXrf13QFV9CiMdZThTd98isBk/iAkVCyPtZ0\ndbPV7jJfnt6nGbxZMKC6iOme1mQ6hPb4quZZcSnH2l+hRPtF2UmEVfZ97krc\n6cJlqjDX5+a8PsKiM3N8sRExRy6G8e8kHX1WdZy5Kq5N/vMSC3NITBQnodhX\nm4RKhdeMrD1AKJjZJ/CmLOfGYa0eURU4dX6n3TG7gjk9wdnrwx2v/CSRk/3B\nJawtN3MT5ZAAWtinu/rD5xUPkCShSY45KV/9eG4ptox7dXbPognmpZ/dpWmG\nwgMjbse2oriLJ64lj7tniHHZEC/mK9uoKUg2VSjNXGnj6Hg6GGto8iAbwQr3\nV982YBMaDgvwwXPKV2ekAg1iDmWER5ih5zte+FDpSWlZ7LGP+Y6snzJMW5o5\nKN2A2bIrfuJHe+LOMDYnxIyiSQ6FXfVkAC/X15ugx6D3/EE2mnL6+02YJMQI\ncYns7gn0+UDRHnG93lAlQmHCnUOKX/LbdswwNlXW4K1rsu2Q7m9lYSLwGJQL\nSEzDE7YNp86eBwwNbOppgxlXE15vm9XvV1JuYq35MCaax5bEu+PBgK85Rl3K\nksYTJ0PfcfipJxn532b9Nz7ERFe538DbBUjJ+W1bmyuVYs2pxxUTJuxOC+Wd\nMkl+\r\n=3Isn\r\n-----END PGP SIGNATURE-----\r\n", "size": 167220, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.21": {"name": "@codemirror/view", "version": "0.19.21", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-us5ENWr3ZFdXnIkylqVM3KCIPYepLyFhNmQqYMI3w2i/+ERoJGtyKPhiFuk5NpUjkONaZB3cHGBU+Z3mjqQFiw==", "shasum": "60c7bbb4bedf5580dec73c0b441645f6205f6206", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.21.tgz", "fileCount": 8, "unpackedSize": 647580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoK8oCRA9TVsSAnZWagAAGGIP/Ak8bu4jPvABWzeufCa4\nwIEeLmq7k5SHUFvI096UXTPNC7m2oNgSYJjgvOGpnZFyb091Ebx0evMIOdHe\nIZcgCpx6pUB2XeKzaBDUTQspO4fDLZM1k87Jc/bTYkoMiTxrNqH+VwpjyczK\nrfTv6YuKb9RBhFDHaArbjXzv1gBOjHemGzEP1NFa6Z5ntv1MBkQMCN0anZmk\nRgC5j4bpj/Pba8o3WRnP95a7t8zMj1f8gz1TTPWMROJWvt53fzvyhIHJnkt9\n7E8sdw7iBIWbrfKAcFwBqs2IrlYbIwcvtvm7Di0AFsf8hhy7AG5Y561gcQXx\ncDk5gHFcQWOCJ5OZyVgOTXaPzPcADf1ip88bJ6euNH7nkpRaU95qUiXpcKPd\natYLdbbX1QyMU+EtBtOv6D2AFdQTbIOMZ8NqkG7wBPbpvIX+7GVw/w+2L9Ug\nAcZMvv+NFDr9PFMHaOz+Lnwnz50rJpgtoXxx70cJdlQI3JdaKyF2qP4nBl99\nJ8KtrFcSQIaGG7hxCDcgRncderyU9bUNhG/CX7t8vxOHeu3g2GO7DqGYyKaY\nTYPl7pkk8irMFwq/eDF2O4jIirmSrIMEws5wyKa86ou8Tk7SGWbEbG2wgY7s\nOKfByHYERUXMJ7uWU05VQI6pRWr4vD6I8+AyJid7mlJQEUDJWbhI7DyPBBor\n5Y5Q\r\n=6TyG\r\n-----END PGP SIGNATURE-----\r\n", "size": 165011, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.20": {"name": "@codemirror/view", "version": "0.19.20", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-j4cI/Egdhha77pMfKQQWZmpkcF7vhe21LqdZs8hsG09OtvsPVCHUXmfB0u7nMpcX1JR8aZ3ob9g6FMr+OVtjgA==", "shasum": "77969ff93cb097e3d4c9245b32e220efb80131be", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.20.tgz", "fileCount": 8, "unpackedSize": 645873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhl7c+CRA9TVsSAnZWagAAbjwP/3ey7+cBFH/HOTzuCZU6\nqY3MYb4ai5c/gad/aESfcl9u7d9a6SSMiADk+KYylvsuags+/g3pj3sZhMFL\n8ONXG4Zs0sh8pPxj/qXVtYK0F5CQTfpm3Rl5jvQFRuedGD1y/C+wk9N+d1JY\nUrKioEnwE4S3DNUnh+7QpJO0CCsgxP+CW4YnZLA8x+ncMh/zknRLp35OeDFm\nHzuQbelHlLsrDGsT+UlbmYdljUI8nB9FB5QRVQmXXvm51mwquwev5QwSxcli\nPLeFiAU21GOij0NGBpK4FCQEW2gtcF2/t7/TJKOlqSCSJGOCMo2IWCPwX/ci\nD5mXsvY9JiYg4c2rwDOvTWu4Jk7skP1lysN6u2CDDoMfsRFasdh6Ye+bMzRz\nAM9MlP6mODlpwu3abp93L7hGgHFEHj7HdAMDRem7eRuW3+itERypOqcWtY0c\n12nH73nxpRWQqRZcQexNUGbf/F0ZI97Bxcuiy5LB4BRA8y+moazMsZ4MawkB\nJoahltZSm4MfHizYm0mRlatygCbJlGnGtx4+wq+KxK/YrAtMQLqWl5K2npbj\nutWS3rdxbdpUQP2Af+S2C9Y6AIgcW1fX4cKFxdLvdwMKTbEO5hC5jyV8SXex\nL7AeFZTxk0VIN7uxrPwdP0wygG+QgwFMtSYEHePsVBCFJh6MGJtXR8+9sN8x\noBk/\r\n=k+cS\r\n-----END PGP SIGNATURE-----\r\n", "size": 164546, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.19": {"name": "@codemirror/view", "version": "0.19.19", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-SG4idEsBwe4tKG1+aM2cyfeHarWDWRXBqX9J0R6CvG24XfpObtDNnosZZ5ktdm+j0Z1wIMe9H4C31cuyTJRvWQ==", "shasum": "533e312c4851e0d1eb982a361d9484b1b555f5bb", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.19.tgz", "fileCount": 8, "unpackedSize": 644064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQHPCRA9TVsSAnZWagAAriwP/Rql9CV72frhvfXo0U+U\nwW4PzlUuFNwsYMN/kLBvtxoH3kqkADlTNGYvKsfGstsYP4+gPQNVzAMumxUc\nxzIF0Kk7Noa4ZL87jAO/uu/+2DyvzH7FBJhwhzuOErGUhodSUHWDUZkQ7AGi\nhTLdmFvH2zWNsphrMUfNdz7iqHonG10k1mwtI2q8bqAhuDzEJ66CpLS0yCyJ\nwWFGf8iwPdnVYoE7SGFZbKwDr2iY+lQveLgWzq+wwq7YEkrb5YkZGAiJArOB\nGf1aEg2bbKMGD9b18UNy0wUVSKtLQ6VIxAbpIwKVVfO/GtH2S2Al/n1kvgXu\neEy4A6yMw6Fi2C6Ap2W9FitpqWYTmcMNAc3MIFAjg1wH+hf+cMMeFRLNch7J\nBvjUO501qakT/oMkCQnqtPDh4+n2jlTtGK3UlG2N0rWtUZk9Zyp/47OP/8e7\njYJM5G1RBebwxSPxJ7zpSBJGvOefjZ5N2n79sBcjWrO6x3I92txo8Tq6FJNd\nLxDrRRt2t4bCtQyZouATXdMSz0QTe2mAAUgv1xyYTXhTGhxl9kxFDe+GSgio\nqEF1+gAQiD+TBQ7jJAKyqF7WpNJKXlVHNHcS58dURe8o1NJi6QGdhqGJLpfS\nxk/mLVco1imW1J0cSOW68inl99PBfxbs5OooP8viEpSPUdRSItFVYCjI+ctZ\nTOeO\r\n=2e7r\r\n-----END PGP SIGNATURE-----\r\n", "size": 164140, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.18": {"name": "@codemirror/view", "version": "0.19.18", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-OxYc+6PvppfFVKcheLZtqRxzYoiC9dGzKZfCBzx63en/HsKuM2ZBOHHKH57ZtGNcI2ctW0j5Wr2yNm9QAdgGsA==", "shasum": "8189dacd3b07cb728d425a9f43633401300297c0", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.18.tgz", "fileCount": 8, "unpackedSize": 643678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk4v7CRA9TVsSAnZWagAAX5MP/1Zg3nCQuVHx9r7EtMOY\ngfuW2gDFcfpMhcVSrkd9n12oi1P/FFi2GYs818DiC7+US/r715zNZgnuwpIm\nnh0ZWtj+hyKpDJG406phKTRpY6xCHJJkIyHcFEDXxJWwerYMS6cCL3jb3D8I\niPkdNLg4dVCIK5DxyKZ6HXGhzIEWvMzdRHJ6ti13zbFWqjKYPoa8Hmit8PMo\nz7jEXF6lZ8+j41zue1yrDG+KP7J4FG2zds7sj6O0vC5PeHUchQOuNU5/rOIA\n62yZ63lHFVWgUKMVoBrkTisiEFtPAVjHCX5Uipmy/t9LQoFv74vxl6GAE/Ov\n3Ongv60U3mb4tDSQMGLivsaXUlGzf19KELdH5LqOzbOEgF8WFMzNB2RXOC8G\nvoSR6wx9E0bU2sXVV6oJx4/bSInz4SGKSv3JVL/lwm5uGskgaATapsUzBvsO\n6Rjy+Snr/vWTKgiftsBQRxqhuHyP8CQ0BraNpHQGtoexthRHDaF0lEBnUjFj\ntgIS4Je8wFss99Cfle7myQ6zBzp/+WztDlsE5ru6D3/8hm55fuSvQnvlAb0W\ncdWHGRYZwcujbr0SI+c7RDI8gZBPRj+HvGrR2weCo4/vfK+a7M51z0fm9iCL\nl2qQxBDE4BsDn072g3C0aEYsNe4kqI1FXJZDAuPgD9cWob0zLZTMifGH3QXY\nqzP3\r\n=vbdq\r\n-----END PGP SIGNATURE-----\r\n", "size": 164067, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.17": {"name": "@codemirror/view", "version": "0.19.17", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-w24FLwTrgloRSKtjOiKPUDVPMhFJmuYzC3KpqcpuLLiNRCLzmt/f2QyN3AB/LRHZs6HDPGuQcmwVAftc2ZjbDg==", "shasum": "2d04c0a541a6804f67fae60c1d42f4ad5ae8f6d4", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.17.tgz", "fileCount": 8, "unpackedSize": 643494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk34vCRA9TVsSAnZWagAAH54P/0le0JpOrmwmOvmV4HQK\nQw5kTF+Y8OwEjSwESdnQAYY40nLjshKD3sEul+9UPfv4FfpBiIbETV2BXRZ8\nyXXnOqUIENEp3m+XLV1vDss6J5z0gAOFgp9hq+Ap5+IZQ0VxmOHSO4+zZ1yf\nKt/aUx0XMtotaRz62r1vsfPsIT9Du0oXEfB1EwzbwHfRFaDrxK0qEsaYTO0c\nj7hLhdus4URDqjkDT1TXpTS6/GXz1AV797NUlZPkf1Q6NVYujbEm1x+Rv+rS\nx7eB/3fgsITQ1AfjSnqlB4BltB0Iy3xDFPAncGLa91EpWKcp5iLZRLjZET35\nmMg8BLwaS6FM7PkKusPYcQOBczpzuS6ff4p4kBLiUkpz7K5Kd/D5yqXteHcX\nskd1b3J/8TXgflEgnG/6WAxa6UNZaOMTWJfMug80ufBDI9yNrc6d0CJHRyyz\nJjSysZJUF1Z8Vzu8ftanXfCvh/ohL+oKWM8RRi115PzG6Gk4+oMBgY4FhRQt\nCqmn+wU/8hiRjwUWjiXaI21vlSYYWv2JVXNjVFvG5EoVz4IMz8pg4WEySj8I\nJ4/hdXVD6jX8WxRjC6DZ9Z1adf7U8dwoOKMgJeNrilOEuvj3JQKU5PoXI5n7\ntzHygB98Lc6zIV9jDRBIO51yV0by5Mnv19yR8x1mvjGowLc7CrwbirSfPZpH\n6/Y0\r\n=JL77\r\n-----END PGP SIGNATURE-----\r\n", "size": 163999, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.16": {"name": "@codemirror/view", "version": "0.19.16", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-VumZoAQRX9BhHU0cD4++izO4mfCH36J61xz9MxtfOKEggzuKlyuGDrdix67FhoDfYiDRvqv9lt1J5YZ/zdU2WA==", "shasum": "647d6dd4febdd28cfec8414b7b1dcddc6b40a986", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.16.tgz", "fileCount": 8, "unpackedSize": 643190, "size": 163895, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.15": {"name": "@codemirror/view", "version": "0.19.15", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-aLWjINi9ReVgq2lwHpf8EzzbTO8KTufcYF7o74SL/Z1yPPayxEEGl/4NyIX6cbAxJaQ+hyN60kJ+XAtz3Ag0Kw==", "shasum": "9580b8b69d957d073667dc3f9dd5989028cbbced", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.15.tgz", "fileCount": 8, "unpackedSize": 642772, "size": 163799, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.14": {"name": "@codemirror/view", "version": "0.19.14", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "25af82dbde8102f792002f38d7e085962fb5c887", "size": 163888, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.14.tgz", "integrity": "sha512-NCaR406AaqVfYBUcM/mhqHOiQArlBbXrxS7ORTjZb2WtBOQQ5fiTbd7xro5hjOWCTgYP41CzHPdBGoozJhMn4Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.13": {"name": "@codemirror/view", "version": "0.19.13", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "228f3b0aa2326aa460bd91ee67f9bd323f078179", "size": 163949, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.13.tgz", "integrity": "sha512-zKg34q5KqbonZn6sHABI3TmeZ6K8/ojzpyR7cEfUe6aPonH455fauc2h9c1wRESuQfCq1uxcs9+QwMaCwWljTw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.12": {"name": "@codemirror/view", "version": "0.19.12", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "ee9a28d9ee6ee0f5d0a4871f99c45ff8d8b8fedd", "size": 163867, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.12.tgz", "integrity": "sha512-nvgqUaIGaRfCwpa/BI83SsOOenEoxBp6PlXBdw+jSfxgYGAYIdB3kuPzExPixZemvu7+ZoJRO2iEjCKigLIOaA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.11": {"name": "@codemirror/view", "version": "0.19.11", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "da6b0251a1eab99906c5c4e1d0a76f019734f73e", "size": 163580, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.11.tgz", "integrity": "sha512-j3eQt/+1VccW4zDQmKeOJKNjtmSgE+ywj/VbO/wrNJKivhFZd8W9I/bOXImno8Od/anb31Q9tGBIJKNtljrQXA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.10": {"name": "@codemirror/view", "version": "0.19.10", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "0fc53a352b470d776409792e47a920c565a48331", "size": 162846, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.10.tgz", "integrity": "sha512-wvYhw6P0eTqH/XvAkWYJdJq8cQ8loiRyHSaZQDZOMwAG9AQcJJcMiFLPh2nu0kdjmNQQqbkdidMJF2StWhm4RA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.9": {"name": "@codemirror/view", "version": "0.19.9", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "cbdcbc398818e7c9603c81e4b2a2868ec0a1f954", "size": 158798, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.9.tgz", "integrity": "sha512-d2Z2rFl53yTwvhsRYtS97xT7ce1f/Q/NNE2uDg/Be1MVjP2v1OlwUTyTosBmxiCt4oUvMklfa+EdPGOATVJhtg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.8": {"name": "@codemirror/view", "version": "0.19.8", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "2de5965419582712ca1f268cd549148a29e32fb0", "size": 158191, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.8.tgz", "integrity": "sha512-bhjLn1fYYpd49sLDuZ+oqFBQQh04eyskHUZQJL/7ZBl+RZ5L973yX/cybZfRG/O38NQASTv2Uug5xlPjcFUJWQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.7": {"name": "@codemirror/view", "version": "0.19.7", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "af2c5d9246bb44cac24dcd1a60e7b45dfe06f209", "size": 157654, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.7.tgz", "integrity": "sha512-m9AKO8gec/QnyxR1uq182It0WwauTIaHkdjqtdlKx1IRgknH44SKIJIxwxZt4Y6VDhhivcEbugjoadNy0zR3wQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.6": {"name": "@codemirror/view", "version": "0.19.6", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "49a3ec61633009f7d32db0c8f1f0f093857e545a", "size": 157472, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.6.tgz", "integrity": "sha512-f31wmIbP621r2EYaxU2rj3pLpHPgtPdnRL9D+H9EoEz3ow+RuQKBGjhsHYCu01pLoFfWOOx9oP63o67ZMIA6dQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.5": {"name": "@codemirror/view", "version": "0.19.5", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "affc098f5027be8bd702c0a107e00bf987888c19", "size": 157485, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.5.tgz", "integrity": "sha512-I0XkUDW+CDt63r14+dqyZPgySPdPvbx8rDMB2WAHk4yml1JuDbEhXS3Bx0Qqpq+iDy/fAZylYhCycOvukiFESA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/view", "version": "0.19.4", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "ec0aa184cdfcada36609f4f143346496bf3654c9", "size": 156654, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.4.tgz", "integrity": "sha512-yfJ6zdxQOnNIkZqrAyPPdU1/qeucjp5NzvfZfrI1X5niWvwd8re/o9MUeG+nvOmtac+uKmXYUU9dRZUFCAOcaQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.3": {"name": "@codemirror/view", "version": "0.19.3", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "e2f9d2baffc5916f06bc076bbf3b206546d89107", "size": 156571, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.3.tgz", "integrity": "sha512-z/i5jPtLHV6hrwXupS+ydiqWX4MOg4jBMseEVROvz3/gxCvxoDXhJRCyWQ7lQMYn6mAsuA/S9bANU+b/ZtbwXg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/view", "version": "0.19.2", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "6cc953665eeba329d44fefd48c4dfb9c5a4d2733", "size": 156386, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.2.tgz", "integrity": "sha512-rYFOpwT5EYrxHwNuLdaAKh62nwu1KTcxSwdAZvUFi/xF/xGKJwfwQquRYA9H0x3YUrj+C8aAuiB1yMIR43Q5Cw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/view", "version": "0.19.1", "dependencies": {"@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "07d1bf73c29da3ee4b11655faf7b878817ffe8b9", "size": 155708, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.1.tgz", "integrity": "sha512-CxSM5Q9hK89CSX6mpT1ZPDq3kjzE1Ynx02l12M/MVTLvExge/8Ly8L+NYbKHpsR/uRtNc4E9V+dep+JXYOJYqQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.19": {"name": "@codemirror/view", "version": "0.18.19", "dependencies": {"@codemirror/rangeset": "^0.18.2", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.1", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "9d4f943a9e79ae00f6ad2ace0d6c759624faa86f", "size": 155328, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.19.tgz", "integrity": "sha512-TmazVl8H3L+aYwlNb8xk6qADRb8KiYOO047pz51R4mGCg4Ja2siSjXktZgUvklsyWbUY7h9q+oAf4piH+mQZTw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.18": {"name": "@codemirror/view", "version": "0.18.18", "dependencies": {"@codemirror/rangeset": "^0.18.2", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.1", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "b9508c03a3f5e5166be14f1d30ea30a6fb870748", "size": 155104, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.18.tgz", "integrity": "sha512-pbgpL9WCAFlpQM5m2FxSpkmchQ/MVHQqUFa1rcXjzQDlmGz19WbBEeywdgmtZrF0yc9lBX6sE187Xzl3I8YRIA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.17": {"name": "@codemirror/view", "version": "0.18.17", "dependencies": {"@codemirror/rangeset": "^0.18.2", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "16723d36a2079168782ea1d993b11ea6fcc397b5", "size": 154878, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.17.tgz", "integrity": "sha512-AneqrYFgQJFZY5CdIRvllaLWL7r966JZK64d05PrScYhhRT6y5iiq0VBO9nxX5Y2gbTXBdO1/eZdtJlhwa6hww=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.16": {"name": "@codemirror/view", "version": "0.18.16", "dependencies": {"@codemirror/rangeset": "^0.18.2", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "8cfb8751bd0224df1fa757abebd2ad2113ee1745", "size": 154099, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.16.tgz", "integrity": "sha512-5sFdVXV4Gdw8/JfyjzIAVL+IrLIGPoXrqs+fZmu3rPMQEnw7u/EkG2aKxnwf/IdDPLLVK6FDRWoddaZ25VIjdQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.15": {"name": "@codemirror/view", "version": "0.18.15", "dependencies": {"@codemirror/rangeset": "^0.18.2", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "07e68a7b1050aba8a1754aff9309abaeb7ec52b4", "size": 153804, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.15.tgz", "integrity": "sha512-xb95w3fzyw+rNp7dJpEL+aqn10YqeFNcG2sUk4KdjdTu3RWC0+eAJ11uXaxLi1SqKisJ6klxHA+fRZbCjSiyvA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.14": {"name": "@codemirror/view", "version": "0.18.14", "dependencies": {"@codemirror/rangeset": "^0.18.2", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "509f2a487e1fa62b6b147e3a9db5aab6ce228495", "size": 153613, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.14.tgz", "integrity": "sha512-b83favt2XtbV343wzmY4e5lTwYBdpklFZ31jJ/SDGixx6Yj5xTgZd6NU6jkK/XOT40D1rcRoKcQfFbbpjLG1SA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.13": {"name": "@codemirror/view", "version": "0.18.13", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "34c268895f2b1b3b9907abe9ecfee0136b2a91fe", "size": 152470, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.13.tgz", "integrity": "sha512-3aFEh9UB3JyYXG0GTneC8q6KQ87obftwHtis8FI/7NeiLfT+miuVj1By1IteRDpTQQ+7FbFnjDw4WFYhEHBcaw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.12": {"name": "@codemirror/view", "version": "0.18.12", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "28d6fdefd7362481bff3b05ab893c10710311e13", "size": 151997, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.12.tgz", "integrity": "sha512-8PLqxl136aRiNO9dIKuB4CIMP6pgHAMXIbM0HxYMdFiLKaAH+nnvVFJVKCY3DUqaEWBB9R+OvUPVa0Rx/LpqLw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.11": {"name": "@codemirror/view", "version": "0.18.11", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "3a9655758f0743cc57d718723b57ea49a72cdfa7", "size": 151358, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.11.tgz", "integrity": "sha512-hNWTEGTpfFk1tjUnq4VjREe77rQQiS2nnhK9CDvU2M44g7wtkCFLB8ymvNp+Swo21hfUix33o2MKBeb830961g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.10": {"name": "@codemirror/view", "version": "0.18.10", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"shasum": "2b4e59fe09ef5e62402cbe2d8f4f37448e1d4f78", "size": 150665, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.10.tgz", "integrity": "sha512-TEeR9YdEr0YwL8BTrd4ILSupv9dnU0yphf8cfG+I/EN92EwtMrJG2xaJ+b6/jS6XuPgCm7sI0ad/ml9RCmVvGQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.9": {"name": "@codemirror/view", "version": "0.18.9", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "bc2e4a30556888a07490df293171880275e0cdc3", "size": 150417, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.9.tgz", "integrity": "sha512-u/sqtt1Exbcpwzj/K37++eToT/MehS6LFE3rfYMNsoxmjT75z2oFM2Q9PLymu344ZxgjgUNWh5cWmVxdjFdTwA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.8": {"name": "@codemirror/view", "version": "0.18.8", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "e8e6b26adf427ce65356b9f1020876d7947d7953", "size": 148775, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.8.tgz", "integrity": "sha512-vzP8oUBiLMbl5OCWUMGQdYtonk0tt9eUzi/xEDpYmo8Ao48/49fxPQUqBpUwy5Rcce9kUnkTi6r44iJKsEHpmg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.7": {"name": "@codemirror/view", "version": "0.18.7", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "e7c839734240e4d3c70a20b56ddf39d93a9348a6", "size": 149484, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.7.tgz", "integrity": "sha512-klUW15Z74JXA9Njli1bwNyLQroC3OWHYKcAaHO1WUChhxVVx6eC1EbSlkuXpIqYmXFuo3f4iFGcwM2RkuLJ4Iw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.6": {"name": "@codemirror/view", "version": "0.18.6", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "3598e72658e37b30e3260e4e623a81599b67a9a0", "size": 149640, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.6.tgz", "integrity": "sha512-j0TtJbV+41g/0eGH7Pgx9wtO7Y3Rg0s9shLFGvUtJ4jMIimkCelQsEBtUmfEbNxAVXOsN+CbmsKg8M9p0ISeCA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.5": {"name": "@codemirror/view", "version": "0.18.5", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "6582c44cc278303e208ec888a92a5de582403e4c", "size": 149585, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.5.tgz", "integrity": "sha512-tKP093ICnypcpDJ3/83/9UxA5rmwIPrtcb+T4oXwTH0aODzmPTN2nTcS7Jble1QZdTtd7eBxE5UEZIKU7i5RWA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.4": {"name": "@codemirror/view", "version": "0.18.4", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "350e3c941e4d41f7ac7a51fde5f5bf87d1e121ee", "size": 148124, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.4.tgz", "integrity": "sha512-apLvhUtjMVDDn3yV3fkKSNUhSLP8zTilwOZZK73Gi1cwqraSDW4iFOw4heHYZsaD7N3hUbxgw8k0aZuTH4A46g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.3": {"name": "@codemirror/view", "version": "0.18.3", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "31ffcd0a073124b95feac47d2a3a03bfb3546fca", "size": 146827, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.3.tgz", "integrity": "sha512-9scPYgDoUFRjDKjClCIxPBMZuoiATn01gKGm/OqSODUcsWQ37LS9qs/gJNdrIn8gQNlzI9wNRyBck7ycZo4Rng=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/view", "version": "0.18.2", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "f1e2fd653929b10b60de5de63cd48433c28d236a", "size": 146086, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.2.tgz", "integrity": "sha512-q/VbCDxHNM7XyCEEDV6Uh5gGfa8X/18NmXirmCKNhpWYo5LQHhxkVAUcd5/ANAYCe706YJPglO7VgbfmKMIopw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/view", "version": "0.18.1", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "5fbc97acd28997ed2aafbe6e8d48d9446b4bf5ff", "size": 240901, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.1.tgz", "integrity": "sha512-chyy+oEnywKMUFDMafVAMcrV+DkjJT3l6pSfN1cvM2LBM/eY54gekv/aXtmsBFRSnd+u09mhjb/kGB+EdNHIjg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/view", "version": "0.18.0", "dependencies": {"@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "3bbb95d71fd526460acb0de724846851d0f7137c", "size": 240568, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.18.0.tgz", "integrity": "sha512-+ll9SL8dIAvaI4OMv4wZWRrW6AGUY45Al5L88KIp+bd1jFKYO1cu21UXAG25EvqYInZ4ctc6el0vYRP9A+VOrw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.13": {"name": "@codemirror/view", "version": "0.17.13", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "e805ccaa31c22fcff87f0a1c88c143ff17960458", "size": 241995, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.13.tgz", "integrity": "sha512-TWmQF2OMzWYY51gvzcq0ECvW1EyhEVpy//dmu1MDvJrv8nDZWGY/a414fUCc+v6DKNpujg7IIpP9sD2izYJFpg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.12": {"name": "@codemirror/view", "version": "0.17.12", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "3720323d32ad359372621f0b16f8e25a3d17a933", "size": 241596, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.12.tgz", "integrity": "sha512-sHA57N2yfFQTgoOfF5OneHPAltupcSH4ps4cD3fj91F2EhWLqfzryImom8RxhJrwiE+3Kv3MiRtOXPthHIbswg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.11": {"name": "@codemirror/view", "version": "0.17.11", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "d9fad5aa405769f97621bb77fab7abc2edb55f2d", "size": 241188, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.11.tgz", "integrity": "sha512-pl5fOiBLifExuqAuqhv/yOZvDODQrO26HEtljv4DJqx2Em5kKjzWVHhQymq0dh+nPY+qA4vZCGcT5r/ySNR52Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.10": {"name": "@codemirror/view", "version": "0.17.10", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "d3bf4b2435b68c7ede784583f2c240f58ac3d5c4", "size": 240809, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.10.tgz", "integrity": "sha512-Ke3mgfw1qnLekFDFYJsz8ewKa0dBnnUrlYSZd+1X1XkMaqmKZj6enbcvpW0/GnxOvGJpa+HrWCMSrRvCrlaqjA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.9": {"name": "@codemirror/view", "version": "0.17.9", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "4a934cd0347e3a6dbabb991f6fd9edef7b69e5c6", "size": 240505, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.9.tgz", "integrity": "sha512-/7Y0qY+SsbzXJoRepbaHQS9SsI7Oyr/96x55BhU9L7ZxPEUwfN1dcCtJGbORn/TnBryWJkMMULnbyy9UHxPA9A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.8": {"name": "@codemirror/view", "version": "0.17.8", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "7888e2110812102e3e51968451fc7e8d67a2b53f", "size": 240514, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.8.tgz", "integrity": "sha512-zX685PnZyFT6JCoMJUYj67sMGO3dk0EANWVSOFc2FSFHpZDsOxKohehnZ4EO+uIXF+nb9Q4jdZFdaaVg1aMOcw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.7": {"name": "@codemirror/view", "version": "0.17.7", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "d2913f119e3c9eba669d54704e219ddd9ab942e8", "size": 240006, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.7.tgz", "integrity": "sha512-OK+eNbcNXdlam7epFG5p5caH+3OoOlcRHy5S954VDUjOlUEMFUtwWjHa9WqaKEGvFwIgsG3AQQVstRQtyexRuQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.6": {"name": "@codemirror/view", "version": "0.17.6", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "fa804c8296f6cb9e2c140fc4896c3cd2a271ebd5", "size": 842728, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.6.tgz", "integrity": "sha512-/7AKzFSs9PF8ap/Wh5eAmUUCgoiFVfardpC4fbASF6iTitduHFhEH3PHZQ5R52nVeHtDVibVFMlYw3SxVmHeUg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.5": {"name": "@codemirror/view", "version": "0.17.5", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "c15bfb1e593f952bfc0a44866bc681c98ef82fcd", "size": 235788, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.5.tgz", "integrity": "sha512-AxoGjpiWOJA74FKXkQ8etaZ8PUrsc4qdzEkX1xfV8cIo8zLKFFHfhiUhzp4pYiMzKqw1HmH7cw/iQqinMYJ6Pg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.4": {"name": "@codemirror/view", "version": "0.17.4", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "00b6ce89c5bad44c7e627957270701b140a18772", "size": 235332, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.4.tgz", "integrity": "sha512-JAa0LxFKLij0P5V0wkHcFhVwX4MtoH/naE2fpTwkK7OjmgXNQc7dY9VlnHLhsjHSdOVc5RdJfLETtfD4FDb0kA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.3": {"name": "@codemirror/view", "version": "0.17.3", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "feb4482db91e1e612d334dfbf690de7c2a0c7732", "size": 230899, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.3.tgz", "integrity": "sha512-Mrp+BLIZWCi08q+ewGa2YmYD2RcWr1htu91xDd+JSjTfKsQQ0RZq2q72h05KmhZNSS/+rJepWWdLghCa2urRCQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.2": {"name": "@codemirror/view", "version": "0.17.2", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "74d4ca22abb6cd52705d8f0c55a4950f3ee7ba1d", "size": 168842, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.2.tgz", "integrity": "sha512-A5d62IhzcXdCbhbfypEzbchf1z+fCngruQFQrr94DNxAWGZw8F1LVHFFZ2UzG/Gr4z1qjTKCUHuBNiVuGC4PvQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/view", "version": "0.17.1", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "dffbaed3774827fddecb53aa17f14d7288baa918", "size": 167618, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.1.tgz", "integrity": "sha512-rkJylaqZYRkgvTXFswDwRSmqrbEpQWMazy3vLZJz6Nb9PK7AKV4doHvjpG+SSlR9lufmCOA9vBTWrIBdYdkoNQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/view", "version": "0.17.0", "dependencies": {"@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "style-mod": "^3.2.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "e7f8554986085a94cfb2e2b88243b1011b2334ce", "size": 167250, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.17.0.tgz", "integrity": "sha512-DE5Eb5nRc8QxWzNrtxhHisEA8cpzy87FUKb28jn1yLt69FpK+ZSfZSyub1xrzGPLN4RPM6H4jF2Ku2X5dyrOJQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.33": {"name": "@codemirror/view", "version": "0.19.33", "dependencies": {"@codemirror/rangeset": "^0.19.4", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-hiIJ+E0gg9X7jYQEIXpS81PwDpYedYPFkBRl3Hooypz4zHiZxvteKb6WtxM+sHkPYWdGhVntWx1JeISpvYgdwQ==", "shasum": "5a0f9e0b661f1dee4ba004856a30237480de50df", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.33.tgz", "fileCount": 8, "unpackedSize": 680599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhu2HKCRA9TVsSAnZWagAA3ZoP/jd8uKTInr6Araqpokj0\nPqSy+Vv6Qpf6Nbvw0NG28cpeh0S8CwMPMh2FdJUO4Ha+ISlyfRdnbsw6XDNf\ni4kuC70c6CQiMSv7IqzVppUJ0n1vc5pGmK5643J+VHZVm+ZmeCL+798ekZn8\nKah1yK93SM8UEebsIu7d7iw4RGrSu9+Tb5XnY2qIqjo1V3Ru8Zd9y9SuI2i3\nn1mmfJWn8sVL9RZUC/17+EeZRhIgzTXweu4JqWMetvMAeZhZT7ICqohJl/jh\nUEA5df4o1JNdR3jNtHPul4P3DTzLyaIDQYvfx4Pk7DQ+jvpldv+SOlH9DftG\naNsDbrz1SFkzJmdtpkiPLHIOVYpcx2E++6UQCjCDqdQU7ArMYm+zAnDlIL3i\nP6pIn//S11ET2Ypk4VIcOJxTVOh4szOgKWeL6F+36pMSOoH9kmru4AfJ/Ngu\nphFek56X9Y6N94f0RQB0eJ+esrE/3av5gdi1vlHnIDfo7jkV1069tOaD7Vf7\nLP/rVJZaTRwnfaEuvz5qhXz+NM8Q8w70ETnO1T8dxoesBYOYwjQLA39C49Kn\nlAo7PBREkigBUV43n7+9BQmH2pDjowqrvihIFQWHXkb7NHs7TSlFfXcCsys6\nueZL8yUP/6o1pwBnhoQ6xcxDyCU/3KTW5r/FBaGVAbe3OVHy8ac3cqQPyJoV\nr62Z\r\n=Ov0o\r\n-----END PGP SIGNATURE-----\r\n", "size": 172739, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.34": {"name": "@codemirror/view", "version": "0.19.34", "dependencies": {"@codemirror/rangeset": "^0.19.4", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-X62xoOXVOMugVif/Wj6aVKUApZO1dc/gRPEKt6vearaq4Wke4uMnsR3raEqGHw5w6X0SSuN2xpAer+Pgx7qh3A==", "shasum": "d253fa9db4c405bc43495a8d20829c81791d4ad6", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.34.tgz", "fileCount": 8, "unpackedSize": 680842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvGbpCRA9TVsSAnZWagAA+9UP/Rix1pzaeGFbPb8JoXIZ\neMQruZXa6RcqpU+5NqTGiViN/GU2iKyCv+RrQ3xLGgNXHxEaR3UZ9Kh8mIbq\nIO5fkMQpaJfZCQ+aAUNY1uDsU7HUR2vsl/ocEga/WZgbV/ujf7XLaA/C9v7k\nOi+y1WF2FnOdyLjbFhS9ZatADrAW1YvUeFPFMycq3g7vs3HDNagtwIBiUPQ+\n5g2GDSQk0vOp6CHIBo3lGU/+BozNLzAvpmWPbDd1mPZb7Qc6JaEBkMEFmpae\n1WSGRo7K61qM28cAjjFDxcW2YNRbuXMILeIQZJLUVepKSOU+NKh+oAegpn8p\nb4VeLF9IKvG2MsQ7Vir0K+U2Evw3u1O5YJwa0NR+N9v50lif4vCKlknxCULA\nPxnX/2rjRUqsaIY3YpCwtQ1Sslb88auT9P74oVyvbaAj96fViIjM40edC5vu\ngkenyKNmUnQC43bm6oGpl3H5qB6ndRc9xGhnIGuMvlHX8JZtnNWZIm+5AWor\npiUn4rwHrdyFkRd5MQ7iM712OhKMIzTzf6+WxCWoijzwmHBqeMvWaUQqbTly\nW9YzxDO8T3ChiBNyZazmOas6qdejUrrQIdwY3T5JKOo++PTNSYRgO/uiwYEc\n8kv5pgcaMCSGXevc7iJrVDOPV8jkLsN1LzpPkA5G132vvFSJXvSBXRL0XMdO\n9Seh\r\n=zMEU\r\n-----END PGP SIGNATURE-----\r\n", "size": 172783, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.35": {"name": "@codemirror/view", "version": "0.19.35", "dependencies": {"@codemirror/rangeset": "^0.19.4", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-PSBtbClHKvF9DHfYh0QH7puzjF268MrP8sK/I31Q0AgqBCiJvpGIXrbGTZJBDZ93F7tvsvj2BKd4jQuYlJPSYw==", "shasum": "1fd755c112e067a8309c5d9647d11cec5170d3dc", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.35.tgz", "fileCount": 8, "unpackedSize": 682237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwDf5CRA9TVsSAnZWagAAuz0P/RdHr6mdQvbXPsHPBHj7\nnsQ2n/9IwQKDPqPp5/H2RXD8+pXPnwjsZ38siDz+jY5P8WtV4yOcXURN3A+2\nK5EFmvU9Yk56PhTNcu6lWn23NVnWp1VH1lsVFaccN8P7pCuASC9U0ZLuWOQF\nuQLecnMR5SQCUt4Gfi5odm3ULGN+0OOV4iG4ugANyQFUFJBmkfhH6ce/WK4z\nN9y6s6tv58phlQ9nh+sDvxbvqVRDA+SbFncYwGZNcJu+mJVr8/xbR10WBxjx\nY+ipuf2wGzjYcrPaEWLLXK8hNLFbWgZGqsdRPJn6NaPxdkJX2w4sSPb3W2hj\niOhHmyGgxibUIgi5x6EgQ4N6CJzFlgaseJ4ZV17q24yC0bDlgORj3PKH7Xki\npv2HYvjVoackHTjY2ZxlfqgiUKWCSrjpIlaNaQvQAHJYhp9B9D6H84YQumdF\n7OhagkwZzFBlDELDaSfEvKPUain6wJeRkSxT9h88Dd6q20Kol/F1P+RNv5pK\nFb0IC0pBh6WbNx55iN6RagmF+MTxfRgfHQOiqwq/IupM8w3gw3hjuqFxAYN7\nKc8/6031PzJG6ZDar8LoSZNBoY6yCSVEeCauhCqTDLwvHOgkRQjZfL1HTHHd\nmsf6IH+Mlz5zI9HTA5txAy7OeBg+eKXmxTwlB4RRvXY1tB1KBlSzsZ9qcBNM\nUfeD\r\n=8+z9\r\n-----END PGP SIGNATURE-----\r\n", "size": 173152}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.36": {"name": "@codemirror/view", "version": "0.19.36", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-GvLLhQAdVyR5BxFprxDKpU2C8eQXdGwRvrtvyGFHH4h3ikYO5JH6PbhU+7l7hFJzI9QzmYn/0/eNVw+2Y537pA==", "shasum": "4daeb1a19eee8b393d439ab764634d9f8157471c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.36.tgz", "fileCount": 8, "unpackedSize": 684322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwtshCRA9TVsSAnZWagAAffgP+wWpJSH3E8gg9bghbCfM\nnQojft7HcAZsJVmbySHPgmx6JAYRS8x62NydCEe5+hKzPNq0EPMXg0ABgVXT\nmYEmmMW3/aJIj6dFiZOn7jZPpAZ8WrVQ5+sRJhq/Avp2UClaarVV9ltSL8T7\ngWALxBiJiPD58hZQgaw6sXBC2577syfH8VF82tK1pLTOZNb6LsGZuZOekXT4\n1xVfWM1q1N+B3TtgXSTgV6pHuOhCaYwjbWmFEmVbFNmrG2BDQCnQiF21lSwN\niMAQdslJQZ+sWNkHt31yuAQx7E7VGE8MGn/cu+r7lzvLn8Logfm0JEqgRLWj\nBCYXp3uCmnI2KiE5Ec0r170VLl9oHBfk04WYDLWLyt9LdRLHdxrkMag9GMed\ngQHg/d7F5M1i1tzbn8GLSFbv8romiSmBjxShhr9FoA/fdfsejyTuIaJO0pJ2\nnTXlo5RsUSY+Eq+/HVGL/aixN4LKpkMvh1VjsfUQlz3HpRM5XSUZzmiNwwG9\n4AEyvWwfbF4HFJTiqVQaVsSEgiCvAeWSmwt3slBNdvJ9Pu64NTZaPJ9nGuF0\ngfifJVPtrWpQL40d3Ppo4ULJucEcCplJwv665ydJkWH05IHQm/99aLZ8yp1Y\nCdYc5pOzC3sixi6Ne0lKbUkZFZRsZCg66ZS+S0Qlyv5z/oRN/5xiAMXrJoB7\nllBj\r\n=1Znk\r\n-----END PGP SIGNATURE-----\r\n", "size": 173631}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.37": {"name": "@codemirror/view", "version": "0.19.37", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-SLuLx9p0O1ZHXLehvl5MwSvUrQRcsNGemzTgJ0zRajmc3BBsNigI1PXxdo7tvBhO5DcAzRRBXoke9DZFUR6Qqg==", "shasum": "36fe17c774525c775af57e7dde2867b3b7cb400f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.37.tgz", "fileCount": 8, "unpackedSize": 684461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhw5T5CRA9TVsSAnZWagAAaw4P/jzGpEdputAT+qT+WAFS\nyj4/Ii+b6UQw0F2i5LKNRIzGnIQdSGUqiqDvtZbYBXVuUbERD3oKswILcKqx\nBdrxDtbJ4WmwLkZtoh9/prPl39bJfftBYuzlcncywaAKvT8HwlB4TYVbpLqq\nejq34kxxF1maNDPXXul77174HKxstpPiGVEoEZwHKxvx5U4lJ+k8byONzLUj\nB6WS0N0SzuDRu1BL7/Kh38RsbpsbJjWTSJbpJK7d/LBqU/tAnR5IUZkG/+DL\njnTnJw2Th8A7bQ8VbF3uCVcDCDNQbJEe7PKWyHvU0lBwUsJ8YGUrv7J0VMp6\nxM58x4l7aZJ5COHecx8dNtnIujMGIglDxO8agFMK9z6sGM6ZEMi3yS3zQ4k0\nlCNH6mCdD6DtNL+NjAtAmp5do3E3B26pQaC3XCeMX7kFwMD5rLRB48pE1VYR\nBX43TQWct9d7nmoJiOEeBWgBkHEzzX9ifHrzSX0uQdjSuYsxx9oqHa0tZzZH\nq9y96Cwj6Damcyw3T/xjOtm0lxLjDzhITxJ+WnpRS8bAtj2+CFST5celVjiB\nNt3Z13u5FgP8NY+H4KhiwKLFeSRZIwm0w3Cvq/yyNslj1auaQZYYS08IcvM0\np+OoqpEFGDvWlX+Th6EEJ5Own7tP3MAGMFTdRFlQU17Bc+emRa8LM8mpBcfL\nZj+h\r\n=IVh5\r\n-----END PGP SIGNATURE-----\r\n", "size": 173655}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.38": {"name": "@codemirror/view", "version": "0.19.38", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-efgWPKpKxFd5s62qxvAxpb1v1b9BB0C+9EPZZ8R06DzBr9zv7a8WCtHAoaMK2kL2brdPuqEGjqC1KyRIihbNDw==", "shasum": "618e67cca60763ac2cac0da4dc2604e60ab306fa", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.38.tgz", "fileCount": 8, "unpackedSize": 685187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1cwiCRA9TVsSAnZWagAARLQP/iUh7hiLGmZKTjp17OHV\nFRulEr4BKVnbt1qto4rPjoRBTJSyzyrSKBIV2l+SEYHkcLT7HCzL/0LNq/Qc\nqjQu7ReaOvY3D2HjmHx2azRJJq8avWoQZOx1GnYIpY8RvQlKX3ooY6dZClKz\njwWjA9ZopLItDWhcCX5kvXeBUjPmSqzDmzSfkv4u1DN9E6Wwrtr6QzrL7XEk\nppt0lkEkF0lBVgry3GM0d9SIi2gUP98pZ3ash2dJODDyM5ews/vFiCTRuf/l\njjVMmOQTfW0Lc84OR2DOHbrDWcr3mns+LAHie8XD+M85eFFhDArbiIPy7lBk\n201csDd5SzldqxqR4ufmMTuLwA0r8gAC31h5020tgO99jopZ0rXO6Q76f1Lv\nCFIS3W12TRfAh/TqiuyZ78vVOcpbS/208smjyftT7KaSYK8sZ/9+zpO5bo3Z\n3LNq4RknURTH7TL7RFj5SVHKfVYbcFLWuwMocEVoWSPLIKJA2EBQCB1P2rHA\nraNkxaBWI62F9WDySNJ9QWUMe9H77FZUloS3Wq+7989xgk5xRH349x8os+PM\npZy3xP6xw4nTqqWllCxDeFte20AY+RI2NJnW35SLG2XSlxCaOvkazXWaW4Eq\nA8ZhZxPKREk2B3u9INpa+hfN5LpNe/AEIc/XP+RoFH49NafqZi14iKpxX3dE\nnFDe\r\n=Zewr\r\n-----END PGP SIGNATURE-----\r\n", "size": 173841}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.39": {"name": "@codemirror/view", "version": "0.19.39", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-ol4smHAwhWkW8p1diPZiZkLZVmKybKhQigwyrgdF7k1UFNY+/KDH4w2xic8JQXxX+v0ppMsoNf11C+afKJze5g==", "shasum": "016c49c5aa0e7f8aa50f5456fb866b75d65febf9", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.39.tgz", "fileCount": 8, "unpackedSize": 686372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1uxxCRA9TVsSAnZWagAAABsP/jDz+FzRFZNeYQmKjdSi\n00Q8pDY7x/gcZxAIaid69T0CU7iLn69LpeLTOcY1aP4wxiRayRyEKFWfuS1d\n/mwOqlT4ugc8U+axpILHnItUgALML6PrnSv8jEf7WkNXbQO7CGsTETXpi+G2\nKpVEAWLp85gg2UE7HlXtt0g1J3GSgjEKlhztNvZ150VL6yK+eEDlzhxDtgcY\nEBpgOvcTKtkw5+y4rzolDa6f1BMoXA7TnPudqHSZQ7PweYJZyZS8rT5VZbv3\nKkg1Rs19Xl/tjh4ctCFZ6H3FzBpHXiG/i5smYbjESsM941RvKRsUEcbQLhFG\ns2y0liq9ioTYX7Agb1Eq+BBZQhtb9wIL8EhUYrWKDWRnUN2NAm9ZZlSLRP87\nFXo5N0y8iQyiDYDC2EpOjmUMe9DdIbAndjDJqDpZlCVA8rLDdxCxPpMccWuN\noL6gsPWtJjJRSpQLZ9raByGFVEZ20RXVexwtFQtC95x6FektWUBtjibASEqM\n1iUrw1GKQub2ntFuFqomoeOdXQzQBLoC63txdGQHiyXK+ZVNWeLVtOPLhJcO\nIM8o085/xohhs1IblmH3bx/YBvlpijbqENXt1H0yPrm5KU8CzYQWXoLzItwy\nwy4+xqv4J8z2yuLcCH/dW2RLA1QYa3lwHNUOf/Un/NuZkr8/PrRz0HOla63c\n8ehC\r\n=h32F\r\n-----END PGP SIGNATURE-----\r\n", "size": 174130}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.40": {"name": "@codemirror/view", "version": "0.19.40", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-0CQV99+/nIKTVVbDs0XjW4Rkp8TobzJBXRaUHF6mOroVjuIBBcolE1eAGVEU5LrCS44C798jiP4r/HhLDNS+rw==", "shasum": "1be9cac1725568b7fba2252658a6f255b29339eb", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.40.tgz", "fileCount": 8, "unpackedSize": 692903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6CSlCRA9TVsSAnZWagAA7yAP/igyFrSPwj73h8e/6eBt\nHP8zc0Hg8asntGeqck7ceEW2Xu6nzz0IszES4zJYkA53li/BZT0Gc7EFOmUR\n3AdvoxLuGKhYcM4/L1avW6T6W8TzO+gnoeuqv/yepyU3/1u6jwN+TBDnKHoM\nDV9+UsmCKOOCQKdGs7vZf+MRy3FH0rOIiXbhJNtaOmw8NqnEJ7zq3/JyLRta\n6wqFoHGWCBTBkDXq8tqPBziEpLAjzIBZR4yiGZpVtXFxvR0BMuZnkc6zUrPY\neQmAf1s2n2RW6vg4qH7nq4V6A1Jo/6PDRFnneJqgZwDThwuRlBrgKUvOjeEi\nQ28Oz2Y9d83Di4dpgavda4VxOhSZMfYfdGMO3u1f4MnWGL06Lnd/u37zrhAz\nzQhRRdFD/Id57Zch3vcFC7b3Xz7EDLkb6qC+EwoXIkygl68LZHmFidTHX3hD\ngQ/NLKSxr0pXUja5fX71qhcxscfgxg2boskoyenDAq1gB0b/fv8xBF2W5wSD\nMGmhDx+cSARatYfVdEaGdMoeSicg06vrWz72/TnPZdcMBXghQt+v7tcX23iu\nzcLkpETL6ftq/7CIk4wLoKh+590ZDVxbf/vM+sZUshbwIBsLCjqzIyq17K0i\nluZu6iZFcQ3YiXaXlEvcmgdw5Y7GAe4PO1MPYhAqv6O9DXx1TtHQ4rK4cpp1\nQ0Rd\r\n=VOnn\r\n-----END PGP SIGNATURE-----\r\n", "size": 175666}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.43": {"name": "@codemirror/view", "version": "0.19.43", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-caT53/FD4gDLgb1fcYWJ935oMfRmy4DwKcDuMFg56T5M5hzf20H6nl4+NxPB5eiV7AZ1ux3zy40LQVB4U6lFxg==", "shasum": "4c77039d0462a6034797a6f0d38b7f547b2edaf4", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.43.tgz", "fileCount": 8, "unpackedSize": 697125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDKupCRA9TVsSAnZWagAAun4P/jrqkr5nJfJIYv3cqiEH\nKcMUSbilhL3CUffqav7AlTcoBjIs4mUFWmOvov+eqRHjeNEXOs0igtmUNn13\n5EGjhmhpU0bdrvQh6NbkRYyFwj2cgOprfWcHA1BKCWg1ICnAccRDZiTKGViu\n2jHvuIf5whgdenPoV4WJ5zbjpIwI5N/IPcJqPDnmnV65YFIn4OkQH1Jy6o3K\nPHcsLJKSuRmTq/N//L/b2abeS9ZKxNAonMKwwt7n/X5U2rHJsKhRs/8mdXw6\ncByAcN2o2hkF/6VEZuiuFRKXEd1v8UFAbURn46DvhVYQ6l8kiMDdx01aHMf+\nzZcEi8g6VrwiUnD3CQTSr4mcahvFzS+YhfTQSraetMtvAaOGlvWhJJutpz9z\nOcFBvhUKeMcyvfSm5RUARhdpKmcaErqRcmPNiLqL5xfTx5SJg3OHFpc7ZrqW\n6DzIKspFdFqCsK2MgDfZ+Po8FPFFvtGS3UXezSrweYn+tHY/fp7BBrtsl49V\n+JAffIVHP2jgEpwKIJNzzle+VnwRPMNlXxCEX+37GoxgsZHqIGx6xU7el/Pq\n89QoyLUYJiTc50ue5OliWu13l9RtACSJVwVfZ9p8MCN65O738MPKT09qcVFz\n3o6EVPsGB6ZPtxEgFB9fwvDXNzPBlhxVzFLzMlJkXkKv4SPKofDOtKoI1NM9\nXz58\r\n=FF2P\r\n-----END PGP SIGNATURE-----\r\n", "size": 176492}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.44": {"name": "@codemirror/view", "version": "0.19.44", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-vahNUE6hSXdjzs1gcztKPJQhZu+ZIwRpK4ZGZTSD81/CZUVqtlF75W3RCYVgEdjTI1l6ogJmIL6FM2Xj7ltn7Q==", "shasum": "153301eda413e76d2b98dc3870307f918c05c646", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.44.tgz", "fileCount": 8, "unpackedSize": 698485, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDk4sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpePQ//ZyREOqCK/+ardo+LAij0AAc2GdKRiFYA79emXcjcDWG9/uV0\r\n5j7hB364c8my3qb1CnsK2w/KI3t95Q/lcLQKnwpet5JSgJH2o7XNvEKDX2p6\r\n+tSSBVhtQIbkw9J1O7wCtSL1MojT2377PsC39h0XuS9QzoIiEdeRQOPiwjVK\r\n2c32kh9NcepkOKPJQeyj0fG003StG5ijg0/jQGoh6Ox8aaITNMrkQnEQBwBC\r\ntgeRj7ZI1p2KqdJJP5NQcoBuGctDPqBquQfRYzXP/FtnRtyEY55WcBoEP3Nb\r\nZVt9geBLaLRh3VjXm5cGTxpRI4pvsXFCW1BY2Z6w8LaxR8dezkjUvOoHA8c8\r\nO5cNryB35kn810UexfJrK0joFGXyM9Bn8zz4wTmkqoO9f34W9F8I94I2Z2wA\r\nFxiqoUNMcHujHqKGsoSiBjIJitrWIcknR5Huy/+pY0NRcMh8hga2bO9Ul4Qq\r\nkLIpx9xR3tKpNADiJYbdpF8IpmCf1FqyJLWgg9Tq9YR/OeLRzwimAVwEr/V8\r\n823AliaXUfzdcnqG1JyLR/4VFAt/2YinO5xbxRkdIs5oJtTzYiLUwv8iYYeY\r\nkEQZuePfKmhgYV3vRyafhVBCYoeZigOyje2w4VUPbXFp1TXMtOJCt/tL2q+h\r\nWrWnkWNnty/DyJhyfX2S+8no42qJFOa8ELg=\r\n=gzKW\r\n-----END PGP SIGNATURE-----\r\n", "size": 176882}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.45": {"name": "@codemirror/view", "version": "0.19.45", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-wR19UBYvJMeV9axa5Xo6ATbAP1jl30BPFZ5buu3cJjYXwlRhJDjzw2wUbxk1zsR1LtAe5jrRNeWEtGA+IPacxw==", "shasum": "fa608ee1412808e2fa555e48658436dd9e309d5c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.45.tgz", "fileCount": 8, "unpackedSize": 702107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFmYmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb/A/+PQlrjt98etKL32zai6hKg/65ekVhvK/S8vEyz6BTweOIYQkU\r\nKQdoru+vgx0kXwOeFYowCcYNGNTSMIt8nmJyG/3BvUUBdwk0cbjppymd5GbS\r\nMeNGu+luUFQoG/Tu2FhSTr13bNYsQTuI5z9ZNBaJP9eKeGNuvnMQT9Y3X1BK\r\nU78lK+MIgqpEw0PWZT5HbGc+NgCQavWMOlMfaJdrTjAXCeeSFoEs/C3YBTH5\r\n/Vvk0Cr9iG8XZ9YBGMoGLVFPPy1FZ1IgejkFG0OrHBwhCfcFvJdxtaURSxvq\r\nfnuLkMTITKegfgrjxZvYxugi2xDHyr8J0irmltJ1l+QGsuFBOLwI4HlcgGYV\r\n5DWm6N15N2Eq3KApP0hgSQCz6fjFDcb/nMj/7VXCx95u5OhAkpU/MDmCcQbk\r\ngMIATY1tIpMCWz9K7p4kFv2y6F9n3oAe2VsgI0G0LpJO/WP+bzZI7LtRaSdU\r\nMCOLAEcpZ99CWU9NPrZfzcN5Kx138yYvWGu1jcTnuZR4Pjy1SrleoKLxTo+S\r\nqGtnptW1+lLG3JQL5eAuzHTzKlSB6JhKrV1p/3zwelUQk0+4htCZZlKHuQb/\r\nUCouBc0/2rSt2tw+EjuTpKBQ5hN+b43kkyAGK8iCdtmFBJItHxdDK9k0Rp1e\r\nV7/cu+1kTuWWatQcS4Li2FEahVC38zTsuLc=\r\n=MlhD\r\n-----END PGP SIGNATURE-----\r\n", "size": 177598}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.46": {"name": "@codemirror/view", "version": "0.19.46", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-gnIRJ4OrHt7RIC8q+NNOJeUy+nah6IOM/BPbS0uNnYPhfhpO938HCKbEE2CDLnD6LqcIBLD6u2xCKHYD9g5pTw==", "shasum": "ee39f908bb536a4e4f1b3f8cd160dac90b20dec4", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.46.tgz", "fileCount": 8, "unpackedSize": 707777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIMKBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq80Q/+Neorqg7IEqT6I95CIWN3Sa7Lut+n7HR9GXvZR4KX1bRtId5r\r\n1XhvJKWvdiKgZY2kiO4ICcXbwr4VmIMveNc3ZzHAzTvT+CQjcLvKNJSqr3lN\r\nKyKPVTeLY5JiMDGWLvonxq8ecBjZGwEtiyhjiptzHHicrFLV+7UxVQeDD417\r\nkIe20c4B8bCNiUWwvtdm/4O+IplZshW9UFuHf8FGvFcgxaMddUr5HyMted2o\r\n//8K44oESFLLjLtKTlyLtCWr5vr5TuqKTzEBLEkmm/x1tR5CnjZZv5OHdC1x\r\nkOv+/x7U5M9mxZFsun81PwXmk2e9olS4NKvft8oZOttsUERj6Apw7DsiXhF2\r\nPAvc0ucBZstwdIa3jQGmtjiL36p6O9YR/oh6xJ3yJ7DA0rBRNv2RibtQ08cV\r\nGL+xDwMt3B89R40sECfE1xzGmcgvocMD+/MYNQCih8GUz40oafCYsBaNIRzE\r\nzBxo5DoWSCGq2BQwiqZN4+bg/OQomOya0/Tzv6tFzYJBmnKcXEdVdQ/eRCUh\r\nSSSUf5nFUrfjUM7FX83C5MT8dBF4OzCIFgp4c2yzpuA+wY/i3RJdcttzfycJ\r\n1PeIXglAaTb3r8z9MzGeFzo3D9vq8vUNVew2I6vURdpYNao2rbOb2WBHhfn+\r\nVQ0yyaC5scSgetoXuZ1mn7KfmOYX+JVaSlM=\r\n=lF1E\r\n-----END PGP SIGNATURE-----\r\n", "size": 178747}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.47": {"name": "@codemirror/view", "version": "0.19.47", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-SfbagKvJQl5dtt+9wYpo9sa3ZkMgUxTq+/hXDf0KVwIx+zu3cJIqfEm9xSx6yXkq7it7RsPGHaPasApNffF/8g==", "shasum": "2163c3016d7680bf50dd695c0e3abdaf80f45363", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.47.tgz", "fileCount": 8, "unpackedSize": 708051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ1EwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoewA//WgvFXiZpSN9yW5ODWKQebqfffRzxwitpPluiFP7fIdFK3zS9\r\nMeDXB9bPpNb9XPWe1/CyRECsUoMHP2YrRsjsQf6HbNt25cvZOwfLD/t22uuz\r\n1xp0fQa7Oaxnr6oinpY93VN/KY7cyJ8Agii/L2/nNdmgdNeNoNFm89i++qWP\r\nMNvCuVGKDbsqHwqKUK93a8Pd3dIbMjDSqDppzUY2riGmFBT6ljqWmAOTjZkB\r\n4eW+aU60p/ZQtCW0Y9jfdACfLFPXqhztOhIA1yTlcVqq05d5OYNSS26LixiW\r\nxTrcVZX1+gs8+RKZtwJugr/TzPJz5qa9iJ8cwU/eXmznfIKqBBiWhHC7HJyu\r\ntv4ey9bt1/Cft6hM3o/UWXwR2m1Hkd6Yp/3VprRv3NahtOV5/RJKG0odTNyS\r\n0Qm+43QFirzvjxVZHC0WBcd3ThbARO8Pj5W/uVULwDrYnUmJCwYXhaKWdx3f\r\nvz/bPIEeoY5LZw14q6ezU19JcPrtGBnJL6C2E0Xydpv3ioFz6oyxDfXGFXkR\r\njFS4kZpo/c0SmfM5tMKx7R2jV6JsOr1JRfhhcic0cKNljJTxmIrpCTCh/C+u\r\ncsVMPC/MjQLM7vHXzZfOAp778nnLMAjGHNvSjsqP9K1NzfYWhpKyw8TKw2pH\r\n31wePpCRFrFg9ufVDjS6a6gaLXSv2n59SoU=\r\n=KS2v\r\n-----END PGP SIGNATURE-----\r\n", "size": 178811}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.48": {"name": "@codemirror/view", "version": "0.19.48", "dependencies": {"@codemirror/rangeset": "^0.19.5", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-0eg7D2Nz4S8/caetCTz61rK0tkHI17V/d15Jy0kLOT8dTLGGNJUponDnW28h2B6bERmPlVHKh8MJIr5OCp1nGw==", "shasum": "1c657e2b0f8ed896ac6448d6e2215ab115e2a0fc", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.19.48.tgz", "fileCount": 8, "unpackedSize": 709433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRCwmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVKQ/+LG52jKbTyNKh39Gu7EGzAKSFHYb9VnMoZhEwCLbxmVlGX7PW\r\naKyAlL4ATXslmTCzRlSoyiGTq/J3eQM+gDWIMMrdA7lcEx3OYSf2CQISyF6F\r\n3JCVODvkzdLchxsbJZNSpaM4VpXz9VCe4vukTwIjauwaiE+aLjqP5WMGP384\r\n6f3C6IpxvZZc2SGM5qoAo/bCGTjQul0RhcVYPiawZlxpFc73T7EKZRXFJFI2\r\npE76oRliLS4FdeQW4uQwt6QG0X+Q0/1+wGyt3rytW4ZxUeBr6sO79nVbHAYM\r\nHGgz8G+7ajdgmrOqEQciGlSbK3sBL0mKf65un3ddgKPaK8w07VHx7rv2Y71l\r\nnpXaaWqwDqVVCZt1M8gbQ/rXNE5YXKDiymdrMgcfgx8HaMO9orI0U+QCCLEU\r\nafAf/ql5enuk2Qz9wNKN5IrjT/M1MYUSigHhWpWUJ1HS622dcHW4+LJOjHYo\r\n0xUSYuv4t1T/kCupkSeekbDTNfvB6AXDk5QbzLeZ/84j9Q+Qb3nYuGuCpR4g\r\n4IRuvxqcAEjmRuv0V4ug+YAe3FmOG3sTsrYkUrhGQZgrm9n0ouVFoovantv3\r\n80FPgZ3HU9glzWE6WecZekDLAzUHXvvpVvP2HPA/X7r5DPa77ZoxExnKz/hk\r\nn6uHDSD619hI1zOacD6Dp7EgwfbbUwyA5KY=\r\n=av9y\r\n-----END PGP SIGNATURE-----\r\n", "size": 179148}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/view", "version": "0.20.0", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-PVRdgI5DWC94GAYRaCsfh2Nw0sAMYnUBbO7FOnZry1yIonX/x3+TfLgay1HzxbZSwyDs/EXOINO5ZhJHY+XR2w==", "shasum": "276d010cfaaaae1efe336e626d82650f8dbf5e54", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.0.tgz", "fileCount": 8, "unpackedSize": 814548, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpq3lne/uWIzZ7529NCMXX9wl4iX6QPhOZpRyeTaSpqwIgTvyKvGk8yRHRbJTfPcDJpY/swXHzoDrJphM5Ui5luEs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBUtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM/RAAic93DOsNO00CoHLkX2S9vGDAQfvUBDSZ+Mn7icB/529WCUUH\r\nBslfdrent/n4t5Mq8aLKsOvI/0QyEA/C7caFDvDByWdcIGgv3hKdX/A0hB+H\r\nSeRZjDUmmNzTOObpoNC/rfqAQvu/jVsOSNKN15smV4a4XxLOmKxNL3G7MNAd\r\nIFqa2O8Ozholo5IQim3/b4I1Qo2WLZQjF4Pr8VFHRO9rJGoF5EJCwqEsIsFF\r\nC03NaE4PhNLId+RHlgtaqfBK7/PSoWbZqwaIX62utRTPcxjl6A9frgZ4V2aa\r\nPJvP8vEgZNCBM//l8Kjv2q0jTQeSTs0QXsVHimueoeMf9yO0olgG5pf5wGVR\r\nm8H6U+BOwLrKCaxmL3i+HP4f23W7c29z1oiAMBeGsP8sAno1XJTd0iFPi5AK\r\nK60aYBbbqpF7KbxkyBt2LpDyRjWLyVsiiMVrYTqJc7CDQ7hbT72dj7V0J49C\r\n1vK71mR8W9md8Svcxy8RwtnZn52OPFwYpfKcWlrDohN9U0TnjWJhWoRZmEtG\r\n+frJ22syWyFEO8hQPmpWlLaIlubncqsMO9jSixKcweX7NFQ5d5TNzVdHLOEf\r\nfu36x18UxU1vc8tDN75IZ9GLy4lbVEcPxuzolOnFMuv6xgCLrEIUtx6Jphpv\r\nnYlCkABI0lrTSgV4aCIc6KK7tj5kP5Ak234=\r\n=Jtk6\r\n-----END PGP SIGNATURE-----\r\n", "size": 203427}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.1": {"name": "@codemirror/view", "version": "0.20.1", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-DpTtg/w4LxgYmEYq67LY4k7ZDRJ97ppeQVg0+VsUi7CqHjfedi3zmjiUq9gA9is5DKIpGARDKyHigslpw3cX5w==", "shasum": "071f192b2586e3122f1b8b2a9edcd06709a31e9d", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.1.tgz", "fileCount": 8, "unpackedSize": 814176, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvNmB+YNg6oDzXXFq3x6dI2zDZKFRzutU8WcuPon9q3AIhAOZd5ELhZdMQA/iGNpKyEiaqThtKDV1K4xSoYiOfGQFz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYB7lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmobjg/9GOtiLuSttQADg+pHHJd+XNVWldKdpikRhJJxegOdKroYY3Xm\r\nAmLhmqgZC5iNXbDMDSk9whKxST+xep93Cdv2zPwsREEVRXXz6lii0rJ5i2kS\r\noSD53563MAdG6tP89xkoBvYkP6zAs1YjNLMoVILaHZq1UQuLlY/pzFnSdjfB\r\nGAonOXsI2LCY9URIaJQbXZysKLpjyxVfpQ3POL5AmcB+p+3t4FEL9qk+3UWw\r\nGFiAPV5BwdWIbKZ/YrjGDwT2t6w9U0TmlN7cqvIzlVjbTFH2pWVdAKSXnJA5\r\nNINBvhDAcXUVhmJLAaYQ3D1TdU+a5PmAWOhiwOg23PRjELiV0hfl/8Hlp+Yy\r\nH6xuYsQ1M+4UbIfShAf/NdCnWyZ+6gr1softmzfIWTx96/dh99TsvCugawpN\r\nMGnYKWV+4JoJWugv5SpnD39Log3gyWdXREm3aAtZwFpoOqQnVyZ+1F1HLfA3\r\nbp4dYa5IRfE0BMDTRL21qcNpHu1gysjlMB9ncD2TVcIoGgtmc+oqnuiAwbls\r\n0qjEjRhNfddPbeWXEWRddRsnKKKJJjkK9b6aOWvYnJDbRgTGAkIxx/tddMl1\r\nA8599PfT0dPrzeuhSAQZ5Ad9JCa8GNHmOnD8OyJgwWcbJ2Pbcy2E2Xu83C9Q\r\n6C1KFOGWEnC0ybLZtwkGNW1AUZLwGWKwu6c=\r\n=5SSa\r\n-----END PGP SIGNATURE-----\r\n", "size": 203325}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.2": {"name": "@codemirror/view", "version": "0.20.2", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-vbvOlRTgN6DX3DnZM2yCqBfUfIlDM48Mp7E9NFIDZEnrsUAgoIgGH8dUJYqyplE3H5YBXa4eBhVTOmd9p5j0Hg==", "shasum": "2724a8f0e7ca576fddb2a6989d369185b9acedee", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.2.tgz", "fileCount": 8, "unpackedSize": 814651, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH9YEE5D4I+QgrmwRx4nEAq0C1bKSOXrBV7vq4o8wKt0AiEA5NAJChO1/JwgwMPMfFlnTh13bj/uhX5UJkjCEyqst68="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYoc+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Pg/9EuWxW2HMq03i4aC87vnPreAAW+GbJIeSS3mCGTwkvoYWQ8RH\r\nceq6HFKj3AuxjXtPvwlfaweTMtKgeWxuBWjnlGsbdkv2loR0pqpySO4dDV82\r\n3sV1l5CVn0Q0P7Bv7nylzsOPLUTpkG8jetnhWUbS4yGniWr4yPf7KMDeAn4J\r\nkv2VVv5k+u9xsMcLkm3nQj1x736zmOJnMTla3dFBnMCaTHn+7CyC3uw3ztr7\r\n9TJhEIvaMNICiurOaH7QBYc3rvoLDOMcrERgbM9YlLsg1N3wS7Tbv/PIsNIP\r\n6+2NOP6RtJVrLlTfHPd8J/1zUNPXmcfEXdwpiEfL/33o19NpakIX0lVT8lUw\r\nV8ue8Nwgzp9YrBoh8dEbsTq/q14HGiU14iPMYJPATq2nEyUep4oMFbE24JuG\r\nNTF66x+b+GLO8+7pjLJvnpjsTCaf9bbRMQfomLvJmdDFsotRh2Ff3ULfUFMv\r\nibv7bMalRQfhwhdPw1C9XT5uIMq2pU4tC6BZe0UvWeXJeoXJp2mZ4n10u1wk\r\nqEqV7lovfvU/bLY180w3AiCCGBCQiQC3Dau9xOAcF5tdjtGN+wZ3OQ8XuQOx\r\nYhn/KAeZ8WPpE5NUYnHDQlPl1/e3tGVpAkbzF1KHE12bBdfncuvgov+j1cyz\r\nHGNDKLfgMH/xbzzjYQg2JV2nql4zi+txsDg=\r\n=6wTl\r\n-----END PGP SIGNATURE-----\r\n", "size": 203426}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.3": {"name": "@codemirror/view", "version": "0.20.3", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-wAKK1SSmNOEAG1QEDlY5NhzVnzcXU21pcs6wzYa4G6IC6EWO208N69Ei11A4SIHk9QL658p2+GQ+ealpwHtAcw==", "shasum": "c907b93afb540400c0088c4203c42deb949cbc08", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.3.tgz", "fileCount": 8, "unpackedSize": 814935, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfRuKecbhr/4/xL8ov6XDyb1cjMsg86CLRGJy+e6q+YA<PERSON>AFlaiibi/TBTMxvEp36A9EkT4qcz3xvn3YmXpca/OOuw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaNArACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosKA//ecnmlpRjlVGCW2iv1D9cf4K+1irp1X9FSyKR/xpksuauX9YX\r\noQbO4tzjB2RCEe5bUJMIXVwS1vYIOAm4+YFSQpL3WVx4JmWNfULSI557+Oab\r\nhWeGXIa3OuDUhBzKOkuYJnOX8dGa22FWsuacHOZ/oqMnt2iTGNaTA9b06A2/\r\n9CgsmbhVQpqUogsiRMO/kN1ONMlz6+LedliMxGxD7ZpuxIGU7la0FvyNnjSB\r\n55C8ZMHxJTPGaoFCjMHClQMdkopUZtsfzLWM3feNwsEcdQM/RFKsYmSCc3u8\r\nzt9/LOcOpoAI83ZnpCOAeiLiqwv1BCHtVKMhjQ495iG9fFneoXl6MH6SqrYI\r\nFjlPihcPXaEMPgQQX45BlpBJ5udZbKXv8YKmL+8VoPLWcTNFWnZehC/z5o8N\r\niRIA0us9mZJxBUlAuk9U63fxjQfK2vLbNONsfWBylFYeLkmlhnpAPXWrQHdr\r\nNpHaojX+mRs12Jk5TOBiyavUyy0U/NQyHuUhvQczAe5VJ2JcL7IDnbdyhyd2\r\n8+IKcX+y1EB0UB47rvdjnA+BEyLCeI/d5XEpNN5msNxV5nGw/4OfWjJDb856\r\nE/JjnRvCZcYoF1XVFt+CpQ8AYsE5pMcpBJwkpu1ghP6PP6FQjuEAZZ4DAbUq\r\nstNJyVoDqwhghdYQX8oDLHi1fDLer1w9uNw=\r\n=4p8d\r\n-----END PGP SIGNATURE-----\r\n", "size": 203463}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.4": {"name": "@codemirror/view", "version": "0.20.4", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-6yprcc0/39EzI6y9Jm/J5F9lFzQik0yj8gE3CO/wOCQ4vd3Drol114mMK91LGtKrfoS0MFKVGulw0TdfN49Bfw==", "shasum": "5d988b929103f8148ec92512b937a3a8601bcc72", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.4.tgz", "fileCount": 8, "unpackedSize": 816815, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZKr9ks+s5i2bQhy+cYUBSXChrZYYhzHvGl1/RvZsazAiEA3YCtXzec9OVadhckWhsR3ORbzoN90ivwXflYxC+dfQM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicOubACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+URAAitRDBTSO6Dyk7KACsDQ7zl6aO1H9A9ZkJVw8uMkZum+R9GWx\r\n/JqaNjYPUCiCp5uvqDExstYBO7GhnvLtaKU+ZIFH3JB2U5oZ834Zge64Xav3\r\nS/G/V6gBGgRQyJ7ijUBiMT3+DFurgmWt7/gvO4udR37ixqPVnA9mwZjJjSSX\r\nR9/coSWWhAfz00bc+SB06zuGYpDifCtKWzQzqESoVxi7oxT5aSAIPwCqOlCC\r\no5JJxP5FNYwHyjY/8MfS8zashPkf2sQ+u82HHsGwmTjAl3QOupQlB32U7l5W\r\nm4fpRCYeshq3gtNfyQzzy3d6b3219LiX3sTq/Ql2csU9mMGz6odFPo6iLKHd\r\nXJ8KThss6i43k8MES2Ex7BJcM/j6q9ADCaHptfvzUJPXiafwGZJXUGzY7+qp\r\nLf8A/PXDtEpDIuHL4HkVqo2vIbtT+5LEHLfGs3+SSfv6A/uBdAadTV2Ff47q\r\nkto3rxnOz7mdL2hdL+IajAz/KP/OXdx+Qk2a4gfYAg3rBlrKYPcgHe8Vm8Zv\r\nNnFv+DZahmqIdmP8ZDdExL9j7gQdNDds16RDvUDa3ZrePbvNh74LrDqlQtxG\r\ntgdj32wHjxUZ8s1/XK5D6gUeP9aq/OobEgcQoG0OKIKiGmCOmsHEoQS3pCpU\r\nchQdwf/KVUCOJ1dcN44JcLJM5XX+Ob7jbdw=\r\n=UlgH\r\n-----END PGP SIGNATURE-----\r\n", "size": 203948}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.5": {"name": "@codemirror/view", "version": "0.20.5", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-oUKoSLIKo7ps3dRc2pE6U80TOOPPA/s+fWBxoiy6BL1lUXegmqnFp2iVKKSJGoI8k08BHwW5knEsN6ebuZjc9w==", "shasum": "ba0b46c3f6c9a2282accb196cdd5fd9bb11f3470", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.5.tgz", "fileCount": 8, "unpackedSize": 817228, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzvbI/5e1rGcxxa3J2D+L9mUnayP87Tr1zYYgTxTJa7wIgR3UcbwIipObFlyIrj0iOXQx4NnvzNN2i2oZqD4gkd3Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihLA1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpynA//ZEeU+KLHBd1Qn9pvVO345avqzksYC9oZV37vcD5X51HKq7Ou\r\nhlrnZvtcuSSg2As315RPXVS+0GcOmJeHgyaLtJrJTrLt7cQDBRXPmcIeqCM1\r\n/glg8s/faS3O3TkVdNC3gtSppAxXUCCIpd0ArBXuhFi1QLNb37wHTOsE3oMf\r\nTnxmy4woWk1E0WBSBJzcAqhW3wG6b89zVV/BaD2VBIfc7Mmc3xQJrIVDIfUw\r\nYgJ/27WWN10ZTqiL1kIbnm7rUl0Qi1pmwODTWpGcDRLn8hvLQLaUqY7OB5Qp\r\nahzP+hRnmdaOkbrK7MAyORo+8YrtlJITzWXDBLZo1xnJMdFPvcYGklUAVxgh\r\nWxabD5y8g+kz4Zj4kZN0jRoNNYpfFZ4pAq5gp6toFs29hskXupga8jmmVwi3\r\n7KWRRCpTNZKPRoArr0gpLFIcBWF0Q8AJgGb5JPgXalM/uRZjVDgfVBfLTiHI\r\nuG0YAFNHTRmKB8uQkHHrv8I7hdTGZgMhgUjeI1FmIR6TJ0iKw5fmvUiI8pPN\r\neOOTRKbiS5NZhGtO/PSkcgYGWYeWMAq7sBo+jCUHrBbp6aCocVbuEUsCEHJp\r\nTZUK0DiC70HGOO8K89jGlISFQ+Qcsu4XmHQQF+YvkpAUYowrKfF+dwMI9yFK\r\n6rkSHLrWZ3ibkNAO6s0nJR738ZXQhTWdRbA=\r\n=euWr\r\n-----END PGP SIGNATURE-----\r\n", "size": 204005}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.6": {"name": "@codemirror/view", "version": "0.20.6", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-k/Enz4HMcST5Waom2r8y8VtiJfgnU5+Y/pNVO45eIhsRH+0LYtAMgJqqqspWVv1apOuMzmlttw6keaPvkloakg==", "shasum": "ccc55547ad01153a3a146a57bac8854b8cd37e79", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.6.tgz", "fileCount": 8, "unpackedSize": 817723, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeCxN3MZqaqsTB13DTBaT9nt6b2rVdTqtBaD16EOMCIQIgGxq8rPHwnDeJ9StY9lHZEGDEZsQ6BbKCCcq/GMCd/S8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih1odACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6fg//Xtt1DHa+MfytjBv8o4OoOi4NBK2o80cch5ksO0Sb4dgXEEXG\r\ner2alJWwm+EPQBrMo14apllHtlwUD5qscrlSVlfXs5U0ORUFXwXqCieit1kf\r\nUBjDrDsD3ONr8KJgAXe9EkQBqKox/DiD58I0LY+1qZvfqQ/DzdjY+SbAcQtE\r\nhvb78NntTn7pQCmtnL8gItwcYm3O9ok1/1ZJxNJKTKEheB9XSg3kFDEJes+V\r\nRzEuG7xilBYVtsw7ubxxl5Zl/qbofeG763f6vfj/s1QUcnfBRdKiFfmbH9oH\r\n6OGVaizEKu+0dPe90VatbRy/MVND5MbrTFa+QUHlz4EZkefVNXUpMWes8JRq\r\ngMPRE1RpYAvTW5eUcMwdiHX+HxLLpfowNim/py30q/zkdlshLlaufnMI1Fpw\r\n9WrjAGxfyo6Q8wtUM1h/jeVJ4z8She+4brJodwSfuJCmzvqaeOqbVsqBhNkK\r\n4xJ/ZigbLaaDNPtu8DAYTfZGUioMFnuzDrwyQYiw54O/4sBk1kCakxjTsbrJ\r\nEVnXxWyrLEDSLX7oKrfuTr+G0VIyvQN9onnh2aKb0iIEGo+steB2nPSRj8Fs\r\nMdkgBwxo0f9lXj1wLsOLzAt4ag4IsSe3ueo+vTQEDwtaXZYFStutqUKBlxIx\r\nl0ehr3Y6rYDgpkh/8wL0oJ3s3PTZBodZY40=\r\n=8lE1\r\n-----END PGP SIGNATURE-----\r\n", "size": 204182}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.7": {"name": "@codemirror/view", "version": "0.20.7", "dependencies": {"@codemirror/state": "^0.20.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-pqEPCb9QFTOtHgAH5XU/oVy9UR/Anj6r+tG5CRmkNVcqSKEPmBU05WtN/jxJCFZBXf6HumzWC9ydE4qstO3TxQ==", "shasum": "1d0acc740f71f92abef4b437c030d4e6c39ab6dc", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-0.20.7.tgz", "fileCount": 8, "unpackedSize": 819896, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrgk/8IgNX5DmSOYWcCHXR9ajNWzHdSnMNYLbq2MrB2wIgc7lUmXrSUFqwSFwWsfKLQ92N4khC8aFUBODSjaA81IE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilJRsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8mA/+JF1ObIBPTi2dxJqSJnbBg3C3m21EC0MTEqDZ1w3b0qa0Im4f\r\nAEG96nTLnRZtkIi0S5uOe1r+m3CK8Bx8RVnK+5AsqHs6e15+CIcP7BIkb7s0\r\n8uGqUs4Iw33Mo7l2lGDhTFp2TqRL88fT5h6G3Gu06hSk1LZZaz43sHUswxGQ\r\nIzGJpQTpOfiDtmJnAAFTER/5Y84odi0Wjr7fi6x8pZ38vPZshu4hI08efPbJ\r\nSlQqSNJbgW5rtQwsTVQrSGmaGyM7DFhtTOo/liEmYqt2l5kV2uqVMd5EVAcN\r\n6rHRd9EGfPNIzX9duVMDOUAc+GyqY30rfoO3gkhIsGvvLGd3dTZaB+7Vqth1\r\nxrIffoaN+DuVlkQxV1TU4sLoPfkFCCZmHJ/FL200qEASiLewzABPP9sGkgHk\r\nElR+WFBodkVp739yRKnJy809d053IuBXVCpchi3VkUSxMkRjw+2s/lnc22x9\r\nJopFu1EHyNFUcp9G/7n5/YFhVYfxnK+2d99NjL/jetsdJ6R/HA/QaAGGe8hE\r\nZ9HXJBcvEb0FRHd8wOkLpddYTYBGuIqQ0/2CIjx38uCqhgo/N3ZhzdLoe0U2\r\nAW5hqEL0NiC18bNYc+zU3sYajBMu+c5dGGBfW4Zvsla9kbbAlK10w1A/LZ5O\r\ng9hkKamSnSTpKdGHMaqpTlZvEvRVe6Fxhtg=\r\n=kOMx\r\n-----END PGP SIGNATURE-----\r\n", "size": 204732}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/view", "version": "6.0.0", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-IdbZtg17jpLdNnetoPpSOMZNtsCEAexFNdG7tU6TlJkLQakaTNrzXiQEIx9oiZx3f1ql6Zbr8+qyLr/pkrmsng==", "shasum": "b3aa07e9bec497a465c27767c825c83c9b2525cc", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.0.0.tgz", "fileCount": 8, "unpackedSize": 821452, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyW+y1lK9UnLgXKQ7+BzWh5UZj87UcT2/6UyBxeTzo7wIhAOiA2Eos7sgBtt9qSw09ZO9V4Z3QGzZvS2J6y1ElKRZE"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBAhAAiWUrDNKBYplG9beO0kDKx+oYj0Znsk0TbypFnfNbshSTx4mW\r\n8TfHbc0bAr+3nj3oIKysewZGIxnO4e/Ofwh45LCgWf7aFCtlZ9SWdBJ1gvnh\r\nx6tuMPsVkYKQg3Ii7an81NFee9obaoJjZ8Ycy0tDdA1DNJcW7pKNS6D/2zHC\r\n656HN71jr1RRXA+aMugwguTc919AQCHLFHw1RQrAAVKwgHaKW5UfRMr5dX4e\r\n5Ibd3oKKbPWv43JFVBfI+cUTmp9R9Qcvb2opqEBNDw//UJF8l1Gc75gwUmVW\r\n2OoNfDw5JkghZq+HViX7inst4SFmoacUFskGaceMEHn6Ygszizo0nOy85P4t\r\nTK6SPMso4//YZ0Hj2lgr4mv8/0BjyJZVSGxn4G3mcIbcc82BRNnRY5QQZy2i\r\nIEM9N9+6pwn8R7s/Y7oV8A0r6iSqQZmUZjP1ZkIaXn+vh7gcBXwNS3EXkDRv\r\nvcVCredIRCnTlwa2s/xcBlNxSk88bSQ+s9RQyQB+6aEn+APKbUdnCuIkklTk\r\n+D+DUQoAdy0o9u5q2jHTlSAl3b7x+uyr+MLwdo6e60tZ5Wm1FEx8jT3dUwVv\r\nIWpFmW4BtOsRf0fs3UxHSOA1e2rpW3/tu/98hwiTtXhSJxADtzwiOwNHGICF\r\noUeyhkp8v+Rn1xQ4SForXrZ58k58vv70J9M=\r\n=UyGm\r\n-----END PGP SIGNATURE-----\r\n", "size": 205174}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.1": {"name": "@codemirror/view", "version": "6.0.1", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-n5olUr4Ld+XDTZN8+cnUHzkqJPeO2kr55HvBQ+4C1xWlDaRGrsedAoxCgeuGZsXRTUup/H/0e2kAN+a0R3skdA==", "shasum": "f56327a5cd241eff5393cfdfbceea897b5266967", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.0.1.tgz", "fileCount": 8, "unpackedSize": 822590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxnv00P9eQuBxGu0hw51NuT18Jh8PYz90yNJO+mc3tkAiEAyvqduW9A0lPt4fOx60BmWN8acfFBM94/D2G1NGZKu3w="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirCvOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv6Q/9HQvTji4gV+dpv7fnKwNS619WVNQAnY6ZrdtOKia09jJAnbDU\r\nogtPbJ2qmKLgL2ZsVX2FxEbsTGoiP/MWC7qIkEJzp6M4XcpbJaX3oVgy3omy\r\n9oNvk09+JGYYSA3DAA/2PXN5Pw9g/rYdwB+6txTTlInQfnyd3MsSeFBqX+UH\r\nnHMil7F1tI1603GsXYTldErqTx8z3bNRvOBOvzMPIcwsd/N32YEgj4+yrYZM\r\nw3PCv3dD+4KxQAifBVgAH+18MU1FcRYqlgC54ftKnVTBbDJQgZx+PmPiMbXr\r\ncHJ7c6fLunj+xNZbHi4KfRpBNcYtPF87n2Lzi6IpXNcRgJKCs2YtW/XyMOFM\r\nnhI+yqtlOQY9PhRv6scVZh8W+ncl5TprazJxqvzepR8vE4ROWo6yW2iPEMqe\r\nImzsanr6TNEa9qL4jU7oJdSu2cGD8gsF0xuf5ibjywrtjEfL0ziImU5C9M6x\r\nHnwOvSNNoMrGLffyrIfHNFZ5GxF2pgqLsEsvh8itEYEnG+IPag6eTxP2ScTq\r\nHvOp2a3qq1m/WSrgdCSp1KpqPkN1TZFfHjj69lvPtLlvAUtPssmV91DT1O9e\r\npQtb37kfdrMJeyQ85JiErL0ICFmS7zdUd+rKRBY/G8aZsenM2eeXIeVAreXM\r\n1SUWVT+shK+8g+IAQPoxg4f4Rn4/ZhT/g9g=\r\n=150H\r\n-----END PGP SIGNATURE-----\r\n", "size": 205489}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.2": {"name": "@codemirror/view", "version": "6.0.2", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-mnVT/q1JvKPjpmjXJNeCi/xHyaJ3abGJsumIVpdQ1nE1MXAyHf7GHWt8QpWMUvDiqF0j+inkhVR2OviTdFFX7Q==", "shasum": "27f4d08edd10a3678cf15390b4fba5e2a7220873", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.0.2.tgz", "fileCount": 8, "unpackedSize": 822955, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHgXzPGj6GILndhRNEu4lNetYl6spn/Y3sYZJPEjWitAiEA/NEaVlvAHcePNYCwcj0mDlCjami6HHH0nhFh7GlU+ic="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitAcwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrvsw/+MLYj5geeiEtMWMKYe+62BmFtzGZ2lC0/CR2N6hQ9zy71xiDO\r\nAQaC+M5LBpvWq/9nMRgNeChP8zGWzKwbFGS3FHex9otx6Fy++J60YqJvZ/39\r\nJ62WZG6Wk7lspz1BCm+a7uCqu0LLDkMqDqxGMb/8cibhfrxNNwR1TFbAPBZD\r\nC8g/byNnfN3TFjLMtfR22yVQ9AJmV0T0aJI9N1TPIxEbrg2xqcIZtaEEekQK\r\nKajkSrPQeEm+4TgvW7FvVXRCYrTOGQHWY49K4juh/xDfjQ6ub/IlEU3sV+4o\r\n/qk7cqdizmI2R+GjixaZHUUB+0v2iGvMQ33pNWg+ui3mTPQO4m/dLzzpjt1f\r\n6ahbfOiO8VA8xGfuaDQo6He8wUQv4ciY1WYE+4++eiw/i8I/DHjwAQl9rdeD\r\nETWhEPckLXBYLiVTm0jY0I17rR1bDH1JZp31KiFmHymdSOnYoFNlFgT3cBd2\r\nIgh5j1eZr+7ckXfbBr+VAF5ZT36HGxr2K2tn0zce+wCfgS5Z2/s8ZTxpzNQx\r\nPJVicNSKDqRr9iKYYzXW1c/yQQeKK+Anowqehr9KIVj2/ru/Fy24HCgxKTJJ\r\npsdXSiZe5tywX+ktOgPHtwRcZsbWjDVEbXLWmhyQSc1wWGIvfArOedWCd6sF\r\nKbr639XmXlCRRMN5NyvCV9lkegqvvWTbMN0=\r\n=ANSQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 205580}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.3": {"name": "@codemirror/view", "version": "6.0.3", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-1gDBymhbx2DZzwnR/rNUu1LiQqjxBJtFiB+4uLR6tHQ6vKhTIwUsP5uZUQ7SM7JxVx3UihMynnTqjcsC+mczZg==", "shasum": "c0f6cf5c66d76cbe64227717708a714338ac76a4", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.0.3.tgz", "fileCount": 8, "unpackedSize": 825646, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrTFiSeRTBr2peCRrE5DGrPBLNI2p4jGfnOG1YVeXHXgIhAM5GKZuy9RW8J5mk7tTTS8F+cj6owpUjPijqxISp3Xu8"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyAPiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozAA//ZN/2YHyHGNoLpUqxzk8fgaMiZUXBQ35hp9wKd+81X8RAwPe3\r\nqidB/tYPSzrhnsF2gv2zRXvT59cn5sxrniEmCdBr1BYqt1t0JPQrg2GXNK0h\r\nKkEw+30tHHt9//nLNC7K8bO427cIkDqcwE0dNIrz80PCqbL0O5k+Nzb82ToQ\r\ntilxixjOewxHed4MRjeIKP2kgOuPubeiTTJMuFMpIIVPhjvlKmiFFMU1uoKp\r\nUtNoVeGzN4zzc1N/68HVNzTz4/l/K6L4KYW+RwrhKzYKIoneB0tHI1bg15ds\r\nhZsIPI9l1xQGhQeCK/MFiKlEt5HC7OUE4af9+6v3gjEUBlfKUjIIxeFCyUEI\r\nXGNCVcT9PrRnvpD2NdGFoHRggWjn+SXomcD5jLpGjuFrgKX7gS/1v9XI36Rn\r\n1CfwYXxHXDx7Z061tLFrtSGpyeBfQ8K7t1vvpRnJLZgLiycYZ0LySC23ygTM\r\nzMMpBiow5A4S4Oi2mm5wNVjhNxhMtahOJPjzLxrb4u510ZnNYlWRYEm+O5eE\r\nyVaT+MHe/NyOUkyvZEcSR1Q8OS09xHkOUMvmJcxvubvxMa8RD37lBLBIa+wZ\r\nMLmUlg+EhTdPQY1fkxNGraCL2N7ASubAEwUj9LC/zMuBgxkfY1CiUEB+kxrI\r\n7Jmqe4J4eJh7Au8Qj6rcHk8ZsW0+5cd7TCE=\r\n=EPUA\r\n-----END PGP SIGNATURE-----\r\n", "size": 206201}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/view", "version": "6.1.0", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-T5QTuzwxbQ+KnZzz1ef3e3QCNH2qMdTmQhA4tbsK62lJGyCMZHSaSAJpFAr67c6Wl34IBgx2M7ue6WxJpWPOPg==", "shasum": "a8b83545f6572323ee09fbf0891a647ecb8edab6", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.1.0.tgz", "fileCount": 8, "unpackedSize": 827224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ9imbInVa47t/I3UIyoc19RwodKG9Ay9VJ880s856IwIhAIZDoXmDhQcVT89sFNeX3aBvpPHQeac5xheZeUKB89mv"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1tzsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIrRAAm7ugVhc20t06HZ8/xtdKUqHllAm0Y3tQRtTQeKFzo0FSnVok\r\nzanwngGSvvU7960Q/Nx1l0eGnxyujgkE5zPLBcu0OiW5qpK7DXFYoWe6vVTN\r\n5AbtlCzNb7IrxwAwbz2JBVKQhJ6vaiOStTvvqrZXUtwnHBajt50gUKA5Tjpc\r\n+c1hY7lH0vKY6jVQ6TjhRT8L3s7bPNC4TY9QDNTFhCZaEhlmRbbWX3LrYNMG\r\niW9TGZ0MmcaXVu1L1ELZ/dV54nyhZVAI+5HOGP3rQdolECk5c4++yJqqA4MU\r\nC7CdzbJsOL5cj6EpgZKf+3PqCRqQey9EyN1QVH1KD4ddpFM3kQ/JxVg3YaII\r\n4gxBr7DNOQt4vYDSoBYKKD/a60E738gJzKncB5aLnrr08qXJ8AWQw1/cHw9E\r\nzKNMsVL9EDbiLd4eLqBusgvFqmjUr11IKFXlOQZeO45mLB+rZ67WLcQZt5jj\r\nV1I471xNz4dPYYjOrdwZnSnxXIFiNFClbSN05mVtJyv7nsYJ4WNjG4rBIqs0\r\nZVRqeKwuU+BqkmR3XZYTMORgQonam3mke3RYgpoA8vd+uhRJDfOFuwk1Nxsn\r\nmpX0fsODWy6+w68RUCDYJHwc7Wp25jzZoesbw5NqeI0HISchgD5wvuEn9JQ+\r\nh0J7AZnBzdvU6UFJKfCKn6O7ftcbhdb+WPA=\r\n=ChTg\r\n-----END PGP SIGNATURE-----\r\n", "size": 206573}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.1": {"name": "@codemirror/view", "version": "6.1.1", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-ZBLsphk+Tbqxv7Z1vZ+rky7QbHV/ILoCN4rtdcboBmSbkDmVwsU0wmfqTGZyrOw5ulBPu2aE8esQf6cIUZbWqQ==", "shasum": "a9d085128263998b389682b11e75eb157a11011c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.1.1.tgz", "fileCount": 8, "unpackedSize": 832002, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuHsUlnWz0Lysd51O+yKC5KnHztiId/cgVFyvKIvVrawIhAMr6MBfBH8NYrGNguK2qZTragPThhgkr/o94G6CYkKms"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3mniACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2JxAAiS6a4YQncF4nSAGy2vVVZZc3PNKm/kas6HFSmxpuenc2OiME\r\nmziN27T4Zv8RJBjPVuiTk8GmqRgeY+TIE8xOHrqvKpJ9NCTa39lFCMNG4Mbx\r\nKl+n0BXHwYRs5gUMUlPSUfDcwkLsyOePIIrWocmaU7NjAPDyAJdWQyRwTuT8\r\nWxGbawYDCzPX0bxjDggy06P5D9yAC91vB68gSQcF+8zFmMio4WQ+TUgibeed\r\nYbZ0eY4aSI+qYXk0S72zM0Ktw9xxgLiUg5ZIUW+krllAsVPwzuyCUBOXKj1e\r\nfhxiDhmwYN8am/ucMPGYqGuECj25jRXivTR8n4stMKnOUpKQ4VN6xsd1kh0G\r\nhbSa5wOxO0i7dmoeGHj++4cQYQjX2fI0AlR+bMbszbSNbvpclhhIrDgFaPJy\r\n7cJ6dHExS9NmY8NgRgNpaG+A/edFBsXUktJz8HA/rnJkSzpbwHZKEJaRTvlJ\r\nVlYjfZBTwpuygXMSVSic2tUsg5I4e0JPkc0hwKf6D22RT/A/6W//h/EIFiLI\r\ncDOUmJThEvgCO2GhoNFPb1xdVy9gw4VWDSu3Ymk34+Vsavw1XHDogENw+yKh\r\nH4e+RAK/ShEwEA/Ux0BLXo4cZ6XXp4qWyOkgGEGwZ/Xdh+SLy/QfuZ/d7vDb\r\nppRcu2uYEgjwkivSkHM5VjjDAhTzw7Qwhf0=\r\n=VU7w\r\n-----END PGP SIGNATURE-----\r\n", "size": 207563}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.2": {"name": "@codemirror/view", "version": "6.1.2", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-puUydfKwfmOo+ixtuB+uN/ZpcteEYSnpjHmMaow1sOQhNICsKtGBup3i9ybVqyzDagARRYzSHTWjbdeHqmn31w==", "shasum": "22dd4f6867581aa09dc1d952253f655c1a44bd91", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.1.2.tgz", "fileCount": 8, "unpackedSize": 832650, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAddgZr2myRAQLwe3Pzq4J6Fql1mQTIJjacCXac2vqejAiBP1N2tEBFYuN0tb5GEzNdtt1awkKpzskfu0u8RNndFFQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4VzFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQGg//RKJENYss9oLk+wMG7lJjPAoyNoJD5Zdc2GijjvxU3ghSzDCN\r\nGb8jWEGW4O6Hb02aaDbwSRxZLguS3Qs1eC6R0mGgtEPPaHs6huVfau/CHAYq\r\nvF6qAZvI4VfpzGP4ZCNbYW4vY6T4GrBRvDuvMeewhXRYNy5//moMpcjpUS6a\r\n5tPUaZwop8DsTFLF7DzoRqD+HCBAhNW91zkUlerc8jv8PgZcTh1khQ0kAtwt\r\nMuQL69oeL3WDizs8BFkjGd6Q8S+y1eg6tTLWjlNzAyl2WMquev1v6cprmXUI\r\ntW6QhInHv/iDwGHRv5mGpCbV0DTAKjElib166NAILLIAozfOX4rrNQrlZdpQ\r\nuttgACZNLw6D7Tu0lbvKr+dQC7mBfL/k8SZ0TOi3knX/Uqh0hb0nHr2ZXViu\r\nl6p14+43uJMIiacpnXxFp8eGRUwiHetSMPlLpWhvnPiIbsW2PljDH15SNL9m\r\n6IHt0BTFw3xw1vttsjHlz8V1xJrfkbw113wyf9V1tnsgrd3j0P0RhVGV5eil\r\n1jyONSwXPDypcpzDGNvwhr9mmQwNpp03C6w3hLgDVBJfbByucb0Z40yiqPMJ\r\n/WtZCb1iC2q+4YDsjIwXhNBXPw815zfsbccHLKAVgPYhu2hzRTZsS5KKLeIj\r\ndSaAR6WHGAx+g1BKQ82xXVdEof8uMZlfq04=\r\n=1LbV\r\n-----END PGP SIGNATURE-----\r\n", "size": 207784}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.3": {"name": "@codemirror/view", "version": "6.1.3", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Ba8TdKX8cphIsgJ3NGT15Ac8c2mIObwo6MsDryffYKqB7Nw6l1c9cnjrahPF8DoN3HVfryW395O77vkuCNhW0A==", "shasum": "5b476dc64066e4cbc22148e5bc0a4d948e41ff01", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.1.3.tgz", "fileCount": 8, "unpackedSize": 834262, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEv9rD3hWc+vy08UhMy/OFQ4OA4bjPeQzFI9fZQY933KAiAVkdkCDwo7kmdvM39biPzN2REPLB4YMbM+yxAvb4DyLg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6p5pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqESA/8DxKXVEX0jg+sKQ3onmLLWQtDvDpuDe+F95woM9o/tKt1OWwA\r\nzF55IzR9aazF8t5A3YsMa/Y/KOpkhH7aUDtE+MKVtco5IVA1sgF0m8uGWHYI\r\n0iAjLp4VH0yV5Jfd8zi/OkmwRQ+ySLtLO71qvAMVMeLKfrSb4h+fLuwkO1/f\r\nZa3ypqRAoYNH0y9DqPpp1yezucd06nw/KC29IdXKHGDsJAt15Y5D49+BY0cQ\r\ntBzVHs//HMnX3HvGHJmr3eW24crxBLX3RiWZwWKO9EqkWUs0zbsTJVaSGfb1\r\nffh5JpdTLitTtCazfWIkTfXs0bhoMib4RPSaCyItV9b4KEd97TIJrzcbscTg\r\nEecmCegpWJ9rAl1wqy8AevJQSHNMkRf2+l1K5Sj6ozSyN585VAY4uN4hRhOF\r\nCVpAplkt/FDQduFApadvpv6R6NqCSIU+hot7s47uviMJIxHlZaBYMbUoyRiZ\r\nk3x6g5QflCY3VDy61xPTZSzzRVAKA0xYj4IeDgX+tqblPq9+XId+/cbiPxaH\r\nogqZgGOKgJOAmGBsbrHG2NniQPs3A7MSqU02OCySAWG+3e2HfJQQJT6O0Int\r\n2PqYvtlXp3xS80mlot3p8NkMjluN9YBUOTbablnjRtfxuKx+8sq/55ZtZGcJ\r\n3nXzGyLmX8vVcuOZxyAOqh9DY6iIhQWVlpI=\r\n=mrWW\r\n-----END PGP SIGNATURE-----\r\n", "size": 208185}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.4": {"name": "@codemirror/view", "version": "6.1.4", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-pekgUX+0hL4ri2JV7/bu7jhhwOgOhU1eRc1/ZyAQYCWcCI4TPB1qLrPE3cD/qW9yjBcYiN9MN0XI1tjK7Yw05Q==", "shasum": "c4b7ca6ea6920e206f6b3de89b17da1b66675fd7", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.1.4.tgz", "fileCount": 8, "unpackedSize": 834294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICeAqH0UYuiEpzP/aGygBdExXtAt1e8n/2oUYtRIzPyIAiEAooeSZHU+kehZ/9WwEFMcKGUtCff36pzX9aB8kt9kxII="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi65oiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj8g/9GYgQh0fikYESakVDm13tN6AXklwFFB5497hf3Uwknk+GIRjR\r\n/Du5VpGulDLNDDdlXXWw3xFqe8NhLN404chLlcQibmqglyHXCuBhpPVsArm/\r\nR1SVFqLk6ZgpBHQc1LAun5f3MIuqlrm194wanoZ4hzuwZaX3g2coQ9kZQYEw\r\nH3KtoD9Riakt7sJUl2MhMHgM0hPwjQaR7V9/1wSYfc0OYyfwvG8hCmi6kb2h\r\nyUjpCZuUxsSLTd0FMOkhS+5yrznF/W62YOLyjDOk+h20SmaInn9XK1nnlkI1\r\nft16Ysn31He64qR4ClBLyaJ3SuondmyBhfnt+0AuSUAXBiQnZ16XtmQIJXF2\r\nLmdCKDff2OwxlJPVP1FNaorM0PqkyaBSa2GaKBYFI0jSqysP4aUa/f17ta8n\r\nZlN2mxJEs2tA1GYm3GUWgmd2TeLZHMkbXqoHcUok5siTUM3RApJ+imQsPez9\r\nrl80E1r+yUGWfG1O2YSVqt+3ji7dV9e8EYeXXgYTtyMppfhAeqsnFCwMJM2e\r\nqUewe+cZr58oEhQ30x8UWtz4NQB8JrRCPvCMXzMxng8yTFMv1Vq3X+1lAbPD\r\nr6dBIIhuZXVBAUepTeE+2d8fT7WZOFpDZ2NuSYiY+kB8vmEAQZcADlb6mpfp\r\nljm6EUG2uu9BBdEZPOn7zMBmmFCajK/IhsU=\r\n=yybn\r\n-----END PGP SIGNATURE-----\r\n", "size": 208201}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/view", "version": "6.2.0", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-3emW1symh+GoteFMBPsltjmF790U/trouLILATh3JodbF/z98HvcQh2g3+H6dfNIHx16uNonsAF4mNzVr1TJNA==", "shasum": "bae4c486a84174bd9656af9c9d2bd709d5b26c9f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.2.0.tgz", "fileCount": 8, "unpackedSize": 837667, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5Bj5x642b/5f5ihol5fEJ5wbXNBWR8I6ii94xP2eyUgIhAKwodlPPlcOibpnRTDi3Vk9dZz7D/NMpsiJv3s14sXpa"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7Rk8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG0w//QFBWLmzn2bOdZ924iujvAZugLjZ6Sfo4soFrJ+ajXwQKZViS\r\np1Rnk0oNZzn0Xx8EgNSedn4/kHECaMxO169YnjlxSUA7QCZZr5la+rTgVjDd\r\nbHaFGfc1FWGCsw82vkWll9tG3FKdZp+DRMMx8JdsByGEJaJDAK+lqFbL2f+K\r\nR1hyfHjEaVc70AvOxHFaVPZ3AS976FkBCVNwQDAKQHwouGGgYamdEXwUYeGD\r\nkHUIqvfPeYHPUf1Y1UEB30BhysYKFIOxe8hTgeZVr8XUdrxlOstp0Kpqc7di\r\n+7p/6/Pf9PbomU8UQu+4o7vvgdS6ykoFofs4j7fVftFzfMw5s+lsBx30NR5E\r\n1R7mQa3CylfPQ3X+9NDpo5XW7nEaombDcHH5i3MzuocSSvYsU9o75/IER2bD\r\n2kS31o7UNWEmL6ZUk0Du8SzAEdLmeMGRvjrb6xfY/2ZtAU9Zaj5viPcU8FoA\r\nBptG8JbdbT/Xs2urlBCO1SgSOZgGG+MbdVOedLK0u96rtt+ImVaXaE0RABZK\r\nyPXrQr0iwD37NsMb4wdx6v3/j/w36ApHeKak4uUW6EVCksg7KKuxi0XjQ2uR\r\ng6iOEwkgH5NNfb780S3LwRIqXdCNFGKAeYLhDgy1rjkn3nKtlyuXAKH8y/8x\r\nTTpDid2yi7vHcT3ZzXxEx8arIx3ms7ntCvk=\r\n=rwhd\r\n-----END PGP SIGNATURE-----\r\n", "size": 209012}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.1": {"name": "@codemirror/view", "version": "6.2.1", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-r1svbtAj2Lp/86F3yy1TfDAOAtJRGLINLSEqByETyUaGo1EnLS+P+bbGCVHV62z46BzZYm16noDid69+4bzn0g==", "shasum": "299698639c658c738f10021c5ea78a513c63977b", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.2.1.tgz", "fileCount": 8, "unpackedSize": 839578, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYgUBgFvyOG58CKtjSpgjNf4IRKwemV71ATdGarieTaAiAX3X60Uy8ODIMgAJZtx0K6xHYvkGretpEd20FJ1dXbtg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBzcrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOIw//UPvd966EnAhL5lptDkjfGN+VuAEd79ulhv+r0E+QdOb6ZKh4\r\nRP7bPjyonzUGpi2HBgAfZu44c1+Fb55t55gtt30ayCNeMBNzGnOSk6FsPr0j\r\nIToXiKTxHzEjNOrfClOK0lZhEKBLsqGcFoCy4zf1mi0AuPK3FkKjBvm0swG4\r\n7G6aWqcNV067Ih3KoMfYFd4Fbkw0DE2V6Ky2skKDW95/aL9YIkE5Gqx1Ydho\r\n2iKQA++HExa44/Jm/2sIQ5q/iI9dwyV3WFKFcttVdRnR0Uxg1kYpY79K5m8J\r\nElWOoFD/DbCI/J9Ox0ifni7fJnKncLR3vhjwVr2f5/6wq53IUj7/HxLNVGzA\r\n/Yvub2s5vZhY8+BgxyiYQuqxrWFJfhx1bSck7XcEsEhfTK97Fb3nQxmn6587\r\nVxLRNBSLsMF9MInVnNaJJlnyHcD+R2McnAHvFt5Vjwu3C4xP0Dfmqb0dxecU\r\nUXHKVXSDE2r8M7SWVzz2WeXRCMcJNPPXQtCChUEaBoTVfKOVVF2zy8x/YO8e\r\nZUbETtiRiZb/bKfujKCBbPTWTOtlVXOELTgQWDhxJR2AvyOFxOqa7F+QRUED\r\nptsnasS9gVCz2YKMojrAWfTpplI8wpQGyiWlkBIShcmRN8k9XC58Rd2KXEvJ\r\nWCWw98HEzjQuQSZEM34qwcvEw90uxVYUqtQ=\r\n=/b5t\r\n-----END PGP SIGNATURE-----\r\n", "size": 209604}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.2": {"name": "@codemirror/view", "version": "6.2.2", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-RwtFlBM/+xnv95urH1D8bVsX3KYLZGygGyZG2TZoXW2Q/GMrI+AuUXh7jBE1M38IOX3YnLTlp6RdgCx3Xo2jVA==", "shasum": "1198ee5c303fdd6b4384b8ea8bcc43378c7753b9", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.2.2.tgz", "fileCount": 8, "unpackedSize": 840750, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBdhRs6jAQCFn7W9F83Gn2ytfeOBO335ivWzKq5GeFeLAiAo+mR/GXvsHKa8ervmAPNK1MZUHV5WlfswlfO3aDzw+A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD7JwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZsw//ZksWfYHobHxtKUL3oeGx61Nu1E2050Tlw23MzkzL1eAI9g7P\r\nL5naSvpf4sCz91D0E4FMnQGLv5H75e42OEaSUimC0D5yOKJVvGaS7qMLqp2Z\r\nFohawUMOQA/wCBDuYjNRl+7QuNuXUl4+g5n5oAw8Yh1sODJUSROdRA3takBs\r\nw8+rQcZPxQXkpeZJ/q5aLwQYs42Shk7s0zXQiSZHlQKEf+XI/EKs1SEukS80\r\nJ8EQwursxM7vBjc/puRdgPlW1kqGqt74IxQ6ojct9GEGEil74PzPN9cDafWX\r\n83JW8uAXKwn2vNKnkVkORP/lgZGUBYaiBkfSQW78zJ6DPxeSjhya7qowwKa6\r\nHxRPB979lsE9mZlizQUK4IPM0ptwZ1wYf+VBO7+mfCO+EbMGGmNI9SPg6j4s\r\n960l1YZez19svJQsV1U7X5hy674xK4/pjW/j1omB0+UCqyKi5TzBv7RYXWVc\r\n0HKKDG4N4HcNIl23yGnwRBoKBSK9T6OtTQKwL7CtTQcrb2rNiC0ovDyMAx9J\r\nRCVaVv6xuBto4ZPz1afJRFDMDnSaV/LX4n/tyDm/wxFpU4yod7bRslgJwCy+\r\n+0KCamsx6RVuxHR3oqaV8AhIuTa4Xq+JJ/80DhsQsYbta2B5YeBbYeIDrClL\r\nNH4KraSVwa/xK2r9GBsG8xCTSiuWr+9V/RM=\r\n=4BiM\r\n-----END PGP SIGNATURE-----\r\n", "size": 209920}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.3": {"name": "@codemirror/view", "version": "6.2.3", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-cgN9gWS9+kv9+eOgVJWMrGUk4EwYKBZpuFYvxIlu4CmMye3+U+gMzuZhBgtPDOCbCp30hxFIOO0MENhGfnaC/g==", "shasum": "ba6e70037b1deee5e2d9e94541305aa50ede4715", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.2.3.tgz", "fileCount": 8, "unpackedSize": 843120, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGtYCBAQmBJ743lvRQmjYwnWdtnhbWWusudQZ5kCCllVAiB6swAtwBL3xxsq8V5Q+GVGnByifQxGLqCMlFl/T2v2Ng=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGbCvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry7Q/7BmRdgwcUXuTXcjU+9PWjy+MuJG8iuMDgaeqjejjnmHfLcMNg\r\nCN+mEQ8bS5SkogpvfMYrJraZLJ7FrJ/M6/tZUL99cNA3Khml3JYw+vIktHOV\r\n381s0drUj+rAtVCuoJQcs5mfhWjKASKx1s6gYtLcY2zGSrvP1nSVU74IeAU4\r\nP/Nk1DI9sie7O83U1hV/Had2Y1sE5xBUxFkYlUY40slopAJ8TpnPc2eCu4UU\r\nnENjsZVMMl4q1eUuwT9PKVnN8jyh3yy9fCcoafZlJNiTmGiL+TvpvJ3JHHg+\r\n6sItBSqpOhNjM+j01A8FZJxzb8SbYpXXW/ReOVAIbxtHRY933MDgSAG4STPu\r\nw34JrKRFnfdFTvK4Bvp46ryKj9UUAKfaChfflr5PFlsP7U3vO/lSZSA3rJtM\r\ncXem9/T4h1PncSuqmDqG/5YWq5S1jaTILoXHiuV+VhlG5bC8A5vgcIwnC9fy\r\nHCfBN3MWHQ8p2Z9Y13/iJdHIBl+HYppLuBtN+Hrp1T7nfa/LcZHhGPk5m2et\r\nYJT5qF67OtHvCAQeKBGa7OPRgK97jQkPSikIE4Z7ZZyGB0WqImhGxjAVs23M\r\nIOsQULfJ+RwQxgp4U4YcPKscxHZoDw/Mh/GkdSKQ48xhqMj50SmS1AJxlOzT\r\nPVxF8My55zyESxXrQlZD+CZe6YL8+9uS7o0=\r\n=SIt2\r\n-----END PGP SIGNATURE-----\r\n", "size": 210330}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.4": {"name": "@codemirror/view", "version": "6.2.4", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Zc5qDv+CD2ubWs6ShGJL0tf4y2w5vObdg7Eus0ouhg9g5lrvsAnwO9PvavDZdK4bpH6O+cnNDRhFlvDuieXo/g==", "shasum": "dad3511097764ea3280bae7f63b7c1c09db9c21c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.2.4.tgz", "fileCount": 8, "unpackedSize": 844303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAa2PPzYIzis+ATNVdRTQI06a8M2Km+cVNcnwT+XLzfWAiEA6DAIK4Y2S56CyreWkJRsDu99mlVsB/dSxjZ/G9s3bNU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJIumACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMOg/+OiYppMeTBsSKJYec7s4RQ8j6p6gh5qmNXWCeSmxDlJ7nOWXU\r\nw9e1Xb0ouOh5u6ouLsU8nin3MSDwpKTnbLeqTgCwrh1o/WmlMIex6MsGggl6\r\nyehwAUOZdl25ECUSMu7Y81vvrl90sDP1oPv4Knllg4xviwlynDsn5Z63NRy/\r\nJwMWw06eaL4BGd1P8q6B3f5+P1pkurd2+pW9wKpt20moDFp0U4GXF6Vuhjoi\r\nU+1P2nL8FvRVyeEAdNA0s5vfbRZHQwipF7OHzQh+3nj80hBOWCNoppwP2in2\r\nKl/fF6Xnr/1f414vTfQFrQZf5HKJKUEzER2o/NCDZcEd5ZwIa/mNLtyVbkez\r\ntKrthY64I07tfVeF/OGyJOVE3i4/E7QapqDwQ98lhfAwJPKCZ5FPiFEdcV71\r\nrv3rXIv46+vaq5JLzdZCBLGGfGUyghSXx9idCtrnXE+K0TqNhHtYRrTAxmFf\r\nb7dVW3ac3KX/FIDI5hXNnYtJXaAm9fu9xi3h95kXPUi61kieoZmJXGlemGfl\r\nWHjXEDxcBnrA2wBOg/P2Wd1XsbCe4C2X8Gwudu5uu7uQf/VfYQ2KIPHnRurY\r\nsIHTv+nH3+ECrmBc+aI5CmdOi/cWLT8rnZCFgfUJF3Do2oIGO5oEBoqDntD9\r\n/lbh8LV2pkOmlSRUXlpPP5VEerW8uU3qPgY=\r\n=lPMa\r\n-----END PGP SIGNATURE-----\r\n", "size": 210603}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.5": {"name": "@codemirror/view", "version": "6.2.5", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-4kVS3RZi3nkPNfG2X/7+2L8IhoJv6dJ9yJz4KUlw4rwxI8OmVCKJzC/jnJqp+bVNOml+oA3gSF1kX2VvOiI72Q==", "shasum": "ed11b066828177bd8b80715b100265ea5066a876", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.2.5.tgz", "fileCount": 8, "unpackedSize": 845286, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/cBh4+3TwyZpaLv5s9QoMGKGxlPa1vJL9tBNWo9a7HAIgcDbxe6VBiKOT5Xrgo/PbIecpUN/obWwFW8TDpscUa4E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLtcNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5dhAAlmDjwdKXbvp5VdPmq5tVSgDz3GgkvkJyb8Gjw/uJ93bL3gT9\r\nmyeiMpTIjRJMNejsZD2uOr79X66v0+6bk6Cb4BfmEvpdt65gXu77DY1Swm7A\r\n3Hfp+4+0qrnXlOOPhMbtQJxPygpbiBrMoj1BtJpNyKbPStMz94DxnhRm8Y56\r\n/VSglzH3t2w7yJhw7X67AfxI3Jf9LLK5fOZerOD4W8pBaqpOsRPIzIb8yxdX\r\no1qpkdYWzoSs4uMX+71CV+YGaMx5Nqaz3hA/d8ZNyUP1wR9PawtQbgrLgCBU\r\nFatBtWazIjiOsuFSU0VZWgYkc5KEgsRvjxz10ADAJehqluv/Lu/VY7D7hApX\r\nVXLtmRDmNd58zyIfTrJS0O0EcIRmFyZNxNw24oAwx9aAxFwh+ZTbZq4TWBh2\r\nq/PN0qiPJWJ0IyJrNFaKxNX2PSAPowBrb5Zgpr9tm6L2UPOBNeGlqaeJ7MkG\r\nvkMoZsYgSj6AHCfL1tjyG/meCnF+oRkCXmXfUU3HS22Ujehs9GPHxet9FaFX\r\n2RcKewWY2Q0pgFgHSkTR1/fIob/b4dBfAtYeJo8/GYboqNICD/qtDit40xZz\r\nbq1QUZwC84jituB2AasoxwbDeoDncZ8WVhRRAmTBlHdhDv9lbZ/+K/0dWZtp\r\nkF4NVazYcD3cmTZZxL9lXgVPy8Cc5OGrSEA=\r\n=bL1C\r\n-----END PGP SIGNATURE-----\r\n", "size": 210902}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.0": {"name": "@codemirror/view", "version": "6.3.0", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-jMN9OGKmzRPJ+kksfMrB5e/A9heQncirHsz8XNBpgEbYONCk5tWHMKVWKTNwznkUGD5mnigXI1i5YIcWpscSPg==", "shasum": "715c97d64e7e20c9674782e7101ab7aefef8e23d", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.3.0.tgz", "fileCount": 8, "unpackedSize": 849781, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGCkDgMtxc8gfHkudEas6sNXUMSSDJO5D85S7cocFhGWAiEAoV2N1Itz0PIBnoq2leF6I5SXJ5VJz91/mLbBXp3HTcc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNBGIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5zBAAgEXELtRwKIZ8jCj4RfrWRP+JqurN2sj+5D7R3K2LngkpAnjn\r\nHAKQHrUgapERAOSFS3j9SAsRgeMHDKedpV1ypTSsfwqeGLUwHOQicFsipNkx\r\ndvYMyuKu7gSMB/ORSLiC4P3L5LicYLVpUEwDy20rra2ELjdNIdSmXUq5Pxe4\r\nvDh8z2hlnP84NMY0o9SQdSzUp1uOIVOMJHbo0EqxrUxazikr5+qwllcflCDN\r\nZsBLPAoL3pUnYfKEvXe7mq8s8gMd2opLPHr5XKn1ywvPpqY5XEy5ot7ZoY2V\r\nCCNRplSLkW1sUgSj8kE+mUdf4dECKfzFaEEzsan0OEipVCcH7luqo9NP264j\r\nOA4bvL0VuGhj2JGc8CyZPuVn8axaxSOc3gGw8EWM4XLv/hkzLDzx1lwUz3Mn\r\nQh2Ex3G7GoZOfU+haCyAqkZ6VljWTii4sf0G2bkF6571L+VwR2zr67JAaHbt\r\nVT0fYBVMJJWP97WXtx7//BZ+As4w2klFsbMUYQSDftC9aoZA0JfZ70iXP7O6\r\n+ymtTlD80J3/7BkHGTvEGe9Y270/u9LtmolMdeNtB+zIoTggCXR9QdYXdzBF\r\nhEjd0LogDQK7BdgzIvOrE5MgjbYJuUoC0Ss3IimiHun3Ny2lPGb3d+SpWrfZ\r\nKdaXeWFbz/wyy71PB2XRdpI4NFVlYEaSvIQ=\r\n=zuIQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 212019}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.1": {"name": "@codemirror/view", "version": "6.3.1", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-NKPBphoV9W2Q6tKXk+ge4q5EhMOOC0rpwdGS80/slNSfsVqkN4gwXIEqSprXJFlf9aUKZU7WhPvqRBMNH+hJkQ==", "shasum": "c2f1d8ad569351eb9f8d57956536fc63cd61aaa9", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.3.1.tgz", "fileCount": 8, "unpackedSize": 856378, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICS7l8PPL347Cr1d6dDknwYbi8vbBOkW5smHvZmRRAU9AiAzsDDJuTXN3QGA/hIUMra+rUVckHSULgdqKoDWWuQMRw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRBh3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsJQ/9FqecdYeGYtitF5s6HuaDfzGom1P8Ihk+YXvhm1vWhZKL+9DZ\r\nod278HIm/9PXZH0E6tzfsjK4pesghR3wEPGCM2Bt1XzPtvrGs+Wz9in29TbW\r\nZIUGDWiAfiw5BKWWZBlHQGjRXpU2fS4vbxekK/riDvN482c8Qh1fQ2y22OcI\r\ntjp+GBAOHJZJ5D4dEIxvcA3R2JafdMoagl1ezqmKlveBr8Mq/KVVQ+x1QWGv\r\nUWCqihEgwW0sAm/NJTvqALQ/I/Qv+mdnXwszX7n4wpqx6m5DAViAW1jwKIHZ\r\n1pdmuLMjNc8D2XwAuckqqgDCox95G9EwkZ+avbZ40SFOhqmBM1OZ1Ro5PrY7\r\nIkoJXrKfMKgMq2so5eioa28kbSuLMyyqEFXvpsN17ZjHbkTYbNRXAwjnAORv\r\nXIU4Dt2rGL89JPE4FW5qbydAhnZAxt8b3fidl66SMK++ILNsx9AqFE7hCfgD\r\n16oGtodn0mv+HxT5cZRMDzzb8BipRgSwMxbdJXiscYURuLCwrSVzmXMlDB7L\r\nnwzKM1kti63cCwTUp+5vO4xvherQb6uToowpZ3kGceTrKDBMI/0fI86ivnm+\r\nGTNlY+8ZFQ7YhOExdHJTz/Z+BQ8aTOKbDyw5FvLVTfHVG6xdu8YZKiYlbLny\r\nuBaD7IriQvXjqMQQwFJcVOTaxNz2qTTEM0U=\r\n=VZmn\r\n-----END PGP SIGNATURE-----\r\n", "size": 213059}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.0": {"name": "@codemirror/view", "version": "6.4.0", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Kv32b6Tn7QVwFbj/EDswTLSocjk5kgggF6zzBFAL4o4hZ/vmtFD155+EjH1pVlbfoDyVC2M6SedPsMrwYscgNg==", "shasum": "f8c213ab6dfec56048b372d2c378213428e2b4e5", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.4.0.tgz", "fileCount": 8, "unpackedSize": 857416, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGK/qy2D5lym6CKhHYG3EfbhwHiy8rxJRirzrKTeezv7AiBcInFhViM/ufQQeJ7Wb5vhSq/mDq0G0QHz+PVpLnzaOA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTmdDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCMw/9EZWJGlQcsQHlL6L3hxt9bogvz+H5xJWIYr4jnA/DpZM6ZSyF\r\nYDZFBWtQzlltKrchGcBvUpy5B8SVaYfXjQq1Bv4yu6fD9hVLlIxVl4khUicx\r\n5VV3EtdEGvNlsEviHFnC6LhG22gP2/3MgjJ0z3RvNC4yrZu9QT9Njz6MdHAX\r\n20ooY8/JIVPWhoxq8M7itzR91qshJxMxGnd/gOiL00rh7D/pSUEKQEXwLrZe\r\nj8qdLT/oKqF/kmpv8Pz3j7/sXq2yZATQyt7ALa9+martUj/S9fHgwZq4pjF4\r\nywNDgyItNACDQSvr5y2oB0nRh/k4LkQxymHbY/8AMOz5mMXWhhJbX3xEaLP9\r\nuVhgMPM4xtNv0gxVde6b4ij0+oR4JDhUBxhs8jLarezmHm8h9DG/4ZHnsd//\r\nihtQkp51K5eJyhH5vD+2nJIX80lfNKC1e9QbDFpYRHa8IwFzfXtNgyT2Bg96\r\n4uwwAHG4fAVpumVdM8SFLTUej158BvCKl3NIrj80zazIYxi5hzAnuCou+2lG\r\nVZ8IiicRnmh3/tUwYGdzHK+2JRPF6SwXFPz3boyHw1DkwplXEqn6WcWbRbzq\r\nlCU3qIiQFX+sCOJjinAWGzCxvpITWo36CoFuAlGFBar0dQX+LshmRsiixjbh\r\nlZMUskO949c6LBD3YtoLxrAMYQvJ1dQH82s=\r\n=Wjdp\r\n-----END PGP SIGNATURE-----\r\n", "size": 213491}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.1": {"name": "@codemirror/view", "version": "6.4.1", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-QdBpD6E5HYx6YFXXhqwrRyQ83w7CxWZnchM4QpWBVkkmV7/oJT8N+yz2KAi2iRaLObc/aOf7C2RCQTO2yswF8A==", "shasum": "7526bd473b8e5e5f7bade834121864b8f7d2649f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.4.1.tgz", "fileCount": 8, "unpackedSize": 859801, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyM9QJSVxmVHzfdRIQbM26m7Xrx3hmofeMLa4MQX3sxQIgHqcv1z5mIsLXyTiWvquTLWQdZi8NQ32IAy7iB5ysCJ8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaOOFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoINw/+O4dtwqT19G0nGM/86vIM/Zskmhqi/qeTqbzKDmAnIrwsu52x\r\nqiCa2ESGsan6EG+vNyZD3RF1KBApGzCt2Xxm7Yj1NSHc+vrw+nk+OpCZOSAx\r\naQ+fnX19FrC9mVxEuH6hBWiYBHaqqdPzbDiab4P67b9cb8VxcKn+w37G3vgj\r\nSxA8BK8Lxl+8P2tUbSK7xqQPE33jRpVW7pIyqmU8hvpg7raI2VM7FuF5wljC\r\nAFTwjEzcvZjLzR2iHaVToBTjgUbHypTX5uzDmaOPuObG5QvieDUz33Cy18T/\r\npQzCSgCFQgYaZacwn7AqHY02Vt+F+5/JyooeOUv4CO4p4wnJN+WSmkfwRPmI\r\n61tdKcbDOoHVTBepoC+NQZuJDOpre35NdvHNtxWTYxPvnNYPdWUlsZ53lamp\r\nALYYFrFtMMjX4jLlBkyIagz3jK+/e0YazUoSS/+P3NnDrp8mn3LKYcjfH9pI\r\nQMphUp+CZUu6bt0PqV7pxpkdkzrH0fbvxzNhERbpRu951XUC2CAhnqKSjMyE\r\n1Z9Pn/rhNgePJbrzc8etvk0rFCyy0p0bKFG4B2RGhgkkBcDoYCKxdh2+ZKOz\r\nbyca6LV9Qnb9vJeZ3uUJZi8bQU6BZXRBWv8LPFJvKeoA7yB14TeaTO+cAZkt\r\nNRR9UNLuzcyci7YwtFpgV1ZyUt73T0YKMoQ=\r\n=hGY8\r\n-----END PGP SIGNATURE-----\r\n", "size": 214007}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.2": {"name": "@codemirror/view", "version": "6.4.2", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-pKqUTc41sKxNrcqxLm6wV25J2tggSG3tybV+t/nfZNwA16S3vlBFsFLLy18dGOV1APajAl2ehXb7ZxJOayux4Q==", "shasum": "402a70830598b8ecb87a755ab03433899ce020f8", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.4.2.tgz", "fileCount": 8, "unpackedSize": 860187, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDWChaaB8q+By5dccXi6wnFY+j6ThFjeDcmV8BZbUElgIhAJXYD5WfIfV06ZW+EWyX3jcr6KdHZ+OMTo67N2fmmeuT"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbRlVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5bQ/+NtXcxqGrs+2yS1DLpLiy861B2+1OqxpEAL9W+EKBgIFq4PIB\r\n/pnvJHdSyKMVGPBeC13IXxtNnyVoAcGfWCy7Jb5IM4kwSyic53x6d1124dXT\r\nTtSJafCm7mya7h+XpfeQSFfmmgfVKBUroXD0ACpACDn24pKiUKEca6vqfOYv\r\nSQpW+Xeiv3Wg1rlYWHFeOQjb5j5qV6qBDHymcTq4aYNeU7bt9bUXzlcfnH3b\r\nhihZorRG5LWdd5TjBsbyW89uYCdUr1ZhjL7p0HkJPTUt2y7V2IUvRm2tDHX/\r\nP6yV2INicjHgcEfJq08F83S/vEQoZXRrT5w+NTed+xHgf1u+cc5c9+2kI0B8\r\nArWK181g0NYiigSBiGxChxSYVYI1zMI3edvfKNHw0lMcq1o6Xkm1w4nGY2GJ\r\nRq7FOUt4DEdkNFeu8R2bUy8mpHp9auzQ5aSL/ABiqGrkSa+VrxRyJLSsXZhW\r\nEkiCbvFOsmawndaYdRMBSU+w6c3VI82B9rkYVcxJ4lQfGbMvtb3R5eLCjkrT\r\nCM+TYGiqfn9NjegkJLO7pih+Ea0+wzMKairUGuEWLkJI3zyZq+dW8njdy7Mc\r\nV4OjVbVMFifEQ9RaqeEIvl1T/WwobBz3wvOXY1hlDg2n4vv/6agtx/MMyp11\r\nU2t4cRQN6rGS1ceFCOanmrMM4DFw7TM2PAE=\r\n=sgu+\r\n-----END PGP SIGNATURE-----\r\n", "size": 214151}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.0": {"name": "@codemirror/view", "version": "6.5.0", "dependencies": {"@codemirror/state": "^6.0.0", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-dapE7AywjyYoHBHn4n+wCRKFqMEmYZHHlfyoSO+e1P6MK4az1wg9t7mfwbdI9mXuBzmPBX7NmU3Xmq+qmxDOLw==", "shasum": "b9a4f9741d65c4e00159956a94c249fe0c864fb6", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.5.0.tgz", "fileCount": 8, "unpackedSize": 861018, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7FJ0xAh8i2pGeavHJUDMb9Ukz3a/rpqAWQiT2P2EfGAiB04/sbVBzaHx6ME8J2ipJv8Y/QjIih7CqgluTefzwrVg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcl4eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp15Q/8D+D+VFHrwWsm24aQp8MSNgkwTUe/9xYockmB/nrOhP60joFj\r\nwlF3Ks+CElR0AO3bIYSAd6AWek1FxKZ/g/LHyhtCNdO8Ata7u2WZpY0YtknM\r\nLuJO2izOc0fe++OGRgE7PFoUHJARIyOhe934S6c1XTpBaGSwN0ZyGei7o4jp\r\nnDDZhuG7Za9oT/S3OxanlRU9rcGt1rStJc/J5W/nudbdva1k4FNvQ5/x2K3g\r\np1eDhmllbE6oKmhdZhtQG13ybvHSSvEuQ7fC4ak/uAI1zmewQ30muLRPSJQp\r\nxkOSkALvQ5poJ8gT6xv0Ai+2CEgvXucsk2Ivhyfiovs3/KmEidFV65Il5PkG\r\n6pFeri25W202WgzpHqqdA8DL4DSMC3p2ry65Ga0YzDQRUdUkKLj7Rd5BY3ET\r\nSZ5rNg4S5lIF0vpXnTkkYulmHSxbEaoNBZWClheJzvB7mAz5Y4y+T3QlzFeL\r\n7dPyH0VvY8RFso+fm6hHkHBhol2BZxo5cGPPyzTsDaNMDf+TBmt7DBGAYcA2\r\njdeGPAOe1kuM7+fjZb3nfZ6Ds/GKDrSNzvnYBJ0boHrc7Q1+229O47S+t8GC\r\npJQ9C9MmGMX/BKKRa2Q8po2tihR3ozKBRFl2EB+ecdaxVOm4H8PelMcgWSBx\r\nTC6Rlf3MxWqIZ6GL38M+q5qUp3+Oh+7vSuE=\r\n=b0pP\r\n-----END PGP SIGNATURE-----\r\n", "size": 214365}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.1": {"name": "@codemirror/view", "version": "6.5.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-xBKP8N3AXOs06VcKvIuvIQoUlGs7Hb78ftJWahLaRX909jKPMgGxR5XjvrawzTTZMSTU3DzdjDNPwG6fPM/ypQ==", "shasum": "f4533cd796f0569508822d0012bee9a15dc4b3f9", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.5.1.tgz", "fileCount": 8, "unpackedSize": 861181, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICIPKiHGzOMvJ+v6MeFTSM02f0F7wVvLvp0GIQxrjlpuAiEAy4goAY2gZmpWp1RqNKxFq+fXwevIVU6ouxwSH8gUsd8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc7qfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8VA//VubAEY/QDE5A1cN9YtfNoEb0zxurbyS9AHDv6ni/G56+mBF+\r\n+f89ZosqzbwpjmMBEZOSqfjukgJ7V+JEsJdxcHXpAvDxC8k1SwNV1OqKHrdY\r\nT4fqw7ZPcW5GmUwt1yBvFmeADfCcf/WvGhYndWZ0c4jhKsCt02y7E3yvkzod\r\n5TJ6fMSByvnl7VxZrdvyV0DrO8cNODwuke4qzjdN/J0EEgGQe3x/2LlSCzZF\r\n01qmRCdWlIY2MzcQFoj3wRpgg98eJc8E48uC4iKTusrcZmZlF12vGTbOUc1w\r\neIYUUHrgNoueQWaguoi5IBcfmami3olg0TH1J5wGCKWTe5yTfQ8DaiyHPHse\r\nEbmD2ht1y2Q9RwbYbNEGe8iV6GEnJA68a/RrYDyeOg+rRJUPWXajWx8sTZkz\r\nsIDr5CrbQo2K9Br9XN+vLMxL6QAQSRSR1kEyUnYqmU3WmFNe0DAE8bxO3jvT\r\nJgB5dokIZYH4Nykn7DQhDb/xwf1TaRY9vHV8Au3LnVpUpwvjR4Yx1A8/98W8\r\n27St92GkoUHOUt7DSu/rwPF4muwKnIrI7q4xIlRh2Kfx45j26jrJijf67c7V\r\nBT8QUGgUg5LlRQFjet1ILEibK/AromlhKTBoWDiVo0vhOwTInUss3dGVxNhi\r\nKH3O2mgDbZYYmet/XdReX6vyTxJmkkyLHgc=\r\n=lKxa\r\n-----END PGP SIGNATURE-----\r\n", "size": 214418}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.6.0": {"name": "@codemirror/view", "version": "6.6.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-40VaFVZI3rkyjO5GHFAbNwaW+YgZexjKyx5gxLU2DvfuXAEZX0kW0apOXb0SBRLnKIQJ+U/n2nPfxgBVFHERrg==", "shasum": "d89a24912ae2f4b9d7bd430c7804c25c5e32838c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.6.0.tgz", "fileCount": 8, "unpackedSize": 867769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCITh2XW/2jBoSOA4lv+/+uSzF8laPkTV8AEEGlxToYwAIgFoy1qZ93sAu0Uk0oVUB/4QB+bJECVxpEQrTWSnzeZDI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjf4YoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrE/A/+PD1RV4kUh2CZr+CI40KK2/a9UuptM9lHXWbIQY+giymGSMpQ\r\noBYSZrJn7U/8XWr/alJvnx10w0KLxYHIaciTmKKWVESQnFaFrl6x/18a1cGO\r\nX1bTdXOb3bHF2Vk77g38xA0Rz/NxIemyqjx+pcFaSGEbMuEI5qo6cXZhqAP+\r\nm4xvFC6n/kXtiNrLkcBOPyJWSoVDnpL7qZ/hbBRUI/kC+DxEYIa/ZMckqjm/\r\nH836weJm024HYIZhEbwjzlC0PnL58VL2+xOGTTsXu7hfq6g96fcL26lG7/jZ\r\n8HBYH5tU/m0xbOupKbjhiXQg9HdQJX0j/Ab3yC1Z9O11ZQJK2559pFBrO5Uv\r\nEs9B42vb/VKc8B/RjSbsUkY0wlRStXNC+7DGPwo4wkX/sYirjaVHPTfpb1jE\r\nzJ74VGHUO1KU0PhawKrCjrV7ZWbsiH7g+IpYWHhuXAcSX4rC4tqVkmfDJmS8\r\nl+eCCVGNU5ngSrdZ3n/Jy2G3sbY7hZ9ufbcBt+hcLOJEzWsM0nYKWYkLfFtz\r\nk2zxzlRVLKwH+6k8qtx5OyDWzbZtZ6ULonBJndavuMs+juzPKgQjULW2oDxK\r\ndK05EYh21XEOh0DoSU5TdBz4Fs1MGUMZP28qhpfynhTysPN8OOqyEaR7rYKt\r\n5JxOHJtQ0lD1lcR+DrhzxV260YUyTA8/RbE=\r\n=ivvS\r\n-----END PGP SIGNATURE-----\r\n", "size": 216258}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.0": {"name": "@codemirror/view", "version": "6.7.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-sI3CngHQlguxAquc2oZ4sMFDgTJiCnKkRcFmw5apqcnNLvQfQtEDIlYvCVl1adJ6UV7ZUV9wOdqkeJ8kz2+5gg==", "shasum": "37a9ffafcaab2a1aa90bb17a83e35fd9a7ef9efb", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.7.0.tgz", "fileCount": 8, "unpackedSize": 876915, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAhieSgfv6Ojvn7JQ7+AjUWSJo1+m/ORxXeKDMcHG0cCAiEAjAiqgraZjQ1VZUoefP2VyhMKw7HKemeaUGcXx0eugY0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkFFBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvSg/9HUyRCJ8jcTegBS6iTJypna1HTLzFueF1ybjnYmXSycWnEItG\r\naS38k7wWwuA68eCBv2jX/dMbqd3qytQFkGzmBxEvWiFp5h5Hg6I/e4QOEXIN\r\nW+1oTdxshC5sw3ILkX8193LIPoEkNZ1aF/5REQpkxXsN1NvLbXukjIGEDtWA\r\nrLbjWvxm0tp/5naCZExyHFLRU/RkTfSyr5LEe6UwWQY/SPFn4bt9et5ZQgYw\r\nIoDvoQKWmrabkrCBtTv4Zdgumi2k3kXhuF8YPEBagU9aN0l6FZ0WgZIZWkHT\r\nLrkTA1k7Nle2fZRSpzqAwLdfe/L1hvA+Liu8ApHywcaztHbGv6YIzBSnnJx/\r\nDMjFa/S1Jat2MCnyUOKAamDa6kqzz300b7z1H2XAWCNYsY/+ooUo7dwRFuCI\r\n0oqkiihhIxUxqVMBJ6dDjpZTlONOhF+hIIjxxnDxxUmc17wLXdzXZ7Gz++Og\r\nC0axXLY7RwZc4lFEP9vpfaahigvLxVKa/NX3QREm9F3eHEX4UXX5yAAD9kNf\r\n521xOcmUhnUHxuoWZdq8a9NxrihKued8zkEkfkHUPVEUbUPP7QS3WYYMyzDn\r\nXoL5Bl0pim3ZxXPiDvHVL8geP+S86kYvTrdmCkhPzvQl6gn7JJ4CiFKOCB1z\r\np4Vbb4UDBvl8YOAmkCPYXNAcvDuDywLJdeI=\r\n=GxXO\r\n-----END PGP SIGNATURE-----\r\n", "size": 218631}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.1": {"name": "@codemirror/view", "version": "6.7.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-kYtS+uqYw/q/0ytYxpkqE1JVuK5NsbmBklWYhwLFTKO9gVuTdh/kDEeZPKorbqHcJ+P+ucrhcsS1czVweOpT2g==", "shasum": "370e95d6f001e7f5cadc459807974b4f0a6eb225", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.7.1.tgz", "fileCount": 8, "unpackedSize": 881432, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHgYVxm9qFd5DZXA3C375hJYPO6fIV5R3D/k3JIWMnOZAiEAkDsz2RACqlE/g+CYBbUMnFV8evN9KLCPc1nYL7QI/LE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlu9oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIBA//TuZiZW7uWZTfns+7Yce4RbmJ3CjHzuakkGmlpsk3qW2nUCWP\r\n/6wHUO4VStjqyCsRE/m7nJY3MaoRGGpugo2qUl7By24GczBBvyjezq/CiF/j\r\nSEaV8A6655Iz96TY3TUrpFS+DkoEIC7w0ipBKrZzkdiHO0yrViBnQUkod0yo\r\nFI3FQofQzQY2735zVwLVh1OHFrlgVZvl+R9YTvTs3kktpT+MKFQ3E65OkAtI\r\nwxZuFuBqzIAISaUq2dnaThp+ah6dbStXr4LgmUhGVTBEHU0omXGiqJz1ifQz\r\nM8Vg5aVZwGBqe1Sx+oeIJsZMgr/HOFnpXnQWFDMxIhoeY1rJzXj9NXgk2Up7\r\nLXIJgdtduNGDniivPuJYVDFk1rkIgFLQCnIPtGXVtwBHaS/S5gb4VOK7k/jc\r\nxCwwQoIII/YLfAw6S9SY6C7AI28jEpWdYvxQjB0GhBC/TcFG5sSxiUmFcQjs\r\nlMO3GFr2kdnmjO62PQxOGtxQda6YK7KYAv0RjcluxvwB5wxWx+f8x5ExTJEX\r\nVwm0klWFPrZ2RFZ32NfctvRSiDCoACdKF6LWoBPCdM3tzNI4/Q7DovRKSZW2\r\nu+jAnFXKhlUMuVtygmTqMpR2WBQGEdTm9LpHz8OyKTo/iplszkkL3U7e4J6c\r\ny1OmNJVSjE78M4oftceyJaaFbmBtPUAVtRk=\r\n=ZzNC\r\n-----END PGP SIGNATURE-----\r\n", "size": 219572}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.2": {"name": "@codemirror/view", "version": "6.7.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-HeK2GyycxceaQVyvYVYXmn1vUKYYBsHCcfGRSsFO+3fRRtwXx2STK0YiFBmiWx2vtU9gUAJgIUXUN8a0osI8Ng==", "shasum": "13830dd6366af15d48e34a5518ab26bb42c440cb", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.7.2.tgz", "fileCount": 8, "unpackedSize": 883442, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIERJPMlaPKv884eZW/3SQ53ebC/B/MpvCJ2eHAemoDO5AiEAuWlq//3M7jOiNVyavxsoSSrKebezU1UzwOx4GdqsqKE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtXLSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpgeg/9GkXNDD+Y8BPzy3UvKPOwt6GM7VmJINFiqGoBX5PWJ7y9q3bz\r\nRAVlb6EnQyL3demyuC9S8q/qMDWwCHFHuDFRyKmLnvCgdNff7OfDTJmTrrIM\r\nFgvcJ26uTEO4KYqE2Wh2PnvQoSlhN+EXroLkrJDY7pnMQnn1ghck9jj2uQNZ\r\n3fIwxOxv+ShxcOBRbfFUxT9APGiphA8OIxbB4pv0zSqUd4l3SPy6bJEs9rft\r\nRcGHL5dOYeN2S2UMhu7FepneOQ5l2okNJaeJfb/9sN6O3Ux1wVqExKy9jYpU\r\nSYSOlAPd/jmSD8Y2kXyiIEAIm6E3DNb+d7FdVRdBFW52DvNtWbN9Kq0th5oX\r\n2d1//XTWEz1tw4ZSYmTxWrlUuQajsxoxdjLr68uA+w6O0G45MovMJCnRJBUv\r\nuw7n6Rx2HJ6qXr3KyWVj7s5KQd1rcf/zO+cnDGweTDfM2mDqt6I5UR0VtIX6\r\ntvOdsQ5EaA6ZHgKv5FB3lcw4KRSkVLLUAsMYfWitK8TQ9SjL/2R+WeHu+Ns1\r\nEDtKL34Erv14GEG6yxAmqLzsn57fefP+/Y5DBJg83oP30S8/vrLmFUOjqdxb\r\nt42GW5ieSWSSWGaZMcFHymYFcJZYPK9XICRlQTMOpvd0RRo38/XO7lVWNN/2\r\nWwNVFUBKoZnqOgimLtP7AVfmNtqhjzGevy0=\r\n=XxPK\r\n-----END PGP SIGNATURE-----\r\n", "size": 220084}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.3": {"name": "@codemirror/view", "version": "6.7.3", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Lt+4POnhXrZFfHOdPzXEHxrzwdy7cjqYlMkOWvoFGi6/bAsjzlFfr0NY3B15B/PGx+cDFgM1hlc12wvYeZbGLw==", "shasum": "be2f9d0e6fc8882fb192041bf78425ca04999827", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.7.3.tgz", "fileCount": 8, "unpackedSize": 883782, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFcJfhKkb+mIza5OSqbCwylZAyG6SUT5npaqa2Oa90HpAiEAqahZKcz4eKlbK50Z+oOhq1jYySSI8l9CNjefSVVe6hA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwAxhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2QBAAmbsVSUQtHWjMoET5gpR8Ct2ZSh+i7LIBH4AaLehhztIYkCyP\r\nMUHdOBnigzQk8pvouUuzPBimvrzNS7hdpura7S7VDDbdZSuLuErsoHUKuJ2o\r\noZ+IKunk6P2rBmKN4y+wTCiPL7po/5KxsL15IJFBzLb0vm6iTJfYtUu744vu\r\n3Bj0ZWQ6jmTFGEs0wlAFWvqKaNDza7irBHKw7UGzLWU2uABrIFoEHa89viw7\r\nJA5nBu9O7Ey295lVz2jQ5pBGwmsD18UhBTOIzqDz9HCJIQzOgEJHOjm6GlLZ\r\nekFUGaes9U+X5cbm7Y+V/5uYnZfPDtN+IxwTtrmU8vkqnB8eZdm6xhzQnGvP\r\nn3j9b6MWmccrG2PzLpkxi1tNofxTD/OYwkomZWB3x1SEFEz6LpYLQNKri3o4\r\njchJTE9KmVzc3k2aEu6WPztEmcDi4pVvgCcvywvPDNikuUUfz6x/hC8YhE9X\r\nIiWPETfewu4wCDx9htDfwxvUDovYamOQa52f4Nly7xJm2Cjmto8tGuR1dY2s\r\nb1HBYxD57UYx53n1gfnxa3zTFJa46RKoeNIELufDwUaXO2Wsc6kFXugCB/xi\r\n4QG93KYyAwMIDFT/Mg0BH9zER6t/e8+DNKf7lsjEwgUeDmDUv7BTZvcbYSo1\r\np50P0rGB8TZZWk9JvFir8BrcSyD/fJSjyJg=\r\n=tl0p\r\n-----END PGP SIGNATURE-----\r\n", "size": 220169}, "_hasShrinkwrap": false, "publish_time": 1673530465884, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.8.0": {"name": "@codemirror/view", "version": "6.8.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-9LR8uwkivHTvZxiYCRreO6VYleZUVbpvZ1+4oCQT2RU0VbpL3nYgO2/1hhgpIWh5mrIj48Rj+AGVFIr2RN4Zcg==", "shasum": "999633184c843692c70e33297b2d28c2d8174610", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.8.0.tgz", "fileCount": 8, "unpackedSize": 884402, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCw6XEDIq+ruNCNV0B7D2CuWV0ApjTw4QxGOywnmKlaDwIgI5eoZwWMHboxeSNuUI92j0PeZRc/JyXc8xKwv1tZMxE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4onOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxOA//WfaXBHeKZoUjRzT5nScbjxOgToJKaMZI94wXrS+kDMtaUsHF\r\nFypesuoTTLR8CQ7dHXqjWIenfIfK8ZbQMphRwNZNtXBicSPOCaKsAAYsicBD\r\nNe9ArrH/a07bA5uA0q9S6uNb8cZbixKdvlEIq14XBjdhI9Q1lUq2xjv9fsjk\r\nmzfdq7KJ4Ls5NQhlgpRb0YNtruAE/jfMYHAt1HDWJhujGB5PFIxUPlDEEhi2\r\nc/6waNZmkgwzHcXQg5/uUyPG4mv4uA8tNEYKpVGSFwKpLikvZHldu679FQ7i\r\n4APCiygq+x01eQdb8kyt25UkzE5u/PIJqf6+jssNCpuv0VRQQ2Ms6yEjr6l1\r\nLUXltRLQBBuYBxjLItzIrUoqb8zA3t6+loCGwCVRpOpSxR+M0OIdnZXXALL5\r\nwRWsqHPvZWzWpHqq7r9oddRR/mM8JJpunJ8dQDIgwS74llFpdEltk08ZRATC\r\nSHo5nxPdPKRo2SjAY9quO40isK993z9IHQ2xF+7pIh363UpE+x0Ph3ian9ol\r\nwwH6E/t5/sziKUwKL7b/QJIViMPNWMt96Mzg2NXlHmiJW4MLLvwnxyiCempm\r\nzRQvlSBr7POW+TeXarHxSJWiHYryKVHpFLte/eqyyl/+qskY2RhgpKWP9CmV\r\nHe9yIT4mksNo7xvkdT79i1N2V0wpyZZz4GY=\r\n=gaAC\r\n-----END PGP SIGNATURE-----\r\n", "size": 220241}, "_hasShrinkwrap": false, "publish_time": 1675790798685, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.8.1": {"name": "@codemirror/view", "version": "6.8.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-bXWs42i1mnBexaktPABaEpYbt4FbJMnlesObDLF0GE8poRiNaRgm7H/2NfXfD5Swas1ULdFgONLLs4ncwHuz8g==", "shasum": "6d4796344edd62670d1f202f9f014959f53242b5", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.8.1.tgz", "fileCount": 8, "unpackedSize": 884920, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFdA9yBCJ1A/Q7O8k1MsMGYQHGTFy2WN6F+Sap03+cm1AiEA4cRBTuoeOz6cxQ4RT+xFUj7QYLgb1+df/feHQ/lU7/o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj44wVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpW2RAAmYyl2b/8TlLThzioKbA47zX0xeF6/ARtTZHTOQKYydc8iTGB\r\nDUNwhgvyT+ogNT4rxZcxm0UUMVXFn7Et6S0OsZkqlXwkdaWntcUoZZzCpnDl\r\nMGVBJuWEfI9LNmLpaa4SEpfYP1Cd/w4csEqlWHFarf3qotP6nLt3zewhRaiZ\r\nPZLKtI14m339LrvWG86XtojWcwEI5pfDLj7TpVmF0foonc1mDyDEMRQiL93j\r\ntG8S1371ZQK2pgHcXs6dDzSGFGCS5YsBB4QHaYkKafRoZ5RQY+6qpvSerQhm\r\nUiZ83TW8fEHrKX2Xrj149w7FrFgA+Om9gyNo5B5u3aHlfUb89SAqSHQpkwhm\r\nPeugPkAAdBqvkvK8xiJhDZeQ/38cBgL/nKoGzUY4gcAawti2HyOBsYNBgjen\r\ndVzhD225HfINjMfKHqC2Pqsglf8wAJnDZolXCPjnjz+7CgGhi0CHVEe7zHDI\r\ndwz67Hrl6nqsOlpYoya2419/XvbE8HbNJ0GbEfLoogeUXsqFtCSnrZGnWjIn\r\nTN6gr8jqQd1aUc1vCrktkpK8oo8rNL7NRTs4oa+gX9ZsaEMsvjZ7DljUVIhA\r\nun8q9rmtMVYNGYl2Dcvzi7oqnwIaIYyDlpKzKi52TzvkwG6kDSfMfzxLa+P8\r\nORWYGhkz4YbfDhRuh7FbIZWB992GyGguiTw=\r\n=KMue\r\n-----END PGP SIGNATURE-----\r\n", "size": 220377}, "_hasShrinkwrap": false, "publish_time": 1675856917094, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.0": {"name": "@codemirror/view", "version": "6.9.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-uFaqE6fBOp0Dj/tmWoe/TFlSSIPdpAzhvATgbq1eAKRkgq3hY79FioZ7nfdiT+24kz68AIWuSZ9hi3psKe36WQ==", "shasum": "39116d84523884198d000153a8cc24ca6a28f2b9", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.0.tgz", "fileCount": 8, "unpackedSize": 890256, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEMDQ+uwYxkXPpoQ8f5PqsI8CeJFu5hh1P02RrH93G4WAiA88Bs1F0/Rsv5m/O3ASJ433KKDPqrpMxPkpyvCHrYP6A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7O28ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm+g/8CY36jJntYXwkngLbZ71TFV2l8NOCu/M8VE2WfESDhHQ0P2mw\r\njtiTCdMHqXthL+kSMmWhwKCNnTnWLjkmoxBng3/uCMEFxiDsETdAebnXYLxH\r\nIGiQsDyey4C+43xlCiqV7BiJjVIhjbKWsW2f34d6nYG2iklxWzK3CfVg/hRR\r\ndWnHm9BYd6lzjfWcHuqhhpGas9TonHsjXSri7VpoS6XAOz+z+0AKTtM3VLcJ\r\ngOEElQxrb8eCyYCLSggdUo2D5xEjOSAlLUlxtb2TcHX/jNcs4SOVuORvMQAR\r\nkR/aBP5q8DDPTpeYaNRzifIuOg4uXuDhCj6mFZodyfjL+FrFol4iStt9ejhl\r\nQ7/qGuDJeLfg/0XpG0kc9SqsbA5+AZMSgLeMDr+DonhqBsGH3Dj74BLirs0X\r\nHfHKSCcrRUQa+Sosysq/Bi9+s8zJ/o8vrio0B32b5X2Y+VoxLqP5r73X8Rol\r\nL+CqjS9eHTUPvBRVPwuIVXUmcbz/4n4V5OCpMZVt5kuG70HlHGcez4num8WR\r\nSQSqI/pGLhaz9SkejlpBYljlkOt2JShgX9iysBTvmbS+9iXYr5s98i5G7gAY\r\nNTNfE1/58NwJ0IgqtE5X4fqSMQxoy+jnBPUDcQBTE7UwuFl2h5IP805wiG9t\r\n8sy1hPnu6Otp5/3KzRNYylZVyFYyFYVB2oo=\r\n=HPL+\r\n-----END PGP SIGNATURE-----\r\n", "size": 221434}, "_hasShrinkwrap": false, "publish_time": 1676471739970, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.1": {"name": "@codemirror/view", "version": "6.9.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-bzfSjJn9dAADVpabLKWKNmMG4ibyTV2e3eOGowjElNPTdTkSbi6ixPYHm2u0ADcETfKsi2/R84Rkmi91dH9yEg==", "shasum": "2ce4c528974b6172a5a4a738b7b0a0f04a4c1140", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.1.tgz", "fileCount": 8, "unpackedSize": 893594, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYbCfJYjzFi63mb94aMe0HnJsoTl5dKNvLmqhj5tuVtAiEA7mp5notbkrlnhTDdqr4ZvIWmZ6fSyoO27KzTjkWiecI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj78XwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKoQ//VLm6d2LM09nbEeVUK8OrcPwNDhNzRv1wWyqrMLhPJm8/08jJ\r\nL5LQt9hlhr/DLexfI0JAiCfb6DRpMzsAE9x1Lcz3xjigrMTTwpBgskpVEfGG\r\n2yBOHazUnMRL98FUjh2Fw3N7eMxxlEylt/pifFVj2piSsLBGmEvmMvPQDYwi\r\nGXoC6/Q/EzzUi27lEm5WY6nzW2WqejaBSpU31na8WIhip2VRWYQjLuyF6Xw0\r\n0rTCzgwvsrfnIVge/gdJYGCj4zMnrVIcI/yjzDqiiNJo5vHewQIvhbP3saNo\r\n9evd2Fr101ngSYK3KE2Mj5dRIhrAGHfOgsiIZFKyAh8ryHqPy/zpcjfAGQRX\r\nmN4mjlt2J12vVI8+Ck2cu0O8nWwYeZOO+pZRHE+zjQL6zH4zEq4OZ3dSkBZl\r\n/hEeh6Cs0iz5eNyzhzDiW5xYzXGW7u8AQXHEtZuw3TXaPBwuWZ0t5pSRQwhK\r\nU2hTBJlcdp0vqOQqqdbPc44TSV3zVBpCXu8yqnJg/cNIrHkGbdviQWvG3M2+\r\nZxQpZ5AnhpuX6ruoKb5If9yjuoZucsA5xO/PVXsWZOm0itR6rgIhYcKUC0Oz\r\nr/pAY21w2OBIL/UD2WmqsdLN2l06/xfF3JguRuwlDvS1h3i9ayTmvMm2rP1B\r\nI6oHMw7pWSXGOXC9xM5dvbyZ/jQ4U10ZDwE=\r\n=7Vao\r\n-----END PGP SIGNATURE-----\r\n", "size": 222231}, "_hasShrinkwrap": false, "publish_time": 1676658160311, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.2": {"name": "@codemirror/view", "version": "6.9.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-ci0r/v6aKOSlzOs7/STMTYP3jX/+YMq2dAfAJcLIB6uom4ThtrUlzeuS7GTRGNqJJ+qAJR1vGWfXgu4CO/0myQ==", "shasum": "c8ceb4ea36f17bb20fdecbe7534162d4b83185e9", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.2.tgz", "fileCount": 8, "unpackedSize": 894432, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG9rbEmU/nOcQenaNO7sbSLb1qQy3VtKAsokWhi50b5gAiEA4MpQwDyAr5540/3tRFB1Zev8NIfEhG90JSbmF7FYuj8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJFlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV1BAAlgk8UxJyOUFZXmdLMaHpO75gOs4r2XCUfxy0+7O1lxQ20t04\r\ngCThBnMnLElt+FKA/W8sUpwNfXuzDC7oRCFCNysyiKp0PHngw+pNp3xd1CDA\r\noNz0Bm/57uTOh3FfkA92taZYXzhjDCwrY0tsFrNnpaTyCqMzeFNYThvXqGLR\r\nbdalJjhJB43/nzLGFvi7Gb1IEEp7U8q7QipJu8gjdNikuBDm3+e5c8Z1JDWb\r\nGwKu5SVGYC+w7RrgthJ/gN0mDicHC4xm/RRt7SGjAGtETpyL6qLRCO27AaKQ\r\nbq8ms+FLCdqZw8xng0p4wqv48iqdo2qLjHGAMgHIIvylQvvTMuhTFpIoAXBW\r\n3rUtPrhVN2Ld39i0zdVQA42XdoNciezyKtt2E1+P6Pk21oHeexsx8RsTb3uJ\r\nqj8aB6YTetTx+3F54gs0+/aQzwBIW067tvm46604uy/ym8Y1gqkGGK0PywhV\r\nJdotI8tTjoBC7L/DmJvrheh/+99wZOxNclocSBXYmJltgVIPYoNptnnSy5zC\r\nAhB/PvN1TcuhL9ojdy2eq1HmA/gxiyj+r1Blp9YusAkNp30fsjsvvxiokPfj\r\nATNJiVuD14A/90FAd7jjVKjFzjRjmPXKblDuGocK1c4WAMAzV3oqw42or6he\r\nlP8M0MQE2eHjfguNJba+LDqn/hf1723dmKk=\r\n=nj9b\r\n-----END PGP SIGNATURE-----\r\n", "size": 222443}, "_hasShrinkwrap": false, "publish_time": 1678283109574, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.3": {"name": "@codemirror/view", "version": "6.9.3", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-BJ5mvEIhFM+SrNwc5X8pLIvMM9ffjkviVbxpg84Xk2OE8ZyKaEbId8kX+nAYEEso7+qnbwsXe1bkAHsasebMow==", "shasum": "4475dc0ff5d9a55f8144ad7484e7e17416861cce", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.3.tgz", "fileCount": 8, "unpackedSize": 898573, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFM2kOJpUjz5tzjcKF2wVo3TRl9tqPxr3k3lquXxThvlAiBsfwci11aAy0rIGFQBF/5SWDL2dFtOt2GMWiJODRDoTg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGXKjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYvxAApAX6jJbfaATNFKmfelRqm3zvy/k2t1Dlbj16qCE3ZtUMPS37\r\n+/CczRNMzBLhFo6PA8RV6+PUMqhS95OLPyIuiz4rXqzDNoUh3FGWrGsKDxwo\r\nKFa/tTJJQ2/AhPTiiUiI9otafRuIhdiOZIcmxWxOsv/V66fuHENBtwwck6fK\r\nZ3n+Qs38yOoy9r/w3ShmEN1BmK4MlokEKsYepfihkctVZPggGvq3hGlEi8Rc\r\nMIF646rIcxVlucw3z5w8C0X+By3EoQzk1sZ3BvuGTkh4f42zzc65lvC7BDlJ\r\nQ5ImfXHsZ/Il6ifqcVdFhM8OiV25E1DGPy1jbliok9Q3pg8IPY7dgoyaezkQ\r\nPg0vQN8xrP5RKrrkJ6CbRdbHt/XJ7aLGMjjEm2VfHaUfuNrVDaHkwFopaZPU\r\ndc2E4K+ImjyFjJs1AcolZtZi+C/9bjz6REXtgwKosn9BuiZZYLAC1afLLcl3\r\niu+shqdRnMJolF+JqletcCL+10tU+5ZimQBNDfm9efuEMHfXAv5Y74UmsRPg\r\naU1z2vXELKeIhgRueghfZ9KRU/dog/SAoiEC4jAzm53JECiiuQlghlCmlIwt\r\nFiFSyBFyVRY22wBelHjWqvwGx9UCKIFVFvxhBsmZ+etVAveOyIURsfqf9pLN\r\nZCcR/PUj9DzpEZoSv94mbA55Bk4xPp0fp54=\r\n=4/3C\r\n-----END PGP SIGNATURE-----\r\n", "size": 223445}, "_hasShrinkwrap": false, "publish_time": 1679389347662, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.4": {"name": "@codemirror/view", "version": "6.9.4", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Ov2H9gwlGUxiH94zWxlLtTlyogSFaQDIYjtSEcfzgh7MkKmKVchkmr4JbtR5zBev3jY5DVtKvUC8yjd1bKW55A==", "shasum": "2cee977dd0df74d27ede6beb01f9de20eeda8f76", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.4.tgz", "fileCount": 8, "unpackedSize": 902446, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxbNYmXCafNQtXMfFDTRtmjV68HuMtUqKA2vza+ZrUQgIhAJCEVHwnETAe5C7+y7JZw6StdVKJUXpsT547LUIYmdoy"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNU3lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4Pw//SaojBsSriFNa/pFpMZYuNe0b/4s9ugrRBDViofEtYLyU3qyz\r\nLVatgtdy1HFO+0oA3DdRrv6lanRkYDObLE/r7sbY/srrtEZLENGWxvvtAdi0\r\nWaFwebP8/lZGgbh5O31FIXxpE7Fu/7l8chqWl7m9WgtMg3j9xJ8LricBZOSk\r\nGYgmVPnL4OBgOoV40FGQsXAYCfCPD/DMmq2QVXA6EM3xlaPtA2U/kFPkGJgp\r\nJVuzQwCcCoCZdK9PZkb2o3kYBe/UmBLqWOusQD2/E1IhqDOa05wfl/FZ7Uws\r\nOzzxrdO8OGuEVdl+rIpGwm2tyx9SwPC07GDIJ+g5JytnyWUFXlONtqrHiaXM\r\nySfOamp0C1Oy7BkqEyZzR7bSdsK9fm4DJ+fVto+PcuFJvuZO0tieLTuZr6Iq\r\nq1UAH/6bqJ/e3vqtFePNR6T5JkaA8FdGZ5uxyBjmrSJF3kMVVn3x8fos/nHL\r\nYPRkiFjLllGFGzip4xTciEkP/VHA/qNxWGauXKtbp8iCQSdPUlKbuNMHsQCc\r\nnSKL0T1bNvMrvamj2XC6/FDv9JYa4FXS66LSYq0yHRcTje9INvX5ADEL1YXy\r\n024M8eFmdX8m+phDc/scFNfo/tQ9lE/s7jyWyl0rnlhQZOuyJgxQUx8gjFA2\r\n919vUzH8TlGd6ABOuTofQhK+YcEypwPFY5Y=\r\n=26H/\r\n-----END PGP SIGNATURE-----\r\n", "size": 224236}, "_hasShrinkwrap": false, "publish_time": 1681214949595, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.5": {"name": "@codemirror/view", "version": "6.9.5", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-IZwGdkn8PDyMkGms7bCMpFuTCg6+6eCZNgLOe9xQL4RYvMw1yYLviH+qoAqsNoYGpDt4HhHoW9v3FmalobFoKw==", "shasum": "8149b5e93e6fbdff2573ed205a53be311dfe80ab", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.5.tgz", "fileCount": 8, "unpackedSize": 908463, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAZl3cQP3sonBafk+nikY3E8Zw0HEiex3qUp2pozi7hwIgTBwdk5v2y8xZ1QNaYFmE0PHMTfArEp7oakd+NWgT/ow="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPWzJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/fA//bY9gBBG0svRkJbzJ4mSNgO/X8hf6mdQ2kurVOwGtTxcdsPfz\r\n8CI/E1dc0dF8WRAUAuPlHpxQHcxbWs2PcECxQwc3rLEvoznjinPQiqEPGV8E\r\npxJPh3M7GY/lUwkZ6X5F6O01Lyxb45IFAH+hg388b3BwnrcKzJgKVVhI1GHy\r\nxhlW29zgPYUcSpCPmJPlbhB5wYgF1L08LW7M0CvfCVT4rYcFDW8dUsDSZbxT\r\nMDdpUgwJsGtPYjFTdwMa4BlAaOFfvyFTPLfXStp17wxbKU0zMM71iaYKFbWV\r\nrx1lNVY4dh6WN61u7g6EI0Ei3W9arEpp/CIJdVMbXDF5/6yIxX7HeDtPkoY7\r\naJ8ursJbxBR8yboBU563RIrTuX7cVeLueYNSw1LrcvIbhH2MJo3HraDlQGDq\r\nsD4RukO8sEAQjDlrOdNrhSOsTTFVhayGQljAhRJzymtwLICQR/YaTF6kjnuG\r\nBFX7DKr1qCd2loAVFpj+CpaGdscgPAVjcq08Qs7V1KB3U24Tx/mwtvGrLgWa\r\nuaVG9fVIZW55bNH7/gniMfzIuo3Xe1kq8qdcr8swAt4kJjZ5NvAXW82E7nkP\r\nAItn3RIkDIlxVwzJKtT3pJMGismEKOEef0Q64uDYhBWheJ2/0248i9dryJai\r\nyxGjdJeT1136K/x+jCN64IsHSgCG6RMljIw=\r\n=J7Ju\r\n-----END PGP SIGNATURE-----\r\n", "size": 225238}, "_hasShrinkwrap": false, "publish_time": 1681747145893, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.6": {"name": "@codemirror/view", "version": "6.9.6", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-g68PxS3RkHpxfYS6DTWCy1jeA5/oHzmdWjMVPkOzqQyxpElHEcPncUd4EeMVSa4okt0sS3hNXVaRnJqO/7MeJw==", "shasum": "6bf302608e03566688d5138c50b38d07356e11a1", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.9.6.tgz", "fileCount": 8, "unpackedSize": 909609, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDh2bil3Eek9Ygtj3T0W0niruHblfgA4uK5Lq+qV3mafgIgJQ1ZZEOkichR6iTqUkHXcklDnP5t5zHSPmEDMy1+SEg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQv6QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtDg//XgYFvq88WZbEaO3acjuN99jkZeZLfzIHcgi3okW2FHjFPcJy\r\nJ6MLzlcCEt/Gy/vLtMg+bYCXwIrULnlS2uk3cq+3N5TnjHQpo/ULBAO0QtM+\r\nxyhjDccLFBpI7KCh2OyAEkaQrqoTR38T6DnNHJXV3Oj+16oBDW6L2bQkkFFu\r\nKvO6QETE9q/r37mqCSiOSj+B00AUA1P6V8a0ljYyzeVd3GT9AMuJ2KXPMpXc\r\noPd8Ar/NVzsxeP22w/eijMxaNM0iEmmipBGEc15+CC9SnyDt7SQNJIaKfih5\r\nOrXf2K4dVqIVFnhifrUf9VcNHrhJSq8/JFlKXZbDhKgdHH2+P+CZO2GOwzVq\r\n1jHNxFMnGngzYaZ5UHJiVSzGwJigcgnTDyZiI29to9G0TS0NWLS3rXI88VMf\r\nLMG5acgPULQHGDygXniRtMUa4lU0tmBnW3tclFqrxApTGg5IFaazmCZGHJhv\r\nZ0ZPGKexveb9DXvdkLnJJBBSj6l0ea3xsoeq4+ZLZT9IR6mb6kqcqUwmrCa6\r\nZU6ht9OTSoNwdvZxltLNBan0Q0tLxon+FkRd01gwOJZB6/uPhOE4sBPPkS4M\r\nfHq9FRN3eUZynP9+pVBVmskcQWmLKbjFbVD67lcokmaRWC0q3FQs2m/GGQdd\r\nVvDmQmepVSBJrw4bqgKYAEtlMkNW8PQ7bxA=\r\n=+3er\r\n-----END PGP SIGNATURE-----\r\n", "size": 225565}, "_hasShrinkwrap": false, "publish_time": 1682112144071, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.10.0": {"name": "@codemirror/view", "version": "6.10.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Oea3rvE4JQLMmLsy2b54yxXQJgJM9xKpUQIpF/LGgKUTH2lA06GAmEtKKWn5OUnbW3jrH1hHeUd8DJEgePMOeQ==", "shasum": "40bb39f391955db8960337a9e80fd7564f8915e2", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.10.0.tgz", "fileCount": 8, "unpackedSize": 909544, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbifW2e2uxH8YN8vIrtj0t9wjwOsFMht2jndMOISmsagIgSvRkuPc0U/eh0DHSfBLqYbFTBexwg2ECl9TCeMHvTaw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR6e7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFixAAgGpgCjFDy5DyNysVcmY03Mw+O/RP9JHxnyqQcQ/hklXV/NcX\r\nPrLIMX7nyJimw3F4vG9Tu/gI2hLLrlvg9m/Zbc0p2EUc8weIUylHNR5SUft6\r\nhhtocfZdK/oBleQBpwDrqNNQVewbP6SynUzOMqJjDbYC/H5w3xyAAm+1UbP/\r\nmCnDj+3GWRXDKSrHAMSr0wdFdAEajHBPfoXBW6wO2OpqS1B5k/HX5Y+Wbxzd\r\njaKiuKn/Cls7TriL1xRwd1GWcSn8O21+V9d/Y6UpynV/XNKEMNinIDn1meUs\r\nFuRHg/ZO44oJ/tmsd1SPsOSvtM6Z2F1FEf59cY6slufuZ3ytEVJdjhvYxMKr\r\ni04Ipq7zvXFHQYPICQsm5dAnhL3XFBvLa/ZwK0cwgsQrNiIbZyZn8UCzUdw3\r\nzl9wiPe1QZtebYp3XFK5Yu+AdL8Fs0efmCWg8CV+88wK38DIt5ReYw8HENTL\r\nr/otGOb65ItOjVeas2HZOGRE7fCNAEkRwE9uVfpcaB4mS+8UxWZP/GwUePBy\r\nTe9N85fAA4NSUkaXrz212PdjOLqxVA9xz3ON29elDmJ7i93Z7osCcR0fQVv/\r\nvguk8j62NOm13U7uOQw2zX2vP6m7vC6Ze5NrqY/gwbm7ikPzHQ91OFZAxESU\r\nW+DC6p7Y0m+aeJmgCghG30TABuAOaTBVXdE=\r\n=jHzK\r\n-----END PGP SIGNATURE-----\r\n", "size": 225609}, "_hasShrinkwrap": false, "publish_time": 1682417595188, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.10.1": {"name": "@codemirror/view", "version": "6.10.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-Q3FAmkajqcoqBUlfrlD6p58lnuffYxS/6ImIrsLd4SZOZItCBysqAXvQ/OqWxMCyVuDoh5KaNErl9/FDlWM5IA==", "shasum": "b840a04db8941084ca88af1552c4678cc2980f37", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.10.1.tgz", "fileCount": 8, "unpackedSize": 910261, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAcBcL9DWR8W271rghDwmmdFsMTyGioX5+QJx3aASb4PAiAkQW8nYLw1SivV9VDy+knYv/larloPJHVRZHQMPLEQWw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkT+w/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEaQ/9EWSwU1IjQ/Mj1gkmZmKXIAIqndQLg1QxghC2uaoH7bCog49d\r\nPK+3sugCIUtJWKyrjqlOJ5U5ySRxq3W0B/zhIn2gJFZU0naxCJVf192tDtrM\r\nj6iylwX7xROduTKty2HaAPLwtFIM4WFePmn2RFkMUyBK2xxNdg/MJmtu7XRp\r\nqzNf2rqksGzJmkPo6dAt00FhbCZgSV1nVaHdMlkE1EZYYlhND4+ZR9eXxE71\r\nmjjj7odUMtMgGV04EV5QwXKokRT6GZd33GxuWRGp8FpFLx3Etrul5Pgh0cGE\r\niSEPuRsUuv/lol6gNGaCQwxZokmH3t+lDWKT3rW9K8z43EbglKUYXs8nVQME\r\nHhpy07FTpcjGYLFXL+0T1prr8b1jnjkyDSc9CY0XoZu8+IE0tRfvXvND45xx\r\nXpicdxYoGl1TknIToG7dxIAC0oDGbyXSouZYoZekJsOB/3hFYWiTbedk8qHi\r\nbn/+TK4HD/PJqqr62iO4FadIN44yJEQn0C8JImAeZAOIti2li14NnZuKQbtA\r\nTS6nn6fGPWSnw4+7wj66m8ZnEPSqBvmqB5Nq2gUIVs+SGUnQe3vQH400TnIv\r\nNGRLvSmILiqKKnEsZdBDbAJz2KLelGltqZ4jaqq8tn6YiPauGhczm1wbGo2W\r\nlW/L3N2E1F0hV9rPY+UPGn6IOGRh1vAbfB4=\r\n=l6/P\r\n-----END PGP SIGNATURE-----\r\n", "size": 225770}, "_hasShrinkwrap": false, "publish_time": 1682959423131, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.11.0": {"name": "@codemirror/view", "version": "6.11.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-PRpPRkqMkAKKxEuiUBxapE0YR+wqs9At92ujbJo93PwTZ0jEJDzx9wahrDcXEhQ43Pe0RK9DdZMLWrt+QN80DA==", "shasum": "7840cec0b983c3ac36348fd005ce405a8e788e0c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.11.0.tgz", "fileCount": 8, "unpackedSize": 912982, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAnAW/4Mx4oKSYOB9YSCun8nSQzf169hrcbLQ5fMQzWPAiBuMD6GuFTSnDx+Jy95AQm1JGInPuetdGH3eFGjhMf17Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUkHKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1Fg//SVdzPgp4uC4k1qMwb0SQL0wCoct52Ou6K5CRFcX4TmzXxHgm\r\nmdUM7MLUha3BdxRrSjAO0Dox8hXtWqQQggr8HA3HPBs6jVixzZWNd1NWbQ8M\r\nVzK57QNdwVcLZrThL1Gq4p0bB3uLFpJ+RlDuD4fESYoubs/E7VzaVUcyh+xG\r\nvTGdm+EASVH7nJQaorZ+w+e9mp5z50exoy7R5+rkZ6DgSTT/UbZJrQ63i6WS\r\n9z0Xbe7MEzYGN79P6+y/xZ2Ahn/D8JTZwdt/IUz/Ys9oOCRW6Dk8APnpk8fv\r\nQxMUYb8lHqGlVeUoPgfClp3Cor29ddY1gLx2eSHe6JVV9LYrIANqBbDbf6ZA\r\nWu9afm0zewDZ9SsSdANsOvvt4HIlv8xH4y1CU0bqTO5KOpZVTf1uOeo57wRy\r\nMyFV3sLCxHFxJMa2DO2qpKBx4yg25vCirk1wobo7IH6ajVs8nFaQcadOeLcf\r\nSqL92W9Z1vtRX2xBJQGcg1LId2p4E6fFslDIad/jW6tuWvAAUsR7XpYfLJw6\r\ntyXIRyTf2PRMw2TXmOH+EnJwJpnQhrECQ+RskKqhKxBJF4eOd8Tzz7CwSBxx\r\n4yw7YWnEP1TgknFvPZAL/Mn1uZt7Fg8wiakGmXqO6f5WlQtSYgEA07xXewlc\r\n+M98qqHGaNG+YZWMRArA9WyfZsdr1YupSME=\r\n=YyHS\r\n-----END PGP SIGNATURE-----\r\n", "size": 226342}, "_hasShrinkwrap": false, "publish_time": 1683112394315, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.11.1": {"name": "@codemirror/view", "version": "6.11.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-ffKhfty5XcadA2/QSmDCnG6ZQnDfKT4YsH9ACWluhoTpkHuW5gMAK07s9Y76j/OzUqyoUuF+/VISr9BuCWzPqw==", "shasum": "53626f8d6f5d0a227b045230027ae5471b08f646", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.11.1.tgz", "fileCount": 8, "unpackedSize": 913220, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICEA3HWyefEAcn8no4GHy6vbE1WwAU4jgo99nthN3ye4AiEA6haGx+2NMZ53iK9tNNGudeN/fKPJYinjZs6/Ufl9gFU="}], "size": 226413}, "_hasShrinkwrap": false, "publish_time": 1683627860872, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.11.2": {"name": "@codemirror/view", "version": "6.11.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-AzxJ9Aub6ubBvoPBGvjcd4zITqcBBiLpJ89z0ZjnphOHncbvUvQcb9/WMVGpuwTT95+jW4knkH6gFIy0oLdaUQ==", "shasum": "964a746119e6d07c75fddecaf90cb463ccf59f71", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.11.2.tgz", "fileCount": 8, "unpackedSize": 915268, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVviLev1af0fhyStvIHu2W0m7KtSzY9GpOzO49vbm3xAiB11CBy8zTf0At448ROnAH84ySgHc9v68/WEa0LjB7Pbg=="}], "size": 226978}, "_hasShrinkwrap": false, "publish_time": 1684004424900, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.11.3": {"name": "@codemirror/view", "version": "6.11.3", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-JInirTUhmwDOEZZHcsx4/wfnBgJk0q3vnDZh1i2k7W+t1SqMugBCO/+J5zgfjJ5rXYFjnpBG9Dkz/ZMSn4bNzg==", "shasum": "5f5b4c34d0fc0672a4d2b5ad5ba5541268009033", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.11.3.tgz", "fileCount": 8, "unpackedSize": 918462, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjXwL2DW7dSCCFT0XMmyQSQD9ztfqkdoGYQ4Hwe6qYVAIhAJmEXW4tK2wF2Da4y4LIwzbZCnymIn1MiyX5o8PwXSCt"}], "size": 227710}, "_hasShrinkwrap": false, "publish_time": 1684309773206, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.12.0": {"name": "@codemirror/view", "version": "6.12.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.6"}, "directories": {}, "dist": {"integrity": "sha512-xNHvbJBc2v8JuEcIGOck6EUGShpP+TYGCEMVEVQMYxbFXfMhYnoF3znxB/2GgeKR0nrxBs+nhBupiTYQqCp2kw==", "shasum": "e9d88b82f607373ec6549e38a5a0651325e4eb2f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.12.0.tgz", "fileCount": 8, "unpackedSize": 918560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC6Im7deU7it9UIejjQqC3TurVNk65Jwd//0ObE5iATVAiB3jY2ZMTFUEjEEmShFp55s1m1TRAanXfZKWzN+X4BUwg=="}], "size": 227740}, "_hasShrinkwrap": false, "publish_time": 1684392715828, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.13.0": {"name": "@codemirror/view", "version": "6.13.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-oXTfJzHJ5Tl7f6T8ZO0HKf981zubxgKohjddLobbntbNZHlOZGMRL+pPZGtclDWFaFJWtGBYRGyNdjQ6Xsx5yA==", "shasum": "a86c3c5e588a7e2fc0e8043c8ae9fdcc608f5eb7", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.13.0.tgz", "fileCount": 9, "unpackedSize": 996991, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGrZALHgs+uObLkSsxzyT/39vmQaV/5ikbM0Y/LvbRvXAiBQuouruAJeGITuxgrXqWSr9mXpkVG4kX1fM6Z43psP7w=="}], "size": 249724}, "_hasShrinkwrap": false, "publish_time": 1685950594999, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.13.1": {"name": "@codemirror/view", "version": "6.13.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-O6uW3qOfycEdoD1RlGXb208hEiE75tIZp7lOznMUbAhUf+X/UCLlq4hsxjdRnoM+347b6VJu6e5erWuGMemEUg==", "shasum": "fae7a77b0b46fa5e66e250044427b28a55d5b655", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.13.1.tgz", "fileCount": 9, "unpackedSize": 997251, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH7QKuXrhKDXC8GGJMt0tx3/FCo9+mCT8298O/fo0K8AIhAO/zGrm42OJkt6ht21QvQCkq6C9ZybKEJmZ4XWayhUyJ"}], "size": 249778}, "_hasShrinkwrap": false, "publish_time": 1686581779878, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.13.2": {"name": "@codemirror/view", "version": "6.13.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-XA/jUuu1H+eTja49654QkrQwx2CuDMdjciHcdqyasfTVo4HRlvj87rD/Qmm4HfnhwX8234FQSSA8HxEzxihX/Q==", "shasum": "bb13e26c0e3ece2f9598d76de9ae9929a658795f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.13.2.tgz", "fileCount": 9, "unpackedSize": 997504, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBYYY9HMQUtwhIQHQet8uz8EFVftpk6IIt/Z/IQhfcFVAiEAyCI/Q161ExS05xIOb5w9cCqGAkwV179maoaDpF5ltck="}], "size": 249852}, "_hasShrinkwrap": false, "publish_time": 1686661735321, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.14.0": {"name": "@codemirror/view", "version": "6.14.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-I263FPs4In42MNmrdwN2DfmYPFMVMXgT7o/mxdGp4jv5LPs8i0FOxzmxF5yeeQdYSTztb2ZhmPIu0ahveInVTg==", "shasum": "a8ecb0216d6f81aeb20bf8b0cbbc7ed563cf0777", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.14.0.tgz", "fileCount": 9, "unpackedSize": 998778, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBaE+mD4mhhzbcolGfET2utMLa+CLOlaZEBMifzV9IaJAiEAyxdI54rV/FBRxPH3XYg6XTd0eTvJR0RzDEj81UvIAbM="}], "size": 250227}, "_hasShrinkwrap": false, "publish_time": 1687506134546, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.14.1": {"name": "@codemirror/view", "version": "6.14.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-ofcsI7lRFo4N0rfnd+V3Gh2boQU3DmaaSKhDOvXUWjeOeuupMXer2e/3i9TUFN7aEIntv300EFBWPEiYVm2svg==", "shasum": "22cbc9b95887c996d1e886e6a85a8bb353cacce6", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.14.1.tgz", "fileCount": 9, "unpackedSize": 1000226, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDExf0vB+7Qlu/nsYjUAE+PRmaj/tCt1KbFmtzLwayXUgIhAJlmSaMdAulLEOoWvzUAed+j+0sF5bB3RG54FjxvZVpf"}], "size": 250626}, "_hasShrinkwrap": false, "publish_time": 1688625912382, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.15.0": {"name": "@codemirror/view", "version": "6.15.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-3hPeBoD4+pUpzezhKSoARQyFjUP8g4zoI5AIy72+jKqWkE6fp0KV3H/dyTxNfig4jyW7x7ypp060/etvv/4yuA==", "shasum": "87b717b28a7beca560b22dcc0a09fb4d4e360c21", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.15.0.tgz", "fileCount": 9, "unpackedSize": 1001109, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCE7eYD7PQEx5ipx3ISu+yTcFn/TkWqdv36NSV3y3GnhQIhANe7s+yG3e5LiLX43M+ZXEVwO0dfBbdXE8YDIMOv1tIO"}], "size": 251233}, "_hasShrinkwrap": false, "publish_time": 1689600592764, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.15.1": {"name": "@codemirror/view", "version": "6.15.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-AFs5HMvCltKoqUXZu40tHoc15BOB+8IjvxAhFHyMUgVCN0eYlUKUUUuxDabQ62eezWF6snOLCAur8nKX6V5enQ==", "shasum": "8008bc43054b43ecd0a642712ab4369745ca8e90", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.15.1.tgz", "fileCount": 9, "unpackedSize": 1002638, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDw5N3v9s/NzKoA89Ul1q3jniXs6A8TlbKswdldpQm9FAIhAKXkUpNFG3rVP73ijYH290fSFESS6HSmXXPfymf2FDxG"}], "size": 251524}, "_hasShrinkwrap": false, "publish_time": 1689682237356, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.15.2": {"name": "@codemirror/view", "version": "6.15.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-vpGTfWhgPe1NPj+aX7TRBzul8Eo1zyyvhMFfnY9CyVvOhqUMxfBEUMcGr9ZUan5/H5OO1H0z+YsdzT9wXeo0Lw==", "shasum": "e702594ce054a3d234c65451b5905524636ef79b", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.15.2.tgz", "fileCount": 9, "unpackedSize": 1002753, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFij3fn41FI4QkBDIhp+X7aDj4Pej19g4bzPTufsxa/QIgWychumlI8dx1s8Z2uAdKsQ950bd1riSyzECEjYST1N4="}], "size": 251524}, "_hasShrinkwrap": false, "publish_time": 1689682880222, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.15.3": {"name": "@codemirror/view", "version": "6.15.3", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-chNgR8H7Ipx7AZUt0+Kknk7BCow/ron3mHd1VZdM7hQXiI79+UlWqcxpCiexTxZQ+iSkqndk3HHAclJOcjSuog==", "shasum": "b26dac3e1812821daa6da25f59ffb26c9b9b75f3", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.15.3.tgz", "fileCount": 9, "unpackedSize": 1003055, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVdQKNfs2URbubtVPQX+uDTN7IQazdFnyZXA7P4819SAiAttUnS7zGd+UQvM42ad11LlxUICvG26WvEK3TjDLqwbQ=="}], "size": 251571}, "_hasShrinkwrap": false, "publish_time": 1689699519109, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.16.0": {"name": "@codemirror/view", "version": "6.16.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.0.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-1Z2HkvkC3KR/oEZVuW9Ivmp8TWLzGEd8T8TA04TTwPvqogfkHBdYSlflytDOqmkUxM2d1ywTg7X2dU5mC+SXvg==", "shasum": "047001b8dd04e104776c476e45ee9c4eed9f99fa", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.16.0.tgz", "fileCount": 9, "unpackedSize": 1006661, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFFozw9/AyhjCXBYdPGAu3i3v4+Z8hFVp7VedkY+TqErAiEAndQOJyGUhufdhpO8eGKcK2qiFMXXharpacf34QrS4PQ="}], "size": 252392}, "_hasShrinkwrap": false, "publish_time": 1690833749977, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.17.0": {"name": "@codemirror/view", "version": "6.17.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-0yVhPSyKWwYDy6Xwd7aDoj8ZXtdoHwC7El4z1/JJpIimrtDR5CVGY4lvQ0r2hP11ezB+eCHexZ6Zbz6rPUe06A==", "shasum": "9e24d88aa41b82669bb5ef26365b0c15916504af", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.17.0.tgz", "fileCount": 9, "unpackedSize": 1035399, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHiZPPNUKyvlR2i6y6Jz5qQJP7YM1nEtCzUnysFH0+qWAiEA2tT2l/NnpB9rVSvt0k7a/CITAVuKml3yQKNpIVLFB0o="}], "size": 258385}, "_hasShrinkwrap": false, "publish_time": 1693231597792, "_source_registry_name": "default"}, "6.17.1": {"name": "@codemirror/view", "version": "6.17.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-I5KVxsLbm1f56n9SUajLW0/AzMXYEZVvkiYahMw/yGl5gUjT2WquuKO39xUtiT4z/hNhGD7YuAEVPI8u0mncaQ==", "shasum": "2fab256eea6d5ef1d29f0885c18adbdcb462c2e2", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.17.1.tgz", "fileCount": 9, "unpackedSize": 1037746, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjdUvrk+B/uoVOSn6WDQZFJseYOP/0zpq5fUXu3DJ3KgIhAIjQadhdN+z41iQeLCXb9hleh7Htgfojlprhs655+b93"}], "size": 259133}, "_hasShrinkwrap": false, "publish_time": 1693497234001, "_source_registry_name": "default"}, "6.18.0": {"name": "@codemirror/view", "version": "6.18.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-T6q1yYAoU+gSWfJFR4ryvDQcyOqS+Mw5RCvh26y0KiNksOOLYhNvdB3BTyLz8vy4fKaYlzbAOyBU7OQPUGHzjQ==", "shasum": "5c02f0446c23be4dd80c7103bdd8ea35fd21b766", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.18.0.tgz", "fileCount": 9, "unpackedSize": 1047386, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAwVYh37d6pTz9Ewmw5N/IAts+zppYPMBFXJrRmbMI6AAiEA5f7EVIVGvGnTtV+jZp7l5fxZYzTBdcpvaV9Shs6AoAE="}], "size": 261675}, "_hasShrinkwrap": false, "publish_time": 1693911263120, "_source_registry_name": "default"}, "6.18.1": {"name": "@codemirror/view", "version": "6.18.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-xcsXcMkIMd7l3WZEWoc4ljteAiqzxb5gVerRxk5132p5cLix6rTydWTQjsj2oxORepfsrwy1fC4r20iMa9plrg==", "shasum": "816782f19354a688b6dc33b30ab25570003f1089", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.18.1.tgz", "fileCount": 9, "unpackedSize": 1048389, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEzP6EGkQGgdciOy9Bj5E2xTp5ztLdmSx8uj63pqeCTGAiBrOz2GvZufHyV2lzszh8/JglZdqszI0uGHVGDb9YDbew=="}], "size": 261992}, "_hasShrinkwrap": false, "publish_time": 1694429153664, "_source_registry_name": "default"}, "6.19.0": {"name": "@codemirror/view", "version": "6.19.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-XqNIfW/3GaaF+T7Q1jBcRLCPm1NbrR2DBxrXacSt1FG+rNsdsNn3/azAfgpUoJ7yy4xgd8xTPa3AlL+y0lMizQ==", "shasum": "24b35e3797626a1b944ecd0a921126f669b2728d", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.19.0.tgz", "fileCount": 9, "unpackedSize": 1050741, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFx1G0zfr56w1trClOH/pgw+LOCtFZRUWNuo9oaZkZTsAiAlPMwzbUc5pQNMEbNtv9EXYTQW8RYDagubEIf1vVCIIQ=="}], "size": 262613}, "_hasShrinkwrap": false, "publish_time": 1694669911166, "_source_registry_name": "default"}, "6.20.0": {"name": "@codemirror/view", "version": "6.20.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-N+lSr/UybTnSnvSLjzbAWe600x3DhBj3Lerk/BMllB6wDMzgW6OgNI/5eOGnbBAwY8lxxyHQmv/R5ER35nlVmg==", "shasum": "6e159405625d5314b35036f255e7f43cb6588409", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.20.0.tgz", "fileCount": 9, "unpackedSize": 1053372, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQoURL/HyeBYKAXqE0UKkAfy/+ZEmj/FcYNt68CzKtOAiEApACHHcHvQXhkltrWm4VEOXRUMcQYo1rc87okagHpZKs="}], "size": 263207}, "_hasShrinkwrap": false, "publish_time": 1695220651532, "_source_registry_name": "default"}, "6.20.1": {"name": "@codemirror/view", "version": "6.20.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-eeLmPgW+/70PBzPLABaeZZA69yweoHl1jga1Oszv2XjG/yEWheJTIpdWHZHwEERe5IIZJpUsvvRk6uWHnPIJ3w==", "shasum": "8d17ec30568e4244c8af6b5cabfd1e26ed36a0d3", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.20.1.tgz", "fileCount": 9, "unpackedSize": 1053844, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQofXFygVk2B61/lz+M0DlhI0vTF7Z0QQgGEPAtxNmEQIgBXjUAMQOWPHiYzh6f1MhllIVrEdfY5Rxz1CwEnYz0Fk="}], "size": 263335}, "_hasShrinkwrap": false, "publish_time": 1695376705634, "_source_registry_name": "default"}, "6.20.2": {"name": "@codemirror/view", "version": "6.20.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-tZ9F0UZU2P3eTRtgljg3DaCOTn2FIjQU/ktTCjSz9/6he3GHDNxSCDAPidMtF+09r23o0h9H/5U7xibtUuEgdg==", "shasum": "77c8e2801cb8c324740780a9f3ab19a15096a51a", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.20.2.tgz", "fileCount": 9, "unpackedSize": 1054879, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICMpL0EeAxkcAtvTr6VAlAxBh4v0prtRD6cYG66Tu7KLAiA2JN0d5pxuHbywvG8t5Ss8AsPSM7K8fzUfxssSCCk8Gg=="}], "size": 263595}, "_hasShrinkwrap": false, "publish_time": 1695629751364, "_source_registry_name": "default"}, "6.21.0": {"name": "@codemirror/view", "version": "6.21.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-JI3asAk/fZ7h2ATjY9ErDFGWoWe4Bu7XgzR31G26RU3M/DLjfdQK8Im7hL1P/PAyprfVrf0uRNqxu02TkBcm3g==", "shasum": "c073e4bef3dbed5a7494f0dbf65f6b4df82ed940", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.21.0.tgz", "fileCount": 9, "unpackedSize": 1057006, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHI2qyJgZ7hIs461tLwyBMSUCe854KtR1BM6Xo3J1iH1AiBMnIWG74OvYs3NrSl1VlaJRMwVAkKjs3PAp5ELm0reZg=="}], "size": 263943}, "_hasShrinkwrap": false, "publish_time": 1695981875668, "_source_registry_name": "default"}, "6.21.1": {"name": "@codemirror/view", "version": "6.21.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-GidMWEM9JrqTyCr13lqw+C122iFFzowhCS/CYwVIVrA9AYcWUTULHvamjOKCUiwDisG0oyVHb4pbDh8JRjTZGg==", "shasum": "1e9d81872c35d07ba2e429e0bd6a73e7b8ab277d", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.21.1.tgz", "fileCount": 9, "unpackedSize": 1057360, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVk0kc/onh+qvPQv8j0dIQVB2IK16iUtAQHoLRRn3+eQIgKmvNxfINzDNyneFgG3QiZmjf9zDnGyReJLM3c4RDnVY="}], "size": 264022}, "_hasShrinkwrap": false, "publish_time": 1696244342165, "_source_registry_name": "default"}, "6.21.2": {"name": "@codemirror/view", "version": "6.21.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-EZ/Q1WeMWVarWiZHcy4E2aOjjDySeipVkPawOIu2iViZ1YNaZXPBqJBd9/2zLJtN/MrXKm0V1mHB8Cxn50t91A==", "shasum": "1e9e3d34926bc890ceb4b7f97533fbacc8930a75", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.21.2.tgz", "fileCount": 9, "unpackedSize": 1057484, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHAaFCbFZFoDyPkUcWdjM/SM5K4z7kqURTvhojNcrozWAiEA+NIWx+mAE2G6wxFJnXC8y02Z/fqmLQsilj6+P/NpO/g="}], "size": 264036}, "_hasShrinkwrap": false, "publish_time": 1696271717825, "_source_registry_name": "default"}, "6.21.3": {"name": "@codemirror/view", "version": "6.21.3", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-8l1aSQ6MygzL4Nx7GVYhucSXvW4jQd0F6Zm3v9Dg+6nZEfwzJVqi4C2zHfDljID+73gsQrWp9TgHc81xU15O4A==", "shasum": "cf8e3ee6f08e06a6912f18bc90548b4b74badb7a", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.21.3.tgz", "fileCount": 9, "unpackedSize": 1057174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDa9JEpzwLSHta/oPrwSAOlyrq5Cr2ULdNvCA2AYueyQAiBtzzObggsgdbLDkNfxYzGx5VObWNGgmmx/LfR9PM1v0g=="}], "size": 263973}, "_hasShrinkwrap": false, "publish_time": 1696601027664, "_source_registry_name": "default"}, "6.21.4": {"name": "@codemirror/view", "version": "6.21.4", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-WKVZ7nvN0lwWPfAf05WxWqTpwjC8YN3q5goj3CsSig7//DD81LULgOx3nBegqpqP0iygBqRmW8z0KSc2QTAdAg==", "shasum": "6ec83ea238e16b2e1fc149ef9d1a36db2b1f3863", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.21.4.tgz", "fileCount": 9, "unpackedSize": 1059195, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGmCXtmP6vwnqLwhaktKxuVoZc2ZhTf7tnAXndaQZF7zAiBGfBMz8kSDnN6x67nBJmZpCdX1NG85dwKabcW56EIteA=="}], "size": 264332}, "_hasShrinkwrap": false, "publish_time": 1698139127812, "_source_registry_name": "default"}, "6.22.0": {"name": "@codemirror/view", "version": "6.22.0", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-6zLj4YIoIpfTGKrDMTbeZRpa8ih4EymMCKmddEDcJWrCdp/N1D46B38YEz4creTb4T177AVS9EyXkLeC/HL2jA==", "shasum": "5a5214a04f149ecf54c4803b7fec9bdac56d0d74", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.22.0.tgz", "fileCount": 9, "unpackedSize": 1069018, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICEMyHTq5D1KStkckECKCbkrHG0v2Z4OnjQEVDtMo9uqAiEAu548FCuuXC1cvl+5fowRWcrfwMtn0cYYNzbJaWaB0io="}], "size": 266828}, "_hasShrinkwrap": false, "publish_time": 1699015723542, "_source_registry_name": "default"}, "6.22.1": {"name": "@codemirror/view", "version": "6.22.1", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-38BRn1nPqZqiHbmWfI8zri23IbRVbmSpSmh1E/Ysvc+lIGGdBC17K8zlK7ZU6fhfy9x4De9Zyj5JQqScPq5DkA==", "shasum": "4e556ebb91d0289ec4a6c7551b08da8dbe4dd5bb", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.22.1.tgz", "fileCount": 9, "unpackedSize": 1072403, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBhwHVuF+GNuSEdGoqwB2XhxWg8rWsV1ijLbN+G735ozAiEAgALeCrBErcHonWbATsmoV+CZDwC7wir6oJbpyHxha80="}], "size": 267464}, "_hasShrinkwrap": false, "publish_time": 1701078871079, "_source_registry_name": "default"}, "6.22.2": {"name": "@codemirror/view", "version": "6.22.2", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-cJp64cPXm7QfSBWEXK+76+hsZCGHupUgy8JAbSzMG6Lr0rfK73c1CaWITVW6hZVkOnAFxJTxd0PIuynNbzxYPw==", "shasum": "79a4b87f5bb3f057cb046295b102eb04fd31a50d", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.22.2.tgz", "fileCount": 9, "unpackedSize": 1072883, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDw9rvs8msrB5p607rw0eLKCahOZqW7XfwlMlcFfRbwqAiAiPIS9nZnbJdaxtACpIAOEncJ1d5xWTe/WhyKndcrF+w=="}], "size": 267608}, "_hasShrinkwrap": false, "publish_time": 1702067114520, "_source_registry_name": "default"}, "6.22.3": {"name": "@codemirror/view", "version": "6.22.3", "dependencies": {"@codemirror/state": "^6.1.4", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-rqnq+Zospwoi3x1vZ8BGV1MlRsaGljX+6qiGYmIpJ++M+LCC+wjfDaPklhwpWSgv7pr/qx29KiAKQBH5+DOn4w==", "shasum": "22514a0256d0fbd3e9079d7c49cb97f35593156c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.22.3.tgz", "fileCount": 9, "unpackedSize": 1073281, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzTYEFilV41AQKBtW1ZhS2yY+b4NQT6BcKSN9bvlSmxQIgbCAOZ85sRIzFft+bBOd4ivYnF0LP9Zbw/pCbVoaLfxo="}], "size": 268157}, "_hasShrinkwrap": false, "publish_time": 1702473857998, "_source_registry_name": "default"}, "6.23.0": {"name": "@codemirror/view", "version": "6.23.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-/51px9N4uW8NpuWkyUX+iam5+PM6io2fm+QmRnzwqBy5v/pwGg9T0kILFtYeum8hjuvENtgsGNKluOfqIICmeQ==", "shasum": "8054a2043273abad7f1587d15accb0623e1960ed", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.23.0.tgz", "fileCount": 9, "unpackedSize": 1083320, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICUO83ba1GWHLlu0QJvOCXU0Pq3yssjgLU+ZAec0zji+AiBy/wUzywzy7UmffgrCVnYQO4ZivgurNCa1gaeKhxgH5A=="}], "size": 271157}, "_hasShrinkwrap": false, "publish_time": 1703784535647, "_source_registry_name": "default"}, "6.23.1": {"name": "@codemirror/view", "version": "6.23.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-J2Xnn5lFYT1ZN/5ewEoMBCmLlL71lZ3mBdb7cUEuHhX2ESoSrNEucpsDXpX22EuTGm9LOgC9v4Z0wx+Ez8QmGA==", "shasum": "1ce3039a588d6b93f153b7c4c035c2075ede34a6", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.23.1.tgz", "fileCount": 9, "unpackedSize": 1084774, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD60beJkic9SrK7P71uzGtMkAbQQvRYI2CV/XsPJQQbEwIhAJXfBqv7YXBZRTyBZ6pVEZMDuq/f2j2+ZDY5MOHHBsPh"}], "size": 271436}, "_hasShrinkwrap": false, "publish_time": 1706088800048, "_source_registry_name": "default"}, "6.24.0": {"name": "@codemirror/view", "version": "6.24.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-zK6m5pNkdhdJl8idPP1gA4N8JKTiSsOz8U/Iw+C1ChMwyLG7+MLiNXnH/wFuAk6KeGEe33/adOiAh5jMqee03w==", "shasum": "2f780290a54cfe571b1a1468c47b483de0cf6fb2", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.24.0.tgz", "fileCount": 9, "unpackedSize": 1087014, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrBVV6qqtNVmWyCYDgEakV5uOQV5v38ZE+byfBsoKrrAiEAz0NN1s7vGav2yZUwwIujM70hTfCnTRsrvjgnU8WNNFo="}], "size": 272033}, "_hasShrinkwrap": false, "publish_time": 1707471113242, "_source_registry_name": "default"}, "6.24.1": {"name": "@codemirror/view", "version": "6.24.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-sBfP4rniPBRQzNakwuQEqjEuiJDWJyF2kqLLqij4WXRoVwPPJfjx966Eq3F7+OPQxDtMt/Q9MWLoZLWjeveBlg==", "shasum": "c151d589dc27f9197c68d395811b93c21c801767", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.24.1.tgz", "fileCount": 9, "unpackedSize": 1087388, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHpTcZb7D+Blw9KG+e57lupstVFnn0RRm0Si3tCXC2fwIgZDGliPpy1s5j/0juQhFkrBbTYGLeyvLPQu+DtKWMwMU="}], "size": 272120}, "_hasShrinkwrap": false, "publish_time": 1708359743915, "_source_registry_name": "default"}, "6.25.0": {"name": "@codemirror/view", "version": "6.25.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-XnMGOm6qXB8znzCko0N7k97qZayVdvqpA0JebxA5fHtgBjC/XlCPhH9TK92TahsoCKMPQlaTCUep06Dwj/+GXQ==", "shasum": "7bf6cd15d1285e9354a83e39ee35cbf8f3957119", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.25.0.tgz", "fileCount": 9, "unpackedSize": 1094452, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCU0D6U+4UJHXf1eMrs1ahockb5ZpIkZAU/KSvdAJL/VgIgT+ODE2vhns0qG4RieR0ON/vuBdIZRGUX9ojQICvhWDw="}], "size": 273816}, "_hasShrinkwrap": false, "publish_time": 1709550635390, "_source_registry_name": "default"}, "6.25.1": {"name": "@codemirror/view", "version": "6.25.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-2LXLxsQnHDdfGzDvjzAwZh2ZviNJm7im6tGpa0IONIDnFd8RZ80D2SNi8PDi6YjKcMoMRK20v6OmKIdsrwsyoQ==", "shasum": "826ea345fd757dbeeab6a6165c1823e851c67d16", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.25.1.tgz", "fileCount": 9, "unpackedSize": 1094553, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDR8O/ERhb5chknFkkcq0wt+EE0q7U7zX7Hon+FDkfTrwIhAIaz6Pefq0eG1PQS3s82EVzq/xP0U/0jqZ0DwlSQWSsJ"}], "size": 273846}, "_hasShrinkwrap": false, "publish_time": 1709722330934, "_source_registry_name": "default"}, "6.26.0": {"name": "@codemirror/view", "version": "6.26.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-nSSmzONpqsNzshPOxiKhK203R6BvABepugAe34QfQDbNDslyjkqBuKgrK5ZBvqNXpfxz5iLrlGTmEfhbQyH46A==", "shasum": "ab5a85aa8ebfb953cb5534e07d0a3751f9a3869a", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.0.tgz", "fileCount": 9, "unpackedSize": 1100830, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCom5EfkOJrd4ZhJtItZX2mR7jZbenJHSY29sJPGzYFQgIgajvVj7M9aUF97IW90McUJIlvmHovlgOQRqX4BBKb+6E="}], "size": 275391}, "_hasShrinkwrap": false, "publish_time": 1710414459678, "_source_registry_name": "default"}, "6.26.1": {"name": "@codemirror/view", "version": "6.26.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wLw0t3R9AwOSQThdZ5Onw8QQtem5asE7+bPlnzc57eubPqiuJKIzwjMZ+C42vQett+iva+J8VgFV4RYWDBh5FA==", "shasum": "ab99cf35c576bc65f5804ab49db730b65c3fad3f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.1.tgz", "fileCount": 9, "unpackedSize": 1104460, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1s7+bRfibrzBaMf4vVjpDHH0f2TQ4GYOmeHQwMWvGfAiEAzPbxtnOQ8vriO3ZP4e6QLbyKHxsL0CrIbKxB8InF0SI="}], "size": 276002}, "_hasShrinkwrap": false, "publish_time": 1711660493709, "_source_registry_name": "default"}, "6.26.2": {"name": "@codemirror/view", "version": "6.26.2", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-j6V48PlFC/O7ERAR5vRW5QKDdchzmyyfojDdt+zPsB0YXoWgcjlC1IWjmlYfx08aQZ3HN5BtALcgGgtSKGMe7A==", "shasum": "6df950954cee33933514978c5bbd2da9d546466c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.2.tgz", "fileCount": 9, "unpackedSize": 1102324, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3xGMvYQKGLsftDTHWoShOTRcNio/vP8MM8DJuXnsCBQIgeHbpzmdYvrfTBMfioX3RZRRNGPnWPKEe+PHWZ0LFlKU="}], "size": 275351}, "_hasShrinkwrap": false, "publish_time": 1712658811873, "_source_registry_name": "default"}, "6.26.3": {"name": "@codemirror/view", "version": "6.26.3", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-gmqxkPALZjkgSxIeeweY/wGQXBfwTUaLs8h7OKtSwfbj9Ct3L11lD+u1sS7XHppxFQoMDiMDp07P9f3I2jWOHw==", "shasum": "47aebd49a6ee3c8d36b82046d3bffe6056b8039f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.3.tgz", "fileCount": 9, "unpackedSize": 1102559, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvpf6guBDtkLiUybs3je6AXLsZuvxsIPtdFKfEzFSAhwIhAP9AYPMaCCMmtP9suZvBuUFAH3Tk8/PR2Q+BPRdgJ4Sd"}], "size": 275400}, "_hasShrinkwrap": false, "publish_time": 1712907671612, "_source_registry_name": "default"}, "6.26.4-edit-context": {"name": "@codemirror/view", "version": "6.26.4-edit-context", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-C3cYBK/sMSZMCfBXVqpe/vvuNl6EMJY9xREeu2xIrekP1Ng2TwXeYX+ZMPs1ppJm9hXWq8aSditDvWymjsNKNw==", "shasum": "799dda14eca3fb3032bb9845142a5c0967c5cd6f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.4-edit-context.tgz", "fileCount": 9, "unpackedSize": 1121143, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC8lakM4wLyul7C8ElmhD5xJ3b5bBlPuYiSDvw478AaDAiB6rRYusKQQjRuNTkJUE+BRmp98x2JRFlA7nIvgouhfJw=="}], "size": 279579}, "_hasShrinkwrap": false, "publish_time": 1713529729950, "_source_registry_name": "default"}, "6.26.4-edit-context.1": {"name": "@codemirror/view", "version": "6.26.4-edit-context.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-ExYL1uJ1/gPx2KxO5Lq8IrM7jz09/Q8ll+Q+hXjByheqFlspbXYUk+cfEu2i5jQ1UYacDhTDvrsHvN/NjXpGHA==", "shasum": "14d35af14c3fce35e2430686675a6b3728cd8913", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.4-edit-context.1.tgz", "fileCount": 9, "unpackedSize": 1121231, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDuBmjcyBN1rzsu4lkXPHC4UyyRy/78lAeXKemLF5seAiA35FaIPraVjQ43NWhy1QUOQUCEy7PZbnJZp+1okDD9Rw=="}], "size": 279624}, "_hasShrinkwrap": false, "publish_time": 1713716422555, "_source_registry_name": "default"}, "6.26.4": {"name": "@codemirror/view", "version": "6.26.4", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-JzhDclps1MKrqqcoZNb9i+C2kxPktaRGESwG0IBA6F/MkiwhwhXllyyx98dijDrPvq1O42VhIyrVTdfCnTbqpg==", "shasum": "49dc47577b9fa85b8d9aee57afd7e7c4f96689b1", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.26.4.tgz", "fileCount": 9, "unpackedSize": 1107680, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZoX7Phb+QfDkmLQa+NC7uzBPdbaS0CuX+pBw/6IA+vgIgepMfH9GaFOlFhBNaKHU7ucopt5JJ0B0VG3sqdnRr4vA="}], "size": 276601}, "_hasShrinkwrap": false, "publish_time": 1717495097163, "_source_registry_name": "default"}, "6.27.0": {"name": "@codemirror/view", "version": "6.27.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-8kqX1sHbVW1lVzWwrjAbh4dR7eKhV8eIQ952JKaBXOoXE04WncoqCy4DMU701LSrPZ3N2Q4zsTawz7GQ+2mrUw==", "shasum": "829882b171106bc50b4f17b7e5d2f7277832c92f", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.27.0.tgz", "fileCount": 9, "unpackedSize": 1111511, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnu1jiagsVNvnvx216Qzcxpiz84hDu6J7GYqzLrYdleQIgW9lteCUvCiE5W4jOYP1T09P2ZkkbHKhR3N2nM5snMFc="}], "size": 277969}, "_hasShrinkwrap": false, "publish_time": 1717510693170, "_source_registry_name": "default"}, "6.28.0": {"name": "@codemirror/view", "version": "6.28.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-fo7CelaUDKWIyemw4b+J57cWuRkOu4SWCCPfNDkPvfWkGjM9D5racHQXr4EQeYCD6zEBIBxGCeaKkQo+ysl0gA==", "shasum": "7315e8f122b76319d9b95042cde2c9875e7eeb5e", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.0.tgz", "fileCount": 9, "unpackedSize": 1130862, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBUyZBlfFEW8OHLuCTr8jU3gTZflTESyum7b8TwTMiMQIgX7rp2zPCCwC/hm8wATdIv44iYf43zN/JpA6VYdP0m30="}], "size": 282360}, "_hasShrinkwrap": false, "publish_time": 1718019677100, "_source_registry_name": "default"}, "6.28.1": {"name": "@codemirror/view", "version": "6.28.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-BUWr+zCJpMkA/u69HlJmR+YkV4yPpM81HeMkOMZuwFa8iM5uJdEPKAs1icIRZKkKmy0Ub1x9/G3PQLTXdpBxrQ==", "shasum": "566b89b0513ccfc4e5518a48f5d1962bc6ea5a66", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.1.tgz", "fileCount": 9, "unpackedSize": 1131637, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcdXQeJ4HKtkb5casrbjvHg+FDmDIKPzWWsiDrf6tggQIhAOLTSwqnjoffvj+Jtlly8tOpqBsUehgXgA4HSaRVf1ES"}], "size": 282487}, "_hasShrinkwrap": false, "publish_time": 1718220064240, "_source_registry_name": "default"}, "6.28.2": {"name": "@codemirror/view", "version": "6.28.2", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-A3DmyVfjgPsGIjiJqM/zvODUAPQdQl3ci0ghehYNnbt5x+o76xq+dL5+mMBuysDXnI3kapgOkoeJ0sbtL/3qPw==", "shasum": "026d5d2bd315aa015c1a1573b6358eeba7acd004", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.2.tgz", "fileCount": 9, "unpackedSize": 1132598, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMxuZf82aB1w/2PG5xcKhBV5e/S34EBpNQTMn5zbvilgIgWj2NT8S8CQuo37xZwoDwyY93xngXoO1Je0RW2pocAAg="}], "size": 282736}, "_hasShrinkwrap": false, "publish_time": 1718968253478, "_source_registry_name": "default"}, "6.28.3": {"name": "@codemirror/view", "version": "6.28.3", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-QVqP+ko078/h9yrW+u5grX3rQhC+BkGKADRrlDaJznfPngJOv5zObiVf0+SgAWhL/Yt0nvZ+10rO3L+gU5IbFw==", "shasum": "7995d6834fb7c741bb7c524092ca6a30255feb3d", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.3.tgz", "fileCount": 9, "unpackedSize": 1132720, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDF51OT0ZvB7DHLfPS2IYRiqwdUEgnhRTkjF0WzzKEN/AIgZ1aqcQyyckZHsBJEVC1Y+5tqPaS7CNGqN/2T1sTzEL4="}], "size": 282753}, "_hasShrinkwrap": false, "publish_time": 1719816912449, "_source_registry_name": "default"}, "6.28.4": {"name": "@codemirror/view", "version": "6.28.4", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-QScv95fiviSQ/CaVGflxAvvvDy/9wi0RFyDl4LkHHWiMr/UPebyuTspmYSeN5Nx6eujcPYwsQzA6ZIZucKZVHQ==", "shasum": "6fb91182ad9cfa1d300578342d3327348f3ed912", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.4.tgz", "fileCount": 9, "unpackedSize": 1132925, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICxsB/uvGCx/nyi2HnWTJ/x/LMgdg1jIdiZess6cztqZAiEA7kLVlKk3NrS8tmpHi5beqJPw2zeUzA58/4JyHTWmHVI="}], "size": 282791}, "_hasShrinkwrap": false, "publish_time": 1720016298636, "_source_registry_name": "default"}, "6.28.5": {"name": "@codemirror/view", "version": "6.28.5", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-NkUtfUa1lV7Jqg5DfHE/uLl7jKyoymDkaueMQXzePYuezL7FwX3ATANy74iAGlHCGe25hBGB0R+I5dC5EZ5JBg==", "shasum": "e98421d8f96bf15a04fbb1a2475d0387b512267c", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.5.tgz", "fileCount": 9, "unpackedSize": 1134309, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDE9XsaI0z0Js1X3YT9Iq6CeqVTJBba+TsiaooKrOjZ3wIgRpcATgY4oWdA6Q2uxHa7cbfmR8zOWxTxd6yjh5XXQoo="}], "size": 283167}, "_hasShrinkwrap": false, "publish_time": 1721211061912, "_source_registry_name": "default"}, "6.28.6": {"name": "@codemirror/view", "version": "6.28.6", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-bhwB1AZ6zU4M3dNKm8Aa2BXwj5mWDqE9IWpqxYKJoLCnx+AcwcMuLO01tLWgc1mx4vT1IVYVqx86YoqUsATrqQ==", "shasum": "570de85d1055f7f9946a0082d5f7db9981a3e808", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.28.6.tgz", "fileCount": 9, "unpackedSize": 1134759, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8d5xvwv75hIsRSnT2BNUJZZ912IKhclu1UjSliAbaNwIgPDKKeEO0oqFnMEytINT6jodlsgkn+eEnxJd8hUiROlw="}], "size": 283249}, "_hasShrinkwrap": false, "publish_time": 1721395692381, "_source_registry_name": "default"}, "6.29.0": {"name": "@codemirror/view", "version": "6.29.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-ED4ims4fkf7eOA+HYLVP8VVg3NMllt1FPm9PEJBfYFnidKlRITBaua38u68L1F60eNtw2YNcDN5jsIzhKZwWQA==", "shasum": "fd44faee1838975d7673055002093a58e1a74c67", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.29.0.tgz", "fileCount": 9, "unpackedSize": 1135618, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+dlHTFN+62Y5jXEKz5oh/YnPf50zkbaAzRl2lux5VewIhAMrMXJ61ZIBbnljZtya5vwO9me37tWh51baySAUnOMby"}], "size": 283446}, "_hasShrinkwrap": false, "publish_time": 1721929380956, "_source_registry_name": "default"}, "6.29.1": {"name": "@codemirror/view", "version": "6.29.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-7r+DlO/QFwPqKp73uq5mmrS4TuLPUVotbNOKYzN3OLP5ScrOVXcm4g13/48b6ZXGhdmzMinzFYqH0vo+qihIkQ==", "shasum": "9c6c6f8a15e12df1b7e9d136dcbf4ff90b81ef71", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.29.1.tgz", "fileCount": 9, "unpackedSize": 1137074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvi8kjufPIuXPNlnoJvnBGhIpJYkxWKEAmd1LoEBx+9AIhAOPbhIORtyUPeuSnAi4fHXgM6CAM3N1E2nf0rljCcUvx"}], "size": 283803}, "_hasShrinkwrap": false, "publish_time": 1722249342696, "_source_registry_name": "default"}, "6.30.0": {"name": "@codemirror/view", "version": "6.30.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-96Nmn8OeLh6aONQprIeYk8hGVnEuYpWuxKSkdsODOx9hWPxyuyZGvmvxV/JmLsp+CubMO1PsLaN5TNNgrl0UrQ==", "shasum": "4daceb2b3951477b99283b59b98ed0c01ce016b1", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.30.0.tgz", "fileCount": 9, "unpackedSize": 1140084, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF0kqCUuxNr0QkjrJmQ4txTnme3egq9lC4Qf59EHU29TAiBfbtp9OqyuFHjpPvjjEcQdRxaOxLh2KDZAV49+gCpY+Q=="}], "size": 284618}, "_hasShrinkwrap": false, "publish_time": 1722850214302, "_source_registry_name": "default"}, "6.31.0": {"name": "@codemirror/view", "version": "6.31.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-pEqRyzcodpvIuWOhGSo0AIs3WKnewDl1EffAnaSZjqGtxk8HisBLY3VltYKA6Lr94VBH3B6O7G8G1tsQ/Wyi7w==", "shasum": "120c5ae4564f0d5c1d72feba7104117956190591", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.31.0.tgz", "fileCount": 9, "unpackedSize": 1143016, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE8vy+3aXhyiXZx9kIU566bPhwB0zB8YKev3tT/KgYXCAiEAv3sLKkPFaUH1esbJ5Xn6ug1r7pvrz2/Og40ey1+KC7I="}], "size": 285096}, "_hasShrinkwrap": false, "publish_time": 1723377315451, "_source_registry_name": "default"}, "6.32.0": {"name": "@codemirror/view", "version": "6.32.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-AgVNvED2QTsZp5e3syoHLsrWtwJFYWdx1Vr/m3f4h1ATQz0ax60CfXF3Htdmk69k2MlYZw8gXesnQdHtzyVmAw==", "shasum": "94be8aa18b60044471200ee80b0339513036680e", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.32.0.tgz", "fileCount": 9, "unpackedSize": 1144901, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHbZRZW0jGSl/JPit+Q63mIf9s99/Fdw4bCnChzbggRDAiEArzM3rowiPXO7USJ9LVrQhbWzgdbdSWFvvYY+m3SdgwY="}], "size": 285383}, "_hasShrinkwrap": false, "publish_time": 1723449132433, "_source_registry_name": "default"}, "6.33.0": {"name": "@codemirror/view", "version": "6.33.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-AroaR3BvnjRW8fiZBalAaK+ZzB5usGgI014YKElYZvQdNH5ZIidHlO+cyf/2rWzyBFRkvG6VhiXeAEbC53P2YQ==", "shasum": "51e270410fc3af92a6e38798e80ebf8add7dc3ec", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.33.0.tgz", "fileCount": 9, "unpackedSize": 1148196, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEOL4Mf8dtiZrlxAgLBozzSBHvDZhH/wv74mjB1f7qK7AiB6cU9x1L+7m4Z7oZ58tqW1iXEJllchPyOsLSnBq/7mKw=="}], "size": 286182}, "_hasShrinkwrap": false, "publish_time": 1724488720124, "_source_registry_name": "default"}, "6.34.0": {"name": "@codemirror/view", "version": "6.34.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-2vKJ79tOcVfgPYVJM2XjcL1BH5Bsl7/tgn9ilBj3XWeCS5kTRy/NE4FHEj4aMylOl/D3IPNsmZH0WPlB+DyIdA==", "shasum": "415a5eb9eca139e11b493425e087131d8ed43a58", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.34.0.tgz", "fileCount": 9, "unpackedSize": 1148651, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC86oyVmGnlu3io+/mCuSXj6iJuFiTlWR7cm+UOsiBuXwIhAN5gaZ7CwzDs1pGsAOwVEWEohbQ4RKKXXCSvueZvyMpc"}], "size": 286301}, "_hasShrinkwrap": false, "publish_time": 1727249405326, "_source_registry_name": "default"}, "6.34.1": {"name": "@codemirror/view", "version": "6.34.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-t1zK/l9UiRqwUNPm+pdIT0qzJlzuVckbTEMVNFhfWkGiBQClstzg+78vedCvLSX0xJEZ6lwZbPpnljL7L6iwMQ==", "shasum": "b17ed29c563e4adc60086233f2d3e7197e2dc33e", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.34.1.tgz", "fileCount": 9, "unpackedSize": 1150751, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD82qFV0gGHxSwHuhKv52tuvwTUlV07l/FPQDhx2nOjmQIhAI4+2cDgo6auEXe1NJRnJnXxBuf7q/q+7Xre3yD1YOzT"}], "size": 286818}, "_hasShrinkwrap": false, "publish_time": 1727434051894, "_source_registry_name": "default"}, "6.34.2": {"name": "@codemirror/view", "version": "6.34.2", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-d6n0WFvL970A9Z+l9N2dO+Hk9ev4hDYQzIx+B9tCyBP0W5wPEszi1rhuyFesNSkLZzXbQE5FPH7F/z/TMJfoPA==", "shasum": "c6cc1387be217f448af585f05f23e681f76aeda7", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.34.2.tgz", "fileCount": 9, "unpackedSize": 1150861, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAs1xMVv4yhGcoF1E9pArpoLIHwZGttSBJYfQmz2aWm8AiEAhe/T1qHru0+r5kXu8zqxZvE3ZV/9j5OWmz7Ol8rZyyY="}], "size": 286843}, "_hasShrinkwrap": false, "publish_time": 1730791655309, "_source_registry_name": "default"}, "6.34.3": {"name": "@codemirror/view", "version": "6.34.3", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Ph5d+u8DxIeSgssXEakaakImkzBV4+slwIbcxl9oc9evexJhImeu/G8TK7+zp+IFK9KuJ0BdSn6kTBJeH2CHvA==", "shasum": "ed6300dc4e0368fc9a5ecd0417dc28234bfacaff", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.34.3.tgz", "fileCount": 9, "unpackedSize": 1154068, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAFAkXGsdEO2O6NsdsIGGsx71C3t+rY602HvhziVi6SgIgGyJI4+LfIrG1MZf3JjCvJHIJmlalIJVJh9UHPmvuOzE="}], "size": 287629}, "_hasShrinkwrap": false, "publish_time": 1731684768678, "_source_registry_name": "default"}, "6.35.0": {"name": "@codemirror/view", "version": "6.35.0", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-I0tYy63q5XkaWsJ8QRv5h6ves7kvtrBWjBcnf/bzohFJQc5c14a1AQRdE8QpPF9eMp5Mq2FMm59TCj1gDfE7kw==", "shasum": "890e8e31a58edf65cdf193049fe9f3fdec20cc82", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.35.0.tgz", "fileCount": 9, "unpackedSize": 1154636, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQx9h7kSys+EJxbaoYvFegi+aRjBlmuNGaBDGTDQbjZgIhAKtv5itI7zg7R8I73j369oGS+gTzf6I1YLGq8GkxU9fZ"}], "size": 287785}, "_hasShrinkwrap": false, "publish_time": 1732183310163, "_source_registry_name": "default"}, "6.35.1": {"name": "@codemirror/view", "version": "6.35.1", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-OUs9Z2UabSfJxSoEnuHUzGF0wHpWiJ/3IW/cgrKBqbp5Yj7XTYXQAQaLHZUP48ctRMvxgarEXTginrocUG8J7A==", "shasum": "4ed9ab5cf4318414d39d18c6d90c4494f0cb63c0", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.35.1.tgz", "fileCount": 9, "unpackedSize": 1155755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbkN11DDIFWN/XRsYetUDKIJsV1Zl9rISEc3PUnj1bPAIgT3y4zcpSGoTyFxrInr1G73MirhmgU9xGERpVBDdpA0Q="}], "size": 288128}, "_hasShrinkwrap": false, "publish_time": 1733490656922, "_source_registry_name": "default"}, "6.35.2": {"name": "@codemirror/view", "version": "6.35.2", "dependencies": {"@codemirror/state": "^6.4.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-u04R04XFCYCNaHoNRr37WUUAfnxKPwPdqV+370NiO6i85qB1J/qCD/WbbMJsyJfRWhXIJXAe2BG/oTzAggqv4A==", "shasum": "27f1a063ea7fc4654674c55abdb230dc8816f706", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.35.2.tgz", "fileCount": 9, "unpackedSize": 1156205, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkyDPeY0X2vS3+v33AWx4u+nAyC0NmsIvgR2D+wLcSIQIgLz/mVfGVTcQaHJ4niFCDDl5Vtx/goUe+R9mlYDNgwWw="}], "size": 288238}, "_hasShrinkwrap": false, "publish_time": 1733527609737, "_source_registry_name": "default"}, "6.35.3": {"name": "@codemirror/view", "version": "6.35.3", "dependencies": {"@codemirror/state": "^6.5.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-ScY7L8+EGdPl4QtoBiOzE4FELp7JmNUsBvgBcCakXWM2uiv/K89VAzU3BMDscf0DsACLvTKePbd5+cFDTcei6g==", "shasum": "329de3755cfb347df3e32c354c675d0e83df2406", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.35.3.tgz", "fileCount": 9, "unpackedSize": 1156906, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD575o3GPgIVfDQXiCNPzE6C5mkXkIiCurSUJoiIUgyOAIhAJpKbGbxkGgrORGSjhGWkDRg4J0L6glnlmUkThTlO/W6"}], "size": 288487}, "_hasShrinkwrap": false, "publish_time": 1733758255012, "_source_registry_name": "default"}, "6.36.0": {"name": "@codemirror/view", "version": "6.36.0", "dependencies": {"@codemirror/state": "^6.5.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-aMePDnkNNKE8dSOo1w689xYa3dijREbRajiRcgjSGc2TWN7MTdE+9pm5fxwdz0C4D9Di1VZomrn2M+xDe7tTVg==", "shasum": "4f4a24002a58ab4e478b5497cd8316f642d7a2f6", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.36.0.tgz", "fileCount": 9, "unpackedSize": 1159535, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB3/Mc+GVuVYUtSNpX7/zVPpX0W6RqE3y8QUOObohJc/AiEA8LAIFuHb6uBLer3pfhjLNDd7rLU3YXESnlE/Qza0wgs="}], "size": 289076}, "_hasShrinkwrap": false, "publish_time": 1734452071525, "_source_registry_name": "default"}, "6.36.1": {"name": "@codemirror/view", "version": "6.36.1", "dependencies": {"@codemirror/state": "^6.5.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-miD1nyT4m4uopZaDdO2uXU/LLHliKNYL9kB1C1wJHrunHLm/rpkb5QVSokqgw9hFqEZakrdlb/VGWX8aYZTslQ==", "shasum": "3c543b8fd72c96b30c4b2b1464d1ebce7e0c5c4b", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.36.1.tgz", "fileCount": 9, "unpackedSize": 1159659, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICjajXbqJFa6IBNhJoEeSik2/Ha57QwGXMaPnNg4vcg0AiAKSDYwkkIeHO3Z+j/7eAuIt/08SebqIxV9l1XU/8r+mw=="}], "size": 289097}, "_hasShrinkwrap": false, "publish_time": 1734618000431, "_source_registry_name": "default"}, "6.36.2": {"name": "@codemirror/view", "version": "6.36.2", "dependencies": {"@codemirror/state": "^6.5.0", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-DZ6ONbs8qdJK0fdN7AB82CgI6tYXf4HWk1wSVa0+9bhVznCuuvhQtX8bFBoy3dv8rZSQqUd8GvhVAcielcidrA==", "shasum": "aeb644e161440734ac5a153bf6e5b4a4355047be", "tarball": "https://registry.npmmirror.com/@codemirror/view/-/view-6.36.2.tgz", "fileCount": 9, "unpackedSize": 1162224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDy1GzhbQ5dLc9iRejTNJ6LIozAvtUb0yAr89S9a6kdbgIhAOIxCnHHZe0nPDqKKhHH00iv4So0gfBj9pMrJPGWLCtT"}], "size": 289831}, "_hasShrinkwrap": false, "publish_time": 1736441192574, "_source_registry_name": "default"}}, "_source_registry_name": "default"}