{"dist-tags": {"latest": "6.1.1"}, "modified": "2023-07-27T15:31:33.019Z", "name": "vue-codemirror", "versions": {"4.0.6": {"name": "vue-codemirror", "version": "4.0.6", "dependencies": {"codemirror": "^5.41.0", "diff-match-patch": "^1.0.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "b786bb80d8d762a93aab8e46f79a81006f0437c4", "size": 9109, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.6.tgz", "integrity": "sha512-ilU7Uf0mqBNSSV3KT7FNEeRIxH4s1fmpG4TfHlzvXn0QiQAbkXS9lLfwuZpaBVEnpP5CSE62iGJjoliTuA8poQ=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "4.0.5": {"name": "vue-codemirror", "version": "4.0.5", "dependencies": {"codemirror": "^5.32.0", "diff-match-patch": "^1.0.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "3a784d454fbfb17ce769b9fc90f10e9868687ecb", "size": 9017, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.5.tgz", "integrity": "sha512-VeLTG6/c/8+q46XT7Uoeiiwtb18+USSEY7uajG3dtfWS4tkA/R8PDKUdTwI1xJh7rnAMMtZ8tlQGASUoA5Yebw=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "4.0.4": {"name": "vue-codemirror", "version": "4.0.4", "dependencies": {"codemirror": "^5.32.0", "diff-match-patch": "^1.0.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "e319b7a6fe35b3364a8c40969a22ebf162df7279", "size": 8799, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.4.tgz", "integrity": "sha512-SOi+ymlrXusaWL/QrHjIYGCzw1OpFYU97ASgQM4Fs2nxkHL9Hc+gYhK3m8yXfZsckQWCwUWM4tBe/x5dJ82g1Q=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "4.0.3": {"name": "vue-codemirror", "version": "4.0.3", "dependencies": {"codemirror": "^5.32.0", "diff-match-patch": "^1.0.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "83721031e1f606f428136a3dfd9fc710ffdec149", "size": 8639, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.3.tgz", "integrity": "sha512-cWc8wR05NaiG6dTkfjVYwQHd1DhO1XpGJJ8MqJ+w9pT5jM1fqI8K8fjR8xwuMm51Y11r2NT8fdtB5eNy9otAWg=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "4.0.2": {"name": "vue-codemirror", "version": "4.0.2", "dependencies": {"codemirror": "^5.32.0", "diff-match-patch": "^1.0.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "23bdabbca30964e467561eb80677a8299235e5e1", "size": 8565, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.2.tgz", "integrity": "sha512-88JfXhJGEdhRkwPngBAmHykLP27PP/p3YHM75xzAN9LARnl12ZY87gwwHZLgKu067YRsRaJysrmUJU8pCOuk7A=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "4.0.1": {"name": "vue-codemirror", "version": "4.0.1", "dependencies": {"codemirror": "^5.32.0", "diff-match-patch": "^1.0.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "270b4fa1165c9596888612f47b9fcbebcecf5cbe", "size": 8538, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.1.tgz", "integrity": "sha512-kONYVeVTFfJE5kiKEz9rf9n6BV9dqywjw82CDu3PymAX7oXpySeJmh4zQaqILIL1RrbuS58hDg1GtT3B1J3GpA=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "4.0.0": {"name": "vue-codemirror", "version": "4.0.0", "dependencies": {"codemirror": "^5.32.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "directories": {}, "dist": {"shasum": "d94b6884872c8e98ee9d6dd774cba3bfe305ad9e", "size": 8504, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-4.0.0.tgz", "integrity": "sha512-rh/Q9oMNd8cBf7Tr1cGZT5+ol/p8UOqfADMPoMe4fGEGnL6QPohHw/xYJ9H587thqY6TNA4dWb5VS57wXJJm4A=="}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.1.8": {"name": "vue-codemirror", "version": "3.1.8", "dependencies": {"codemirror": "^5.30.0"}, "devDependencies": {"babel-core": "^6.24.1", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "browserify": "^14.4.0", "cross-env": "^5.0.0", "uglify-js": "^3.0.15", "vueify": "^8.7.0"}, "directories": {}, "dist": {"shasum": "c080cccb9cb88ecb455a75538095d2ed21790214", "size": 497980, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.1.8.tgz", "integrity": "sha512-F2NQqEt0M6WWcx4isB1jqS7g1xtG2rdSXPTiH4kK8zET2yNLPmKdLlgnWIw2RyHvcgL1y+T7lWPXNpjWThtidA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.1.7": {"name": "vue-codemirror", "version": "3.1.7", "dependencies": {"codemirror": "^5.30.0"}, "devDependencies": {"babel-core": "^6.24.1", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "browserify": "^14.4.0", "cross-env": "^5.0.0", "uglify-js": "^3.0.15", "vueify": "^8.7.0"}, "directories": {}, "dist": {"shasum": "6c15ccfe675b07cfd1341cf2426f287ddf825abd", "size": 497999, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.1.7.tgz", "integrity": "sha512-sTMeJ2qYrKZaK0hyZuGOzp6p67P9UWe2ts1YOOcCcEv/HcKQjqaBklurYktGNjN4Ddu1dfoD8QfWB1/46OGdsg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.1.6": {"name": "vue-codemirror", "version": "3.1.6", "dependencies": {"codemirror": "^5.30.0"}, "devDependencies": {"babel-core": "^6.24.1", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "browserify": "^14.4.0", "cross-env": "^5.0.0", "uglify-js": "^3.0.15", "vueify": "^8.7.0"}, "directories": {}, "dist": {"shasum": "3993e05dd0ec6cd6e2d759127d5ce7955841c134", "size": 497975, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.1.6.tgz", "integrity": "sha512-Mzu8LOzXJMEEwc7elDjiuOJ4TEZM51ZljALHGlPZiTMUwkH12E2zSEbc/AvKvlSJ1rzrF5kfnyLXzDhKZX3Afg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.1.4": {"name": "vue-codemirror", "version": "3.1.4", "dependencies": {"codemirror": "^5.28.0"}, "devDependencies": {"babel-core": "^6.24.1", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "browserify": "^14.4.0", "cross-env": "^5.0.0", "uglify-js": "^3.0.15", "vueify": "^8.7.0"}, "directories": {}, "dist": {"shasum": "daf5f567d1f7532ee45bae48b863f9b621f42958", "size": 490219, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.1.4.tgz", "integrity": "sha512-Yg/rWOgXjRltMZB/BnQnbNTxAh/V5tVummV2LKjpWj4SPOCoItcPordaKIo4yUX1xi6v0nWBLL9IgFkyg6/Amw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.1.2": {"name": "vue-codemirror", "version": "3.1.2", "dependencies": {"codemirror": "^5.28.0"}, "directories": {}, "dist": {"shasum": "5f86662ccc19370d89be20b9540b894876185fa2", "size": 5908, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.1.2.tgz", "integrity": "sha512-3221AyeFVw+PXC7n2r1b14E37wyL1COReXM0R/ao/DQH/DNDio+LnZzEi+ujDSb8qHX2BQCnR8jOqSQCHl79yw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.1.0": {"name": "vue-codemirror", "version": "3.1.0", "dependencies": {"codemirror": "^5.25.0"}, "directories": {}, "dist": {"shasum": "4a3b098d95a588a1be49aea5e15458c19acba819", "size": 5883, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.1.0.tgz", "integrity": "sha512-6lmVol38E3nVz9SE397cjWh1D5X+nqjbzxEuXCeMg+EkSfTF4qwOsyfYzpPk8R+axcwCIyHO31tB7lJYrV3oGA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.8": {"name": "vue-codemirror", "version": "3.0.8", "dependencies": {"codemirror": "^5.25.0"}, "directories": {}, "dist": {"shasum": "a011b47cebd7e5659a6c2464b700241a18dc6d67", "size": 5865, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.8.tgz", "integrity": "sha512-sYV8JZhfHCLuSpecptzz0Keq/GTtAap+MdTMrR01cqPdHnZP+Qi/Wd18PgqGFZN8EOEVxbVUYRMeUCN/3UqTHg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.7": {"name": "vue-codemirror", "version": "3.0.7", "dependencies": {"codemirror": "^5.25.0"}, "directories": {}, "dist": {"shasum": "a1cbcc73a522b82a32c00baccb6fd1fd50063982", "size": 5808, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.7.tgz", "integrity": "sha512-5tX45d7Qj5lQ40lAIOcBqPllbJmjo/wqmRAsw7cFrElvagBLL23gPk2NKodlztpBTvAZah/02lvGO10MmqNImA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.6": {"name": "vue-codemirror", "version": "3.0.6", "dependencies": {"codemirror": "^5.25.0"}, "directories": {}, "dist": {"shasum": "d2cf27f2c7a7f124733ab2a3afe2b045a04d12ac", "size": 5799, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.6.tgz", "integrity": "sha512-JtaHF2kjNXP9nNunfOSYh1OrqyUtTyo1OG0yWIh9tso96Bnudj/WT1DWpSmbQmMHFd00E2pbwT+Gp56+fZppuQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.5": {"name": "vue-codemirror", "version": "3.0.5", "dependencies": {"codemirror": "^5.25.0"}, "directories": {}, "dist": {"shasum": "4299f922093cdd7231b5b9962689f3506649365a", "size": 5787, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.5.tgz", "integrity": "sha512-upJQqsox0Ywr4fuG9/pHuYo3/gDIvGSCS653aVYCwJxY98PQ2uK7b0qw2OFdYPtVnlWibMATJtQ5kwScpPc7pA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.4": {"name": "vue-codemirror", "version": "3.0.4", "dependencies": {"codemirror": "^5.25.0"}, "directories": {}, "dist": {"shasum": "5183b8bd7c1c6b87d68ad3928486e0418a2e771a", "size": 5726, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.4.tgz", "integrity": "sha512-hjOpKA3Wa7JaFszB9aedu7ovHmZbfY3HuM7BRWMb5yvMcrjs6wdpZVISrgmED8ujpc1nFhKRtswadATBFL9+tw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.3": {"name": "vue-codemirror", "version": "3.0.3", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "3e94d9773ebd21a029b2555a799bee14d50af143", "size": 5722, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.3.tgz", "integrity": "sha512-BAm0JvriNSJUjn1ti7hSMpQGR1uPExh7wd2arX6syy6IMLoaYThJK2RKlrIFFKbQB4jzS5BNOoLxBTjm0/OcSA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.2": {"name": "vue-codemirror", "version": "3.0.2", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "b88e666e8ee3e2c189870510287994bd23de3701", "size": 5725, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.2.tgz", "integrity": "sha512-ezhXjN+jZz/aAHtEg+cFcw+6bWzle5Kf90DDCGq7ms/7WrKxP/aTahIuryUxzsauhN65PRvRx/ipTvUhzO5y6w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "3.0.0": {"name": "vue-codemirror", "version": "3.0.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "b499328447f9f878037b354e50f54396a1957508", "size": 5729, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-3.0.0.tgz", "integrity": "sha512-DxOan7A6Zg0SIUZ4Gx9bUDl+f+K/uieeIzW3IruxnsP+UMwcLEXyjTATTapOcg100aQEkzIZoiUwxVbvkJJKBQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.9.9": {"name": "vue-codemirror", "version": "2.9.9", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "0f2e6f2c3e5074929ad5fbdd31f50cbf1514916b", "size": 5730, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.9.9.tgz", "integrity": "sha512-YXxTayDD9fP1A5BthCe5TXZtfU4+UIaH4TN+c9B/aPOOBYy+5hixFtdH4KlDha2H+KwjZmSfzRr6dW392/0kXA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.1.8": {"name": "vue-codemirror", "version": "2.1.8", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "eb09a4e6ca0e8ddea83003905bbebc18b73a92d0", "size": 8690, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.1.8.tgz", "integrity": "sha512-WykjJaDTlqlPB1WxtM8rsdzgR/d604xiMhbxO3uWIFvJMrvhpIXMxdPa7utx78NP6R35CgvrsvYziD2zTEtFpA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.1.7": {"name": "vue-codemirror", "version": "2.1.7", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "15a7262463155f0afe906569498d1b3cf1551f95", "size": 8577, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.1.7.tgz", "integrity": "sha512-LUhsyjWuPJaA7Dm7dpfyUumr0NbLgY6HFA4ifMbseMyWGQM98HZsYaIuJVLnr9ZLTwr6sjkR0UYnHsYDyX+CwQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.1.6": {"name": "vue-codemirror", "version": "2.1.6", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "85507e3e58dfdd85562ac6600f4c31d82279a2a6", "size": 8534, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.1.6.tgz", "integrity": "sha512-4AcQlCR+nqkFVaMQaZGuQW/9nk4xaU61DSAbnqBZHhL2Yj4xWxnoiCBQFlI28TM9XG7ijf6E4d8+mW1/xJFdWg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.1.5": {"name": "vue-codemirror", "version": "2.1.5", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "b1ca98645f11b7cdccb9f8ec6f019f49f3c9dc40", "size": 8369, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.1.5.tgz", "integrity": "sha512-WuknDCFX+LXVDV6W/1JaMwJNvxLnBloKCPe5G//aG4Ho+sqsYQ7weq+bkAWLlcKSaFjoE+siCk33rEnimzEdLQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.1.2": {"name": "vue-codemirror", "version": "2.1.2", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "27f423e9f57e0cae63f5b6b1e4f613109972c552", "size": 6695, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.1.2.tgz", "integrity": "sha512-LupIBS+HnKI9gHHHFQeeeNz7kHyW+dJqTjZOLX93JcrHZgs3SVUJCZmImCLRIAu4kAspcMK5SKQqR8fhZg1vEw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.1.0": {"name": "vue-codemirror", "version": "2.1.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "5030fcaa538a6983cddedfe7f4a7bb49bbdb4908", "size": 6438, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.1.0.tgz", "integrity": "sha512-BEDRnWTcVx3z1S1FeLUuSUC62sZUx9Ich91pv7bq7o10ss1/rGDEm35d4bvo5FwiJzbfa9yxLDY4J2rnSxdNPw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "2.0.0": {"name": "vue-codemirror", "version": "2.0.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "599c757ac8dc1eee5b56b57c8a777f70c22a9776", "size": 6402, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-2.0.0.tgz", "integrity": "sha512-5MDy8UObwMtOuXOsBYQWpI2H1Cf9AXsXSOsC9KUrVJ4DMuDTMROFtsA/4LYkR82o8xVR3HqO2OKtfrgtFdR2eg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.3.6": {"name": "vue-codemirror", "version": "1.3.6", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "b2023df2bcabdb06e49d63606bd5fa5668039581", "size": 5854, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.3.6.tgz", "integrity": "sha512-hFTNycAgdgm+7CuMkIMZuU5xskns9r8/+4gHD9KHLFc6c6RMNHOCa916WWIfR6Xmcifscd9UCYliNu4KjJTidw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.3.4": {"name": "vue-codemirror", "version": "1.3.4", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "a45cade18150184be562e3681c41f76112023ff7", "size": 5664, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.3.4.tgz", "integrity": "sha512-9lbeVl4xbcZtQCPOxgxea8tAEGJN8EPAs53kbaue8xCFPWo3nSXFu8WMdzAmlA226ET3SDv9Ovt1V3m1PVEQnQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.3.2": {"name": "vue-codemirror", "version": "1.3.2", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "f659618e95595203a2a88677e0e14f11b14b50bd", "size": 5653, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.3.2.tgz", "integrity": "sha512-y4K5hExVYJiEiHo6IVlcrgjGxt9sYU+nqXw640DQhUAGYVcDUuWMQ37+iOEq/SFHwAZ36AwQLq0odRHxZeFQPw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.3.0": {"name": "vue-codemirror", "version": "1.3.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "3c25fa99919a20a3872fbb2e717ed6ae433f55ed", "size": 5644, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.3.0.tgz", "integrity": "sha512-ML4UcAo1ptth+XkEraC4SWosI4bNz6ZEA6zJG8J5H3zo/lsmtY2gWK5fUKLfqtgewxuKW91kg0bIo9K19Z1R+w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.2.0": {"name": "vue-codemirror", "version": "1.2.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "9497cc5f55db5b80597374cdca2923e31e26755b", "size": 1639, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.2.0.tgz", "integrity": "sha512-8wco91FO69o+5WXKAX+vqznSESRv7pCsdnl+LPFLtGThwDspnjDRIe5Tx7hPZxqBkKcya7Q5vX06lfXLXQfruA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.1.4": {"name": "vue-codemirror", "version": "1.1.4", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "68af66731092879a5f2c74b457757789e66853d4", "size": 1842, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.1.4.tgz", "integrity": "sha512-mqPZh/MT0dMaXo8OsDfdrlDy6vHq0AXfCvfF/0FESsVOQhC+zGcEz2xyBYQgLOOrDWT4/D+YvNcYT3hVj5WeCg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.1.2": {"name": "vue-codemirror", "version": "1.1.2", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "28b414f983aec5a77a2815404a98566feb405c92", "size": 1888, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.1.2.tgz", "integrity": "sha512-EQeBmT7CocQ5ijgZyUfrKg+5eHesZsuH9391o1Ybs5FH+WLx/zbQ1k5wmVDlWfMRIViwb5QhSJBk5lmFqOI5YQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.1.0": {"name": "vue-codemirror", "version": "1.1.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "beba8dfa01f9e4b98b47f67987bf49c5b95dba4d", "size": 1875, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.1.0.tgz", "integrity": "sha512-/WKTPgNIgwyC1LwTus7PmtHpjodkzpFHq862fp9+IPjqTkjOA0oHy59kHj5K8TJbvybj9ZLrjKBKBkQfFlB/5Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.0.4": {"name": "vue-codemirror", "version": "1.0.4", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "c7a34aafaf3d0456a97403dbe52fc392cf22907e", "size": 1579, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.0.4.tgz", "integrity": "sha512-GgI7hpwz0UKb3Rt5LJ1/SNLYitejtKT3MYvIV71qN1DO38I1+g58X7mLDJaNA/0Zfb/jLSm/jbKXjMwgWITQbA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.0.3": {"name": "vue-codemirror", "version": "1.0.3", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "482cc7e4be480b920bfa064b85815c43a40daf06", "size": 1517, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.0.3.tgz", "integrity": "sha512-UNzfj4G2PcTgWgmaZVcI0sFFwoOI6eMcdlGe7m3Io+2P6v5LRpGnGLIqmyXyVfuQitumY4a6qZiIJJ14f2hegg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.0.2": {"name": "vue-codemirror", "version": "1.0.2", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "d82ac41b42a58b895915c00211b9cd5c93d33ef6", "size": 1440, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.0.2.tgz", "integrity": "sha512-PW3u6dK0D1xEchWMa4WJ8mOJC8mRLABEKAYwcSCI+qDJEW10NndKdM8HXEUrMFKAVXs/qXB+JLJybDXNMZnuKg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.0.1": {"name": "vue-codemirror", "version": "1.0.1", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "2b5c5db8902e27d8c01ff6b17377791eed8f364f", "size": 1478, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.0.1.tgz", "integrity": "sha512-SQ//8QYtp/Mi2D7UDyRG6XMd0UbYfxliZM4zrcsqaT+ya2oSTDyK35zN3vc5U7HO4nZIEavxnYwjlp4JPPtDmA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "1.0.0": {"name": "vue-codemirror", "version": "1.0.0", "dependencies": {"codemirror": "^5.19.0"}, "directories": {}, "dist": {"shasum": "0613a202bd674b9772cc27d9eefc24d889c77ec2", "size": 1497, "noattachment": false, "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-1.0.0.tgz", "integrity": "sha512-sFfJqULUTLL/S6tN0rgLkooybeAJcOemObc0pK+8HVFXrefmH2Jo8MduzEzxGZ47IX2o1v9sNfUHpv3MAz807A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "5.0.0-beta.1": {"name": "vue-codemirror", "version": "5.0.0-beta.1", "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "0.20.0", "@codemirror/language": "0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "csstype": "^2.6.8"}, "devDependencies": {"@codemirror/lang-cpp": "~0.20.0", "@codemirror/lang-html": "~0.20.0", "@codemirror/lang-java": "~0.20.0", "@codemirror/lang-javascript": "~0.20.0", "@codemirror/lang-json": "~0.20.0", "@codemirror/lang-lezer": "~0.20.0", "@codemirror/lang-markdown": "~0.20.0", "@codemirror/lang-php": "~0.20.0", "@codemirror/lang-python": "~0.20.0", "@codemirror/lang-rust": "~0.20.0", "@codemirror/lang-sql": "~0.20.0", "@codemirror/lang-xml": "~0.20.0", "@codemirror/legacy-modes": "~0.20.0", "@codemirror/theme-one-dark": "^0.20.0", "@surmon-china/libundler": "^2.2.0", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-vue": "^2.2.0", "@vue/test-utils": "^2.0.0-rc.21", "c8": "^7.11.2", "eslint": "^8.7.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jsdom": "^19.0.0", "prettier": "^2.6.1", "sass": "^1.49.9", "typescript": "^4.5.4", "vite": "^2.9.8", "vitest": "^0.11.0", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x"}, "directories": {}, "dist": {"integrity": "sha512-weoUT52VD6z0aKE9EhxxmUbTMfPaS79LsyOTeUSe6RNonStSOZhPjwn10fstzWl1vyzNIZfCg6+qQIgCtXr0ng==", "shasum": "3849288e00f184a9ac8b2e91adac42544bd566bc", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-5.0.0-beta.1.tgz", "fileCount": 9, "unpackedSize": 102518, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3FuhWjCMFTGc+kfEf0Mumv9ZqLl1DcFiIArVIVI2b1gIgbEDEda1D0ceqa1j20p6pCUZDqxpHbLlzEZVnhgYwX3k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiewZBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQeg//Q2rXD8FPbJYTT9u9OMzEnCHR76lB2kLypwIDav8IcTyjexgc\r\n4XFGO9mwjqUTvrN7pnz63lSt2iQ63BE4QJ9oBJqJHlPav3j3iSa+5LD1cG2S\r\nrl/930m0xvzVr5sBbfUBETZGlrMDgxnZldzhvnJlxRwFNSLLXas4CeTLQPz2\r\nZJe6vwEZ5tYH+c8Ikmw/uTE/lTlerlLw9nNnL4E9+MlCo2LN38XdkfMX6ibB\r\nHwhcGsZal/+qrjPIoDPB1eLmP66p3Zh3zGJy0Il4iVKoRKl13Vq2o5/CXfsi\r\n2t2h9CBTSF1HVGLyJdFx4t30uM25rRA9pe56McgeVmCxLjWXrM3ahW131uId\r\nlLpELE7lEfflvNe5sKSAx5cySi3kjQwEdqC8w8c8GE0LTPONVvih6AOAqLon\r\nZGm0uYbM/QRHqFIFo0x7fu3LaiMOuT9++Otq0YpyAWZUY7WKd9BQK5TKvKds\r\nfAxIaGA0eFTAeOcWOAUMJKxolki2GMkOlwlOG7rtpYc50/heHRRFdA/BMMy8\r\nkeUdhm88wSAfrhpARRFnfm9X30OmsBiONiiTZ+4fgTub+9bzKx9QfS6dBVmM\r\nfk6J8Y2cUQl2JWnpAhqfqjaIw71ACrw66ucHxA/C0OWs1Idt4/RKwV2dYfiy\r\naUxj60X//mYc9MpJP7xFU03IvHMPFL0SFmk=\r\n=hdX3\r\n-----END PGP SIGNATURE-----\r\n", "size": 16623}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "5.0.0": {"name": "vue-codemirror", "version": "5.0.0", "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "0.20.0", "@codemirror/language": "0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "csstype": "^2.6.8"}, "devDependencies": {"@codemirror/lang-cpp": "~0.20.0", "@codemirror/lang-html": "~0.20.0", "@codemirror/lang-java": "~0.20.0", "@codemirror/lang-javascript": "~0.20.0", "@codemirror/lang-json": "~0.20.0", "@codemirror/lang-lezer": "~0.20.0", "@codemirror/lang-markdown": "~0.20.0", "@codemirror/lang-php": "~0.20.0", "@codemirror/lang-python": "~0.20.0", "@codemirror/lang-rust": "~0.20.0", "@codemirror/lang-sql": "~0.20.0", "@codemirror/lang-xml": "~0.20.0", "@codemirror/legacy-modes": "~0.20.0", "@codemirror/theme-one-dark": "^0.20.0", "@surmon-china/libundler": "^2.2.0", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-vue": "^2.2.0", "@vue/test-utils": "^2.0.0-rc.21", "c8": "^7.11.2", "eslint": "^8.7.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jsdom": "^19.0.0", "prettier": "^2.6.1", "sass": "^1.49.9", "typescript": "^4.5.4", "vite": "^2.9.8", "vitest": "^0.11.0", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x"}, "directories": {}, "dist": {"integrity": "sha512-SvaTTaPaZ/7ASmrUWf1tajQikY5Y4yuflkA0IYoIx/L2/BhIxrsaW7/J1cSZYk7de6cRngu93+/k3f9EA2ncwA==", "shasum": "e6031fde5b05f5e1d2cfb599631eb3ac99b7448c", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-5.0.0.tgz", "fileCount": 10, "unpackedSize": 103942, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFpEgCHdDwn8nguIyXO8Gy7DARAqahmRpaMP2cSdqyeyAiEAnDxn2qYyfRlXDhXq8oWPuNxLMqYbtfRm1h/J2Y7BPaM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifiMjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSYxAAh5TGqwwcnVejzhRqcKLuCaDbDJwRBbxqciifc4/NlF+nxVXQ\r\nombBvUpwV8JI4Nl2sRbjgISFaVArBHH7MrB2YSD6iMCNGYy8Zwa+7yKaIYTT\r\nW9Kg8Fsoek3dw6p7HV21kGLcS5gJSkfYxQ/aIHJ47d4RTLJkoCQvKzwLtF4T\r\nsQ1LD3Ts9ih1Avxjg/baFzA1NqWgcpL+AEr0vssUTppgRFgZXNOnug8NOKKa\r\nDF1d9HKfaVac9o+hB/tQqE7ea9gMmr7iCHosV0hMWsbHLcWxpFhcsu3vPadr\r\n5pMnhbWvxUGoRVVN4BJnfrAWMfnciWj05tm6MY9SCWzhh/ZsSjPbpYIT5YE8\r\nTf6MN4zLRCZ4wsPSsQPxgs9kSj9Cv1deBIs3aHmI6in3ORMLGa/ybloElBvb\r\n+HdLPdZWMJS6+eagpXU6tgpYVPW4rSW9psi7Y3J4Wq4aWWD0/QzAm+a4n3DB\r\nc8UeMCpoWHoME9fGiR6s1eq4KgV9O5NnK/N1XgPQGWSuzwxmunHmniQe9fSi\r\nnY1ukdyIPDLlmSf8/Y20H3jslxq7UIoot4Qo7AW5qM+WB0+cv/IJLkV9ZLy+\r\nufY2FpyKSOOlJAECj7H2KdSu/Jenph//gyIPgv9bESMZI5UVGv4uMFzXgvJo\r\nGh8daI/Ajxq1M7okA/z96jOeLZs0WA2m/6Q=\r\n=b5yZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 17441}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "5.0.1": {"name": "vue-codemirror", "version": "5.0.1", "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^0.20.0", "@codemirror/language": "^0.20.0", "@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "csstype": "^2.6.8"}, "devDependencies": {"@codemirror/lang-cpp": "^0.20.0", "@codemirror/lang-html": "^0.20.0", "@codemirror/lang-java": "^0.20.0", "@codemirror/lang-javascript": "^0.20.0", "@codemirror/lang-json": "^0.20.0", "@codemirror/lang-lezer": "^0.20.0", "@codemirror/lang-markdown": "^0.20.0", "@codemirror/lang-php": "^0.20.0", "@codemirror/lang-python": "^0.20.0", "@codemirror/lang-rust": "^0.20.0", "@codemirror/lang-sql": "^0.20.0", "@codemirror/lang-xml": "^0.20.0", "@codemirror/legacy-modes": "^0.20.0", "@codemirror/theme-one-dark": "^0.20.0", "@surmon-china/libundler": "^2.2.0", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-vue": "^2.2.0", "@vue/test-utils": "^2.0.0-rc.21", "c8": "^7.11.2", "eslint": "^8.7.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jsdom": "^19.0.0", "prettier": "^2.6.1", "sass": "^1.49.9", "typescript": "^4.5.4", "vite": "^2.9.8", "vitest": "^0.11.0", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x"}, "directories": {}, "dist": {"integrity": "sha512-bSr601Okb4ezQOcGdxR3urlcWpIV4s8hbIJeMuwXlRc1qFDkHlnkcsvAzP0bPK3+TpWBq7ovi3FAjE4tMTbf7A==", "shasum": "7637839be5f3329374ebd5ffdc9f5dfa46e63ea0", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-5.0.1.tgz", "fileCount": 10, "unpackedSize": 103906, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDet7FOpWZtswhPsHTB17C0kcs6fkQTTGEQSw1QxGeduAIgOK1KR46A/fsHZ8YpA5PZwwo1Hp+Yd7nbO5Umnj4xl+A="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifvbWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYCA/+ImXiTQR2+5QivijUGshMR9H9nJ7zBCYVkRWD5n0d2EONXsVq\r\nPmjPqkGSboJMlSiaZYcH6Jf0Jyj+fQBAv28OO3aEcGFchp7rlws+9SGHTWdQ\r\nGLEARF9MNzZFzOYpjJ6LJzMzYsfkKLn+JuNa8c4NbjdGlD3p+dXkpdIbQbTi\r\nJ4kSiGvflrsVmiq7/9SN5ybejlS9CY7NsyuJ9RhPx8Ro892uEbYx4mas7WR2\r\ndZdHgrVUDfHstW51whiNyxI+z2U4GGuQ/HeMKNifR+FtmInuy6lPM+7M+AOo\r\nClLvh/1v5W2HiIjqdqO35VsBZgIltXuQ+AfQQbTVg4wCRzyiP7pNrkv7U6eJ\r\n9HgD/SLxbrcPrbrM6JTX2IiRQL4T8kHsB6fJN4JUjZ1+CXGL15/iEZknZXCn\r\ngMSiF91Qyu2upcyt2eA2ollvxvds9crODQLSXj4WlQjQtpchq60NeLWvxQ7I\r\nAKrw768uUi8Crgobw35ycCZqBdgAsmzotmlGExUCEtGiAJl+MgKXJCLawkxT\r\nkq+61T72mjhq1LkPi/+Mh49L/PrwRPvZC8C+zuXVs8EEY5GrLInhEKi5vuMi\r\nT4PuMW4epjF48ITqKnVHjWrJwRD2f8Fe0r7uzqbe6I4cdDVQv6efPxkSZzRE\r\njT/X6dp3cHdqVClmGOws3d6V3XiEUhivd3g=\r\n=wCHA\r\n-----END PGP SIGNATURE-----\r\n", "size": 17455}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "5.1.0": {"name": "vue-codemirror", "version": "5.1.0", "dependencies": {"codemirror": "6.x", "@codemirror/commands": "6.x", "@codemirror/language": "6.x", "@codemirror/state": "6.x", "@codemirror/view": "6.x", "csstype": "^2.6.8"}, "devDependencies": {"@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-lezer": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-rust": "^6.0.0", "@codemirror/lang-sql": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/legacy-modes": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@surmon-china/libundler": "^2.3.0", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-vue": "^2.2.0", "@vue/test-utils": "^2.0.0-rc.21", "c8": "^7.11.2", "eslint": "^8.7.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jsdom": "^19.0.0", "prettier": "^2.6.1", "sass": "^1.49.9", "typescript": "^4.5.4", "vite": "^2.9.8", "vitest": "^0.11.0", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x"}, "directories": {}, "dist": {"integrity": "sha512-U8t71S8Dk+9W3Yxfwv30E3vGYjfiKOqluoKkV1bW3Mh9y/T1cQAdGLzz7pN+Z8llivX+/8CJ5sg17xLPSOsvvQ==", "shasum": "208ffb25254937d50ea6caa248b38cd8e52fe040", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-5.1.0.tgz", "fileCount": 10, "unpackedSize": 76369, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9ChG5tjukpAQPOzjR8ZiB9VK4Qh9KbSsFG/GET69+xAIhALqDUlcmaZ5T5Kf/QBlYY1YN4Q2ATow/V7yE8ZvJ9eWl"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipO+rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNKw/+Ie2hrZu7tA4/9vdkx1eJlb1EpTYDEI8Z6P/0dhNvfGcIa5JB\r\nPm3lFhG2OwWMJ9BHXVaFeNVaCijyjFAqWj1XfBGCeDh+97XQz7d+wchTcF8o\r\nEsKKb5qBhUTEiH7hLEEmlNyqoi+cmt8T4pJ3PDyjJ9Lh4RcwKTHsxbTQ9Wwm\r\nBg0MJtAqM0uZTXPrPlDfv+NZORWxAYmlAALChpd9qQ0jZ5RbNstiSBYBHu2r\r\nvPLeIddrPJQLDoT5VYqImTr5IwASVRzXeMNwlfjOBUQX0mume4bWhhDt28AP\r\nA63onFk3oX+9b0kUn1vUk686q14X35tfOg7f0wC4lTRf17rY6z7MuBHEau+O\r\n4JlH+hYV2bpRKTIre4+QcUbjIk3BYPw3SFmjYa4xmgnQqegSEOe57elZiIVn\r\n6mYyz9BWBFVIVHsJWRIKc+BebrzqH50PNRsSzA0+UraFvmF2CZKWLKleHAvx\r\nFuvRPQc+hEpoZoej+Rjp0fEqoFNJhsNi1jcQmFoR4oHbmf3Eiyza8OOXiQzk\r\nsTc15B2Wexh/3jHMX0ZegZw7XmPq+4XhUBC3NTw32PzXG0+0RMFABU07b7ev\r\nkoCk1Ld6CiUFrDb5P0miP8hoJISh/2sZe9QsoIsLLfOAXtqahNnuNC4GdhlI\r\nrmHfg4jh7gEv2Ukw7jkN/HntFzF+j+fHyAw=\r\n=mmQE\r\n-----END PGP SIGNATURE-----\r\n", "size": 13498}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "6.0.0": {"name": "vue-codemirror", "version": "6.0.0", "dependencies": {"@codemirror/commands": "6.x", "@codemirror/language": "6.x", "@codemirror/state": "6.x", "@codemirror/view": "6.x", "csstype": "^2.6.8"}, "devDependencies": {"codemirror": "6.x", "@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-lezer": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-rust": "^6.0.0", "@codemirror/lang-sql": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/legacy-modes": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@surmon-china/libundler": "^2.3.0", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "@vitejs/plugin-vue": "^2.2.0", "@vue/test-utils": "^2.0.0-rc.21", "c8": "^7.11.2", "eslint": "^8.7.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jsdom": "^19.0.0", "prettier": "^2.6.1", "sass": "^1.49.9", "typescript": "^4.5.4", "vite": "^2.9.8", "vitest": "^0.11.0", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x", "codemirror": "6.x"}, "directories": {}, "dist": {"integrity": "sha512-1zYlS1l6Buxq0/PCw4gn2YQfWbINE0arEjtS/bZV1HcNMsgzotWbKmvRh9F+Ie0POX1F47gQricR731j4B/Ftw==", "shasum": "11c104603b99876839334ab26e9b1c29869cdbbb", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-6.0.0.tgz", "fileCount": 10, "unpackedSize": 76674, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID6DAAED4r0LKWs3ae53imecZHH+QD+K6RqR9NeaQQl0AiEA8WVO9na+HjLcOamy5K0WENlwoUEvDNREYbs6K9JwsRg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipuR+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyrQ//XaTRyOFN9PYGzgaQabOKIbSXfkZedzUnjKOMm6sqzXvXWTIE\r\nHLDsQr7SJuvxuNTdRwmF0XDCU6g2xgUxJoqUnuQUrA2Iv/+h2d3BDuw9n2q1\r\nQcCbgeBFA87+UNQrjOf9bBCfkW8K9DnmQmrzwZKZTJBDmUaUY71UCiS7hDqd\r\nccYAEjmEod1D4RLi6phXBbLs7Gr6jRQVB2dQaqj9z+rjuzx/fHlgmLp0kbyJ\r\n4456eQvo7o/KBRsW/s7R2P65hdpjgMuhd9sq1U+VM2rwU3Ay7KfKkTzcSSGG\r\n0ynitMXf3drzcHBZrCdSqBCyO7JDbP/7qi9e+i+M3AAZiX35LkRWihYIPqs/\r\n30+nLGuL05oA1h52K9G2G8Lkf2b8t5fXyI9SOygHWVkIjH8gNoJyng1oFJ9Q\r\nxQvfpZYbq6BrbHg7gFRtUqd+k1Wkt7dmsqwTM2kkrG7P82Dd7h3j2WXw8zMN\r\n1cT9bcCGm8ZXhuecw8CHWSZ4XdCAy12z7UsnBfUuSKks/xmtfOG9AioEidvy\r\nFQzQUgpX9ao7C1Fhrp49xtSkcLgAVkfQGKG+VlIamNeBD8CTXnQhq6RNv5ru\r\nf1iDpP5W7TljYRuM0Y7OGKlkSK38mPHNwWoXbD3UpcPk/ygxuIInEhdSw+ij\r\nNWAeoBL3IrEfB91+2p+aGGPCkluuvBroZq4=\r\n=hl3u\r\n-----END PGP SIGNATURE-----\r\n", "size": 13616}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "6.0.1": {"name": "vue-codemirror", "version": "6.0.1", "dependencies": {"@codemirror/commands": "6.x", "@codemirror/language": "6.x", "@codemirror/state": "6.x", "@codemirror/view": "6.x", "csstype": "^3.1.0"}, "devDependencies": {"codemirror": "6.x", "@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-lezer": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-rust": "^6.0.0", "@codemirror/lang-sql": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/legacy-modes": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@surmon-china/libundler": "^2.3.0", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "@vitejs/plugin-vue": "^3.0.0", "@vue/test-utils": "^2.0.0", "c8": "^7.12.0", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.0", "jsdom": "^20.0.0", "prettier": "^2.7.0", "sass": "^1.53.0", "typescript": "^4.7.0", "vite": "^3.0.0", "vitest": "^0.18.1", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x", "codemirror": "6.x"}, "directories": {}, "dist": {"integrity": "sha512-tEoaEFU2xZSADcfX0emHwGWXCZB8FEyoyUuGZmEPfLdTxK1nB+hp5WxFxuK7GWQxUD+2+bsAhJWT2IEaTbOpuQ==", "shasum": "0d157a121053aaae89f79c3a95711381a70e3193", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-6.0.1.tgz", "fileCount": 9, "unpackedSize": 79581, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/OXUGWofJPHQEQSP1978FZleA+atZIW6m67Kznq4lQAiEA3IR9Lfi2wMU4unv1rcaSHPRsGbY75A2GK8AEvBeV+Tk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2bsSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEAw/7BFIGEVatIosEPGwO2ALP/iSU3pMHLqVaVO7BrR7TBW7WU9F5\r\nfqgXWWEZZwCWChHYT+zzRHq1cMxCBDX2Fb0zVJTRGjAV3tIN7ZzcagUUvtlL\r\naYYoUMYCJwuqKHtAexHXhbUvvSveipL8fYWolBqKhNYRz+ksDPwY47mbvbP/\r\nbmsfaJB41oOn2q3DzSJ7EZh5FhO56b8n53As9OC9C+Y2wvGq1SDHVj3/HV//\r\n5ZyE/ASxuDdNUJ0hedCsoJOG4eb/2F1cs/vz3PrLor48juJzhtlcjPd4Ntuo\r\n2fe7MbMAZkjq6SnTq8d0wG/vxCfaqqTmZ+HBulsXSnKLXQizRDXjZLXQ813v\r\nQ39aQdBFhixOs+GgKLzGdK1F6O5xbZ3LUz74/iYn6agVdKoZiQFxTp3+L+9u\r\nvJWphfZSdT4RYyWdE1ok0mfS3ZlTOphM8uicahV7jEAQdaCnilZ3kSnxE+QT\r\n243bq8u83WJt21fDgRhynG+Fd2l9QkWp9gWTpF1rgg3synNrvf6xuOTRpoSf\r\nHDTfEP8JDeUu74+RqPntF1Zdl7DnmQytuEKFyUwzxxx05StL2WcoAiuiA8aV\r\n7am4jwSuf3YwwvZQM/T601Cf9eMRvk22Bbjay13jvTNqiaCIJ4rvz6NWVe1R\r\nFaLJ+X2ywBjmvvU+h1MdDsO4bwi2pgkqpac=\r\n=1Y+K\r\n-----END PGP SIGNATURE-----\r\n", "size": 15025}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "6.0.2": {"name": "vue-codemirror", "version": "6.0.2", "dependencies": {"@codemirror/commands": "6.x", "@codemirror/language": "6.x", "@codemirror/state": "6.x", "@codemirror/view": "6.x", "csstype": "^3.1.0"}, "devDependencies": {"codemirror": "6.x", "@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-lezer": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-rust": "^6.0.0", "@codemirror/lang-sql": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/legacy-modes": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@surmon-china/libundler": "^2.3.0", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "@vitejs/plugin-vue": "^3.0.0", "@vue/test-utils": "^2.0.0", "c8": "^7.12.0", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.0", "jsdom": "^20.0.0", "prettier": "^2.7.0", "sass": "^1.53.0", "typescript": "^4.7.0", "vite": "^3.0.0", "vitest": "^0.18.1", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x", "codemirror": "6.x"}, "directories": {}, "dist": {"integrity": "sha512-/fUei5398vsS9oLV0tm1tUFc69wst44uSFrNaPktC9qto1oshi5VrK8MXqybMIMo9XUUQNc4BAdzn2jkwg9fxw==", "shasum": "5455ba9882a8fe2b90c1a105bf80a59ef4941427", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-6.0.2.tgz", "fileCount": 9, "unpackedSize": 79773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgZEYCBWlA5ayMxr9Cphpsm1EoAp/nv3nvt9jyJ4xcCAiAb3bMDEob0quAqEDxy+DwP8aFg92Z4PBjQM/7iT7f1AQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4UeMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkDxAAnb25uEiu5D964sGO4QK6ZZFxQkq2NjxkzVmAbUS1Fr3BOnS1\r\nI8k18BIKKS/c0RlicTD75S9HOq3IgZSFDwE2ghAp21h57DxlpQTFi+brAb4G\r\nsVZfLo3twZGQb1hxKyO1dewMsomkeqAJxKQewNVADucTyOkkOt5bdc4tYPVw\r\n7HSWt5jZreZsiCA9blsp/iKhuK2tIlPy+ist0r4ZoQJGP0X4XN4BmGtf6f4F\r\nKmreIi2joTEAMHD5Lqz4SVx8CYvK7gAqjb2TtKFkqlh7xpg/9xs1KhA2uYNW\r\ngy3tUNrDPQATpTFwD6WKGXRK5J1W4hh1//483kpAZiEufuDPobIkwyj/LRX7\r\n4rHyxeQJaM6i48+zfJ4+d/UQsRjzmMR3p48+Udl+ust29zcEbpia5k5roTNI\r\ngzaol3aM2A8gAX1dE5HJVnDzRq341aaQZCwl9JdG6O2A1qAXNASTujH7scZT\r\nHwVeQzxyXfOhEcLNM0h1kTYBfyMqn6fZd4Pf7j3p9X/2yz/iBbbh6jIfrkiu\r\n0axJb6aahHr6LF1WkNxqIH/90SZ0WfvAuYWMo7Fz/IOiMAAMxmtZYnvZw+O0\r\nNyHs/UBlU158ResFD/8kSwdGlfQbm8wFaQU4VBML9CszK5t/cczPetPCQQbc\r\nstpEtMAae5jEx13plXoGqRNDFLMNQpI6HWw=\r\n=VsMD\r\n-----END PGP SIGNATURE-----\r\n", "size": 15057}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "6.1.0": {"name": "vue-codemirror", "version": "6.1.0", "dependencies": {"@codemirror/commands": "6.x", "@codemirror/language": "6.x", "@codemirror/state": "6.x", "@codemirror/view": "6.x", "csstype": "^3.1.0"}, "devDependencies": {"codemirror": "6.x", "@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-lezer": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-rust": "^6.0.0", "@codemirror/lang-sql": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/legacy-modes": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@surmon-china/libundler": "^2.3.0", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "@vitejs/plugin-vue": "^3.0.0", "@vue/test-utils": "^2.0.0", "c8": "^7.12.0", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.0", "jsdom": "^20.0.0", "prettier": "^2.7.0", "sass": "^1.53.0", "typescript": "^4.7.0", "vite": "^3.0.0", "vitest": "^0.18.1", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x", "codemirror": "6.x"}, "directories": {}, "dist": {"integrity": "sha512-pJC8F1cwGp+QaESZ4eHNtytOa8BppaV7mTfRM+GtPZw/2EnMXvjMMMgX1x4HzT3MeI8F/+Var0e132mW0j/8tA==", "shasum": "260ea6cf7af448c33b14624038b583011dd753ea", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-6.1.0.tgz", "fileCount": 10, "unpackedSize": 85365, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMi27qzun+uuQQRP97tOu1I+hNsTevPXHHgWvFVjZtuAIgE3ECBiK7G57zcPn5eSgLpIyz5ZbJpySVr0blcPS638E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjChQaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOUQ//W6DBZ2yHxAhlk7eXaWf9WtHWkednazKAWs1i/Br+pIAsbuWs\r\nvwqI+KOlgWp5QjHgXBPsu8Jp5kr2eFfYFgBssXkzQAhn1Deod5nX2hCcUyrD\r\nqOZHzwwKuI/OJDyleO6a4DKtXH0B77MCZMU4N9N17qKv7+pUWvtG+qD0nqEd\r\npyXCQZNpIUmVFCrcY0sH/XjD17NtkrVDPMdv2fdcHwhArFPsp6uw30ELiNGA\r\nkDQpIhy50EhpJm+A1w4v6AE4w2PmdHlj1rvG9b94j7G2EjGMey+L3O5LcoHr\r\nMgwvARnz6ek1FCY+XQqfIqxcTYUN2h6eU8oOUBciSrRn7OGnBAq2qHv9Jrjj\r\nuBI5iFNVZJ0w7o5TUAl7oeMDlfLssy8RZ1aUVtEJpGXDwfycqBryHNNLHgwX\r\n3YxzEz2AQAaSQCDPjWhtc/ra/p76Qe7RnlhlIeXfS2rltCc2vZcdSE1N87ag\r\nOMs1Ilfr+epU/I0Uic7FPYqlDyrGSi7YRlKQvOze09ZVjjCnTg6g8Cn0dTju\r\nRUsVZcMbB8JcG/2KnpmtH1bsAGO0SlAcFPT60wYXNQkGMuK/gBH3CYxRdsWS\r\nBV0w6d0OnKfzzLlAyU8EmZyjnkzmElHWxsoEa4UuMpfSzMk/mpi7yV07tYov\r\nGGbF3oJEvEKQTdOAuLZyZsebiQIw3B3CUyU=\r\n=G/nW\r\n-----END PGP SIGNATURE-----\r\n", "size": 16351}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}, "6.1.1": {"name": "vue-codemirror", "version": "6.1.1", "dependencies": {"@codemirror/commands": "6.x", "@codemirror/language": "6.x", "@codemirror/state": "6.x", "@codemirror/view": "6.x"}, "devDependencies": {"codemirror": "6.x", "@codemirror/lang-cpp": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-java": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-lezer": "^6.0.0", "@codemirror/lang-markdown": "^6.0.0", "@codemirror/lang-php": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-rust": "^6.0.0", "@codemirror/lang-sql": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/legacy-modes": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@surmon-china/libundler": "^2.3.0", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "@vitejs/plugin-vue": "^3.0.0", "@vue/test-utils": "^2.0.0", "c8": "^7.12.0", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.0", "jsdom": "^20.0.0", "prettier": "^2.7.0", "sass": "^1.53.0", "typescript": "^4.7.0", "vite": "^3.0.0", "vitest": "^0.18.1", "vue": "^3.2.31"}, "peerDependencies": {"vue": "3.x", "codemirror": "6.x"}, "directories": {}, "dist": {"integrity": "sha512-rTAYo44owd282yVxKtJtnOi7ERAcXTeviwoPXjIc6K/IQYUsoDkzPvw/JDFtSP6T7Cz/2g3EHaEyeyaQCKoDMg==", "shasum": "246697ef4cfa6b2448dd592ade214bb7ff86611f", "tarball": "https://registry.npmmirror.com/vue-codemirror/-/vue-codemirror-6.1.1.tgz", "fileCount": 10, "unpackedSize": 85054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICjl/VVTQxUnQqXciedjtk0n7oomJ/r/aVDE2T5ChDEZAiA0BDGu1QL262ih1ax1nDUDxhf/bYHQdQcNxLr9Ug9l6Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCnKwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRWA/+MVlcrW2MfTYNQ0N+yijBKsrQwMnuFSR0EdXNPZoRGxOa/+jE\r\nF0tGFhwcRMJR7lixq+u7gJPPeqY6kd81NMGtUG7PEhXGDg9wgFmEopFXEXdU\r\nhEOQcFDH+Bxne1xtSSrEIxF5ljHa22zencXfRT5jJmYa8/Y+lKECLk1O5nQL\r\naCsBE0WInFVsKMCvXtR43a5f/izg1DEdbl1A8DRlhWjahaYCxOkkM5zoBGon\r\nfAi3uNWeDONywbDOB8YgIkvv0UZwNEFIpsagVqyi6QjMoKTEJ1oOGp6FJ3ZT\r\nXCGsoRMu0wMa31O7JxQDMTsDQ1PejyYgd7xOZUlY4irFM+OHThy2Wx2Alpwb\r\nJSOo4Vdz9DT+GZhmP3uICbImfSRA57g7mNVkZsI0wA+Z7dpZnbl4CzM+GLjZ\r\n+Y1J9zcOiNU+N/2GydxwvrTLcMDjgTGGqVEeOOh1g6F62/AMGmCyHusEYfAL\r\nZl5JQC7ISRAFJ23++nQx+lnOuJw0Tqvj+pPY4otY/OLgYLSxj6Gn+KtJ6EzL\r\nLTERh6oEaq+9hJn5e/FldQWpftxx8cy7krC8iyIod/syjKfGxjFIS7CLBQEJ\r\nx97RFJFB0t6XCb1QBZcMZYiSrkgu1z4qE3r1Z3d9k0PoFjNjJo+ibKjoBSMM\r\nCXOY7OGktziYSmp7NSnBaaspQ2PHkwpNu3I=\r\n=x2Di\r\n-----END PGP SIGNATURE-----\r\n", "size": 16344}, "_hasShrinkwrap": false, "_npmUser": {"name": "surmon", "email": "<EMAIL>"}}}, "_source_registry_name": "default"}