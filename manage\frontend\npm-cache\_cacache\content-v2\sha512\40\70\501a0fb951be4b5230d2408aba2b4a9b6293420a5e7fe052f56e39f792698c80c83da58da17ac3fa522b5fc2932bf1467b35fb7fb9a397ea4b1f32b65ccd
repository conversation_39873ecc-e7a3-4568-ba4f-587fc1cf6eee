{"dist-tags": {"latest": "4.0.2"}, "modified": "2024-05-25T19:24:33.239Z", "name": "picomatch", "versions": {"2.3.0": {"name": "picomatch", "version": "2.3.0", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "f1f061de8f6a4bf022892e2d128234fb98302972", "size": 23519, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.0.tgz", "integrity": "sha512-lY1Q/PiJGC2zOv/z391WOTD+Z02bCgsFfvxoXXf6h7kv9o+WmsmzYqrAwY63sNgOxE4xEdq0WyUnXfKeBrSvYw=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/jonschlinkert"}, "2.2.3": {"name": "picomatch", "version": "2.2.3", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "465547f359ccc206d3c48e46a1bcb89bf7ee619d", "size": 23213, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.2.3.tgz", "integrity": "sha512-KpELjfwcCDUb9PeigTs2mBJzXUPzAuP2oPcA989He8Rte0+YUAjw1JVedDhuTKPkHjSYzMN3npC9luThGYEKdg=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "danez", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/jonschlinkert"}, "2.2.2": {"name": "picomatch", "version": "2.2.2", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "21f333e9b6b8eaff02468f5146ea406d345f4dad", "size": 23357, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.2.2.tgz", "integrity": "sha512-q0M/9eZHzmr0AulXyPwNfZjtwZ/RBZlbN3K3CErVrk50T2ASYI7Bye0EvekFY3IP1Nt2DHu0re+V2ZHIpMkuWg=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "funding": "https://github.com/sponsors/jonschlinkert"}, "2.2.1": {"name": "picomatch", "version": "2.2.1", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "21bac888b6ed8601f831ce7816e335bc779f0a4a", "size": 23260, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.2.1.tgz", "integrity": "sha512-ISBaA8xQNmwELC7eOjqFKMESB2VIqt4PPDD0nsS95b/9dZXvVKOlz9keMSnoGGKcOHXfTvDD6WMaRoSc9UuhRA=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/jonschlinkert"}, "2.2.0": {"name": "picomatch", "version": "2.2.0", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "e16e8587cb70cb515a698ee4a0f2df1edb0c9aa3", "size": 23034, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.2.0.tgz", "integrity": "sha512-Gskshv+376q+hmJbbOiMK+Jtv3bNkwnoOtVSPkcSrvsu9WuKJvBXOcSlBkWgWE0+A011wfafeWpiDrdbowddfg=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/jonschlinkert"}, "2.1.1": {"name": "picomatch", "version": "2.1.1", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "ecdfbea7704adb5fe6fb47f9866c4c0e15e905c5", "size": 21232, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.1.1.tgz", "integrity": "sha512-OYMyqkKzK7blWO/+XZYP6w8hH0LDvkBvdvKukti+7kqYFCiEAk+gI3DWnryapc0Dau05ugGTy0foQ6mqn4AHYA=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "2.1.0": {"name": "picomatch", "version": "2.1.0", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "0fd042f568d08b1ad9ff2d3ec0f0bfb3cb80e177", "size": 20843, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.1.0.tgz", "integrity": "sha512-uhnEDzAbrcJ8R3g2fANnSuXZMBtkpSjxTTgn2LeSiQlfmq72enQJWdQllXW24MBLYnA1SBD2vfvx2o0Zw3Ielw=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.7": {"name": "picomatch", "version": "2.0.7", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "514169d8c7cd0bdbeecc8a2609e34a7163de69f6", "size": 20209, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.7.tgz", "integrity": "sha512-oLHIdio3tZ0qH76NybpeneBhYVj0QFTfXEFTc/B3zKQspYfYYkWYgFsmzo+4kvId/bQRcNkVeguI3y+CD22BtA=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "2.0.6": {"name": "picomatch", "version": "2.0.6", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "f39cfedd26213982733ae6b819d3da0e736598d5", "size": 20203, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.6.tgz", "integrity": "sha512-Btng9qVvFsW6FkXYQQK5nEI5i8xdXFDmlKxC7Q8S2Bu5HGWnbQf7ts2kOoxJIrZn5hmw61RZIayAg2zBuJDtyQ=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.5": {"name": "picomatch", "version": "2.0.5", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "067456ff321d6447ca3dc32273d7bbf19ab2face", "size": 20140, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.5.tgz", "integrity": "sha512-Zisqgaq/4P05ZclrU/g5XrzFqVo7YiJx+EP4haeVI9S7kvtZmZgmQMZfcvjEus9JcMhqZfQZObimT5ZydvKJGA=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.4": {"name": "picomatch", "version": "2.0.4", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "b972630d6ba532d905a4d6524c095d0d2140b4ed", "size": 20131, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.4.tgz", "integrity": "sha512-lN1llt2d+xBz96Vp+yj0qMUVMyDsqvNSecdRDIEuh72kQi1N6ttkxPJ7zDVwKR4ehD2R3WhMKqdp/5LeRfc+PA=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.3": {"name": "picomatch", "version": "2.0.3", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "8523b368260e5f6fb5da5e45fc7a582f9a0a15e2", "size": 18824, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.3.tgz", "integrity": "sha512-NSJqCeCfH7loxpU6i04aBiJZv4oZFlLKTLTyoWCpJnC28Qm0T1XUfNfzwLi+vtFNGIjc9pyvx/NJUJhFQ3Ptdg=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.2": {"name": "picomatch", "version": "2.0.2", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "7369a814568197e8bfaab6a53d6a2f989cb09e3f", "size": 18823, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.2.tgz", "integrity": "sha512-PsJljvIvCvQjzgw2ejIP/t1z9obDXzXxcDLc4IwzKS99UzVEzRqXJS3NEK9V8cuFk4d7DzYb6N8AVKBmq+j9fQ=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.1": {"name": "picomatch", "version": "2.0.1", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "5eb60e9bc20eba769e6bce404d0d4f7c5f5d099a", "size": 18788, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.1.tgz", "integrity": "sha512-zruX90vxmnhSGd3mzHzNL+cbnZ/CTLGGI3MrCrcoP6mSHLxZP8n7Obg7MF3ZrN9Pb1u0I+fEOiw3CDp87D8Fzg=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0": {"name": "picomatch", "version": "2.0.0", "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "2500b6aa582f98f9f30c6d671bd48f288045f977", "size": 18787, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.0.0.tgz", "integrity": "sha512-LCp3QJ9q6YPH7u0dZTgOuxj4DW20Li5R1xQAnzsOs9R0OmadvgxiPsTB224Jh/5pnyYlb48n9nETuQCOqB37Jw=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.2.0": {"name": "picomatch", "version": "1.2.0", "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "nyc": "^13.3.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "ac60ec8b6ff5953a505de2fd5b2b0a1e1d7287e0", "size": 18289, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.2.0.tgz", "integrity": "sha512-cMSUWLsIT8VCmIPlM87m5OASGmDnYJR2fEUIpOnuArPQY5p7kWfJ47wZtIOYHT8GJHo9B/QRr4m5mbsIXxZkMg=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.1.2": {"name": "picomatch", "version": "1.1.2", "devDependencies": {"ansi-colors": "^3.2.1", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "gulp-format-md": "^1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0", "nyc": "^13.0.1"}, "directories": {}, "dist": {"shasum": "122f5efb555576410acc4bc328fb1a1eb69ae537", "size": 13830, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.1.2.tgz", "integrity": "sha512-FcXFq7ttIsWpwlMLaDvVskGLNSfKB0ibgEV4KpuJACD1S45dM4ViQuGrPMxNX3mXUtg3OZ3xC5Ny+lJS1PqIZg=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.1.1": {"name": "picomatch", "version": "1.1.1", "devDependencies": {"ansi-colors": "^3.2.1", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "gulp-format-md": "^1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0", "nyc": "^13.0.1"}, "directories": {}, "dist": {"shasum": "ddd4fd27472e8d558bfb6a8f7ef36b1c1a338d8b", "size": 13846, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.1.1.tgz", "integrity": "sha512-VWDOC2iibCJClkU5A1t3tUEGi4Rs73W7gyxO2enf5SCvkQoz+Q9viQSRu300ZUSRXMip/Esnt00sOffpDUFkrw=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.1.0": {"name": "picomatch", "version": "1.1.0", "devDependencies": {"ansi-colors": "^3.2.1", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "gulp-format-md": "^1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0", "nyc": "^13.0.1"}, "directories": {}, "dist": {"shasum": "5e32a8576929be2cd3a0ce94b5b9e35efe2adb07", "size": 14340, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.1.0.tgz", "integrity": "sha512-ncNfzrfFY1cC3Vtdu8NPVdt5z4iNkjDT1e5fAQo0mmKXGD4nxJqSf07iR53bHJBumJJn3Ya9jgloj9mXZe0S1w=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.2": {"name": "picomatch", "version": "1.0.2", "devDependencies": {"ansi-colors": "^3.0.5", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "gulp-format-md": "^1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0", "nyc": "^13.0.1"}, "directories": {}, "dist": {"shasum": "47a605ba6b76f20305331302b5d4ed731c69bdc9", "size": 13282, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.0.2.tgz", "integrity": "sha512-tTQlevEXfC16KUht/5hM84OBmCVO4bc5MiJ8VSwbD13dfv8ICCjRaDo9Mi2ZgZYm5EaCojRIhk8xIl8Y3+JNXw=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.1": {"name": "picomatch", "version": "1.0.1", "devDependencies": {"ansi-colors": "^3.0.5", "bash-match": "^1.0.2", "fill-range": "^6.0.0", "gulp-format-md": "^1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0", "nyc": "^13.0.1"}, "directories": {}, "dist": {"shasum": "299734ea87cc808a76fd173bee0ae32ea38e0a39", "size": 5950, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.0.1.tgz", "integrity": "sha512-LhU431VW6NbNnXz9K7MxRMLPov3MzO7sFw289mltXXdAQmkmi6TCZ+60t1V2qS7HF/bAPJ3WwOYtJDCJ0lp+QQ=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0": {"name": "picomatch", "version": "1.0.0", "devDependencies": {"ansi-colors": "^3.0.5", "bash-match": "file:../bash-match", "fill-range": "^6.0.0", "gulp-format-md": "^1.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0", "nyc": "^13.0.1"}, "directories": {}, "dist": {"shasum": "ad8e4b63ed9ec16ecce02e55b7cca6eb1933e0f8", "size": 5944, "noattachment": false, "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-1.0.0.tgz", "integrity": "sha512-5RUhp8IAAODWzJMyt8caoCR0mzKjcIk8VHCmNzrId095B8KB7U+yxJ2oTbWnesWVk7BQXdRCm0JFFUCNDnokCA=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.1": {"name": "picomatch", "version": "2.3.1", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "shasum": "3ba3833733646d9d3e4995946c1365a67fb07a42", "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "fileCount": 10, "unpackedSize": 89952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0d5gCRA9TVsSAnZWagAAhvsP/3X0Wzp61vQEMw/gnra7\nCpQhnAtHPgHqZkNz8zCtuudav0Hdp4qNVCQKPcNeFHjYdJFKbFf3C97nPvEr\nVxtxYEQDvk4pkmpLqtY34oFKlT7nlezLjOigwL9GxjBlTqy35mgIx4Hemf3R\nUuKHDfHvQQ+4hH66cDlZwv3rWEMVTrkqXQblQ4ZVnAQuvnhhM1ehIUuSpw2y\ncJ+7DMNW4sbEAMWTK28F/v1IBnlcN3ENY/387Z8nqedY1oJZpmgK1iVojHv6\n29dDa+la+GR5cBQGXBXj66t89kK/7p6BQMSYUJ+FcecHBPZ+rW5rAGXJlzx7\nqw3QdUZQ5TQzcRR/4MJ1ALKEM6Pk7COdO0+RJ+ST0Jitw3t+PZkXGgG3lIW/\ngOghIdK1jqP7Fy3y82ECOwst0akaqX2I1Oe3tktn8CMvlbwg6jaPp8l7jI7d\ncaWZx+peRszFxqY5GicA42xDs1UQ0YyWQeowkLUcFswF2YihfccEZ3JqfGZL\nKPNH1i55aZc4nRX5vajmq7lCSXgRYgGZ18s/UOQXWU/BHY1+GGF4zf4h0Spi\nEHCofcLg7umyRvfvj//Ig8qtUHNUxgs7ioN3tgXq63Gdu8KYN0hU5RwTxSDR\nzboyO+txrwwtntrbZMOIL667Ndfnj9FtMfLdEjBdZBXfddOXbCJPErG6ZoEW\ninis\r\n=D34Z\r\n-----END PGP SIGNATURE-----\r\n", "size": 24289}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "mrmlnc", "email": "d<PERSON><PERSON><PERSON><PERSON>@rambler.ru"}, "funding": "https://github.com/sponsors/jonschlinkert"}, "3.0.0": {"name": "picomatch", "version": "3.0.0", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-G+bUnjy7wQOJBK3YS6hErp7nUOJ+SWI+JZeDEATcdAjnHZwjJ6TXm4TGOHgGScf5lCNGdT9rXdqIa0tJ3lYhnQ==", "shasum": "95ce1a281a24a340c3ddc981a63d89360c6dab9b", "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-3.0.0.tgz", "fileCount": 10, "unpackedSize": 85254, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlgwNuwXNcGNrSM2miknioTM5TQ6E7+nkpnomdbvnl3wIgZgQM+olhYLgVx1q12907ucYPHoxVfvtPVgU9sT5c4jA="}], "size": 22122}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "publish_time": 1698541101803, "_source_registry_name": "default", "funding": "https://github.com/sponsors/jonschlinkert"}, "3.0.1": {"name": "picomatch", "version": "3.0.1", "devDependencies": {"eslint": "^6.8.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-I3<PERSON>urrIQMlRc9IaAZnqRR044Phh2DXY+55o7uJ0V+hYZAcQYSuFWsc9q5PvyDHUSCe1Qxn/iBz+78s86zWnGag==", "shasum": "817033161def55ec9638567a2f3bbc876b3e7516", "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-3.0.1.tgz", "fileCount": 10, "unpackedSize": 85253, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBshR2DnpauYeCpPH2PdLEzVVn/6IVwxX5cKcyKw0IvAIhALWEelCNTAwW0WwNliGRF1uV888N3KKpr+pxbwcc/rXM"}], "size": 22122}, "engines": {"node": ">=10"}, "_hasShrinkwrap": false, "publish_time": 1698567170118, "_source_registry_name": "default", "funding": "https://github.com/sponsors/jonschlinkert"}, "4.0.0": {"name": "picomatch", "version": "4.0.0", "devDependencies": {"eslint": "^8.56.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-luYHSuwP2yHKySJ5J1ujgN2gSGFP+Ua6F1dvOTusl392lTqouuf5KO3QFRbc8nBM1uWiDhltecTxnij9wIorAA==", "shasum": "8410b450c046948141ca7bd8adfc8cf753a4652a", "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-4.0.0.tgz", "fileCount": 10, "unpackedSize": 85192, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJbuXzmUIOMdoVNnsqL81eIs7X7diCc/TkH+4oa4DfvAIgUu7HvSrrNRFmED1HZ3SncfvnwcVDurFd49+2W5RSg28="}], "size": 22088}, "engines": {"node": ">=10"}, "_hasShrinkwrap": false, "publish_time": 1707358932147, "_source_registry_name": "default", "funding": "https://github.com/sponsors/jonschlinkert"}, "4.0.1": {"name": "picomatch", "version": "4.0.1", "devDependencies": {"eslint": "^8.56.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-xUXwsxNjwTQ8K3GnT4pCJm+xq3RUPQbmkYJTP5aFIfNIvbcc/4MUxgBaaRSZJ6yGJZiGSyYlM6MzwTsRk8SYCg==", "shasum": "68c26c8837399e5819edce48590412ea07f17a07", "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-4.0.1.tgz", "fileCount": 10, "unpackedSize": 85192, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHpZMEvmMlm+TOQuad1AEOJydXIMsmBfnlaYa9g7RLsAAiEAphtzMzPrvk5PaPdc3zMRFy0hXjMYtdu0DNxS5zQsZjs="}], "size": 22089}, "engines": {"node": ">=12"}, "_hasShrinkwrap": false, "publish_time": 1707359513301, "_source_registry_name": "default", "funding": "https://github.com/sponsors/jonschlinkert"}, "4.0.2": {"name": "picomatch", "version": "4.0.2", "devDependencies": {"eslint": "^8.57.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^10.4.0", "nyc": "^15.1.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "shasum": "77c742931e8f3b8820946c76cd0c1f13730d1dab", "tarball": "https://registry.npmmirror.com/picomatch/-/picomatch-4.0.2.tgz", "fileCount": 10, "unpackedSize": 85237, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtbvwmKkqlXNXSqLwMF1mRFHpVKpPVOPu7o3tmOeZbrAiEAuTHJPb1fKWoHwYrKEQpmPkOLBMChBX1HltMmP7P6PBI="}], "size": 22098}, "engines": {"node": ">=12"}, "_hasShrinkwrap": false, "publish_time": 1711596191348, "_source_registry_name": "default", "funding": "https://github.com/sponsors/jonschlinkert"}}, "_source_registry_name": "default"}