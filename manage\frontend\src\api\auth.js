import request from '@/utils/request'

// 用户注册
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

// 用户登录
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 测试认证
export function testAuth() {
  return request({
    url: '/test/protected/',
    method: 'get',
    validateStatus: function (status) {
      return status >= 200 && status < 500
    }
  }).then(response => {
    // 如果需要，从 token 中获取额外信息
    const token = localStorage.getItem('token')
    if (token) {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        ...response,
        user: {
          ...response.user,
          is_admin: payload.is_admin
        }
      }
    }
    return response
  })
}

// 更新密码
export function updatePassword(userId, data) {
  return request({
    url: `/users/${userId}/password/`,
    method: 'put',
    data
  })
} 