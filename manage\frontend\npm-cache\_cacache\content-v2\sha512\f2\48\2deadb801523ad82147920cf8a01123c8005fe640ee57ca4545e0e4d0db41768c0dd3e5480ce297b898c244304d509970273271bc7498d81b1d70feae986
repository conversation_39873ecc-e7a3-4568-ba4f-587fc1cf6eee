{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:22:56.186Z", "name": "@parcel/watcher-linux-arm-glibc", "versions": {"2.2.0-alpha.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.2.0-alpha.0", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-YZ/65jtYaQp0UY4uJY+QULQpsvcY5uNWJJDLTuixhXTSqCMmdD50AntHlEopRjpSF+GS/hYOSvAq6tg72WCz+w==", "shasum": "3fa5948e909775a7f37cf41754f72064840f5312", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.2.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 399429, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcG7VzpqawzHz7G01Ll9Aqs7AGVLwB3w3urJX4Ucq86AIhAI18Ssb1rPMmDfx49YQ5dDFiYUQ11QFxDpmhIE34QuRO"}], "size": 157663}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687727613320, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.2.0-alpha.1", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-1hm2+WlvOmaKnEuCgwaKfEHa7neINROQwXXLvKNx3UQX63Cd6uSE5XcAAvEK+ABsaJbVvmlPw5wmBKIPWJ9hSg==", "shasum": "8a862dfe4744d398b4ca90f32e8e3e2f49dacbc5", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 399429, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrQX5uAz3QD6fmWDWpkpAICvQ45Ts9TulArDFHJyQAmQIgHVY5BH5salb5R+z8/2L4g+Zd/zxCGXpgd9R3QH6x+vc="}], "size": 157663}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738105646, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.2.0", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-I2GPBcAXazPzabCmfsa3HRRW+MGlqxYd8g8RIueJU+a4o5nyNZDz0CR1cu0INT0QSQXEZV7w6UE8Hz9CF8u3Pg==", "shasum": "2644f6292b862c52d636978da8711bf2f7b14b24", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.2.0.tgz", "fileCount": 4, "unpackedSize": 399421, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTlTE70b8FuKmLUGkA0EPLV+UlouVV6ZABzyVeNpZDLgIhAJ9jg66EAGdhydd+Jz4iLRyk8mgWrYyREm1CQTW6urVo"}], "size": 157659}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269819505, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.2.1-alpha.0", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-nmLSEhzYACM+EsGGRoZENcmEzluOs+KgjRF9QCNqoNmToKDYV2gs296k6AIRqOIFfK9FzW9BvK7TAzvY4TNrjw==", "shasum": "fca98003c9f37ec4a0e0f0eb5dd24693293db3f1", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 407637, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ9THuHegk2IeGb60IFtK1nc8MvlrGw3dMQr6eTrbpOAIhAMQCFMC7bT0Yu7CZ3ea4E9a7tpTT8MEqPZvCkBlfpndF"}], "size": 160457}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571407249, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.3.0-alpha.0", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-SMOngidEgV8Bd9WxTCKQnW/Wmp3K7485SLnvq7l9ed2haOk3gxnv8K2tOG58mxXZjpGaXc88UyxrCLZg6sMeSQ==", "shasum": "dad2ba73442f1702001407b6440d6f8b8f478d03", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 436333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTuH/dHQOM8FGZ3NXa11ogLcewYrwxDmh2rP0gf/5IBwIgCiF9H/JFm0B25dwTWGbqYS7k3oELHqsJD/6x/RAV7JE="}], "size": 168108}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750503268, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.3.0-alpha.1", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-Z3+JNDP/vY9lsXHwkdFFn4Mfjbf81aBRGP4HO51On2Jdu80phfh0GsTQlH+BbaKhGarnhS/ecTSTAcR+WlcytQ==", "shasum": "9a6881e110e1cc580da818a3bf98a781dec44585", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 436333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICflsI9HoLtdUyQv4uLTfiX0BbAzgUKCdNOjd2pYG+sWAiEAiMWO3Y9QLUDuR5sNuXr5natOqhJ3t3bpLUfQuUBIs3A="}], "size": 168108}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753400542, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.3.0-alpha.2", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-7F2OpTns+yVBT/iHlBO5eYT/lxgP/rPgSkKen/CB+VIEvL9Faal3KQ7AtuCtS/ElGFAqFF8Z60Ni2xqtMGwLVw==", "shasum": "387a73ba8e90babe6e3716e33fb474059948e689", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 436333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCs2+LidioU4gIO4BxHUph125C5+p+5FseujyxkeM4mUwIhAKBwWIOxCxzutj7Ue5XvvYvW7KjfzUkKn4P4QT/7R9lL"}], "size": 168108}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485201149, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.3.0-alpha.3", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-qYujXDmWotJviubv49ElS9cozZ/U9KyeDSSPmHEMvHBdjGojokiNuZKpK2/r/W6I6fS5P7gzLshXPdGZFMLUuw==", "shasum": "66e8ba177ee140de5f490ebfcd0b13974ffe1178", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 436333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDyf5cmuH2fUjW1X2zsx5gMbqs2kL/YYCUCw8fV9H2cfAiAYfMHiwvcBJBmcMl9rsBE9s4tdAiQTq8KgdU0F20BEAg=="}], "size": 168108}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557600832, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.3.0", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-1apPw5cD2xBv1XIHPUlq0cO6iAaEUQ3BcY0ysSyD9Kuyw4MoWm1DV+W9mneWI+1g6OeP6dhikiFE6BlU+AToTQ==", "shasum": "3fc90c3ebe67de3648ed2f138068722f9b1d47da", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.3.0.tgz", "fileCount": 4, "unpackedSize": 436325, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDM1csiSLBSuM/z6o8gQU2DPj8QAiz4zsGHAkRHLXAvAiEAysXLoEyLBXwDHzUk169IAFQtfuQbxrN55wccNFZRZwM="}], "size": 168101}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850809527, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.4.0", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-9NQXD+qk46RwATNC3/UB7HWurscY18CnAPMTFcI9Y8CTbtm63/eex1SNt+BHFinEQuLBjaZwR2Lp+n7pmEJPpQ==", "shasum": "c31b76e695027eeb1078d3d6f1d641d0b900c335", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.4.0.tgz", "fileCount": 4, "unpackedSize": 436325, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4otMSZgbMpZDX/Ppbht7/nM05j0D3aCtIHqU7y7bfDwIhAJzs3PZjZiC0YDrvxPihiO4qKNZtNJYkcQE8AQO/9tln"}], "size": 168101}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360247214, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.4.1", "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-4rVYDlsMEYfa537BRXxJ5UF4ddNwnr2/1O4MHM5PjI9cvV2qymvhwZSFgXqbS8YoTk5i/JR0L0JDs69BUn45YA==", "shasum": "ce5b340da5829b8e546bd00f752ae5292e1c702d", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.4.1.tgz", "fileCount": 4, "unpackedSize": 436325, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1D2vhjsQ7ubQU6bwpYfgO69anVMfP6JFab9iSXibKkAIgIniQXe84pgTHYnUoYduBBcoDXJaGjOk2nnBn9UNn5Yw="}], "size": 168101}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702651200, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.4.2-alpha.0", "directories": {}, "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "dist": {"integrity": "sha512-2NIhU6Ay6Ldrk2eYoc6vd8bvhl9wZ5XAfrCiKhZAHu7mXO09Zjj3sYzIe01XZ4r1n31lTpM/ZCLhjr+dUWPgng==", "shasum": "d666cf4ec95aec066cef3a78ab172c0df2fb165f", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 444541, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHidKG9Yar6BUOx3CADJ0Jn24WT7WY4aJLmnLzilbyOSAiEAg67u1YVmmcdRZKvx7wLnKe1XEgsWwYd4l0cPbd6zk8c="}], "size": 177913}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259253490, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-linux-arm-glibc", "version": "2.5.0", "directories": {}, "os": ["linux"], "cpu": ["arm"], "libc": ["glibc"], "dist": {"integrity": "sha512-0VQY1K35DQET3dVYWpOaPFecqOT9dbuCfzjxoQyif1Wc574t3kOSkKevULddcR9znz1TcklCE7Ht6NIxjvTqLA==", "shasum": "92ed322c56dbafa3d2545dcf2803334aee131e42", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.0.tgz", "fileCount": 4, "unpackedSize": 444533, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdX9Lp6ueCvPCuty04VLcn/IjgxA+Sg/2ORxHQUlZC7wIhANdI01YYml+2t2LB7gXY2T9eFFt9MnRml/JLwvnNXbFs"}], "size": 177885}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701335736, "_source_registry_name": "default"}}, "_source_registry_name": "default"}