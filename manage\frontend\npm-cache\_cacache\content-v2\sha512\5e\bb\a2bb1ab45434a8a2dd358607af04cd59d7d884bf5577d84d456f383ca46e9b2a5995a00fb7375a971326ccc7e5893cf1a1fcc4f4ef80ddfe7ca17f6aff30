{"dist-tags": {"latest": "6.0.1", "version5": "5.65.18"}, "modified": "2024-09-20T12:14:53.482Z", "name": "codemirror", "versions": {"5.64.0": {"name": "codemirror", "version": "5.64.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-fqr6CtDQdJ6iNMbD8NX2gH2G876nNDk+TO1rrYkgWnqQdO3O1Xa9tK6q+psqhJJgE5SpbaDcgdfLmukoUVE8pg==", "shasum": "182eec65b62178e3cd1de8f9d88ab819cfe5f625", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.64.0.tgz", "fileCount": 338, "unpackedSize": 2927737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmQu4CRA9TVsSAnZWagAAHYAP+QEFeFhNy+ouuGfPiXPS\nlUAjqadvmnwTP7mDlZyI0RWi2ckrjvM/U/2YGwuEGtaShGJ/UxwMT/grQELC\nSdlbPfkvFv1oEfF2sEYsxnef4eF5V6gSviTH0JyOPax3oVhXQKGyqIbN1G72\nAUkQr9yTbtKuxUJQ+HLUZ2h00v4VhBiQxxQPSbjeFcZE6z3db+fZpCyGRKP5\n8z8+vaXSfJALYzlntYvkEpzh/X4NhEnFKU7XPJ0NZ+AlcPqCS0cifNFeGynb\ny3DeH4plFTEKWuFvNhnLKtiOGoUBWYGeJwhB40BLpsuDZm9vuXrJDcMC9pnw\ntecJfYeH990cbKi44nZx3cgZ+HI+kE8nHx9aipSRFPRorIynyOF/SGyYO1fV\nKlm8q7q4mFnExeGZyRoKOiMmoVIum2m5rbHseUa+JoT6OIioUqD1hNegb5na\n6PRDlzfC/0ydkz0rgNTdLFfWqY+msXpl8/AytWK7INsX8jKEUMN8qIO0yFuY\nE5dQhnSojJIbKxxqT/AzlZFWCkDmQKF5HC1fIFPsDwgEdD7zHEDuJi+b8Tai\nddJcdFW4Sjh4ozh1l7LfXiAj7CIy5+YFaOTkYzIFTuhTGJgJ+TGoQOkjU+Wr\neCO66BMHAWl3Sd4HKZ1BrONJ6TPbvUXtTyfTICfIizsE1blfZYRDYDasHrx8\nYxwS\r\n=HIox\r\n-----END PGP SIGNATURE-----\r\n", "size": 748559, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.63.2": {"name": "codemirror", "version": "5.63.2", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "846cfa84c43eb5fb7f97f9335e6dbc9102c55d4d", "size": 747797, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.63.2.tgz", "integrity": "sha512-T7I7S0iltJnnLa+RegNaItuo/vbD8QgrrXHnuj3AB8+IMvTYfQ0t+Qvpp/LQI4i9i1+KGfUXg0RvjJYb4CQklw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.63.3": {"name": "codemirror", "version": "5.63.3", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "97042a242027fe0c87c09b36bc01931d37b76527", "size": 747636, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.63.3.tgz", "integrity": "sha512-1C+LELr+5grgJYqwZKqxrcbPsHFHapVaVAloBsFBASbpLnQqLw1U8yXJ3gT5D+rhxIiSpo+kTqN+hQ+9ialIXw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.63.1": {"name": "codemirror", "version": "5.63.1", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "b0b9e8444206fd6a43a58a4b31d5740bb891fa57", "size": 852969, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.63.1.tgz", "integrity": "sha512-baivaNZreZOGh1/tYyTvCupC9NeWk7qlZeGUDi4nFKj/J0JU8FYKZND4QqLw70P7HOttlCt4JJAOj9GoIhHEkA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.63.0": {"name": "codemirror", "version": "5.63.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "070a699108badd9c118b7261ac2e9793acdbb149", "size": 747598, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.63.0.tgz", "integrity": "sha512-KlLWRPggDg2rBD1Mx7/EqEhaBdy+ybBCVh/efgjBDsPpMeEu6MbTAJzIT4TuCzvmbTEgvKOGzVT6wdBTNusqrg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.62.3": {"name": "codemirror", "version": "5.62.3", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "5cfdee6931c8b2d1b39ae773aaaaec2cc6b5558e", "size": 747068, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.62.3.tgz", "integrity": "sha512-zZAyOfN8TU67ngqrxhOgtkSAGV9jSpN1snbl8elPtnh9Z5A11daR405+dhLzLnuXrwX0WCShWlybxPN3QC/9Pg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.62.2": {"name": "codemirror", "version": "5.62.2", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "bce6d19c9829e6e788f83886d48ecf5c1e106e65", "size": 746399, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.62.2.tgz", "integrity": "sha512-tVFMUa4J3Q8JUd1KL9yQzQB0/BJt7ZYZujZmTPgo/54Lpuq3ez4C8x/ATUY/wv7b7X3AUq8o3Xd+2C5ZrCGWHw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.62.1": {"name": "codemirror", "version": "5.62.1", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "917c7406c7b7f05b974c7fe4c4f2088ae64cc874", "size": 746353, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.62.1.tgz", "integrity": "sha512-39ce8tHh/M9J+Epa90R5zMGg06pxVXc1+Y0SRR6eKaUjjzuj5iYkk7rHc2uU+FzvfsWYGEYKPFf0pBVBLmYXNQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.62.0": {"name": "codemirror", "version": "5.62.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "e9ecd012e6f9eaf2e05ff4a449ff750f51619e22", "size": 745559, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.62.0.tgz", "integrity": "sha512-Xnl3304iCc8nyVZuRkzDVVwc794uc9QNX0UcPGeNic1fbzkSrO4l4GVXho9tRNKBgPYZXgocUqXyfIv3BILhCQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.61.1": {"name": "codemirror", "version": "5.61.1", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ccfc8a43b8fcfb8b12e8e75b5ffde48d541406e0", "size": 744802, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.61.1.tgz", "integrity": "sha512-+D1NZjAucuzE93vJGbAaXzvoBHwp9nJZWWWF9utjv25+5AZUiah6CIlfb4ikG4MoDsFsCG8niiJH5++OO2LgIQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.61.0": {"name": "codemirror", "version": "5.61.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "318e5b034a707207948b92ffc2862195e8fdb08e", "size": 742497, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.61.0.tgz", "integrity": "sha512-D3wYH90tYY1BsKlUe0oNj2JAhQ9TepkD51auk3N7q+4uz7A/cgJ5JsWHreT0PqieW1QhOuqxQ2reCXV1YXzecg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.60.0": {"name": "codemirror", "version": "5.60.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "00a8cfd287d5d8737ceb73987f04aee2fe5860da", "size": 745782, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.60.0.tgz", "integrity": "sha512-AEL7LhFOlxPlCL8IdTcJDblJm8yrAGib7I+DErJPdZd4l6imx8IMgKK3RblVgBQqz3TZJR4oknQ03bz+uNjBYA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.59.4": {"name": "codemirror", "version": "5.59.4", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "bfc11c8ce32b04818e8d661bbd790a94f4b3a6f3", "size": 745291, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.59.4.tgz", "integrity": "sha512-achw5JBgx8QPcACDDn+EUUXmCYzx/zxEtOGXyjvLEvYY8GleUrnfm5D+Zb+UjShHggXKDT9AXrbkBZX6a0YSQg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.59.3": {"name": "codemirror", "version": "5.59.3", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "237ec3ac0e39fee50d6f1a6196c8172a7c7c2ba1", "size": 745244, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.59.3.tgz", "integrity": "sha512-p1d4BjmBBssgnEGtQeWvE5PdiDffqZjiJ77h2FZ2J2BpW9qdOzf6v7IQscyE+TgyKBQS3PpsYimfEDNgcNRZGQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.59.2": {"name": "codemirror", "version": "5.59.2", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ee674d3a4a8d241af38d52afc482625ba7393922", "size": 744507, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.59.2.tgz", "integrity": "sha512-/D5PcsKyzthtSy2NNKCyJi3b+htRkoKv3idswR/tR6UAvMNKA7SrmyZy6fOONJxSRs1JlUWEDAbxqfdArbK8iA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.59.1": {"name": "codemirror", "version": "5.59.1", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "cd6465555a87f8a2243eb41ffb460c777e15212c", "size": 744268, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.59.1.tgz", "integrity": "sha512-d0SSW/PCCD4LoSCBPdnP0BzmZB1v3emomCUtVlIWgZHJ06yVeBOvBtOH7vYz707pfAvEeWbO9aP6akh8vl1V3w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.59.0": {"name": "codemirror", "version": "5.59.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "6d8132055459aabf21d04cae5cf5c430e5c57bb9", "size": 744033, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.59.0.tgz", "integrity": "sha512-UGzSkCacY9z0rSpQ3wnTWRN2nvRE6foDXnJltWW8pazInR/R+3gXHrao4IFQMv/bSBvFBxt8/HPpkpKAS54x5Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.58.3": {"name": "codemirror", "version": "5.58.3", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "3f0689854ecfbed5d4479a98b96148b2c3b79796", "size": 741919, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.58.3.tgz", "integrity": "sha512-KBhB+juiyOOgn0AqtRmWyAT3yoElkuvWTI6hsHa9E6GQrl6bk/fdAYcvuqW1/upO9T9rtEtapWdw4XYcNiVDEA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.58.2": {"name": "codemirror", "version": "5.58.2", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ed54a1796de1498688bea1cdd4e9eeb187565d1b", "size": 741254, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.58.2.tgz", "integrity": "sha512-K/hOh24cCwRutd1Mk3uLtjWzNISOkm4fvXiMO7LucCrqbh6aJDdtqUziim3MZUI6wOY0rvY1SlL1Ork01uMy6w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.58.1": {"name": "codemirror", "version": "5.58.1", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ec6bf38ad2a17f74c61bd00cc6dc5a69bd167854", "size": 740970, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.58.1.tgz", "integrity": "sha512-UGb/ueu20U4xqWk8hZB3xIfV2/SFqnSLYONiM3wTMDqko0bsYrsAkGGhqUzbRkYm89aBKPyHtuNEbVWF9FTFzw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.58.0": {"name": "codemirror", "version": "5.58.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "1f1c4faacc8606587b6ac970f30e4fd37c61047f", "size": 740936, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.58.0.tgz", "integrity": "sha512-OUK+7EgaYnLyC0F09UWjckLWvviy02IDDGTW5Zmj60a3gdGnFtUM6rVsqrfl5+YSylQVQBNfAGG4KF7tQOb4/Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.57.0": {"name": "codemirror", "version": "5.57.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "d26365b72f909f5d2dbb6b1209349ca1daeb2d50", "size": 739102, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.57.0.tgz", "integrity": "sha512-WGc6UL7Hqt+8a6ZAsj/f1ApQl3NPvHY/UQSzG6fB6l4BjExgVdhFaxd7mRTw1UCiYe/6q86zHP+kfvBQcZGvUg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.56.0": {"name": "codemirror", "version": "5.56.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "675640fcc780105cd22d3faa738b5d7ea6426f61", "size": 738161, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.56.0.tgz", "integrity": "sha512-MfKVmYgifXjQpLSgpETuih7A7WTTIsxvKfSLGseTY5+qt0E1UD1wblZGM6WLenORo8sgmf+3X+WTe2WF7mufyw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.55.0": {"name": "codemirror", "version": "5.55.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "23731f641288f202a6858fdc878f3149e0e04363", "size": 737317, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.55.0.tgz", "integrity": "sha512-TumikSANlwiGkdF/Blnu/rqovZ0Y3Jh8yy9TqrPbSM0xxSucq3RgnpVDQ+mD9q6JERJEIT2FMuF/fBGfkhIR/g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.54.0": {"name": "codemirror", "version": "5.54.0", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "82b6adf662b29eeb7b867fe7839d49e25e4a0b38", "size": 734624, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.54.0.tgz", "integrity": "sha512-Pgf3surv4zvw+KaW3doUU7pGjF0BPU8/sj7eglWJjzni46U/DDW8pu3nZY0QgQKUcICDXRkq8jZmq0y6KhxM3Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.53.2": {"name": "codemirror", "version": "5.53.2", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "9799121cf8c50809cca487304e9de3a74d33f428", "size": 733898, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.53.2.tgz", "integrity": "sha512-wvSQKS4E+P8Fxn/AQ+tQtJnF1qH5UOlxtugFLpubEZ5jcdH2iXTVinb+Xc/4QjshuOxRm4fUsU2QPF1JJKiyXA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.53.0": {"name": "codemirror", "version": "5.53.0", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "7d5c373388e26bb902082da749a0e8cedbfe2b95", "size": 733867, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.53.0.tgz", "integrity": "sha512-gpnlCCcfp/k9U38X5edaTNRAuNW3vNyAFiG4ZeytlHBl0PsCRYFTomLnAsTfJf/B1eQ7kyzB4k3XXolebpYhtA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.52.2": {"name": "codemirror", "version": "5.52.2", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c29e1f7179f85eb0dd17c0586fa810e4838ff584", "size": 731998, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.52.2.tgz", "integrity": "sha512-WCGCixNUck2HGvY8/ZNI1jYfxPG5cRHv0VjmWuNzbtCLz8qYA5d+je4QhSSCtCaagyeOwMi/HmmPTjBgiTm2lQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.52.0": {"name": "codemirror", "version": "5.52.0", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "4dbd6aef7f0e63db826b9a23922f0c03ac75c0a7", "size": 731301, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.52.0.tgz", "integrity": "sha512-K2UB6zjscrfME03HeRe/IuOmCeqNpw7PLKGHThYpLbZEuKf+ZoujJPhxZN4hHJS1O7QyzEsV7JJZGxuQWVaFCg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.51.0": {"name": "codemirror", "version": "5.51.0", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "7746caaf5223e68f5c55ea11e2f3cc82a9a3929e", "size": 730352, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.51.0.tgz", "integrity": "sha512-vyuYYRv3eXL0SCuZA4spRFlKNzQAewHcipRQCOKgRy7VNAvZxTKzbItdbCl4S5AgPZ5g3WkHp+ibWQwv9TLG7Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.50.2": {"name": "codemirror", "version": "5.50.2", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "32ddfe2b50193fcf573d8141c4a31d267c92b4a3", "size": 729699, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.50.2.tgz", "integrity": "sha512-PPjUsC1oXSM86lunKrw609P1oM0Wu8z9rqzjbeyBYCcx44VL41aUpccdOf1PfAZtTONlmN3sT3p2etLNYa1OGg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.50.0": {"name": "codemirror", "version": "5.50.0", "devDependencies": {"blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-buble": "^0.19.8"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "aeacd18f225735b17cbab98908edace87fedcdab", "size": 729482, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.50.0.tgz", "integrity": "sha512-32LAmGcBNhKtJP4WGgkcaCVQDyChAyaWA6jasg778ziZzo3PWBuhpAQIJMO8//Id45RoaLyXjuhcRUBoS8Vg+Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.49.2": {"name": "codemirror", "version": "5.49.2", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c84fdaf11b19803f828b0c67060c7bc6d154ccad", "size": 727934, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.49.2.tgz", "integrity": "sha512-dwJ2HRPHm8w51WB5YTF9J7m6Z5dtkqbU9ntMZ1dqXyFB9IpjoUFDj80ahRVEoVanfIp6pfASJbOlbWdEf8FOzQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.49.0": {"name": "codemirror", "version": "5.49.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "adedbffcc81091e4a0334bcb96b1ae3b7ada5e3f", "size": 726627, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.49.0.tgz", "integrity": "sha512-Hy<PERSON>r0HToBdZpLBN9dYFO/KlJAsKH37/cXVHPAqa+imml0R92tb9AkmsvjnXL+SluEvjjdfkDgRjc65NG5jnMYA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.48.4": {"name": "codemirror", "version": "5.48.4", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "4210fbe92be79a88f0eea348fab3ae78da85ce47", "size": 725102, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.48.4.tgz", "integrity": "sha512-pUhZXDQ6qXSpWdwlgAwHEkd4imA0kf83hINmUEzJpmG80T/XLtDDEzZo8f6PQLuRCcUQhmzqqIo3ZPTRaWByRA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.48.2": {"name": "codemirror", "version": "5.48.2", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "a9dd3d426dea4cd59efd59cd98e20a9152a30922", "size": 705710, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.48.2.tgz", "integrity": "sha512-i9VsmC8AfA5ji6EDIZ+aoSe4vt9FcwPLdHB1k1ItFbVyuOFRrcfvnoKqwZlC7EVA2UmTRiNEypE4Uo7YvzVY8Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.48.0": {"name": "codemirror", "version": "5.48.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "66e6dae6ca79b955e34b322881ebb7b5512f3cc5", "size": 705208, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.48.0.tgz", "integrity": "sha512-3Ter+tYtRlTNtxtYdYNPxGxBL/b3cMcvPdPm70gvmcOO2Rauv/fUEewWa0tT596Hosv6ea2mtpx28OXBy1mQCg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.47.0": {"name": "codemirror", "version": "5.47.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c13a521ae5660d3acc655af252f4955065293789", "size": 704570, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.47.0.tgz", "integrity": "sha512-kV49Fr+NGFHFc/Imsx6g180hSlkGhuHxTSDDmDHOuyln0MQYFLixDY4+bFkBVeCEiepYfDimAF/e++9jPJk4QA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.46.0": {"name": "codemirror", "version": "5.46.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "be3591572f88911e0105a007c324856a9ece0fb7", "size": 703738, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.46.0.tgz", "integrity": "sha512-3QpMge0vg4QEhHW3hBAtCipJEWjTJrqLLXdIaWptJOblf1vHFeXLNtFhPai/uX2lnFCehWNk4yOdaMR853Z02w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.45.0": {"name": "codemirror", "version": "5.45.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "db5ebbb3bf44028c684053f3954d011efcec27ad", "size": 702600, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.45.0.tgz", "integrity": "sha512-c19j644usCE8gQaXa0jqn2B/HN9MnB2u6qPIrrhrMkB+QAP42y8G4QnTwuwbVSoUS1jEl7JU9HZMGhCDL0nsAw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.44.0": {"name": "codemirror", "version": "5.44.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "80dc2a231eeb7aab25ec2405cdca37e693ccf9cc", "size": 701632, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.44.0.tgz", "integrity": "sha512-3l42syTNakCdCQuYeZJXTyxina6Y9i4V0ighSJXNCQtRbaCN76smKKLu1ZHPHQon3rnzC7l4i/0r4gp809K1wg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.43.0": {"name": "codemirror", "version": "5.43.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "2454b5e0f7005dc9945ab7b0d9594ccf233da040", "size": 700839, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.43.0.tgz", "integrity": "sha512-mljwQWUaWIf85I7QwTBryF2ASaIvmYAL4s5UCanCJFfKeXOKhrqdHWdHiZWAMNT+hjLTCnVx2S/SYTORIgxsgA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.42.2": {"name": "codemirror", "version": "5.42.2", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "801ab715a7a7e1c7ed4162b78e9d8138b98de8f0", "size": 700185, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.42.2.tgz", "integrity": "sha512-Tkv6im39VuhduFMsDA3MlXcC/kKas3Z0PI1/8N88QvFQbtOeiiwnfFJE4juGyC8/a4sb1BSxQlzsil8XLQdxRw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.42.0": {"name": "codemirror", "version": "5.42.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "2d5b640ed009e89dee9ed8a2a778e2a25b65f9eb", "size": 699645, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.42.0.tgz", "integrity": "sha512-pbApC8zDzItP3HRphD6kQVwS976qB5Qi0hU3MZMixLk+AyugOW1RF+8XJEjeyl5yWsHNe88tDUxzeRh5AOxPRw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.41.0": {"name": "codemirror", "version": "5.41.0", "devDependencies": {"blint": "^1", "node-static": "0.7.11", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.66.2", "rollup-plugin-buble": "^0.19.2", "rollup-watch": "^4.3.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "57e245be197643c39440d2840236d3edc4bb1162", "size": 698438, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.41.0.tgz", "integrity": "sha512-mkCwbneCx2WHg1MNCYrI+8Zuq0KMMaZ5yTFpQlAZazy3yxME8bHcuSc9WUFzgPZ114WqWu1FIHlx8CavLzBDIg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.40.2": {"name": "codemirror", "version": "5.40.2", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "f4a41fee2d84e679543591b3680af259d903330b", "size": 694187, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.40.2.tgz", "integrity": "sha512-yoWuvEiD3v5vTwdoMc/wu/Ld6dh9K/yEiEBTKOPGM+/pN0gTAqFNtrLHv1IJ1UJvzFpNRvMi92XCi3+8/iIaEw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.40.0": {"name": "codemirror", "version": "5.40.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "2f5ed47366e514f41349ba0fe34daaa39be4e257", "size": 693659, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.40.0.tgz", "integrity": "sha512-plTYonQ8SwbtS4m9n88mPDR+G7JwFrAL6774VjvoNH8wQJNSJOx5JdWmgRe3pCyuDI4s+vvi4CIuQnoduUTVug=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.39.2": {"name": "codemirror", "version": "5.39.2", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "778aa13b55ebf280745c309cb1b148e3fc06f698", "size": 693000, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.39.2.tgz", "integrity": "sha512-mchBy0kQ1Wggi+e58SmoLgKO4nG7s/BqNg6/6TRbhsnXI/KRG+fKAvRQ1LLhZZ6ZtUoDQ0dl5aMhE+IkSRh60Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.39.0": {"name": "codemirror", "version": "5.39.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "4654f7d2f7e525e04a62e72d9482348ccb37dce5", "size": 692174, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.39.0.tgz", "integrity": "sha512-vpJRray/0ZCt9FiS7UcVr1JAm6OBdUt6TA/94Q7MScr8TnutVdQWh/WPr0migzaBPQmYvY7I9UZNvbsaLESIuQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.38.0": {"name": "codemirror", "version": "5.38.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "26a9551446e51dbdde36aabe60f72469724fd332", "size": 691571, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.38.0.tgz", "integrity": "sha512-PEPnDg8U3DTGFB/Dn2T/INiRNC9CB5k2vLAQJidYCsHvAgtXbklqnuidEwx7yGrMrdGhl0L0P3iNKW9I07J6tQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.37.0": {"name": "codemirror", "version": "5.37.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c349b584e158f590277f26d37c2469a6bc538036", "size": 690337, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.37.0.tgz", "integrity": "sha512-dQaayDJCLU4UJcwg2RM44oFrs0dMNndTp6qxQJF6XI71l1xN3RB4IqiKES0b0rccbARbrD/UBB4t8DNknfaOTw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.36.0": {"name": "codemirror", "version": "5.36.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "1172ad9dc298056c06e0b34e5ccd23825ca15b40", "size": 688586, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.36.0.tgz", "integrity": "sha512-XGR+ic+9DSMSE4mD2XLinGrMcIO4xbHpNrPzAFpCW5bjy+Em8fUOTCpagVP/+LLtTQsAROqrq1f55KyRxDcokA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.35.0": {"name": "codemirror", "version": "5.35.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "280653d495455bc66aa87e6284292b02775ba878", "size": 687730, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.35.0.tgz", "integrity": "sha512-8HQICjZlDfe1ai7bvU6m2uHxuZuFgsUCdDRU9OHVB+2RTRd+FftN1ezVCqbquG0Fyq+wETqyadKhUX46DswSUQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.34.0": {"name": "codemirror", "version": "5.34.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "e345dcc09a6149db65cc70dff9d389c1c4b0cd06", "size": 686398, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.34.0.tgz", "integrity": "sha512-7ke9DJB350sChxq1skTmotVZsJtiJo1ihC41rq8IyOMZv47Z1AQygoevWHs0PJTw2eBphmB7gA3AbPrVrnfwPw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.33.0": {"name": "codemirror", "version": "5.33.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "462ad9a6fe8d38b541a9536a3997e1ef93b40c6a", "size": 685273, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.33.0.tgz", "integrity": "sha512-HT6PKVqkwpzwB3jl5hXFoQteEWXbSWMzG3Z8RVYlx8hZwCOLCy4NU7vkSB3dYX3e6ORwRfGw4uFOXaw4rn/a9Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.32.0": {"name": "codemirror", "version": "5.32.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "cb6ff5d8ef36d0b10f031130e2d9ebeee92c902e", "size": 684221, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.32.0.tgz", "integrity": "sha512-95OxAlYiigW0g4n4ixFdavG07clJGILp3MvHh2pKR3FvyrTuHHvqtKSVbrV3/Jz6o0YqGvyCDLDTbH4h6ciaSw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.31.0": {"name": "codemirror", "version": "5.31.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ecf3d057eb74174147066bfc7c5f37b4c4e07df2", "size": 683562, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.31.0.tgz", "integrity": "sha512-LKbMZKoAz7pMmWuSEl253G6yyloSulj1kXfvYv+3n3I8wMiI7QwnCHwKM3Zw5S9ItNV28Layq0/ihQXWmn9T9w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.30.0": {"name": "codemirror", "version": "5.30.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "86e57dd5ea5535acbcf9c720797b4cefe05b5a70", "size": 681615, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.30.0.tgz", "integrity": "sha512-pfJV/7fLAUUenuGK3iANkQu1AxNLuWpeF7HV6YFDjSBMp53F8FTa2F6oPs9NKAHFweT2m08usmXUIA+7sohdew=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.29.0": {"name": "codemirror", "version": "5.29.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "e68de1350e2f0ce804a3930576d0ae318736e967", "size": 674961, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.29.0.tgz", "integrity": "sha512-nlG9m0YQ0gFhdEdnKDG+XJRB/bW+K6M9Axs01+LScjVamWtd4dEwgyohf/r4voW1efnGi6U6hHHvDQ9tt9BtoA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.28.0": {"name": "codemirror", "version": "5.28.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "2978d9280d671351a4f5737d06bbd681a0fd6f83", "size": 671682, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.28.0.tgz", "integrity": "sha512-E/Z6050shti9v9ivl0dUClVRM4xaH204jsJmEpNYC6KDTlQwAz+5DdhLzn0tjaL/Mp1P0J1uhZokcSP2RFSwlA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.27.4": {"name": "codemirror", "version": "5.27.4", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "0e817c839bfea9959dd16cd48ae14acc0e43c3b6", "size": 670741, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.27.4.tgz", "integrity": "sha512-oOpSTMT3gj27u8NSkkrciuIpCqID3dvb8UGpS7eEm/F1x1DVinp6+ROLR+B8hXdxqVFpCl2NrR2BQe7zd3uH7g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.27.2": {"name": "codemirror", "version": "5.27.2", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "a292d42f079d5b98c68c3146fab99844f3d8776c", "size": 670456, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.27.2.tgz", "integrity": "sha512-4YRvvOsrNc5PIzwqCoj3RT13+Qon1PD3b3dBKVgC/YAPqdbmDaS/9F51UD9DVRxY0yiAgK6bYK/ojW5ViSvkNA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.27.0": {"name": "codemirror", "version": "5.27.0", "devDependencies": {"blint": "^1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "0c72f70c321a7d494fd8db1976698c249c985eb3", "size": 671164, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.27.0.tgz", "integrity": "sha512-9uyxqIBkW/iNqJ1ETKxyOnxK1H+/aUuvy8bMv5qrtZpZ1G6F/Y7/SdwZzKZR9ZTDV0wUuG30VxiYtjVRJP4ijg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.26.0": {"name": "codemirror", "version": "5.26.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "bcbee86816ed123870c260461c2b5c40b68746e5", "size": 666599, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.26.0.tgz", "integrity": "sha512-Mq8xNhBGqcSOizh+nWO51elRTkMza9JtsYuTCmll2nKmdClPeKKfKfodHOQwzIYQ9mjg05DQDxS8L4EZsx6p0w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.25.2": {"name": "codemirror", "version": "5.25.2", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "8c77677ca9c9248d757d3a07ed1e89a8404850b7", "size": 665121, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.25.2.tgz", "integrity": "sha512-sT+1oeCHDVGD2vIM3yCI540xrZBKYp45aogtkcxeyH56n3WhbPIrvQxzJnK0nXOR+CKQ1vEvWHSuqQUmd+KMlw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.25.0": {"name": "codemirror", "version": "5.25.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.41.0", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^3.2.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "78e06939c7bb41f65707b8aa9c5328111948b756", "size": 664749, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.25.0.tgz", "integrity": "sha512-pxzYiTxwOa3R+ApqdfSZSe6c87GTTp0ljy9y3120HnnsSOaaAyRp/eOnvD/F7FwhG2FoPRDcZgTwfNtOAnyJIg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.24.2": {"name": "codemirror", "version": "5.24.2", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "b55ca950fa009709c37df68eb133310ed89cf2fe", "size": 659243, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.24.2.tgz", "integrity": "sha512-opgoeGOmw7Paeg79DrNW4OWe1oEkEQ29X7KX6H2yEYL4P5BvC8FTVNy24rEwMgv13L0Gh2WQBTJCStnFWrdngQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.24.0": {"name": "codemirror", "version": "5.24.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "6de108da4db9ab368f8ef7a9b822528a74513830", "size": 658690, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.24.0.tgz", "integrity": "sha512-EtQeJbgAm5SZQei2T1LpVb6MdsugabvaYNUc6IasQjfEK/VInf1WG3lvI9YND5p4b4ouPrApWSADwTAArOSGLg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.23.0": {"name": "codemirror", "version": "5.23.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ac9b6b2e163a79e915b44cf0f43cd115cf6982dc", "size": 654257, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.23.0.tgz", "integrity": "sha512-JptseVxRaUz5f8GBnfMIs1XnB0dgSx9x8N5u1X0MJdpb9InR+si8KYX9RBoJhxv6P+7XuTAhQg5QrOCtMlaPXQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.22.2": {"name": "codemirror", "version": "5.22.2", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "38274c20e3628b4f7a982bffc2cddb61ceed6a09", "size": 652800, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.22.2.tgz", "integrity": "sha512-Kr2Vza/PgkArhTs/hpno9wGrd/BIBoO0i/R2VHitGOcMtGipx3ZRPvzAzOPUMi4M5ggkPaMJReyR6myDcfTeqA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.22.0": {"name": "codemirror", "version": "5.22.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.15.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "281ec76ed991ef24db4071fdf4deb746e80bff18", "size": 656871, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.22.0.tgz", "integrity": "sha512-07Yxl+s2k/CUL1boKeb+1b0VxF/DtARDOLTO1bktA6xoDGvo+FORKzaQWgqar4A1arRfVpWB8kkCm8SdBWw9IQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.21.0": {"name": "codemirror", "version": "5.21.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.14.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "9ec4caeda50807575b2115226bf12414f85d2246", "size": 654222, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.21.0.tgz", "integrity": "sha512-CptNktp8kvYDb6o44nIeDwyE0MieevyPNnoVJbmTFW6LPFNPrmLAxFaL9/FaE9rNiyZoSXgpMYvQ+DtFy/rHKg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.13.0": {"name": "codemirror", "version": "3.13.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "b6fc0062749424dd8977325bfdc5ddb0dfb3650b", "size": 580661, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.13.0.tgz", "integrity": "sha512-DfTrO+sG5kR7rUDQZ/nr0AcxNl7PKFA/AD36NRt9CWlwXAGkhM44Zz7eKyy38DkUI0qTPeSZbylcVaAqslovxw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.12.0": {"name": "codemirror", "version": "3.12.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "93c92a12fe445f50e724d47e871b63346df299f0", "size": 676773, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.12.0.tgz", "integrity": "sha512-K3uCkxUMMijfXl57mpHaE9O3QEq+8ZgyG1YxI0eZNzjqrlcLJBMcKaYlUbNuU1unjTMH6+Jyv+l+Jdczm1M2gg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.11.1": {"name": "codemirror", "version": "3.11.1", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "82edd3894c1f73ffa69516717cb138ac8a5e9d57", "size": 553083, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.11.1.tgz", "integrity": "sha512-Mx2hSbPror8FkLEwWmhkHQGkaFWvmzDD3McEb/dGx7r1Mdc2eiOR+Yt8x6gLM95crIYbQ/8OkvnDnGgck4KxkA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.20.2": {"name": "codemirror", "version": "5.20.2", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.14.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "918e0ece96d57a99030b2f8b33011284bed5217a", "size": 652530, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.20.2.tgz", "integrity": "sha512-SLHw53IdlQJMWgVzC/vcAuHg0vxWenHhNHQA/wC5XBfmmKpVFtavNO246txgibF7i+Kqp220818ZOn+blyTWtQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.20.0": {"name": "codemirror", "version": "5.20.0", "devDependencies": {"blint": "^0.5.1", "node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "rollup": "^0.34.10", "rollup-plugin-buble": "^0.14.0", "rollup-watch": "^2.5.0"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ef3822ae6e442dd595d365a86ff424e35c3985b7", "size": 651920, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.20.0.tgz", "integrity": "sha512-wY+Vslz7K2nSOGPvp0e+nWFHhJkXfaGxqEpSO/80ZJ0i5cWSQRHYweD6iGAdQGfhAF2feVbWkOqkTSKiSPjqbQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.19.0": {"name": "codemirror", "version": "5.19.0", "devDependencies": {"node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "71fa8523b9425216a2be4734e8aba625ff66e7ec", "size": 546820, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.19.0.tgz", "integrity": "sha512-iUwkCMpl05FWLb1fr5yKA+xMrxUAl4t6H4UaAlDyCwJOKAvC2S54Fdn1ePle4nZxkPQY+Vg0yeBIDB5+3jaDEg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.18.3": {"name": "codemirror", "version": "5.18.3", "devDependencies": {"node-static": "0.6.0", "phantomjs-prebuilt": "^2.1.12", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "d4184c82844088ea98e8cc7dfd5a02b46049a9fa", "size": 546804, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.18.3.tgz", "integrity": "sha512-5V9Bv6p0hwZvkJPpz2arY6Qr5g+AwdGKDBAj0OejN9pULkg5QvEv13v8C+1XOOhbOBWkmD+p32LvU4Qs/3kGhA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.18.2": {"name": "codemirror", "version": "5.18.2", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "55b42d0190f72ec86c13f25d945f347cabc16a8e", "size": 546270, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.18.2.tgz", "integrity": "sha512-xjBalLC1Yj8HqBN0x0NXlkynl7JYLz+Q0e0bbVNUwCKIw2Rr097j8QB0VvQoAHFOOCRge3nQyfoLRMx97TGccQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.18.0": {"name": "codemirror", "version": "5.18.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "32781485fa1c7097afee03578a49e83c19b6c312", "size": 546233, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.18.0.tgz", "integrity": "sha512-dQN3N7mK1T8oIgGbQJQPFLZR8BHD6YNjboKuFehqFZ67QJPZfmf7O94GzH2htGW6ct/WMLUSgNDGk53spdaiMg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.17.0": {"name": "codemirror", "version": "5.17.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "a9431353373f152fe2851f29502a3aa12c1d6247", "size": 545689, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.17.0.tgz", "integrity": "sha512-5PtAlqXqe+Cu+LAKeXXSE8kprcua6JG6JLNr+wxgKDMZ0ah0agWLZ9t4me3cloPfGUSmvGS/ww8tkItsx7wqbg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.16.0": {"name": "codemirror", "version": "5.16.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "468031dc9bda1b52e041f0482e5aa7f2b4e79cef", "size": 544793, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.16.0.tgz", "integrity": "sha512-0WXvdAUxOD8iurg7PoxpQkuVEhl0urkDMWbpFntkDr3N9IkD5N9XMJJrQb27np5vl2Y9NJdok9+Fc2ScqkmiHQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.15.2": {"name": "codemirror", "version": "5.15.2", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "58b3dc732c6d10d7aae806f4c7cdd56a9b87fe8f", "size": 543972, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.15.2.tgz", "integrity": "sha512-QHZClCGimKVK86/+K1YcKwa2/9pzy5OfAsv0nsXlPxxoWOhGc+HxPWQISnIrYMHwA5QCtiObxo5O/tmV4fzOSQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.15.0": {"name": "codemirror", "version": "5.15.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "172bdd3231f220d4e860a8fe3b9c4db999013734", "size": 543975, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.15.0.tgz", "integrity": "sha512-lgruW1UrwiA6UyEuQ3Md/fqUayM/0sy9gQEzZwGrvZCxdrabKMMmTVLInf9iFYKzvcJNvQU+Jr0O3xZQX7VEPg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.14.2": {"name": "codemirror", "version": "5.14.2", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "5bd0aa57c2d1ee7cf3bef8b11bec100544b8b9ae", "size": 542720, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.14.2.tgz", "integrity": "sha512-rDdc4Hv6IZJ5G9vxDz32ezhCkANcC0dFutWQwe8AJPfxqFj2lKpg1Zu9++q8gsTHZ36Kd383tkc7DyOa/x8k8g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.14.0": {"name": "codemirror", "version": "5.14.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "858e4268e7fe592dd4ab2cedbba1c3c8843fe63c", "size": 541867, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.14.0.tgz", "integrity": "sha512-ed8XqWuW+Tyg1AGZA9FknEdIE9yPVIGUcqmtKfji+XwVKJr/Pm4peQ2g1hY/icIWsky1YbdR8EmRnjzbo2AEIw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.13.4": {"name": "codemirror", "version": "5.13.4", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "e1c01f7ca34a1551f5911d3ec35ce9a51cf787e0", "size": 529156, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.13.4.tgz", "integrity": "sha512-49aCjeJTpEULlGcIY3a3eGnnImkmWkLjmpLSnzhE1ozHZNJxApe94WWriIhAwcrVSf3KG1ODnCl84gVsSlkEPw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.13.2": {"name": "codemirror", "version": "5.13.2", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "86eea5499e4a00aca5411b14e9a06671ef9eb078", "size": 528492, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.13.2.tgz", "integrity": "sha512-X2AbTyczZ4WFGFjwpllamxeJr0Y0eUvCqAY/ENNn/ukjMXWlrokzriK/2O/CAV29UXUsuOl0sYcvMOPRgFTpGg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.13.0": {"name": "codemirror", "version": "5.13.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "59a511dd6aaa08c5f5f88667cc51fbacc3b1164a", "size": 528903, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.13.0.tgz", "integrity": "sha512-vlWWuE6J41OU2hkWWEvnO7fkPssLslT57kBpc2b2XeuG0PeHyuCAHAjf/CRunltb3pPNfoksK7rPeJN11jF3sw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.12.0": {"name": "codemirror", "version": "5.12.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "956a472bd2b0f94cb0f730680a1c33671cd7fba3", "size": 527196, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.12.0.tgz", "integrity": "sha512-DnCfWPc6GqPMi57rxfSQZMFCcikzP9qDTl19WxyFMaUtoZdoyj5dZFjYPCHscvb9+3oXc47rnhYTfUdwz6LAqg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.11.0": {"name": "codemirror", "version": "5.11.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "bfb61be4b10c6a8e6a799f298a1c314a11fff1a6", "size": 509215, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.11.0.tgz", "integrity": "sha512-n4hQEWq+FunbjeqLa0km2C6Ocvzh0FbwOHpK0+G8217dre6tM1s3Oh6u3TIg9W2xf1g/Zb1VhOusszF0ssN6pw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.10.0": {"name": "codemirror", "version": "5.10.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "30bcc3d7798b19f452110180890f8dc9eaf1dac1", "size": 506355, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.10.0.tgz", "integrity": "sha512-/AXOtEIKpiUtWUBOHy0kBStScaP2jFxTr/iOJSORwdh0ztQQsfKoqhcQI2ob2OakKtfK9euMnLk8O/WQrxx+bw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.9.0": {"name": "codemirror", "version": "5.9.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "4ad7bfc106837d3a974acedafeeec06c2829d757", "size": 503398, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.9.0.tgz", "integrity": "sha512-4Y7jUonV53bgCtqUklZCjrj8fmKL8VGV71bEn8u53zH4BY6zXFtWvGfuTVIexHb9Kan9ug5sFSmUzZ9SlEYG5Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.8.0": {"name": "codemirror", "version": "5.8.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c62b7ea25863926144990dc24f5f94090575d90a", "size": 502103, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.8.0.tgz", "integrity": "sha512-t2DPbJYom0Q+UWW69rnE2G/CARGUCuZZthgZ0GiQhpkEDERDdZpwV30zku71v4HuJpazB76XzcLDluy78K+/Yg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.7.0": {"name": "codemirror", "version": "5.7.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ca8ac4c5c79e269226ac14ac59b9770dbfb4ed79", "size": 497256, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.7.0.tgz", "integrity": "sha512-218YGqqTCS1gnnTm0bPIyVOR8DlpLS398totbj0cmojq30AJZMFATZM8MSFR3D0OrWsLwzqDkyAxvBT82/f1Pw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.6.0": {"name": "codemirror", "version": "5.6.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "da53d2a03768560d1663ebfb4b3a03523b4c3f4b", "size": 491915, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.6.0.tgz", "integrity": "sha512-FYhDHQl5yCrf23jV6YsuZyZrFsKLnRI1s9gflM2wy2BSqruWIyT7kFj7jeooSUXMWZD2amvcT7vgbo7SUuAIbg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.5.0": {"name": "codemirror", "version": "5.5.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "0fa0e3a02ad51db4c280142e15970df9edaf9900", "size": 493210, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.5.0.tgz", "integrity": "sha512-54KPhVp7GMgerQv1Lpal7So/kAVpi4Xo6M8hG7ZN6n6CYoaOQFym/Fqg85S/+zAYyd8sCjWr7H+AFz+UbF8+Ag=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.4.0": {"name": "codemirror", "version": "5.4.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "1ae64dcb34fc3ebf4fc22a9acc3cce10285d5d90", "size": 827590, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.4.0.tgz", "integrity": "sha512-XOQWOdz+k1qImHwsJfP5tIUVCXxtIMo4lfNHwbGVLL6sn+U6SDza73BZpaXUeQahq/o4pRFueCt5QEITn6J7pw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.3.0": {"name": "codemirror", "version": "5.3.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "243cb268dd61ca78ddb27e82f2ec2cef88fb9461", "size": 818827, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.3.0.tgz", "integrity": "sha512-zyTTrSVprFwlNkwNcAbJXnb3jurEbt8LT4PaXaurfOuQ4R/xTOL7WAUKZFNXPR5JULl2x/msFbwJdUBh8EhfEA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.2.0": {"name": "codemirror", "version": "5.2.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "8257d46948c68430e1d50b1aeb58c306895d5183", "size": 804652, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.2.0.tgz", "integrity": "sha512-zW/dhlfZbwX/zsQOZm2KfJsYT+n7tkFK93yDRlxAV4osiaKMCHWENHY6C/sDD0pCiFfncNyUXC9NZqKmZc0mtg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.1.0": {"name": "codemirror", "version": "5.1.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ec7e62ee0238431661a852325137c33f58aee5ed", "size": 796177, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.1.0.tgz", "integrity": "sha512-4PhOh+oRhwEi4GXmOS/Col8ca1j0PxkDeVyS3jj4RTxrH/1dW5GBo4RaSjzxP+cHyrjZxCflQaZQjoD8CeSMGg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.0.0": {"name": "codemirror", "version": "5.0.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "97945cdc26866f882ad964173e110f24aeba4bbf", "size": 786817, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.0.0.tgz", "integrity": "sha512-7GoUX4csuoMVPhKUUP78IKea2SLQN+SUwP4+GFrR0sZJklB/JRD4cIsH92LTB9sPehWjVY0IHSUo0ttFdqcJnA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.13.0": {"name": "codemirror", "version": "4.13.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "209772d38a7bb99647c37b500db121110dd9af6f", "size": 780696, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.13.0.tgz", "integrity": "sha512-+KOX1KjxkdzFJibjxg4u7r5uuLXG6M9cmVbio7x5qAyXcyT0Y437DPMP3AEmD1bGxoi+xFx21B84W0MfIuTfxQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.12.0": {"name": "codemirror", "version": "4.12.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "540e20e8f3aac1c945e9529613fb0f0b82796a16", "size": 758863, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.12.0.tgz", "integrity": "sha512-h28ztzHskKxbjCjjDoOuWP3KKq77AnhysRx+NgVGAUNraonNEZZMrEOJ8g+ybOGADVEw+s17ms1ty45jCpMcdA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.11.0": {"name": "codemirror", "version": "4.11.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "647bade68f649e1134f8c291966c53df042019d9", "size": 783846, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.11.0.tgz", "integrity": "sha512-+QrJdFfB2kTmwNYQ+HmbtoYOhSeKrXkwFQCz8fJJ+RuckLtHCtYho6yxMv1Me2jnsID6yUwNRrImUbdt7fm80g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.10.0": {"name": "codemirror", "version": "4.10.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "7d44b0e40c2115b8027b350ced7cb83fcb32cd9b", "size": 781170, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.10.0.tgz", "integrity": "sha512-VYlZQk7BRRDmnDz9Lbq1rcSVBx1pNYj1eVdCJgeLqrkXnr/hpAKnI1BZFOM8FEkyv8Qoka9x+6pwF4zjKTJumw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.9.0": {"name": "codemirror", "version": "4.9.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "06a068b260c5e268312c6dbbb326aad400746071", "size": 780454, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.9.0.tgz", "integrity": "sha512-qIjHBO1Sxz3+FYsUSZGKeo/uGPgN5d6RrD/guP20DT2GmyUV4QAcN0Nd1VgpE5TN/WcgQD8Zuzv689fdlzKPEQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.8.0": {"name": "codemirror", "version": "4.8.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "ffc87d84d7cc082cc3518c31b7d1bcb9040c3d1c", "size": 762771, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.8.0.tgz", "integrity": "sha512-DXbFustJlUaNUx13uaNMBR0MIRrogvqg8Sp5NI4XEXwVUZ/ptTbcWAoGYK/SQNEmYv3co2rj4mfGwvUZJso92g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.7.0": {"name": "codemirror", "version": "4.7.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "caea4b29581510d25d78bea91415c6d7ab105d11", "size": 751341, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.7.0.tgz", "integrity": "sha512-+2jia/+Wfv/1zHhuZQLFebURRc9CDX9V+Q97gHaRbas4mUqasoVy4mAdnbiG6KKR5TyPibLDG6jshZN0nd8mTQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.6.0": {"name": "codemirror", "version": "4.6.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "b5533e84bcc43bedcbf5ebc72cfc017cc1a30770", "size": 733982, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.6.0.tgz", "integrity": "sha512-3KH16EhsL4mge0/OdmUeEeCkFhDwXKRINUstFGjfApbfz8D9NwJ9dOEb7BlzTMEKrCV2Jj6mfefHaMXv3vNL5Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.5.0": {"name": "codemirror", "version": "4.5.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "3bd34166f327b8f51d5d61e47aee3b73f362d5c0", "size": 730200, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.5.0.tgz", "integrity": "sha512-t13yKaO4ltJ/gfBUTl73hA2VSxkBoksh+09Tm8CARbu5/Sc0zObi0/pL65omVs6S9c5Ygqm2iUZVIvpkEgd+cQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.4.0": {"name": "codemirror", "version": "4.4.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "6b4f9993e64b47a8aa6bb274f542af6844ebec44", "size": 721406, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.4.0.tgz", "integrity": "sha512-jPzUsmqU/38kuLn/T07cMBsVpUE5V49I0Bu1kLq2oxMlg84NlHkV8OlMwmXFh/d37CkDaq7U9g702K0z2V9jGg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.3.0": {"name": "codemirror", "version": "4.3.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "9373561b577447747a6d2c40e84e3bb079ad412b", "size": 712120, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.3.0.tgz", "integrity": "sha512-fQDTpjN+q1sU5HEaDDrIiLJyF6YkX+0T88f8ajq/y16dE5tRivdb9qXVSX7SBQ6CgH+x6p/MUqIvwgz3OwYkJw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.2.0": {"name": "codemirror", "version": "4.2.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c80985c6e6cd5464e7b05a907702757382f0fdc5", "size": 705996, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.2.0.tgz", "integrity": "sha512-9B58S5H5mXVbhzHSp7QWWq2Lip9D1czeNybGt9TmEm5TaNN5z27GyKMNLloyyYjNt7vlvcWmc/Ti6MQBK/jgAA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.1.0": {"name": "codemirror", "version": "4.1.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "39ab273b2695d66e14ae6a05f764fa1cdf5cc69c", "size": 702808, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.1.0.tgz", "integrity": "sha512-vPeQsksv8p8H+RQxwzSjpqqiM9rKlUwSru6IacQI0jIK/owsqFTER26ZIhu0XKxzvXHlDvwm+QP0+yJmW4BplA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.24.0": {"name": "codemirror", "version": "3.24.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "335634d8aaf469f2d6968d2bc80417213fba99d6", "size": 674988, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.24.0.tgz", "integrity": "sha512-<PERSON>jjkY7IoYDZvrF58xM4UCAJjzl3Mn/I+8zvNU5XmM/tnuVxtvYlTl6hY8Iy70SPItZ/UGEY23Wk5NRsNhuNLhQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.3": {"name": "codemirror", "version": "4.0.3", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "1fed51e652e33f4b971f28a72e768e093af46c17", "size": 705638, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-4.0.3.tgz", "integrity": "sha512-nNHn1yjEiEfxl972hmwF51u8O+ocL/+cTK8XeibX/nC9vMoD2GuiG6E7NksgPYYkL72CWubxmTw+RJcHYovVcQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.23.0": {"name": "codemirror", "version": "3.23.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "3ec25ee1f7a567ce762b3098b8cec4ce009fc094", "size": 665286, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.23.0.tgz", "integrity": "sha512-2/b5yYl2HGrn9GRABSWEVU/E3fGSVPLXlxZhjEDgZ27dW5nGVJd8q+/mUQk9PmB8ikZjQyit7nLHs4UCjdybzg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.22.0": {"name": "codemirror", "version": "3.22.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "a1c5e0d2c6bf7f874d380dc0bf4b40c47be93dcd", "size": 675181, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.22.0.tgz", "integrity": "sha512-TRUup97OMqDY1fTl2137nwvM+DMyyqPnwZZ64JVyZOI/WzrPUhEL2kSBwM9rTpYNOFsNe/XMogLoDMbo9v2vCA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.21.0": {"name": "codemirror", "version": "3.21.0", "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5"}, "directories": {"lib": "./lib"}, "dist": {"shasum": "c631c60faa90f899b96ae2b8e4c3dd9bdb19420f", "size": 662901, "noattachment": false, "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.21.0.tgz", "integrity": "sha512-mOsoZ4LC7WvVxex3qie6Lo1fV1v284nnlHpUcJZAwrmYRYcj3UHPq0gvwwL8Ns+wzzLuGxALKjPWqlJ9XKxHNQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.20.0": {"name": "codemirror", "version": "3.20.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.20.0.tgz", "shasum": "2e10326c2f0bf043b1ede32fdbc06ddfab801c62", "size": 645590, "noattachment": false, "integrity": "sha512-KsrC4C3W5cRFAx3LdmXObdSw1Vs2iFq6RWupxMYo5VeEs2a+iqtsWAiu37632xZi46sLs12kg5exngvHn55amw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.19.0": {"name": "codemirror", "version": "3.19.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.19.0.tgz", "shasum": "4e978f8a8df79b913e405798d70f0412afe39071", "size": 637236, "noattachment": false, "integrity": "sha512-9hwshSP4n5XYN0d4/Amomb/TdkDnQ/sz+Ubv/f2nSZopTJdyCG7kQi1XM/k0vBYeSoWjarZ4CfDnSWU/MTmxTQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.18.0": {"name": "codemirror", "version": "3.18.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.18.0.tgz", "shasum": "fc922d284bdc4faec56629b8436e9f6ac923cff2", "size": 637139, "noattachment": false, "integrity": "sha512-wRnGCtHgrK+h8yTsiCNRo7CwDyNYL4WVibKkN4HxFlsG4WNe8CU4dfLTx6+Cxp9JmC3YkLXcHbmO74lNWrqwlA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.17.0": {"name": "codemirror", "version": "3.17.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.17.0.tgz", "shasum": "94aa58ff869d6c933aa6d92d3bd8e18c0f2b5722", "size": 637069, "noattachment": false, "integrity": "sha512-ZR7ig1taB5AkvhsA4RZ5m+OMo6TjC041w1EGMHgwDWRYSe1iJfpAF+WrftXqCp4QgrZcvoLNLSROwLSOA9+hog=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.16.0": {"name": "codemirror", "version": "3.16.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.16.0.tgz", "shasum": "a6c7cc1dea94534ec0dc0713e1ac5f30c13eb5e5", "size": 627584, "noattachment": false, "integrity": "sha512-0TdO6HFu635t+moFAt5n15Ng08wKs2p5o2LRl7xCEjmd5kDRrfS650H2NWFp9Ocut1K53KcJ2Yv5i8sg/v/KfA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.15.0": {"name": "codemirror", "version": "3.15.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.15.0.tgz", "shasum": "e9719760e3113d7ee8a944b9503c7d94948e61dc", "size": 644555, "noattachment": false, "integrity": "sha512-DmPaG6vmoSajQe2Lo6YhsB+47hKmQH4TNut3/MlQHfV0VvO8NFSqTboXRxldSJibmJ3wcztVO2Syqi1QCKROww=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.14.1": {"name": "codemirror", "version": "3.14.1", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-3.14.1.tgz", "shasum": "f61802efd9d9ee6db8e7fb1e71c3267cfddf34f3", "size": 619096, "noattachment": false, "integrity": "sha512-EYLtKOl49QGrrpBxQ8AiJgCTMTpOLmUJGMYVfaDRSTECEmCem/9qlqGC2YsbE+B/9Y+GKr20CdveGc4C2Bv45g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.33.0": {"name": "codemirror", "version": "2.33.0", "devDependencies": {"node-static": "0.6.0"}, "directories": {"lib": "./lib"}, "dist": {"tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-2.33.0.tgz", "shasum": "19bbe63cc5be105508d8cbf5dcc90d4b7ea791f8", "size": 373529, "noattachment": false, "integrity": "sha512-dWAx0LRg+fGPGOp1TeaZkucMJrXVApjuFeUwYlnfGU+KqDiGE3U8G2Kn7NVcDRKTTrHsYvM9+PPQdmOPYOdwrQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.0": {"name": "codemirror", "version": "5.65.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-gWEnHKEcz1Hyz7fsQWpK7P0sPI2/kSkRX2tc7DFA6TmZuDN75x/1ejnH/Pn8adYKrLEA1V2ww6L00GudHZbSKw==", "shasum": "50344359393579f526ca53797e510ff75477117f", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.0.tgz", "fileCount": 338, "unpackedSize": 2929496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwFdSCRA9TVsSAnZWagAAD2MQAJ5SxVX1R2nYu5DjXV+9\nTg9boZFrVb045Vix/TPnnYkywDGx5FaUtE8cTQ9pA+LxJTXNT1eyVs7V33Fj\nENyAaFmIzXMOvqp+8Gd1YnmvHev989vnH7j6e1mTbdRWuc+/MuyTHNlmPAT7\nAoSjr+yVkQXF9n5NMaF2zwpxwG+yWKeko3Dc41XfqZcn6hbAz/bMe6Ay8tix\n03a0bOHljhwXotpqLhyEe5SIUA6nUtaDyjmGaKohqpdzUJp1J9UvGqYHOono\n3wQEfoE+lOqeTCaGIShi8+//IAP/QDUdNkFyD/isP+xBSqX8HsjWzJ/p+Wxc\nfVhJPtFIZa61KRp0aepIJQryM+yULRRqdEcLQyQ1/Muko5pyxBxiOts3Bw7s\np6+pX859Eeur2o7g/6ndajwCe8PszZOKD3yOwXq35GVcPwdvtb8WdXM06749\nbDqFzohH124qQMQTSmqeT1ybl4OW8BPUhGo3wS9baDNgMBugHyqzHEq3I64E\nolKK32ebI0z0AG/84SQFk/wD6r6C9V2pUHoFIDzy5kKNIuPMsFVdMmFmjc5U\nCCasXqeWcdx2Xnion/zRfieu5IEl0D2qSm3fif73rKLKiqAOzbxbeJNvR7Su\n9F56LF5mBgdFg1WcjwQ/JmGPP2tSoggn6TAscIiHq2PCAo4XMc/RBZ1aFc/q\n2SO+\r\n=3JhP\r\n-----END PGP SIGNATURE-----\r\n", "size": 749015}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.1": {"name": "codemirror", "version": "5.65.1", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-s6aac+DD+4O2u1aBmdxhB7yz2XU7tG3snOyQ05Kxifahz7hoxnfxIRHxiCSEv3TUC38dIVH8G+lZH9UWSfGQxA==", "shasum": "5988a812c974c467f964bcc1a00c944e373de502", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.1.tgz", "fileCount": 339, "unpackedSize": 3015746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6VBCCRA9TVsSAnZWagAAzW8P/RPWyHOXcSwrkY0ehWBO\nsH8STCOWaaZgSUOPUb4guEidR1ak1GmeaLSGJDdjih7vm4d6YynO+JegXc4J\nw2o2nSGnezH8qNi5Oouv1beTC2cZrpUFV6AGowV/UiIdqr2U3FzgiEhZ3Olw\nsUZxN9mT5FUYD3vMVlgWnj057tr0awC0QpPXSTmKI56/fLMvk/FfVD5BoSEx\nkzbA1V961QkzaoOqs8m1Q16rmssJ6gexddzMEgPUG532Rxv3oqJprh0lRGkz\ne1Em6lXDeDuRDUZ6cHs/Uf6s72sd1lSX7kHv+CDH1eGyjg36ZeqK6ZudXYkU\nG79SsuPaVwJVDAXbVGFh0YDjm/987awwItimIjdC5HHXjucAVDGOJigaihID\nvUJ+oXuQ26QPGcPXMHbVVZBRAQDsPoazzvZc2jn6iZNqold76y/igfVs3yhB\nHqTmpTRDMqVokQSKa++5jSr17GnofwM/HSWAZH5YriD4VUzvNW2Jvg3vPjKb\n2L52ecC+TMsXxoRCMCgp3JgcCN/3DsTBftTzVjIHUTJEnmPlIntAwvVMrEfr\nAAaG5Q+1+hahq1cFhO+3l7NFnPbXG1uQgK6pCpspfzgswvHsUT8OaYEDuCeT\n5HI2H+USB3Ax5RhEI9HbtBl2B1uWO+HQ3PqRnZwU5WGiSf5fPIXOYKpMZB9V\nXWBW\r\n=GJbx\r\n-----END PGP SIGNATURE-----\r\n", "size": 772852}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.2": {"name": "codemirror", "version": "5.65.2", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-SZM4Zq7XEC8Fhroqe3LxbEEX1zUPWH1wMr5zxiBuiUF64iYOUH/JI88v4tBag8MiBS8B8gRv8O1pPXGYXQ4ErA==", "shasum": "5799a70cb3d706e10f60e267245e3a75205d3dd9", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.2.tgz", "fileCount": 339, "unpackedSize": 3016398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE0fyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUTg/9FS5CecbNbJ+D4H9TdJ7i8yp2jhiyOV1KG/PXziSKOFxIyGYS\r\nQaAfe8r0NhrT1f5grmtbwJ3/0Eo8x4MFbR68tGQHlGTEzxkGnh4eiQQi5rEw\r\nVCAsrgk2nWpOejdsPqWJMIvAtlP9rlGRYSgGFSsSmwtAtiaw/qNTf0CGTrIh\r\neYIlOZfemxjAMqNQatalcs5PbTua3QVYYOUKS8YZEkdo+caTc3li6qLq/2Nf\r\ndlwJzBmOnP7bX7vTusVPBtmCbDJjQYsMFqIKgGiq3lBm+Yugkbe+UF1JGlh5\r\njVNBte5lwqiUvcInyL6mPzz/FVsMm+4oBI/9Mwm2W6walXgKTuZrnKJ32UW4\r\nZdTqt9/1wA3ss/yt5OD6FvFRYUr7fQ5n1LXBCBnvdYH48u3IPOlN9oalnkxH\r\ncCsPewCYKntxUe44Tu6SU4skJPgA9K3mL76tTkqaq/U+V47nceP3NnGW2lQI\r\nqSIQ42p/DQdav3yRZYweez8v8Al4lHZ5xve27Thaf06KtMR7ek07ftYTwo09\r\n8nmSehP/K+W2EbVpEr5EvWQhjsi4W6EQB3WXbNU7NHdyCnI7G9ZaqaRUWiEf\r\nkUgGpSHl9isfMB80+djtiaF4QcyD97LPZhhQK5r8wbhHdRtvXV+sfl9CZ+Z1\r\nb8PI9YyXEVz9bUlPi1C4zrgXauugEsNEpeY=\r\n=O6BW\r\n-----END PGP SIGNATURE-----\r\n", "size": 772953}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.3": {"name": "codemirror", "version": "5.65.3", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-kCC0iwGZOVZXHEKW3NDTObvM7pTIyowjty4BUqeREROc/3I6bWbgZDA3fGDwlA+rbgRjvnRnfqs9SfXynel1AQ==", "shasum": "2d029930d5a293bc5fb96ceea64654803c0d4ac7", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.3.tgz", "fileCount": 339, "unpackedSize": 3017744, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFwrWRUga7jqDicVM/ufdSyODbmiqO++W/wi/l/BBrb8AiB/ckmbCqMHKMvzj9ATC9SH8AubUtlxqoAEuqA2uZri0g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX9dpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmqA/+PvvPKJmF0fX8f3EQGTNTwezqVVYwHN2XVuxa4fq4iHeGIMbA\r\nsOqmNLw7g2ABlcpLmA1hmv01EuyPrSWYQ+U8JF/a1B6meSKYgQD4P/0AkhmR\r\njFirSatRpGQLQXWOq1Y2PocOBirdu1YlKhnx77Umhr/mGqH0K9ADGUFfLE8a\r\ngWOfQZfL+TIW9qhaaZ+ESyO1pOXTNLfxtUMgz2SFHAtpoBV8774tcjTLGINM\r\nZfqbPQhIAI1BLBk4M/AhR7B+um4ZYP9hwlLg11eGADG2iWd/o8alMv8D0UAL\r\nPaP0Azb3fWXnInZ4+U6LHsYr99vcmIbzYM/ZlF3fGbHHpQ6P1CMEVLJbGpjG\r\n+AP28A3zJiD4ePbMKYA+sWXNW4sJx+u1jCepj+JkXOsLILeJhrp2wFZ04azm\r\ndXoudMa5xAmsmkwafYtYzsCD7JNW7u6v4pP0nPzhqlQ0c+3UFfLITavhgUpS\r\nqdQ+K6eyf5OvufoO8O0RZL11LreXXwL/OQE+ZV29FVBWpMV5xd66wTNTyVer\r\nZY21Hw8vW97dPleAbf1qMhM9QLa0HbpEHwcaHZPiUv1HaTR/xNgKd9P6hypp\r\nGzqVMvkkRc01DHE5cvn9MFjnhXz+avMsxxLt8gM/0xovRONH8S7Qlh3h9Vkv\r\n8ftVCF0r9zK42k2DMtjl2JqdTKvU36q/b7o=\r\n=AHXl\r\n-----END PGP SIGNATURE-----\r\n", "size": 773271}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.4": {"name": "codemirror", "version": "5.65.4", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-tytrSm5Rh52b6j36cbDXN+FHwHCl9aroY4BrDZB2NFFL3Wjfq9nuYVLFFhaOYOczKAg3JXTr8BuT8LcE5QY4Iw==", "shasum": "384c257f27d1d06f44311722c98ad802cce3984e", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.4.tgz", "fileCount": 338, "unpackedSize": 2931483, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEpIn0T5tIIW3X/KNduQh5PXRQTFB0HqCfdNmOJAfIj6AiAlPdJ1/O5xucW+86mnvbSbzuGd0MIeg7sJOpQDznFT4Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih1m6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9rw//aSEwx5+wJy743fHYGlRxofdhNMpuxtc31GhNw6Gfar3BtBpW\r\ny8jFx1zS6HeGhS3+cCvsPSdX8d3cxdobYZZ/vBFtP58qt3kFp/NRpsRPL7sX\r\nKq4Wy3mQkZOpH9WcvfXf3dsYkXZ4QfuJ/rOJoJ7WrNJJlXgRfNOSP7qJfWJw\r\ngUGGVveZb/EQja2J8d9gHeXF/lmTjuf6iBWpI8HUNSM1sW/3kt5UGUtYdE/d\r\nR6CwGVzvQePIkWTwrHtrriSqL8mukHYC6e0+PP00ZAE0fsNu1LhnVNFeCsa8\r\nQgV99kBNrwJD4Nkhc2RWW4dR8dqPN7/33sAphVfK167ma8x/4y0kJ5SfowgH\r\n3oHU6TYiqaF/JqNgsk2hSA5oes/Tn5hQ3KtxIxKfDvAs75LRkzWFwUsx8lBh\r\nhPYAXlI6o1bdj7JRsQx4fUYfFDP5HgNO/2JlnJrTCYF5sykhPykWU1S+R+Nb\r\nodWTIdREaF/6soAXOhHfAdmdqWovZw9vz3hpN+3WcfkMkR9X4K/3VT7lziUm\r\nw2iZOAZakYmCNHlaDT5EFp/ame+6u32d5gn3D6kMh0MI2UABzEjGyEC2m5YN\r\nKek3s9uldkBn041hoWyt7UZzA2Md/UKEwSRCkRzNMBq/6OXrzepY5BAxjhy/\r\nNxnjzsVRikovmJaKfNKszD2QpCk0PeieIYc=\r\n=3VwY\r\n-----END PGP SIGNATURE-----\r\n", "size": 749461}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.5": {"name": "codemirror", "version": "5.65.5", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-HNyhvGLnYz5c+kIsB9QKVitiZUevha3ovbIYaQiGzKo7ECSL/elWD9RXt3JgNr0NdnyqE9/Rc/7uLfkJQL638w==", "shasum": "f38f0e29945c3464df0c81f946fcd9a063fa2024", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.5.tgz", "fileCount": 338, "unpackedSize": 2933225, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFj6y1orJvu0XahZOw1vL+xg+dKN/RstkTrcylQQvgRTAiEAop3/HC/iI76eN93llvLK5Evufvr7vDG5T1zN647nfB8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilJWAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJjw//WjBfiKHFrAYijl2mpdgXIAuT/Waz75/qGRgW1rVHdoFS3LmI\r\nqqrQSu0GmDsD7OPF6N/PRLUBxfvqXblnbGbkrxKWKICBap1JtuZvrXSj9186\r\nZjiy/5OJvZxG03Kl5VRRKb1/hmg7vs9U12BGoCRZgk1fvW1ZzSW1VxJk2ZTw\r\nuXMqQWIhlHBoVvbjs6lGfWFc+pgDVfg6Z6kfHCNa1HXbUKRvMNEynsWNplop\r\nhCIZ7gqfZIk/fWaGqy+SUelJJjoSRcOs6ofh8tl4odAeiB/HyUglZrfetxkV\r\n+0Oc9WcN0FFTjoIm0ftX9aNVKjEWFP5+Xtof/DkQnftvsjMvr3W7AVjkaexX\r\ncYtW8q2Evf/AKfxPHK1JtQ8HR7SDNabu1B3adP45NIbAIjPtc7TB8Y+iRrEd\r\ntyuMGnVkbwMB+LU7hSyOrS2/739y6PyOpcUDaz+lRLraIs00ywDqfpptv+lD\r\n3J8Vef50MKMuQKu/JOuqWIUBwLpriokqbE+Esl+xhycS88OAzpnQkpCLu4tu\r\n264GLJKDxfzta/vfqsnopuqViLr+IEefCgafF19+n/ld7s2ZL4xWo4LczNJR\r\n4mfTOVIope8+iZoMxb3Sw0Msd9wNojDnlqfvW03e2Lz4A96rCxz+5AEwJuol\r\nuCNwoIcOMC7dV+DvCsPYhAkbOdUbjL+MoyQ=\r\n=mSVE\r\n-----END PGP SIGNATURE-----\r\n", "size": 749944}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "codemirror", "version": "6.0.0", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/commands": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/lint": "^6.0.0", "@codemirror/search": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-c4XR9QtDn+NhKLM2FBsnRn9SFdRH7G6594DYC/fyKKIsTOcdLF0WNWRd+f6kNyd5j1vgYPucbIeq2XkywYCwhA==", "shasum": "48aac6370d188f0761807ad9c3b62da7e7f72446", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-6.0.0.tgz", "fileCount": 8, "unpackedSize": 17652, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9SwZBz6IObkGAgELU3uKAnFwDfjvozMlUNqdZsFj0zgIgT6KbvmIDfaSymgG6hpR6peT/wiTPEVTl9nahmOEYsKY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdARAAm3BORx4IXMexSCSkusJ7Iq9A+t9Ajphdtq+ArvsOd/wWAR1k\r\nUufzO3BVDhX4alacNLOg8O5CK45GE9XBrwhyAx5Vo1FwK2pcqXO5HsZ2zWMk\r\nLmEDIP9hvDfe6Vf5Z7nsfHXYExgH+U311wbEipnp5ddvhqnC1ZUY8FD+yJA6\r\n2aVjpn9uRx1IoM2VGeuZZDKiius5lqBy9LrPEwtYPJ5ks/8zihKeo+ff+h6U\r\nDEQYoJWRiyvczS9TwRdgM2jqhJPcstaXyH2YnzscjuFE8kjmylKDtQYHNSP4\r\nJjVi2PnQw23Csm1vV3epN+NDZx5/ESlNYlxy33TLLmRM0AuEqUA1x9gqy3pb\r\nusOI1xBjH3rl8E6Ao+J21m2p3RdD2XIVjn/DgTmNOjst3PiW/NFBgtnwDu0W\r\ng43T9YopvKeRKI5nSHvpS8awCYUYtgSBLUMgkmf580z69HZyTU/Y5B12Ngbu\r\nQoPZ3Jsam2yiP1a3KHhn74299le5Du/KZz+RfHj3LxbHdc6dMGELQU0FNXy9\r\na2wZTNQXoQW8p5gqvcG0n3qE7B1Z6jnNz5lEMdfMsE2myGzZv8NAVmB9RYH9\r\n8Cm+R87PDHeAwIrfgYe7tgZ4hrFWY6kiOh6+jSXrN2uuC36Wh1cTiGJF+SQp\r\n0fFoLzoFq8LyRs0yAcLh0BMh9+02+6A7O0Y=\r\n=z86A\r\n-----END PGP SIGNATURE-----\r\n", "size": 4135}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.6": {"name": "codemirror", "version": "5.65.6", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.0", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-zNihMSMoDxK9Gqv9oEyDT8oM51rcRrQ+IEo2zyS48gJByBq5Fj8XuNEguMra+MuIOuh6lkpnLUJeL70DoTt6yw==", "shasum": "fc313797331cbeb3bcab0652d1ec9d0f40c23ab5", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.6.tgz", "fileCount": 338, "unpackedSize": 2935687, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpRoqDdhZ061cbpl7o98HGh1++RmHPfW6yptZ4uQ4evAIgEMY40yO42DYA8rzD8qoTtzuN9ESW32D4C4F2PA3PrdA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisF3zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo71w/+PPovBQkmhISdI3nZJlU2GfZpR31xNKur7pRfTP3tP7E/z4IP\r\nVMsCoCzfRnx6QhzOQK7ny6xDsIkHznf7m5AUkCTLsa6FU07Pp81VZvXhoVnn\r\ngpZFRrgoWStOwGqGGpcDrRrLY43/Oo4Th9+IDPdep/GRmjHZPkettQOspnz5\r\nHb1qnHJ/vixeh2+SRaUWOq+YYg9dg3uKV3apP+PYaFxdpWG4coYZVrXc+g4V\r\np39uUUoJDKGOd99+S0o0AZUC1b6bKiAggpgxW8xYSJktOo2f/NLvZZ2GG3G7\r\nZJEbfKg0jfLx1TrITzi95hipkq5orlrhdNSj1akFsMt0mq3e2gpSZh3jZ+4V\r\naQir1Ux9t4hDlwlgcstprmR3AnyxWP0Uc86OFADMmHhKOZOZqwRABjqPhLw9\r\n3XoSmm26A1e5fcF7sMAjCCVAfsFd8mbc9RSBghK1IWD3G5r2QkWm6/wRZ4eL\r\nN+dKY2J37/ya/PPYWN3O4qgVsI01mkoMBqzaOuA8N0A0ZRmBUpSa6sN6S/gS\r\nwxXFgTF2ut2Hhnti9+tNv/tGBiKfOJ3UVnnIiSELOG7kfxRMI8u1Hcv6zFGD\r\nP1GdKcUd1/nFFnpfTpErGAwN4Zt3OiBi7bVJU3yaUBX8mYorgQrkrRdRJjp8\r\nY8BMXcTbYyYXcGvuUCJawHoH1S+nzx7mz+8=\r\n=jRCc\r\n-----END PGP SIGNATURE-----\r\n", "size": 750172}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.1": {"name": "codemirror", "version": "6.0.1", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/commands": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/lint": "^6.0.0", "@codemirror/search": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-J8j+nZ+CdWmIeFIGXEFbFPtpiYacFMDR8GlHK3IyHQJMCaVRfGx9NT+Hxivv1ckLWPvNdZqndbr/7lVhrf/Svg==", "shasum": "62b91142d45904547ee3e0e0e4c1a79158035a29", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-6.0.1.tgz", "fileCount": 8, "unpackedSize": 17785, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4iPQTg/+V5iufSAQiqlGfjZCFJ+SFxlH1lwX7a4hSVgIhAKXHccwkQuZLJph/TQ+Ud5N9r7nsX507MSfVKdSAuSb3"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivZ40ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprlg//cr7TySRyq2wIIxxL4EP4udIS5q9nOFtMSKgPacyexZcleCTI\r\nywKY1cAIdH56PMVBNCVIXlkp8a+p3gGuSA0/E8v+busn5JciZDxLyTIUeuZv\r\n2YKLS6VGjFx/WeLY9DAoLm0PO1I6q1b8iOR4F5NFhMt5CkfA77E2KHMATy1G\r\nDuZ7uz4SkfO0oSuIWq1yVgIyeF/Zl8sJ8ojaIoOs1YnRaP8pW0Xw7AbBFEEK\r\nGBZo9d4g9JtSKhjOErcRFRao6tWbge5l+muEa+IZQJ5cgwSeZpq3ItSUAem7\r\ns9lhhKrMQ1sF3xsJFcYNtjGqMiGBnG3c2Qwvy2tXmuDx63ENjqQDejrhUtZH\r\ndMP42cbg/B9S29Ky7t5S4bRFPzfsfm7rgCLxWQxY4mqMeAwyVgy387EpoLyt\r\nL/Cvg+8+NL/A6BUhimHAGsbsu/KHmq4MijcLxDByWLCTkKnqifgpa5ImNJEJ\r\n9Ylt1Jt9TYezwUpGRwySQOLN8CIzOTFbtoPcKeKEroB5crAdKKUsW8B/f+O1\r\nL3bO8c3KBVdlUqk9rzuxaYM0/S3kSIlPYzfMK2gNP8jEdwy7HVqu8Q8ECPYE\r\nB2dyilRFIm8wq6auOQZa7j3B01suJQfCwtSDotl4S/trw4b292hxt1ckQl5D\r\nMSmH1ISvQrMoNUUZZ36shq5Hjum87ZaUyV8=\r\n=eM8/\r\n-----END PGP SIGNATURE-----\r\n", "size": 4218}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.65.7": {"name": "codemirror", "version": "6.65.7", "deprecated": "This is an accidentally mis-tagged instance of 5.65.7", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-HcfnUFJwI2FvH73YWVbbMh7ObWxZiHIycEhv9ZEXy6e8ZKDjtZKbbYFUtsLN46HFXPvU5V2Uvc2d55Z//oFW5A==", "shasum": "0ee51db6a9db59d85c5641515e31c5ea2766f9cc", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-6.65.7.tgz", "fileCount": 338, "unpackedSize": 2954247, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICF7texPH+yzSgwMWSr3g2JHhn28kbYo0kR0G7PmcJXFAiBYLusFW1wRMM9vcIKA4a4YNsUXNWTfWDN1iNz0LXM7Wg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi18/qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrY3RAAooSnHHn0XP18+GlIQsT//YlkS1OxJKE7AkqrOQf3OPg+Ovom\r\n+q2XfmNOyjrQQMkfdwkP6+sKbn5eyWnjns+5kq3m/SU5K1CzXV9tJsW/h0OB\r\nksSf+IL1faCGmIu85nB0nFco/PVY/lbvDy5jcvbWUsz5KjDqUN0o3L89MhoW\r\nd5eQsE0z7PolICaVvWfCWVYI6oEefmGYedus+GYIymrdjKNeRi5zbCva25vc\r\nNFNEle8WmGFhXjC+Rti0VBpNCfv76TJwClvcNJgIlT7TTYD3qpDEJxIEm8P/\r\nWtf6L+5IVwxot/DJz49jODNSAbkAFO6iZGSKGoq1igJjOvLXLkOrt74sxkaM\r\nX9HnXTojTRWuRm3Qedw4d3833IvKIYT11sD+fO75qyWkqkVshycPHyw2iwdy\r\nfAFn/bXr6FwE8sna/VzJBCsdEDplRkmltD8ge1Ou3UEkQT4UAMtqTjO6zmrp\r\nJ6rFG6ONplmdlgSQjXD7qeU2mckENeCMIp60mBvWjYZX9gNvfQbHYx7VKp2U\r\nYEA9Jnvg8fAnOt8AOSF+ltxXs7UV3B2YQaYsHwRUN1bw4PKjC7bI5Ho5At7h\r\nEmlMDA4L+UvgXSfn46cv4t4t5yNgzZvLqfF6+N88zIAmtg9tTJQuUv3xRQP/\r\nSmhzzkpFFJU2hWosa7IbEO66Hcxc6JlklKk=\r\n=xK00\r\n-----END PGP SIGNATURE-----\r\n", "size": 754750}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.7": {"name": "codemirror", "version": "5.65.7", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-zb67cXzgugIQmb6tfD4G11ILjYoMfTjwcjn+cWsa4GewlI2adhR/h3kolkoCQTm1msD/1BuqVTKuO09ELsS++A==", "shasum": "29af41ce5f4c2b8f1c1e16f4e645ff392823716a", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.7.tgz", "fileCount": 338, "unpackedSize": 2954247, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOEzXJCsuhAIplFLnENVMoJ32X1T9eiduSA2XCojjAnAiAj1dQSRW9X8P4gYO+AHyhAqNKlIgksVZwD25aekiVe9g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi19GUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMMg/8CtMPj0GdwFZM49BWkJNsE3BQJFZVerNZ1FjoFA+UhhZjlUdC\r\n2lWMvVVFnUhPpGemuO4DWwRZFpLGNVQQHUpubSrT9d14UvucDGV7NAM3PXb2\r\nWrMe2QeoEPmp3IIG43AanDL7NEBUkHSNMwQyCo0Jr44Mqjs00yVJRSIIiFSO\r\nYhxX/UIrwIv4bYTi1RqUyjcqcnpNjrhsDkFrYDzNj8QCSQkk8vb7MeelRg8X\r\nE9vjdawpN2DmxUGvgI5JuHwpIMBlUev/D5cgOBXmJXNNSqUZAN7cFA/RJKUG\r\n2bAmyAk3+r76yi82sX75zJp/k0qa/XsQhcdckox65y4nDbuO8IKUOT3+eqs4\r\nMc3gb276QsjGJDK8KXXG+EtmQgEVm4Rj7ZY7J+ZrfStUbFDk6A/fSsTKTFcG\r\nNbN8Lm0PK0rgUGCa2MWe+itQBu0sLWjFejqpbiqt/TXmYAxULw44JuzMduRA\r\nhh8nNKrhXvjCOooLYKe5hPEryeZwXd4WUAsvEo8IfQMC4Acw3GeApXRieMh4\r\nw2Pvz4tyMhuaercOyujKKXDPwphYzr52oYntIqOjCduKr4UXEha/EeovqN0t\r\n925dwoazWadoxvgpVg95HqN1g7ME4LJ+GRM+ZGdD5fipN6ai4E0JGytDeN3q\r\nMfxE4RkgUpNcgqM8PRfGHeVR0oVNJJ7mjEI=\r\n=ce1m\r\n-----END PGP SIGNATURE-----\r\n", "size": 754749}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.8": {"name": "codemirror", "version": "5.65.8", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-TNGkSkkoAsmZSf6W6g35LMVQJBHKasc2CKwhr/fTxSYun7cn6J+CbtyNjV/MYlFVkNTsqZoviegyCZimWhoMMA==", "shasum": "50f145ba7eb725091110c31f3a7c1fdef6bdc721", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.8.tgz", "fileCount": 338, "unpackedSize": 2955207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYNi3ibA6XQFKZJn0nQtwTLsu/RVQPnnL+mL6Gm+jI4gIgE9LaUVlgsOi+jtHnhKaxJdkC6px6rZHBDCcI5nVAmMA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjAMJYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrywxAAhPuwbnD9fD9jR0ZBqy3WzxJs4VFA/J9+XHEw2jFj4BH7GlSj\r\nNXmohm9UEhCT5a6cQYPqfYQIhZWkjxDyXb3Ia3W8P4AfPOv7HLs9yp+q+JCL\r\n4IYleT0Jrues2rGDVqn7J3+dBfFnmBZBYVDxBS7N9+u0oVAs19Y3stqPMcFb\r\nN63rXsVeSfY2Vo+8/NRvIjnFR5lZvcTXE7PfbiHFOwQJ9xteTKV6P5V6vc+m\r\nF31lVoYnNiNprXgjE0Af+R4kfBiEjvBafJ+WkT82nKIc+9jcNo1JT3oGTfZX\r\nURCb6Twk9muBJslqEleSJUmoRXRRfRrP3v3TC8Yb2thTDWgGcA7CsU325D5P\r\nUaCyacQUcWJTuVF9NMA52UbycifcuYifI7bhYdGgp2h9r9Ba/BSpinoGng+/\r\npHC7M8d3Vm/TRf8RCD6411DN830jncXgian7pgOjsOnFweVs3ctRp6b+G0ZE\r\nhTxUirqIFAUaFehr9ACVcxB4KRfHt+g9kM7Q/sPUChpTsix6dK+aXAl72taf\r\nReCbg/Jp+PHJ7gfByjKqLQkFNHXyJmCpvLDRtQ8rxHNwFwZY/3+AQB9X67or\r\nr6SgcvtPVJuvXBHs6PUUBOgV8T7KB6p0b5FbRZxVvOiA6DkLsCnG/iEX06nE\r\nOwP/YqRB7OPijFJIbrRDvm/ljQA/Ff4BT1s=\r\n=jrFC\r\n-----END PGP SIGNATURE-----\r\n", "size": 754967}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.9": {"name": "codemirror", "version": "5.65.9", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-19Jox5sAKpusTDgqgKB5dawPpQcY+ipQK7xoEI+MVucEF9qqFaXpeqY1KaoyGBso/wHQoDa4HMMxMjdsS3Zzzw==", "shasum": "ec70c92aa206ee4c9853d5f1e7c4ed356cdab68c", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.9.tgz", "fileCount": 338, "unpackedSize": 2957313, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfus/mTU88+etqn68cS6JiVeTIwtfsMFUuQcUEEUHWqQIhANShs+j6vr6Vt2HgLtjvHAIoKPrP8thNaxrsjMQADuK2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKbWRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1Og/+KHX8M7ZeO1TbXBT/yM57XQ3ormC7R0OIJtykj4CsjMzFyZSG\r\nMGCgSt4FTiZGjMvTVya3Erz9xuovY2wBtQjUDaajsRQIdqVWp+IpxSfx1eWj\r\nMM7fdNbK0d6SOzuMVvm5BR5PpAg+wY02/E0dWOm2QbZ8166SxFHvvtULYFUv\r\nf6Kw+22/ianeupcuf3wZOd66l/cglf7/DDpuccvU07syUi21tEpYaFISRM5m\r\n5I6lN597+41Ca3xnhtquiIki1iZoiz/4w0f47q5bjMa2bvaBHSQsZlpEuy0R\r\nXkIGQxyOABFmJhHqBaBsrZDhbjldzefLSdV44c2GaxsQZwEGBn/Je5+zgRJJ\r\nXxbeo6Blac67+T1RxDUlckkms4Ndfn/zYshEN7MyXr/Moj3b5eYUJvLoKY7Q\r\na6BvNYeHW90ZQVbieOIRI5e6zJIAFQI+FSDXuXc2rVfxDBH0FYefd/XJKx83\r\n+d7XYldp6BVJoMQc7ibgQphRd+fX+3snCDIbqkI/jaKGJz7+0IpQFUsA8REZ\r\n0LolUoXy9nzvuixdrG+k5YhGVKK0HBea3rbalNWZaT9lx85a6iB1eRb2FzrM\r\n+k4YCRZN3M8jJtYkOCrsmnhSZbo3J1z5dTn3xz3b/OfrBmN2H+z5XiyUTkIN\r\nYYv0EHDhZBvNKRikjH2xHN7WFPQnmYmynt0=\r\n=IpSz\r\n-----END PGP SIGNATURE-----\r\n", "size": 755444}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.10": {"name": "codemirror", "version": "5.65.10", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-IXAG5wlhbgcTJ6rZZcmi4+sjWIbJqIGfeg3tNa3yX84Jb3T4huS5qzQAo/cUisc1l3bI47WZodpyf7cYcocDKg==", "shasum": "4276a93b8534ce91f14b733ba9a1ac949666eac9", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.10.tgz", "fileCount": 338, "unpackedSize": 2957750, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+l5tLNKtu9Z9A/D/HPWjqi4vjoICwJcgFEZu3mdRJrwIhAIAVdYNAB6uYAwtUrlVYXt28ayIcEZ5mRKtpK6uGr/S7"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjekl/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBMA/9Gx9SSOe3rnLQcUs8NGVVDPQM+Af60BSb5RadqBAdxXeypZ8N\r\nUNObS5SOw09nDMpu+pNyvAbUrBsoSsHw8QxbiJ+vkpG6l2WG4Rpid/Wm74d7\r\nXtR/rywAx/x68isH/K3wMU/GaZMIKy2FFI18Hh4Tr7nDX0DB5BT1SUx/SUtu\r\ndi+l2DQk4xvxeqFMzeZvS6WasmXcWBd65Zwsnmp4zrQFdxT8mjeq4X2XGI1q\r\n+BKx9qGfD9zS0uOWtgQxtF7bLUbJpT0ePAWmWdchLszF+4tLcCrZ+RRyL92a\r\nD+3ve41esoqT25TtyXk1Hw29t+uQNFknrt9C9IwlXH0RGWaa+eCKUFUTj3Vo\r\nT4rFMnlVGQCwYwKjw0t+YxM0BC2WHC5/hz1vJkA6ChoxHXvgqdV4jLhtAKfF\r\nufuis+W7/dE4yU6PL0LodhP0/muJJ/O2VKwHFwiHIZfRbuSFlFUxH8C9Ojyl\r\n5smbds+mA4mtmzHkdXKw5CZ6Z2rywccEMxAZTy8+UmB9bpdfYIGAUdZyFdUX\r\ndR3Wi+rXfS2/PUle4W1SrwH0gLQBhU3M4HDAUKB5qWKJ/P39YWUCFEcfxCQc\r\nUrOvht9aUEkXjN05KBPmHW59IE2pia8PpX072n/y70LjQVmLaEXm4tmZbMo8\r\nl/8DF6Og6HGClTX8rIYAKE58SuYliOmmN0M=\r\n=b6Yl\r\n-----END PGP SIGNATURE-----\r\n", "size": 755598}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.11": {"name": "codemirror", "version": "5.65.11", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-Gp62g2eKSCHYt10axmGhKq3WoJSvVpvhXmowNq7pZdRVowwtvBR/hi2LSP5srtctKkRT33T6/n8Kv1UGp7JW4A==", "shasum": "c818edc3274788c008f636520c5490a1f7009b4f", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.11.tgz", "fileCount": 338, "unpackedSize": 2958148, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXADbTYVsYA0tJJiRB3gnfd96btdY2Dej6kHaDE5PyXgIgTZ6flUsA70QYbbO/7k6uz+6vEq2tZkRpXaCka/iserc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoYrLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDng//dKm4lsSBfrxPkn72rqSHctnM8JEuuPxL6e/ESgPgnmb+0Lq0\r\nWmnoTtRluJ7Q4Ic0EmQk/FRHCTXw2/jW/urt2+yXzkxd20ZAbaPMujqeHWX1\r\nukI8CbI5GaUrkap9cGSfQ7IAtpUKQpQC5FB0Zm05xRp3PH3G0X5TRegJrQGt\r\nzJbtylLWfzWx5xOatLZqLNYn2Jn8kNIfGIrlaKw6Re2q4jXq4y8pvqNID7O6\r\nufANlnjt3GmJyFr7d6OrjwrpesaoidgdEINilBAGp09/qv0Jrcz0uquNIi5B\r\nszLqOiqguWXqBQBhgkdhkBg0bsLnNGOXM348sIcQjT1BSDn2jeNagYWRWynG\r\npxhQyseJjK0j7rjDcrYee4GrevD5ELG0rOj5sP9yJUyI2tLHcyhploPxE8dR\r\nN2gDdXu7dZx67gBjKNSAB3j/GxbIZc0dgqq0cDao0HIK+WA/MW8ZU4ZgYNjv\r\nfXheOnjBb5nPNTaHzVLlD9i66sV7A9AIe5u2/7M/G/sYDrTkjHF6zJp3QVOw\r\nZlF7yNbGt1eqC6hr/oNon0/YCUcx9pDAYacItMmA76D1kMNgaXtM1Xve+Hbx\r\no+pObEtTqeBRBG1u1tZu/Z/PdAZK7YXTwxIQ3havE5pCoEgPyHGdssW5sboQ\r\nw83CQQUKHZzbosyFELu9hPfVmI3u5mKYzDU=\r\n=QI0q\r\n-----END PGP SIGNATURE-----\r\n", "size": 755722}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.12": {"name": "codemirror", "version": "5.65.12", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-z2jlHBocElRnPYysN2HAuhXbO3DNB0bcSKmNz3hcWR2Js2Dkhc1bEOxG93Z3DeUrnm+qx56XOY5wQmbP5KY0sw==", "shasum": "294fdf097d10ac5b56a9e011a91eff252afc73ae", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.12.tgz", "fileCount": 338, "unpackedSize": 2958344, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID8obFoJFrhnHpVtsrVz04gj3TIlGZ27n/LKVlGfBRcHAiBOZ3oRn9vhu5jQ0ZcQ3Pg6GvWNRJ7ngQPRrjLo0EKZuA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj81IRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNVw/8Dl1ZZ94TFPR4i9Vl/3xmWalBMWtx/9Jot36BkOlmN2u/kMEY\r\nw4jzgDf9hSR+Tcuzl5LR1Dd8QLvmkJ6ptvog8UsEWpbm5lqSQ4EVhqP9PI76\r\nsWMbpIpKf0z3uAzzOAiD6yE9szQyUw6dJ/BRsCU5Fhkad/NN2hljC38F9/PB\r\nLZbJ59Ydkk896lI4xPb0yp4p3N8eVIDXTf7569y9GzlJOYL8FG6ZbKAqlQHp\r\nr5NVEG8xht90KtLdbm0W8OqssSt5o58rBv6U0UfDiHjkE0NFACyShvhisa99\r\nauUHBxdUeP2Yx0Ozvjz3G/Mu4pXFWsuirwkijNU3icOM7nDDgLBsKvn92hgl\r\nGi+ILa7QJIkyRX8l7eeuBhj2LxZz6UaVgN/wGzWiTYGP4N9Y97yc+LRySQR1\r\nUgzrrjTrvRfsUk/hDXnrtW+yYossgEqu0WoeM0KCzVy9y5mpNOz75oR0OC76\r\nLj2ijK/iKhwFJGx31W71HTlTm5JdxRJHIvBOq2wIefFvSDWr0kNIf0akI4XD\r\nOVOhkBFFtNl8iFCQcdndIkMIOiaGVhWWtF6ETkWIRUEiaOpQ7K0QEmGAONoI\r\nj6HZypnOt0zc3Ove6j4VoEjeN250L6d1ZVgS6MvpdcbSHC2IVWQ7HGhh2gdJ\r\ncCWuQaB1LJR/o4xXB1LAqRadU8mREOxihvw=\r\n=UfTZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 755791}, "_hasShrinkwrap": false, "publish_time": 1676890641015, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.13": {"name": "codemirror", "version": "5.65.13", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-SVWEzKXmbHmTQQWaz03Shrh4nybG0wXx2MEu3FO4ezbPW8IbnZEd5iGHGEffSUaitKYa3i+pHpBsSvw8sPHtzg==", "shasum": "c098a6f409db8b5a7c5722788bd9fa3bb2367f2e", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.13.tgz", "fileCount": 338, "unpackedSize": 2958658, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGecPiQi+3H3ePx2D9Xva2Cr+vnS8wVccaAdc3jn+0pdAiBycuGqD5zdcMyygkYj2rp1TW/d7LeghWVd2wBMGTmasw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSjKkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogUw/9GVuDPclmgLvrFfaPp2HVbeBPIy7SRIYqf3sW5g1mTNs4jZQl\r\nzRCz19AJ+R0ojT9pzCtvObxd31ImOQkk5/T8KDDqZeOYASjGUsmvRAsk648t\r\ny47nVCTqXKD+lNLv30D6q866qEXg6K0p2o3697nrq8oz+q+ezAVqM+Wuxu08\r\n2olV8+Du13dShQ0xx6O8Qtjcf6P5CjmskKaBMFCVvArcxNrBQJoa3l2ZHoZt\r\nG5gn6Fq+7FTwcIztJ8cEHimAcWrzCxiYMyMPyjUIdM3uQMcZQj9gY6fd2lW9\r\nY0vRcrQ8T3JMFzE0Uf7/n9oE3sY9H+p+WP6VS7GDSSNeRWjtOX2+yH+I3FsC\r\nVWMnan76HbyEn4ldbTNYIUe5RU38d48XgLk5UpWGD1/8wmhHaCZd/SLsy0oA\r\n8ORmfednRm+LHNCGJreQh0efj8xnNhfYvoH4fdbyNsekDP9DonAZOi1jmDU+\r\nZgGTsrW/l2l6171iGLeYQAKucGWroq+B5tQx4JCNgIyQgjGIPKcT8K2xyEO+\r\nOX4jDnicSiPVPbQfLEAzR08ZnhDSbc/b3ymuKnyW4YSvuq8+i4rcyy9D+n12\r\nJft3QuPhoUWDUdyFqoZhY7ddnV+FoVPlYAmNqKXyNB67R9BLhC0r24aEQGRv\r\nls/wjTVLST4Zce/4P7/XGtKAcXOTsIk3yxA=\r\n=Lhko\r\n-----END PGP SIGNATURE-----\r\n", "size": 755897}, "_hasShrinkwrap": false, "publish_time": 1682584228588, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.14": {"name": "codemirror", "version": "5.65.14", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-VSNugIBDGt0OU9gDjeVr6fNkoFQznrWEUdAApMlXQNbfE8gGO19776D6MwSqF/V/w/sDwonsQ0z7KmmI9guScg==", "shasum": "e75fbc7247453f1baa71463c33b52adba7e41b2a", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.14.tgz", "fileCount": 338, "unpackedSize": 2958765, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwLSjlejCUE6vJjXZ68CSaCZ4ZmNQGaP1uCP5kNmIHLAIgJb6AAchZEAlXA319ZkaJgQlDfxikDLG0093+2sDRGGk="}], "size": 755858}, "_hasShrinkwrap": false, "publish_time": 1689579431123, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.65.15": {"name": "codemirror", "version": "5.65.15", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-YC4EHbbwQeubZzxLl5G4nlbLc1T21QTrKGaOal/Pkm9dVDMZXMH7+ieSPEOZCtO9I68i8/oteJKOxzHC2zR+0g==", "shasum": "66899278f44a7acde0eb641388cd563fe6dfbe19", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.15.tgz", "fileCount": 338, "unpackedSize": 2959045, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJa0UkQoU5uSyii2yv7ckeAAKoPZ01xebP31SZZYAdyAiBbs8o11wv/dluIzGFDqM0UDODxppdxhTDFUX2Uks9fiQ=="}], "size": 755910}, "_hasShrinkwrap": false, "publish_time": 1693292432793, "_source_registry_name": "default"}, "5.65.16": {"name": "codemirror", "version": "5.65.16", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-br21LjYmSlVL0vFCPWPfhzUCT34FM/pAdK7rRIZwa0rrtrIdotvP4Oh4GUHsu2E3IrQMCfRkL/fN3ytMNxVQvg==", "shasum": "efc0661be6bf4988a6a1c2fe6893294638cdb334", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.16.tgz", "fileCount": 339, "unpackedSize": 2960290, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhvA+2iUf9PDi+M7nuWFF+VpvVSI3I5nNn47tOGZAVvwIhAO22g5+J6A5FHM2KY+OikFQt83wQFZ3pDbWom/AIJlIk"}], "size": 756370}, "_hasShrinkwrap": false, "publish_time": 1700474346059, "_source_registry_name": "default"}, "5.65.17": {"name": "codemirror", "version": "5.65.17", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-1zOsUx3lzAOu/gnMAZkQ9kpIHcPYOc9y1Fbm2UVk5UBPkdq380nhkelG0qUwm1f7wPvTbndu9ZYlug35EwAZRQ==", "shasum": "00d71f34c3518471ae4c0de23a2f8bb39a6df6ca", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.17.tgz", "fileCount": 338, "unpackedSize": 2960248, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXIQKriG9b+o9nu7n/Ck3aifzO7Hc02tdVPrlJcaVCMAiBtAQm35aqaZaN1AtclhpE+iFbxgC/K0b/f7V/O4fQCmA=="}], "size": 758673}, "_hasShrinkwrap": false, "publish_time": 1721485522920, "_source_registry_name": "default"}, "5.65.18": {"name": "codemirror", "version": "5.65.18", "dependencies": {}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "blint": "^1.1.2", "cm5-vim": "^0.0.5", "node-static": "0.7.11", "puppeteer": "^1.20.0", "rollup": "^1.26.3", "rollup-plugin-copy": "^3.4.0"}, "directories": {"lib": "./lib"}, "dist": {"integrity": "sha512-Gaz4gHnkbHMGgahNt3CA5HBk5lLQBqmD/pBgeB4kQU6OedZmqMBjlRF0LSrp2tJ4wlLNPm2FfaUd1pDy0mdlpA==", "shasum": "d7146e4271135a9b4adcd023a270185457c9c428", "tarball": "https://registry.npmmirror.com/codemirror/-/codemirror-5.65.18.tgz", "fileCount": 338, "unpackedSize": 2960729, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHAKyKX2yndpdo91F8BkrOMFq5H1/ATXBS7LZUsOOYOsAiEA33K9X1arlYIQ/L7oPQVW9vzNnvUstiAhSxoe2BCBMG8="}], "size": 758826}, "_hasShrinkwrap": false, "publish_time": 1726831144150, "_source_registry_name": "default"}}, "_source_registry_name": "default"}