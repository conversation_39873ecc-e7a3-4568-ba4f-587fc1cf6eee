# QQ飞车赛车图鉴后台管理系统文档

## 1. 项目概述

QQ飞车赛车图鉴后台管理系统是一个用于管理QQ飞车游戏中赛车相关数据的Web应用程序。该系统提供了对赛车、宠物、用户、评论等多种数据的管理功能，同时还包括活动管理、赛车夺宝等特色功能。

## 2. 技术架构

### 2.1 前端技术栈

- **框架**：Vue 3
- **路由**：Vue Router 4
- **UI组件库**：Element Plus
- **HTTP请求**：Axios
- **构建工具**：Vite
- **代码编辑器**：CodeMirror 6 (用于SQL执行器)
- **其他工具库**：
  - xlsx: 用于Excel文件处理
  - @vueuse/core: Vue组合式API工具集
  - bcryptjs/jsonwebtoken: 用于认证相关功能

### 2.2 项目结构

```
frontend/
├── public/           # 静态资源
├── src/              # 源代码
│   ├── api/          # API请求接口
│   ├── config/       # 配置文件
│   ├── router/       # 路由配置
│   ├── utils/        # 工具函数
│   ├── views/        # 页面组件
│   ├── App.vue       # 根组件
│   └── main.js       # 入口文件
├── dist/             # 构建输出目录
├── .env.development  # 开发环境配置
├── .env.production   # 生产环境配置
├── vite.config.js    # Vite配置
└── package.json      # 项目依赖和脚本
```

## 3. 目录功能介绍

### 3.1 src/api/

该目录包含所有与后端API通信的接口定义：

- **auth.js**: 认证相关API，包括登录、注册等
- **car.js**: 赛车数据管理相关API
- **comments.js**: 评论管理相关API
- **illustration.js**: 图鉴相关通用API
- **index.js**: API相关工具函数和通用接口
- **treasure.js**: 赛车夺宝相关API
- **user.js**: 用户管理相关API

### 3.2 src/config/

包含项目配置信息，如API基础URL、环境变量等。

### 3.3 src/router/

包含Vue Router路由配置，定义了应用的所有路由和访问权限控制：

- 包括登录、图鉴管理（赛车、宠物）、用户管理、评论管理等路由
- 实现了基于权限的路由守卫，控制不同角色用户的访问权限

### 3.4 src/utils/

包含通用工具函数：

- **request.js**: 封装了基于Axios的HTTP请求工具，处理请求拦截、响应拦截和错误处理

### 3.5 src/views/

包含所有页面组件：

- **CarList.vue**: 赛车列表管理页面
- **PetList.vue**: 宠物列表管理页面
- **UserList.vue**: 用户管理页面
- **Login.vue**: 登录页面
- **FeedbackList.vue**: 反馈管理页面
- **CommentManagement.vue**: 评论管理页面
- **SqlExecutor.vue**: SQL执行工具页面
- **LotteryManagement.vue**: 抽奖活动管理页面
- **CarTreasureManagement.vue**: 赛车夺宝管理页面
- **IllustrationLayout.vue**: 图鉴管理布局页面
- **404.vue**: 404错误页面

## 4. 核心功能模块

### 4.1 认证与权限管理

系统采用JWT（JSON Web Token）进行用户认证，主要特点：

- 使用localStorage存储token
- 路由守卫实现权限控制
- 区分普通用户和管理员权限

### 4.2 赛车图鉴管理

核心功能模块，提供了对赛车数据的全面管理：

- 赛车信息添加、编辑、删除
- 赛车属性配置
- 赛车图片管理
- 赛车分类管理

### 4.3 宠物图鉴管理

类似赛车图鉴，提供对宠物数据的管理：

- 宠物信息的CRUD操作
- 宠物属性配置
- 宠物图片管理

### 4.4 用户管理

提供用户账户的管理功能：

- 用户列表查看
- 用户权限配置
- 用户状态管理

### 4.5 评论管理

提供对用户评论的管理功能：

- 评论列表查看
- 评论审核
- 评论删除

### 4.6 活动管理

包括抽奖活动和赛车夺宝等特色功能：

- **抽奖活动管理**：配置活动规则、奖品、概率等
- **赛车夺宝管理**：管理夺宝活动、配置规则和奖品

### 4.7 SQL执行器

一个高级工具，允许管理员直接执行SQL查询：

- 基于CodeMirror实现的SQL编辑器
- SQL执行结果展示
- 历史记录功能

## 5. 特殊处理与技术要点

### 5.1 前端请求封装

在`src/utils/request.js`中，对Axios进行了统一封装：

- 请求拦截器：自动添加Token到请求头
- 响应拦截器：统一处理响应数据和错误
- Token过期处理：自动跳转到登录页

### 5.2 权限控制设计

系统采用多层次的权限控制机制：

- 基于路由的权限控制：在路由配置中通过meta字段定义权限要求
- 基于角色的权限控制：区分普通用户和管理员角色
- 基于功能的权限控制：针对特定功能模块的访问控制

### 5.3 Element Plus主题配置

系统使用Element Plus作为UI组件库，并进行了中文本地化配置。

### 5.4 开发与生产环境配置

通过`.env.development`和`.env.production`文件区分不同环境的配置：

- API基础路径
- 构建选项
- 环境变量

## 6. 部署与维护

### 6.1 项目构建

使用Vite进行项目构建，相关命令：

```bash
# 开发环境启动
npm run dev

# 生产环境构建
npm run build
```

### 6.2 部署脚本

项目提供了多个部署和安装脚本：

- **install.sh**: 基础安装脚本
- **production-install.sh**: 生产环境安装脚本
- **run-dev.sh**: 开发环境运行脚本

### 6.3 服务配置

项目提供了`qq-speed-dev.service`文件，用于配置系统服务。

## 7. 项目扩展与建议

### 7.1 潜在的改进点

- 考虑引入状态管理工具（如Pinia）提升状态管理能力
- 增强前端数据校验和错误处理
- 优化大型组件的性能，考虑组件拆分

### 7.2 扩展功能

- 数据统计与分析功能
- 更完善的日志和操作审计
- 批量导入/导出功能优化

## 8. 总结

QQ飞车赛车图鉴后台管理系统是一个基于Vue 3和Element Plus的现代化Web应用，提供了丰富的管理功能和良好的用户体验。系统架构清晰，代码组织合理，易于维护和扩展。 

## 9. 通用样式与组件规范

为保证项目代码风格统一、提高开发效率并确保良好的用户体验，制定以下通用样式和组件规范。所有开发人员在后续开发中应遵循这些规范进行代码编写。

### 9.1 通用样式规范

#### 9.1.1 配色方案

系统主要遵循以下配色规范：

```scss
/* 主题色 */
--primary-color: #409EFF;      // 主色调，用于主要按钮、链接等
--success-color: #67C23A;      // 成功状态
--warning-color: #E6A23C;      // 警告状态
--danger-color: #F56C6C;       // 危险状态
--info-color: #909399;         // 信息状态

/* 文本颜色 */
--text-primary: #303133;       // 主要文本
--text-regular: #606266;       // 常规文本
--text-secondary: #909399;     // 次要文本
--text-placeholder: #C0C4CC;   // 占位文本

/* 边框颜色 */
--border-color-base: #DCDFE6;  // 基础边框
--border-color-light: #E4E7ED; // 浅色边框
--border-color-lighter: #EBEEF5; // 更浅边框

/* 背景颜色 */
--background-color: #F5F7FA;   // 基础背景
```

#### 9.1.2 字体规范

```css
/* 字体家族 */
font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;

/* 字体大小 */
--font-size-extra-large: 20px; // 特大号字体
--font-size-large: 18px;       // 大号字体
--font-size-medium: 16px;      // 中号字体
--font-size-base: 14px;        // 基础字体
--font-size-small: 13px;       // 小号字体
--font-size-extra-small: 12px; // 特小号字体
```

#### 9.1.3 间距规范

```css
/* 间距 */
--spacing-mini: 4px;
--spacing-small: 8px;
--spacing-base: 12px;
--spacing-medium: 16px;
--spacing-large: 20px;
--spacing-extra-large: 24px;
```

#### 9.1.4 布局规范

- 页面布局应采用Element Plus的Container布局容器
- 内容区域使用固定宽度，居中显示
- 表单项布局统一使用栅格系统
- 响应式断点遵循Element Plus的断点设计

```js
// 响应式断点
xs: <768px
sm: ≥768px
md: ≥992px
lg: ≥1200px
xl: ≥1920px
```

### 9.2 组件使用规范

#### 9.2.1 Element Plus组件规范

本项目大量使用Element Plus组件库，使用时需遵循以下规范：

**表格组件(el-table)**：

```vue
<el-table
  :data="tableData"
  border
  stripe
  v-loading="loading"
  style="width: 100%"
  @selection-change="handleSelectionChange">
  <!-- 表格列 -->
  <el-table-column
    type="selection"
    width="55">
  </el-table-column>
  <!-- 其他列 -->
</el-table>
```

**表单组件(el-form)**：

```vue
<el-form
  :model="form"
  :rules="rules"
  ref="formRef"
  label-width="120px"
  label-position="right">
  <el-form-item label="名称" prop="name">
    <el-input v-model="form.name" placeholder="请输入名称"></el-input>
  </el-form-item>
  <!-- 其他表单项 -->
</el-form>
```

**对话框组件(el-dialog)**：

```vue
<el-dialog
  title="标题"
  v-model="dialogVisible"
  width="50%"
  :close-on-click-modal="false"
  :before-close="handleClose">
  <!-- 对话框内容 -->
  <template #footer>
    <span class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </span>
  </template>
</el-dialog>
```

**分页组件(el-pagination)**：

```vue
<el-pagination
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
  :current-page="pagination.currentPage"
  :page-sizes="[10, 20, 30, 50]"
  :page-size="pagination.pageSize"
  layout="total, sizes, prev, pager, next, jumper"
  :total="pagination.total">
</el-pagination>
```

#### 9.2.2 自定义通用组件

项目中定义了一些通用组件，使用时需遵循以下规范：

**图片上传组件**：

```vue
<image-uploader
  v-model="imageUrl"
  :limit="1"
  :file-size-limit="2"
  accept=".jpg,.png,.jpeg"
  @upload-success="handleUploadSuccess">
</image-uploader>
```

**导入导出组件**：

```vue
<data-import-export
  :import-api="importApi"
  :export-api="exportApi"
  :template-url="templateUrl"
  @import-success="handleImportSuccess">
</data-import-export>
```

**富文本编辑器**：

```vue
<rich-text-editor
  v-model="content"
  :height="300"
  :toolbar-options="toolbarOptions">
</rich-text-editor>
```

#### 9.2.3 管理页面通用UI元素规范

为确保所有管理页面的界面一致性，需要遵循以下通用UI元素规范：

**标题容器与返回首页按钮**：

所有管理相关的页面必须包含统一的标题容器和返回首页按钮，使用方式如下：

```vue
<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <h2>{{ pageTitle }}</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
        <!-- 其他操作按钮 -->
      </div>
    </div>
    
    <!-- 页面内容 -->
  </div>
</template>

<script>
export default {
  setup() {
    const router = useRouter()
    
    const pageTitle = ref('页面标题')
    
    const backToHome = () => {
      router.push('/')
    }
    
    return {
      pageTitle,
      backToHome
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: var(--spacing-medium);
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-large);
    padding-bottom: var(--spacing-small);
    border-bottom: 1px solid var(--border-color-light);
    
    .header-title {
      h2 {
        font-size: var(--font-size-large);
        color: var(--text-primary);
        margin: 0;
      }
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-small);
    }
  }
}
</style>
```

**分页组件统一样式**：

所有管理页面中的分页组件应遵循统一的样式与位置：

```vue
<template>
  <div class="pagination-container">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.currentPage"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      background>
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped>
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-extra-large);
  padding: var(--spacing-medium) 0;
}
</style>
```

分页组件样式规范：

1. 居中显示在内容区域底部
2. 上下添加适当间距
3. 使用背景色样式（添加`background`属性）
4. 页码选项统一为 [10, 20, 30, 50]
5. 布局统一为 "total, sizes, prev, pager, next, jumper"

#### 9.2.4 顶部栏菜单入口规范

为确保系统功能的统一访问入口，每当创建新的管理页面时，必须在顶部导航栏的下拉菜单中添加相应的入口。具体规范如下：

**顶部栏菜单入口添加规范**：

1. 所有新功能页面必须在App.vue的顶部导航栏下拉菜单中添加对应入口
2. 根据功能的访问权限，添加适当的权限控制指令(v-if)
3. 菜单项应使用语义化的command值，与路由名称保持一致
4. 相关功能应分组排列，使用分隔线(divided属性)区分功能组

示例代码：

```vue
<template>
  <el-dropdown @command="handleCommand">
    <span class="user-dropdown">
      {{ currentUsername }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
    </span>
    
    <template #dropdown>
      <el-dropdown-menu>
        <!-- 用户相关功能 -->
        <el-dropdown-item command="password">修改密码</el-dropdown-item>
        
        <!-- 管理员功能 - 数据查询类 -->
        <el-dropdown-item 
          v-if="isAdmin && currentUsername === 'Pikario'"
          command="sqlExecutor">
          SQL执行器
        </el-dropdown-item>
        
        <!-- 管理员功能 - 内容管理类 -->
        <el-dropdown-item 
          v-if="isAdmin" 
          command="users">
          用户管理
        </el-dropdown-item>
        <el-dropdown-item 
          v-if="isAdmin" 
          command="feedback">
          反馈管理
        </el-dropdown-item>
        <el-dropdown-item 
          v-if="isAdmin" 
          command="comments">
          评论管理
        </el-dropdown-item>
        
        <!-- 新功能入口示例 -->
        <el-dropdown-item 
          v-if="isAdmin" 
          command="new-feature">
          新功能名称
        </el-dropdown-item>
        
        <!-- 退出登录 -->
        <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script>
export default {
  // ...
  methods: {
    handleCommand(command) {
      switch (command) {
        case 'password':
          // 处理修改密码
          break
        case 'sqlExecutor':
          this.$router.push('/sql-executor')
          break
        case 'users':
          this.$router.push('/users')
          break
        case 'feedback':
          this.$router.push('/feedback')
          break
        case 'comments':
          this.$router.push('/comments')
          break
        case 'new-feature':  // 新功能处理
          this.$router.push('/new-feature-path')
          break
        case 'logout':
          this.handleLogout()
          break
      }
    }
  }
}
</script>
```

**菜单添加流程**：

1. 创建新页面组件并配置路由
2. 在App.vue的顶部下拉菜单中添加新功能入口
3. 在handleCommand方法中添加对应的路由跳转逻辑
4. 根据功能的重要性和分类，合理安排菜单项位置
5. 为菜单项添加适当的权限控制

注意事项：
- 核心功能入口应直接放置在应用主界面中，而非下拉菜单中
- 仅管理员可见的功能必须添加`v-if="isAdmin"`权限控制
- 特殊权限功能可添加更细粒度的权限控制，如`v-if="isAdmin && currentUsername === 'Pikario'"`
- 当菜单项较多时，应考虑使用子菜单或功能分组

### 9.3 代码风格规范

#### 9.3.1 命名规范

- **文件命名**：使用PascalCase命名组件文件（如`CarList.vue`），使用kebab-case命名一般JavaScript文件（如`request-util.js`）
- **组件命名**：使用PascalCase（如`CarDetail`）
- **变量命名**：使用camelCase（如`carDetailInfo`）
- **CSS类命名**：使用kebab-case，采用BEM命名方法论（如`.car-card__title--primary`）

#### 9.3.2 CSS样式规范

- 优先使用SCSS预处理器
- 组件样式使用scoped属性隔离
- 全局样式定义在单独的样式文件中
- 避免行内样式，除非必须

```vue
<style lang="scss" scoped>
.component-name {
  &__element {
    // 样式
    
    &--modifier {
      // 修饰符样式
    }
  }
}
</style>
```

#### 9.3.3 Vue组件结构规范

组件结构应当遵循以下顺序：

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入依赖
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import api from '@/api'

export default {
  name: 'ComponentName',
  
  // 组件选项按以下顺序排列
  components: {},
  props: {},
  emits: [],
  setup(props, { emit }) {
    // 响应式数据
    const state = reactive({})
    
    // 计算属性
    const computedValue = computed(() => {})
    
    // 方法
    const handleEvent = () => {}
    
    // 生命周期钩子
    onMounted(() => {})
    
    // 返回暴露给模板的内容
    return {
      state,
      computedValue,
      handleEvent
    }
  }
}
</script>

<style lang="scss" scoped>
/* 样式规则 */
</style>
```

### 9.4 特殊功能组件规范

#### 9.4.1 数据表单处理

对于数据表单的处理，应遵循以下模式：

```js
/**
 * 表单数据处理函数
 * @param {Object} formData - 表单数据
 * @returns {Object} - 处理后的数据
 */
const processFormData = (formData) => {
  // 数据处理逻辑
  return processedData
}
```

#### 9.4.2 图片路径处理

图片URL处理应统一使用工具函数：

```js
/**
 * 获取完整图片URL
 * @param {String} path - 图片路径
 * @returns {String} - 完整的图片URL
 */
const getFullImageUrl = (path) => {
  if (!path) return ''
  if (path.startsWith('http')) return path
  return `${baseImageUrl}${path}`
}
```

#### 9.4.3 API请求错误处理

统一的API错误处理方式：

```js
try {
  const result = await api.someMethod()
  // 处理成功结果
} catch (error) {
  // 统一错误处理
  handleApiError(error)
}
```

### 9.5 文档规范

#### 9.5.1 代码注释

使用JSDoc规范进行代码注释：

```js
/**
 * 函数描述
 * @param {Type} paramName - 参数描述
 * @returns {Type} 返回值描述
 */
function functionName(paramName) {
  // 实现
}
```

#### 9.5.2 组件文档

关键组件应提供使用文档：

```js
/**
 * @component
 * @description 组件描述
 * @example
 * <component-name
 *   :prop1="value1"
 *   :prop2="value2"
 *   @event="handleEvent"
 * />
 */
export default {
  name: 'ComponentName',
  props: {
    /**
     * 属性描述
     */
    propName: {
      type: Type,
      default: defaultValue,
      required: isRequired
    }
  }
}
```

### 9.6 管理页面布局规范

为确保所有管理页面具有一致的用户体验和视觉风格，特制定以下管理页面布局规范：

#### 9.6.1 页面整体布局

所有管理页面需遵循以下基本布局结构和卡片式设计：

```vue
<template>
  <div class="page-name-management">
    <!-- 1. 标题区域 -->
    <div class="page-header">
      <h2>页面标题</h2>
      <div class="header-actions">
        <el-button type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
        <!-- 其他高级操作按钮 -->
      </div>
    </div>
    
    <!-- 2. 主内容区域(使用卡片包裹) -->
    <el-card class="content-card">
      <!-- 内容区域(标签页、表格等) -->
      <el-tabs v-model="activeTab">
        <el-tab-pane label="标签1" name="tab1">
          <!-- 标签页内容 -->
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.page-name-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  
  h2 {
    margin: 0;
    font-size: 24px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.content-card {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}
</style>
```

页面布局规范：

1. 使用语义化的类名(如 `.page-name-management`)命名根容器
2. 整个页面使用浅灰色背景(#f5f7fa)
3. 页面头部使用白色背景、圆角和轻微阴影，与内容区域分离
4. 内容区域使用卡片(el-card)包裹，保持视觉上的一致性和层次感
5. 返回首页按钮放置在头部右侧，作为通用导航元素
   - 统一使用主色调(type="primary")
   - 统一使用Back图标 `<el-icon><Back /></el-icon>`
   - 不添加size属性，使用默认大小

#### 9.6.2 搜索区域布局规范

所有管理页面的搜索区域应采用以下统一样式和结构：

```vue
<template>
  <!-- 搜索区域 -->
  <el-form :inline="true" :model="searchForm" class="search-form">
    <div class="search-form-item-group">
      <!-- 搜索字段 -->
      <el-form-item label="字段名">
        <el-input v-model="searchForm.field" placeholder="请输入" clearable style="width: 180px"></el-input>
      </el-form-item>
      
      <!-- 更多搜索字段 -->
    </div>
    
    <!-- 操作按钮 -->
    <div class="search-form-buttons">
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="success" icon="Plus">添加</el-button>
        <el-button type="info" icon="Download">导出</el-button>
      </el-form-item>
    </div>
  </el-form>
</template>

<style lang="scss" scoped>
.search-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.search-form-item-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.search-form-item-group:last-child {
  margin-bottom: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
  display: flex;
  align-items: center;
}

.search-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
}

.search-form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
}
</style>
```

搜索区域样式规范：

1. 搜索表单使用白色背景、圆角和轻微阴影，作为独立区块
2. 搜索字段使用 `search-form-item-group` 分组，支持自动换行
3. 为表单项设置固定宽度(如 input 设置 width: 180px)
4. 表单项标签设置粗体和最小宽度，确保对齐
5. 操作按钮放置在底部，靠右对齐，顶部添加分隔线

#### 9.6.3 表格与分页组件规范

所有管理页面的表格和分页组件应遵循以下规范：

```vue
<template>
  <!-- 表格组件 -->
  <el-table
    :data="tableData"
    border
    stripe
    v-loading="loading"
    style="width: 100%">
    <!-- 表格列 -->
    <el-table-column prop="id" label="ID" width="80"></el-table-column>
    <el-table-column prop="name" label="名称"></el-table-column>
    <el-table-column prop="status" label="状态" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="row.status ? 'success' : 'danger'" size="small">
          {{ row.status ? '正常' : '禁用' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="150" align="center" fixed="right">
      <!-- 操作按钮 -->
    </el-table-column>
  </el-table>
  
  <!-- 分页组件 -->
  <div class="pagination-container">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.currentPage"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      background>
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-table) {
  border-radius: 8px;
  margin-top: 16px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table--border) {
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}
</style>
```

表格与分页规范：

1. 表格添加圆角边框(border-radius: 8px)，增强视觉美感
2. 表头使用浅灰色背景(#f5f7fa)，增加对比度
3. 标签(el-tag)使用小尺寸(small)并添加圆角
4. 状态列和操作列居中对齐(align="center")，并设定合适的宽度
5. 分页组件右对齐，背景色样式
6. 操作列固定在右侧(fixed="right")，便于操作

#### 9.6.4 对话框组件规范

所有管理页面的对话框组件应符合以下规范：

```vue
<template>
  <el-dialog
    title="对话框标题"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
    :before-close="handleDialogClose">
    
    <!-- 对话框内容 -->
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="120px"
      label-position="right">
      <!-- 表单项 -->
    </el-form>
    
    <!-- 对话框底部按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
```

对话框规范：

1. 对话框添加圆角(border-radius: 8px)，提升视觉体验
2. 对话框标题区域使用浅灰色背景，底部添加分隔线
3. 调整对话框内部填充，使布局更加合理
4. 底部按钮区域添加上边框，增加视觉分隔
5. 按钮采用弹性布局，并添加间距，确保按钮不会过于拥挤

通过遵循以上管理页面布局规范，可以确保所有管理页面具有一致的外观和使用体验，提高用户友好性和开发效率。 