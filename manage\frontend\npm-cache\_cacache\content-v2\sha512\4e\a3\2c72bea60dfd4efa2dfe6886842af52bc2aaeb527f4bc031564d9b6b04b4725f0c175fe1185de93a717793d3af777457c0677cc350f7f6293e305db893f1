{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:22:52.826Z", "name": "@parcel/watcher-freebsd-x64", "versions": {"2.3.0-alpha.0": {"name": "@parcel/watcher-freebsd-x64", "version": "2.3.0-alpha.0", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-c0hu6pELTNVWNMIWjF3qubIaEHfu80mCstKUzcZlNexTMU+auXlw9jeRIJL2xHfjFfoV95Wk9JKcBQsnOX0etw==", "shasum": "8471be6d04f91bf72f7386f2802dae3a20bb072b", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 551922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIArmWSyouYHijq42iGJwicDUJs0+5Zci9NplHsIUnnkRAiEA2nFLixpLgpIaDzRoC52Bo2AtR7lGFnCwl8k5zyCwhtw="}], "size": 177890}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750501455, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-freebsd-x64", "version": "2.3.0-alpha.1", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-TMRVL2HfBnQO1oZh02Nbv/8NNLjbtBF0IpY59kVqL/UbpfR9/6tv28x79+rWLDYxjV5jODfp9s2zEK42dcRRLQ==", "shasum": "51b513a5e93140ad37ee3dc0b789260b7e40fbc4", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 551922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGU2JqDuxbyLFChGm49Y+Xy6Zjgqlisntvk/DsaxygqUAiA+0HlZjTNSY2uiuhpSGDLI0XMt0oS90ncMY30+TmxyaA=="}], "size": 177890}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753398987, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-freebsd-x64", "version": "2.3.0-alpha.2", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-CastJecn/7PT05YIEM7Sg/BaHNELeZzGJWSEpqSDC05kgycaDbaGewwQN3MSqZb9Mp0l0v4SIW/Tp6If1mt87A==", "shasum": "701e2464160a42f9fdd327cd83de6742a157e8a4", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 551922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICY49lMqh3ZA2nzCeHiwgaxYdL7LX95cDPFkViSkZMLCAiBY8pPAJd/V8ly0vHuPx4d7GE2G0HOn5sh3jk0K2l8cng=="}], "size": 177890}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485199255, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-freebsd-x64", "version": "2.3.0-alpha.3", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-HofQAi8a0yePzKv6/h+lOm07ee6Te3VC606jh/4d+57lF1zRzF0trDZgjHKnwrap561c1IgPql6LXAepw/qu5A==", "shasum": "7c4f00464d579e97f2ffb9e9ad44ff0af5e3c462", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 551922, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPmqgtdIxhaKrXSQW5lePnKryJK2Vjhli3IjckB2qHTAiEAm3wwfwPKyccWSnfIpdPaLcZNhK5tc2zhltdZ3fUfa1E="}], "size": 177890}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557599131, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-freebsd-x64", "version": "2.3.0", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-7LftKlaHunueAEiojhCn+Ef2CTXWsLgTl4hq0pkhkTBFI3ssj2bJXmH2L67mKpiAD5dz66JYk4zS66qzdnIOgw==", "shasum": "7a0f4593a887e2752b706aff2dae509aef430cf6", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.3.0.tgz", "fileCount": 4, "unpackedSize": 551082, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQDGwoWZ6NrJayZwhgq8Fu/b4K7u9NYgNSMEOQlcnrTLugIfXl+Bkw8XqtRDpiaGoqBzD5QGFyd7M1efNrTgC2mu5A=="}], "size": 177383}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850807627, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-freebsd-x64", "version": "2.4.0", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-dHTRMIplPDT1M0+BkXjtMN+qLtqq24sLDUhmU+UxxLP2TEY2k8GIoqIJiVrGWGomdWsy5IO27aDV1vWyQ6gfHA==", "shasum": "7310cc86abc27dacd57624bcdba1f0ba092e76df", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.4.0.tgz", "fileCount": 4, "unpackedSize": 543802, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCm91HJ1LFqBP6JnGrc1lwfGZ/QbL4Pbf+lCJp2/XIG2gIhAOV3ipzDy1qEwvQwjrcK0Qabk4BXdClbfi+zvxN47CNE"}], "size": 175855}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360245522, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-freebsd-x64", "version": "2.4.1", "os": ["freebsd"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-TJa3Pex/gX3CWIx/Co8k+ykNdDCLx+TuZj3f3h7eOjgpdKM+Mnix37RYsYU4LHhiYJz3DK5nFCCra81p6g050w==", "shasum": "0d67fef1609f90ba6a8a662bc76a55fc93706fc8", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.4.1.tgz", "fileCount": 4, "unpackedSize": 543802, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/BJAswOuTT9ug09kZi2B8nBBTkqK9HK3qolxlxV5BEwIhAPjzArqofHiNCm4u//4QyA4vm9gpUttNz98HY9W19CRO"}], "size": 175855}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702646643, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-freebsd-x64", "version": "2.4.2-alpha.0", "directories": {}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"integrity": "sha512-r/h8LjC77dkPqcwzvzWC90OkztyAxCBSWE1hmAyYsQ1mQ8KQLQ9y3HfjA4iRRRiB8N2Bdb9TyIMbGh9mjYrKDQ==", "shasum": "ac0888d44a14ac6c95f041d40cc8fa112f14ae0a", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 546074, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLkwLcxFsNRsS9wrI875t72z2eBfeUhZGhZbSk8MD/nwIgSrN0KnrZw28IQuFnNu6yluaCtw0k44oir6FgcmJTBS4="}], "size": 179196}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259251476, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-freebsd-x64", "version": "2.5.0", "directories": {}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"integrity": "sha512-syvfhZzyM8kErg3VF0xpV8dixJ+RzbUaaGaeb7uDuz0D3FK97/mZ5AJQ3XNnDsXX7KkFNtyQyFrXZzQIcN49Tw==", "shasum": "2b77f0c82d19e84ff4c21de6da7f7d096b1a7e82", "tarball": "https://registry.npmmirror.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.0.tgz", "fileCount": 4, "unpackedSize": 512626, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEHMB7Yzt1X6Y1XE+gL/QbURCWK7K+H7PRPx0qTa3B6RAiEAlz5Zhair+AkSHJ0VSpoPLok7nado8sT68wQnDtUIR2w="}], "size": 171881}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701333782, "_source_registry_name": "default"}}, "_source_registry_name": "default"}