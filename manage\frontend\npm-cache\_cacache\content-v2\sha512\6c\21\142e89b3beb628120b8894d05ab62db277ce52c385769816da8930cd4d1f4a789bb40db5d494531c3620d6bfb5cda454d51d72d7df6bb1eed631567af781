{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:37.019Z", "name": "@parcel/watcher-win32-arm64", "versions": {"2.2.0-alpha.1": {"name": "@parcel/watcher-win32-arm64", "version": "2.2.0-alpha.1", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-V2CKLYTnXovjOMcSD0rzXAHXk519CakTAPq2YHeztByBPhWzo2VBdkn+g2tFqPcnNN/oyWqN0N3hsxwpZQAl+A==", "shasum": "1258b62170675d932a763584e98af7057b8cea6a", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 556810, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvcf/19xdlCf8OEBWEfmrDtRMC0zq2k6X3I7aBKgYraQIgG1BZ/CIF08zik9OL+LfI26Vti6AnSPZkrwRNFki+ABk="}], "size": 234903}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738117252, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.2.0", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-z225cPn3aygJsyVUOWwfyW+fY0Tvk7N3XCOl66qUPFxpbuXeZuiuuJemmtm8vxyqa3Ur7peU/qJxrpC64aeI7Q==", "shasum": "4cdbaf8c1097038e838d12f897ac8595b18dfb1f", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.2.0.tgz", "fileCount": 4, "unpackedSize": 557826, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICEVVap9o1L/Ee5PbMAIyjU9rbTuo4tqv+Yeof5lrhqhAiBzB0q8y83wrMVDLwDuAJG1j904qDvkRXaerYEEv5jVkw=="}], "size": 235952}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269828514, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.2.1-alpha.0", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-hoBLJ2daehtZ5/L45R9wsQ5cIDJIY4J7bST261ZvUSPj6vfIHRJRXok3jei8tc5l+wh1maXtP2ADaE6GkXZuRQ==", "shasum": "388cde12c22ee1447c609302848d44a541fb6183", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 559882, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoLiQfLDjrMtk9fvLNWCYwzwEfGmzrTekWnyaGkU+N3gIhAO7yX99VS0M6AAgRi2TBFrNKHRAeXsKLzaGQuZ4P9z7U"}], "size": 236886}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571415502, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.3.0-alpha.0", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-ZUZEaWaqcSA/TDDI7L2k2mueJ6v1+aXaD6gAel0gAP5Cw3wUx+eh34uOMahl/tPSLtMcMIsqbLyR/np5HW6TFQ==", "shasum": "1283129f041280f36fa1a40765501a0be008f045", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 533770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA7A9nGooCdyobAtBglEJYWQu/gmwe+2Wx21rXBnPyJbAiEAsQhzIyJRtu80eKX9HEJEIhxZ6MOt3rh5jPiqc+PdkQo="}], "size": 223265}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750513608, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-win32-arm64", "version": "2.3.0-alpha.1", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-Z0hmqMOD/oB3hErvCdpyD3SoS0XPXHGihxbNkVr2+mq7Si9nKQsYHvlai+OtmDjJXvJ5sDyNDZMIQcfPEzMocg==", "shasum": "9514985c997f2cae816313b6566c1fa597c9ecf0", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 533770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYMNY5nnRNt6m80pH2THAnNYThduU35xUwbZRc9FNHDwIgcsPcUDz3W9vl7BzvQe9SvNcV/BxwDsvVmebK1MYUQjU="}], "size": 223266}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753409930, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-win32-arm64", "version": "2.3.0-alpha.2", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-Yqh2pxhCAOyWrgdsDWerboaYfsfpLHq0FKL+y91VTkeM+8ePUgQU3GPa/9kBCYObidEiYLdTn8LxboOTn3SChg==", "shasum": "81365a6fdae53ab16315574bd539675cfdd0b217", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 533770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD48VTGBmYd7tkoJm8tycdwPMyqnan6ilyISBeWqlh5XgIhAO0PfZJCYlYclE417X4konRJeWOtpVu8ZCiTS/m1AuD0"}], "size": 223263}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485213198, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-win32-arm64", "version": "2.3.0-alpha.3", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-0MIOtTCjyLF7B3B7qLbDojDCUX3uXYwDmV/KJzCjpgX/Krfmpv9P93dZnJx9i9U0DIRK/TDcapTQ6iDHzy6WHw==", "shasum": "f7122dceb4710348228b4046bbc13f50a93572cd", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 533770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICoS9WMhGbI+d+8+7G+1IOWSPD4BQ2tOdQ2M6HJkTBlFAiEA9Jc7m3WyXUjcubaklVVIF3Vx5Fhf164ns/HG6lOgNzc="}], "size": 223263}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557611645, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.3.0", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-35gXCnaz1AqIXpG42evcoP2+sNL62gZTMZne3IackM+6QlfMcJLy3DrjuL6Iks7Czpd3j4xRBzez3ADCj1l7Aw==", "shasum": "59da26a431da946e6c74fa6b0f30b120ea6650b6", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.3.0.tgz", "fileCount": 4, "unpackedSize": 532738, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDC0Adgof1zKAlao8J9zGpV5vf4SbDDlh6Qj7IaC4r0BAiBsdXV0GpGKDG1Vn3uzKtdcFRZoM0Uo7uAXJJkn/yKHXg=="}], "size": 222784}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850820320, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.4.0", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-NOej2lqlq8bQNYhUMnOD0nwvNql8ToQF+1Zhi9ULZoG+XTtJ9hNnCFfyICxoZLXor4bBPTOnzs/aVVoefYnjIg==", "shasum": "2a172fd2fda95fe5389298ca3e70b5a96316162a", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.4.0.tgz", "fileCount": 4, "unpackedSize": 535298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBG2CMAkv1YkrXaOIIiiOLO5r1dpblOtGRasERuy8JnSAiEApeE7w/8Qenqsu055wpM9A8Ibs/ZG/zByogNGkoyYbeo="}], "size": 224296}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360257315, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-win32-arm64", "version": "2.4.1", "os": ["win32"], "cpu": ["arm64"], "directories": {}, "dist": {"integrity": "sha512-Uq2BPp5GWhrq/lcuItCHoqxjULU1QYEcyjSO5jqqOK8RNFDBQnenMMx4gAl3v8GiWa59E9+uDM7yZ6LxwUIfRg==", "shasum": "eb4deef37e80f0b5e2f215dd6d7a6d40a85f8adc", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.4.1.tgz", "fileCount": 4, "unpackedSize": 538882, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdQyHVENlWD/bjBfT2iWV2QUcro5CX/cOEeZ2WPJeT7AIgXq+M5ZMR8jScvUp1BPWXaZTi7v1gIJUVjHyPuIDwEG4="}], "size": 225466}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702692683, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.4.2-alpha.0", "directories": {}, "os": ["win32"], "cpu": ["arm64"], "dist": {"integrity": "sha512-9mB3SUAbgeGYPLA5kjdXKoTq9oP9asJ84pn6y+7SyOEgMogV5ECRZkcgul1JVSjg0BEMFg724BgUPkCZsawqAA==", "shasum": "0012694ce5302b245b0efbb4f9748bfc348cc99b", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 537866, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvTQmY4s4Uc1g7z2MgP5SL1WT0telyQnVR8Qxzre1rQAIhAN82y0WmrdcaZX8+OQTMxNJoPBN0IKj3UcQcQbhQZuPy"}], "size": 231832}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259267324, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-win32-arm64", "version": "2.5.0", "directories": {}, "os": ["win32"], "cpu": ["arm64"], "dist": {"integrity": "sha512-twtft1d+JRNkM5YbmexfcH/N4znDtjgysFaV9zvZmmJezQsKpkfLYJ+JFV3uygugK6AtIM2oADPkB2AdhBrNig==", "shasum": "87cdb16e0783e770197e52fb1dc027bb0c847154", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.0.tgz", "fileCount": 4, "unpackedSize": 536834, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnAdVxjWOti3Rb7qCDjS8GUgGvJqGNONv6VC3LA+9iGwIgZ/cnzHdITOK76+5lWy8uoxORzBQJE55vL5dnjqnARWs="}], "size": 231189}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701354465, "_source_registry_name": "default"}}, "_source_registry_name": "default"}