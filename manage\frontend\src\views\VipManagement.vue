<template>
  <div class="vip-management">
    <!-- 标题容器与返回首页按钮 -->
    <div class="page-header">
      <h2>VIP用户管理</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          @click="backToHome">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <el-card class="vip-card">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="VIP用户列表" name="vipList">
          <!-- 搜索栏 -->
          <el-form :inline="true" :model="searchForm" class="search-form">
            <div class="search-form-item-group">
              <el-form-item label="微信OpenID">
                <el-input v-model="searchForm.openid" placeholder="请输入微信OpenID" clearable style="width: 200px"></el-input>
              </el-form-item>
              <el-form-item label="用户昵称">
                <el-input v-model="searchForm.nickname" placeholder="请输入昵称" clearable style="width: 180px"></el-input>
              </el-form-item>
              <el-form-item label="是否VIP">
                <el-select v-model="searchForm.isVip" placeholder="请选择" clearable style="width: 120px">
                  <el-option label="是" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </div>
            
            <!-- 操作按钮 -->
            <div class="search-form-buttons">
              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
                <el-button 
                  type="success" 
                  icon="Plus" 
                  @click="handleAddVip">
                  添加VIP用户
                </el-button>
                <el-button
                  type="info"
                  icon="Download"
                  @click="handleExport">
                  导出数据
                </el-button>
              </el-form-item>
            </div>
          </el-form>

          <!-- VIP用户表格 -->
          <el-table
            :data="vipList"
            border
            stripe
            v-loading="loading"
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="openid" label="微信OpenID" width="220"></el-table-column>
            <el-table-column prop="nickname" label="昵称" width="150"></el-table-column>
            <el-table-column prop="gender" label="性别" width="80" align="center">
              <template #default="scope">
                {{ scope.row.gender === 1 ? '男' : scope.row.gender === 2 ? '女' : '未知' }}
              </template>
            </el-table-column>
            <el-table-column prop="isActive" label="是否有效" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="small">
                  {{ scope.row.isActive ? '有效' : '无效' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isVip" label="是否VIP" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.isVip ? 'success' : 'info'" size="small">
                  {{ scope.row.isVip ? 'VIP' : '普通用户' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="vipExpireTime" label="VIP到期时间" width="180"></el-table-column>
            <el-table-column prop="lastLoginTime" label="最后登录时间" width="180"></el-table-column>
            <el-table-column label="操作" fixed="right" width="200" align="center">
              <template #default="scope">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="handleEdit(scope.row)">
                  编辑
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="handleDelete(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pagination.currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pagination.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              background>
            </el-pagination>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="充值流水记录" name="rechargeRecords">
          <!-- 充值记录搜索栏 -->
          <el-form :inline="true" :model="rechargeSearchForm" class="search-form">
            <div class="search-form-item-group">
              <el-form-item label="用户昵称">
                <el-input v-model="rechargeSearchForm.nickname" placeholder="请输入昵称" clearable style="width: 180px"></el-input>
              </el-form-item>
              <el-form-item label="充值时间">
                <el-date-picker
                  v-model="rechargeSearchForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 320px">
                </el-date-picker>
              </el-form-item>
            </div>
            
            <!-- 操作按钮 -->
            <div class="search-form-buttons">
              <el-form-item>
                <el-button type="primary" @click="handleRechargeSearch">搜索</el-button>
                <el-button @click="resetRechargeSearch">重置</el-button>
                <el-button type="success" icon="Plus" @click="handleAddRecharge">添加充值记录</el-button>
                <el-button type="info" icon="Download" @click="handleExportRecharge">导出数据</el-button>
              </el-form-item>
            </div>
          </el-form>
          
          <!-- 充值记录表格 -->
          <el-table
            :data="rechargeList"
            border
            stripe
            v-loading="rechargeLoading"
            style="width: 100%">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="vipId" label="VIP用户ID" width="100"></el-table-column>
            <el-table-column prop="nickname" label="用户昵称" width="150"></el-table-column>
            <el-table-column prop="amount" label="充值金额(元)" width="150" align="center">
              <template #default="scope">
                <span style="color: #f56c6c; font-weight: bold;">¥ {{ scope.row.amount.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="购买时长(月)" width="120" align="center"></el-table-column>
            <el-table-column prop="paymentMethod" label="支付方式" width="120" align="center">
              <template #default="scope">
                {{ paymentMethodMap[scope.row.paymentMethod] || '未知' }}
              </template>
            </el-table-column>
            <el-table-column prop="transactionId" label="交易号" width="200"></el-table-column>
            <el-table-column prop="rechargeTime" label="充值时间" width="180"></el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'success' ? 'success' : scope.row.status === 'pending' ? 'warning' : 'danger'" size="small">
                  {{ statusMap[scope.row.status] || '未知' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" width="200"></el-table-column>
            <el-table-column label="操作" fixed="right" width="150" align="center">
              <template #default="scope">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="handleEditRecharge(scope.row)">
                  编辑
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="handleDeleteRecharge(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 充值记录分页组件 -->
          <div class="pagination-container">
            <el-pagination
              @size-change="handleRechargeSizeChange"
              @current-change="handleRechargeCurrentChange"
              :current-page="rechargePagination.currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="rechargePagination.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="rechargePagination.total"
              background>
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- VIP用户对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加VIP用户' : '编辑VIP用户'"
      v-model="dialogVisible"
      width="50%"
      :close-on-click-modal="false"
      :before-close="handleDialogClose">
      <el-form
        :model="vipForm"
        :rules="vipRules"
        ref="vipFormRef"
        label-width="120px"
        label-position="right">
        <el-form-item label="微信OpenID" prop="openid">
          <el-input v-model="vipForm.openid" placeholder="请输入微信OpenID"></el-input>
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="vipForm.nickname" placeholder="请输入昵称"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="vipForm.gender" placeholder="请选择性别">
            <el-option label="男" :value="1"></el-option>
            <el-option label="女" :value="2"></el-option>
            <el-option label="未知" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否有效" prop="isActive">
          <el-switch v-model="vipForm.isActive"></el-switch>
        </el-form-item>
        <el-form-item label="是否VIP" prop="isVip">
          <el-switch v-model="vipForm.isVip"></el-switch>
        </el-form-item>
        <el-form-item label="VIP到期时间" prop="vipExpireTime">
          <el-date-picker
            v-model="vipForm.vipExpireTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitVipForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 充值记录对话框 -->
    <el-dialog
      :title="rechargeDialogType === 'add' ? '添加充值记录' : '编辑充值记录'"
      v-model="rechargeDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      :before-close="handleRechargeDialogClose">
      <el-form
        :model="rechargeForm"
        :rules="rechargeRules"
        ref="rechargeFormRef"
        label-width="120px"
        label-position="right">
        <el-form-item label="VIP用户" prop="vipId">
          <el-select 
            v-model="rechargeForm.vipId" 
            placeholder="请选择VIP用户"
            filterable
            remote
            :remote-method="remoteSearchVip"
            :loading="vipSelectLoading">
            <el-option
              v-for="item in vipOptions"
              :key="item.id"
              :label="item.nickname + ' (' + item.openid + ')'"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="充值金额(元)" prop="amount">
          <el-input-number 
            v-model="rechargeForm.amount" 
            :precision="2" 
            :step="10" 
            :min="0.01">
          </el-input-number>
        </el-form-item>
        <el-form-item label="购买时长(月)" prop="duration">
          <el-input-number 
            v-model="rechargeForm.duration" 
            :precision="0" 
            :step="1" 
            :min="1">
          </el-input-number>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="rechargeForm.paymentMethod" placeholder="请选择支付方式">
            <el-option label="微信支付" value="wechat"></el-option>
            <el-option label="支付宝" value="alipay"></el-option>
            <el-option label="银行卡" value="bank"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易号" prop="transactionId">
          <el-input v-model="rechargeForm.transactionId" placeholder="请输入交易号"></el-input>
        </el-form-item>
        <el-form-item label="充值时间" prop="rechargeTime">
          <el-date-picker
            v-model="rechargeForm.rechargeTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="rechargeForm.status" placeholder="请选择状态">
            <el-option label="成功" value="success"></el-option>
            <el-option label="处理中" value="pending"></el-option>
            <el-option label="失败" value="failed"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="rechargeForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rechargeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRechargeForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import { 
  getVipList, 
  getVipDetail,
  addVip, 
  updateVip, 
  deleteVip,
  getRechargeRecords,
  addRechargeRecord,
  updateRechargeRecord,
  deleteRechargeRecord,
  exportVipData,
  exportRechargeData
} from '@/api/vip'

export default {
  name: 'VipManagement',
  
  setup() {
    const router = useRouter()
    
    // VIP用户列表相关数据
    const vipList = ref([])
    const loading = ref(false)
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })
    const selectedVip = ref([])
    
    // 搜索表单
    const searchForm = reactive({
      openid: '',
      nickname: '',
      isVip: ''
    })
    
    // 标签页
    const activeTab = ref('vipList')
    
    // VIP表单相关
    const vipFormRef = ref(null)
    const dialogVisible = ref(false)
    const dialogType = ref('add') // 'add' 或 'edit'
    const vipForm = reactive({
      id: '',
      openid: '',
      nickname: '',
      gender: 0,
      isActive: true,
      isVip: true,
      vipExpireTime: ''
    })
    
    // 表单校验规则
    const vipRules = {
      openid: [
        { required: true, message: '请输入微信OpenID', trigger: 'blur' },
        { min: 10, max: 50, message: 'OpenID长度应在10到50个字符之间', trigger: 'blur' }
      ],
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' },
        { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
      ],
      gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      vipExpireTime: [
        { required: true, message: '请选择VIP到期时间', trigger: 'change' }
      ]
    }
    
    // 充值记录相关数据
    const rechargeList = ref([])
    const rechargeLoading = ref(false)
    const rechargePagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })
    
    // 充值记录搜索表单
    const rechargeSearchForm = reactive({
      nickname: '',
      dateRange: []
    })
    
    // 充值记录表单相关
    const rechargeFormRef = ref(null)
    const rechargeDialogVisible = ref(false)
    const rechargeDialogType = ref('add') // 'add' 或 'edit'
    const rechargeForm = reactive({
      id: '',
      vipId: '',
      amount: 0,
      duration: 1,
      paymentMethod: 'wechat',
      transactionId: '',
      rechargeTime: '',
      status: 'success',
      remark: ''
    })
    
    // 表单校验规则
    const rechargeRules = {
      vipId: [
        { required: true, message: '请选择VIP用户', trigger: 'change' }
      ],
      amount: [
        { required: true, message: '请输入充值金额', trigger: 'blur' },
        { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
      ],
      duration: [
        { required: true, message: '请输入购买时长', trigger: 'blur' },
        { type: 'number', min: 1, message: '时长必须大于0', trigger: 'blur' }
      ],
      paymentMethod: [
        { required: true, message: '请选择支付方式', trigger: 'change' }
      ],
      rechargeTime: [
        { required: true, message: '请选择充值时间', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }
    
    // VIP用户选择相关
    const vipOptions = ref([])
    const vipSelectLoading = ref(false)
    
    // 状态映射
    const statusMap = {
      'success': '成功',
      'pending': '处理中',
      'failed': '失败'
    }
    
    // 支付方式映射
    const paymentMethodMap = {
      'wechat': '微信支付',
      'alipay': '支付宝',
      'bank': '银行卡',
      'other': '其他'
    }
    
    // 返回首页
    const backToHome = () => {
      router.push('/')
    }
    
    // 获取VIP列表数据
    const fetchVipList = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.currentPage,
          pageSize: pagination.pageSize,
          ...searchForm
        }
        const res = await getVipList(params)
        vipList.value = res.data.items
        pagination.total = res.data.total
      } catch (error) {
        ElMessage.error('获取VIP用户列表失败')
        console.error(error)
      } finally {
        loading.value = false
      }
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchVipList()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      handleSearch()
    }
    
    // 分页大小变化
    const handleSizeChange = (newSize) => {
      pagination.pageSize = newSize
      fetchVipList()
    }
    
    // 当前页变化
    const handleCurrentChange = (newPage) => {
      pagination.currentPage = newPage
      fetchVipList()
    }
    
    // 表格选择变化
    const handleSelectionChange = (val) => {
      selectedVip.value = val
    }
    
    // 添加VIP
    const handleAddVip = () => {
      dialogType.value = 'add'
      resetVipForm()
      dialogVisible.value = true
    }
    
    // 编辑VIP
    const handleEdit = async (row) => {
      dialogType.value = 'edit'
      try {
        const res = await getVipDetail(row.id)
        Object.assign(vipForm, res.data)
        dialogVisible.value = true
      } catch (error) {
        ElMessage.error('获取VIP用户详情失败')
        console.error(error)
      }
    }
    
    // 删除VIP
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除用户 ${row.nickname} 吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteVip(row.id)
          ElMessage.success('删除成功')
          fetchVipList()
        } catch (error) {
          ElMessage.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    }
    
    // 导出数据
    const handleExport = async () => {
      try {
        const params = { ...searchForm }
        const res = await exportVipData(params)
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = 'vip_users.xlsx'
        link.click()
        URL.revokeObjectURL(link.href)
      } catch (error) {
        ElMessage.error('导出失败')
        console.error(error)
      }
    }
    
    // 重置VIP表单
    const resetVipForm = () => {
      Object.assign(vipForm, {
        id: '',
        openid: '',
        nickname: '',
        gender: 0,
        isActive: true,
        isVip: true,
        vipExpireTime: ''
      })
      if (vipFormRef.value) {
        vipFormRef.value.resetFields()
      }
    }
    
    // 关闭对话框
    const handleDialogClose = (done) => {
      resetVipForm()
      done()
    }
    
    // 提交VIP表单
    const submitVipForm = async () => {
      if (!vipFormRef.value) return
      
      await vipFormRef.value.validate(async (valid) => {
        if (!valid) return
        
        try {
          if (dialogType.value === 'add') {
            await addVip(vipForm)
            ElMessage.success('添加成功')
          } else {
            await updateVip(vipForm.id, vipForm)
            ElMessage.success('更新成功')
          }
          dialogVisible.value = false
          fetchVipList()
        } catch (error) {
          ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败')
          console.error(error)
        }
      })
    }
    
    // 标签页切换
    const handleTabClick = (tab) => {
      if (tab.props.name === 'rechargeRecords') {
        fetchRechargeList()
      }
    }
    
    // 获取充值记录列表
    const fetchRechargeList = async () => {
      try {
        rechargeLoading.value = true
        const params = {
          page: rechargePagination.currentPage,
          pageSize: rechargePagination.pageSize,
          nickname: rechargeSearchForm.nickname,
          startDate: rechargeSearchForm.dateRange[0] || '',
          endDate: rechargeSearchForm.dateRange[1] || ''
        }
        const res = await getRechargeRecords(params)
        rechargeList.value = res.data.items
        rechargePagination.total = res.data.total
      } catch (error) {
        ElMessage.error('获取充值记录列表失败')
        console.error(error)
      } finally {
        rechargeLoading.value = false
      }
    }
    
    // 充值记录搜索
    const handleRechargeSearch = () => {
      rechargePagination.currentPage = 1
      fetchRechargeList()
    }
    
    // 重置充值记录搜索
    const resetRechargeSearch = () => {
      rechargeSearchForm.nickname = ''
      rechargeSearchForm.dateRange = []
      handleRechargeSearch()
    }
    
    // 充值记录分页大小变化
    const handleRechargeSizeChange = (newSize) => {
      rechargePagination.pageSize = newSize
      fetchRechargeList()
    }
    
    // 充值记录当前页变化
    const handleRechargeCurrentChange = (newPage) => {
      rechargePagination.currentPage = newPage
      fetchRechargeList()
    }
    
    // 添加充值记录
    const handleAddRecharge = () => {
      rechargeDialogType.value = 'add'
      resetRechargeForm()
      rechargeForm.rechargeTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      rechargeDialogVisible.value = true
    }
    
    // 编辑充值记录
    const handleEditRecharge = (row) => {
      rechargeDialogType.value = 'edit'
      Object.assign(rechargeForm, row)
      rechargeDialogVisible.value = true
    }
    
    // 删除充值记录
    const handleDeleteRecharge = (row) => {
      ElMessageBox.confirm(
        `确定要删除该充值记录吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteRechargeRecord(row.id)
          ElMessage.success('删除成功')
          fetchRechargeList()
        } catch (error) {
          ElMessage.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    }
    
    // 导出充值记录
    const handleExportRecharge = async () => {
      try {
        const params = {
          nickname: rechargeSearchForm.nickname,
          startDate: rechargeSearchForm.dateRange[0] || '',
          endDate: rechargeSearchForm.dateRange[1] || ''
        }
        const res = await exportRechargeData(params)
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = 'recharge_records.xlsx'
        link.click()
        URL.revokeObjectURL(link.href)
      } catch (error) {
        ElMessage.error('导出失败')
        console.error(error)
      }
    }
    
    // 重置充值记录表单
    const resetRechargeForm = () => {
      Object.assign(rechargeForm, {
        id: '',
        vipId: '',
        amount: 0,
        duration: 1,
        paymentMethod: 'wechat',
        transactionId: '',
        rechargeTime: '',
        status: 'success',
        remark: ''
      })
      if (rechargeFormRef.value) {
        rechargeFormRef.value.resetFields()
      }
    }
    
    // 关闭充值记录对话框
    const handleRechargeDialogClose = (done) => {
      resetRechargeForm()
      done()
    }
    
    // 提交充值记录表单
    const submitRechargeForm = async () => {
      if (!rechargeFormRef.value) return
      
      await rechargeFormRef.value.validate(async (valid) => {
        if (!valid) return
        
        try {
          if (rechargeDialogType.value === 'add') {
            await addRechargeRecord(rechargeForm)
            ElMessage.success('添加成功')
          } else {
            await updateRechargeRecord(rechargeForm.id, rechargeForm)
            ElMessage.success('更新成功')
          }
          rechargeDialogVisible.value = false
          fetchRechargeList()
        } catch (error) {
          ElMessage.error(rechargeDialogType.value === 'add' ? '添加失败' : '更新失败')
          console.error(error)
        }
      })
    }
    
    // 远程搜索VIP用户
    const remoteSearchVip = async (query) => {
      if (query) {
        vipSelectLoading.value = true
        try {
          const res = await getVipList({
            search: query,
            page: 1,
            pageSize: 10
          })
          vipOptions.value = res.data.items
        } catch (error) {
          console.error('搜索VIP用户失败', error)
        } finally {
          vipSelectLoading.value = false
        }
      } else {
        vipOptions.value = []
      }
    }
    
    // 页面加载时获取数据
    onMounted(() => {
      fetchVipList()
    })
    
    // 监听标签页变化
    watch(activeTab, (newVal) => {
      if (newVal === 'rechargeRecords') {
        fetchRechargeList()
      }
    })
    
    return {
      // VIP列表相关
      vipList,
      loading,
      pagination,
      searchForm,
      activeTab,
      vipFormRef,
      dialogVisible,
      dialogType,
      vipForm,
      vipRules,
      selectedVip,
      
      // 充值记录相关
      rechargeList,
      rechargeLoading,
      rechargePagination,
      rechargeSearchForm,
      rechargeFormRef,
      rechargeDialogVisible,
      rechargeDialogType,
      rechargeForm,
      rechargeRules,
      
      // VIP选择相关
      vipOptions,
      vipSelectLoading,
      
      // 映射
      statusMap,
      paymentMethodMap,
      
      // 图标
      Back,
      
      // 方法
      backToHome,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      handleAddVip,
      handleEdit,
      handleDelete,
      handleExport,
      handleDialogClose,
      submitVipForm,
      handleTabClick,
      
      // 充值记录方法
      handleRechargeSearch,
      resetRechargeSearch,
      handleRechargeSizeChange,
      handleRechargeCurrentChange,
      handleAddRecharge,
      handleEditRecharge,
      handleDeleteRecharge,
      handleExportRecharge,
      handleRechargeDialogClose,
      submitRechargeForm,
      remoteSearchVip
    }
  }
}
</script>

<style lang="scss" scoped>
.vip-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  
  h2 {
    margin: 0;
    font-size: 24px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.vip-card {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

/* 搜索表单样式优化 */
.search-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.search-form-item-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.search-form-item-group:last-child {
  margin-bottom: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
  display: flex;
  align-items: center;
}

.search-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
}

.search-form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
}

/* 表格和分页样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  margin-top: 16px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table--border) {
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 