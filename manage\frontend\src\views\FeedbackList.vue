<template>
  <div class="feedback-list-container">
    <div class="page-header">
      <h2>用户反馈管理</h2>
      <div class="header-actions">
        <el-button @click="router.push('/')" type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <el-card class="feedback-table">
      <el-table :data="feedbackList" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="type_display" label="类型" width="120" />
        <el-table-column prop="content" label="反馈内容" show-overflow-tooltip />
        <el-table-column prop="device_info" label="设备信息" width="180" show-overflow-tooltip />
        <!-- <el-table-column prop="contact" label="联系方式" width="150" show-overflow-tooltip /> -->
        <el-table-column prop="status_display" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status_display }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button
                type="primary"
                link
                @click="handleViewDetail(row)"
              >
                详情
              </el-button>
              <el-button
                v-if="row.status !== 'completed'"
                type="success"
                link
                @click="handleProcess(row)"
              >
                处理
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 添加分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="10"
          :total="total"
          :pager-count="7"
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          background
        />
      </div>
    </el-card>

    <!-- 处理对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="处理反馈"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="replyForm" label-width="80px">
        <el-form-item label="反馈内容" class="feedback-form-item">
          <div class="feedback-info">
            <div class="feedback-type">
              <div class="type-info">
                <el-tag size="small" :type="currentFeedback?.type === 'bug' ? 'danger' : 'warning'">
                  {{ currentFeedback?.type_display }}
                </el-tag>
                <span class="feedback-id">#{{ currentFeedback?.id }}</span>
              </div>
              <span class="feedback-time">{{ formatDate(currentFeedback?.created_at) }}</span>
            </div>
            <div class="feedback-content">{{ currentFeedback?.content }}</div>
            <div v-if="currentFeedback?.device_info" class="feedback-device">
              <el-icon><Monitor /></el-icon>
              <span>{{ currentFeedback?.device_info }}</span>
            </div>
            <div v-if="currentFeedback?.contact" class="feedback-contact">
              <el-icon><Message /></el-icon>
              <span>{{ currentFeedback?.contact }}</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="处理记录" required>
          <el-input
            v-model="replyForm.reply"
            type="textarea"
            :rows="4"
            placeholder="请输入问题处理情况"
            resize="none"
          />
          <div class="form-tip">处理完成后，该反馈将自动标记为"已处理"状态</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReply" :loading="submitting">
            确认处理
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="反馈详情"
      width="600px"
    >
      <div v-if="currentFeedback" class="feedback-detail">
        <div class="detail-item">
          <span class="label">反馈类型：</span>
          <el-tag size="small" :type="currentFeedback.type === 'bug' ? 'danger' : 'warning'">
            {{ currentFeedback.type_display }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">反馈内容：</span>
          <div class="content">{{ currentFeedback.content }}</div>
        </div>
        <div class="detail-item">
          <span class="label">设备信息：</span>
          <div class="content">{{ currentFeedback.device_info || '无' }}</div>
        </div>
        <div class="detail-item">
          <span class="label">联系方式：</span>
          <span>{{ currentFeedback.contact || '无' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">处理状态：</span>
          <el-tag :type="getStatusType(currentFeedback.status)">
            {{ currentFeedback.status_display }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">提交时间：</span>
          <span>{{ formatDate(currentFeedback.created_at) }}</span>
        </div>
        <div class="detail-item" v-if="currentFeedback.reply">
          <span class="label">处理记录：</span>
          <div class="content process-record">{{ currentFeedback.reply }}</div>
        </div>
        <div class="detail-item" v-if="currentFeedback.updated_at !== currentFeedback.created_at">
          <span class="label">处理时间：</span>
          <span>{{ formatDate(currentFeedback.updated_at) }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Monitor, Message } from '@element-plus/icons-vue'
import { feedbackAPI } from '@/api'

const router = useRouter()
const loading = ref(false)
const feedbackList = ref([])
const dialogVisible = ref(false)
const detailVisible = ref(false)
const submitting = ref(false)
const currentFeedback = ref(null)
const replyForm = ref({
  reply: ''
})

// 添加分页相关的响应式变量
const currentPage = ref(1)
const total = ref(0)

/**
 * 获取状态对应的类型
 * @param {string} status - 状态值
 * @returns {string} Element Plus的Tag类型
 */
const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'info',
    completed: 'success'
  }
  return types[status] || 'info'
}

/**
 * 格式化日期
 * @param {string} dateStr - ISO格式的日期字符串
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

/**
 * 获取反馈列表
 */
const getFeedbackList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: 10
    }
    const response = await feedbackAPI.getFeedbackList(params)
    feedbackList.value = response.data.results
    total.value = response.data.count
  } catch (error) {
    console.error('Failed to load feedback list:', error)
    ElMessage.error('获取反馈列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理反馈按钮点击
 * @param {Object} feedback - 反馈数据
 */
const handleProcess = (feedback) => {
  currentFeedback.value = feedback
  replyForm.value.reply = feedback.reply || ''
  dialogVisible.value = true
}

/**
 * 查看详情
 * @param {Object} feedback - 反馈数据
 */
const handleViewDetail = (feedback) => {
  currentFeedback.value = feedback
  detailVisible.value = true
}

/**
 * 提交处理结果
 */
const submitReply = async () => {
  if (!replyForm.value.reply.trim()) {
    ElMessage.warning('请输入处理记录')
    return
  }

  submitting.value = true
  try {
    await feedbackAPI.updateFeedbackReply(currentFeedback.value.id, replyForm.value)
    ElMessage.success('处理成功')
    dialogVisible.value = false
    getFeedbackList() // 刷新列表
  } catch (error) {
    ElMessage.error('处理失败')
  } finally {
    submitting.value = false
  }
}

/**
 * 处理页码变化
 * @param {number} val - 新的页码
 */
const handleCurrentChange = (val) => {
  currentPage.value = val
  getFeedbackList()
}

/**
 * 处理删除反馈
 * @param {Object} feedback - 要删除的反馈数据
 */
const handleDelete = (feedback) => {
  ElMessageBox.confirm(
    '确定要删除这条反馈吗？删除后将无法恢复。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await feedbackAPI.deleteFeedback(feedback.id)
        ElMessage.success('删除成功')
        // 如果当前页只有一条数据，且不是第一页，则跳转到上一页
        if (feedbackList.value.length === 1 && currentPage.value > 1) {
          currentPage.value--
        }
        getFeedbackList()
      } catch (error) {
        console.error('Failed to delete feedback:', error)
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户点击取消，不做任何操作
    })
}

onMounted(() => {
  getFeedbackList()
})
</script>

<style scoped>
.feedback-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.feedback-table {
  margin-top: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 6px;
}

.feedback-info {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  overflow: hidden;
}

.feedback-type {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feedback-time {
  color: #909399;
  font-size: 13px;
}

.feedback-content {
  padding: 16px;
  line-height: 1.6;
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
  background-color: #fff;
  min-height: 60px;
}

.feedback-device,
.feedback-contact {
  padding: 8px 16px;
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 6px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
}

.feedback-device .el-icon,
.feedback-contact .el-icon {
  font-size: 14px;
  color: #909399;
}

.feedback-detail {
  padding: 20px;
}

.detail-item {
  margin-bottom: 16px;
  line-height: 1.5;
}

.detail-item .label {
  color: #606266;
  margin-right: 8px;
  display: inline-block;
  width: 84px;
}

.detail-item .content {
  margin-top: 8px;
  margin-left: 84px;
  white-space: pre-wrap;
  word-break: break-all;
  color: #303133;
  line-height: 1.6;
}

.process-record {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

:deep(.el-button.el-button--primary.is-link),
:deep(.el-button.el-button--success.is-link),
:deep(.el-button.el-button--danger.is-link) {
  height: 28px;
  padding: 0 8px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  margin-top: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.feedback-form-item :deep(.el-form-item__label) {
  padding-top: 12px;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feedback-id {
  color: #909399;
  font-size: 13px;
}

.form-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}
</style> 