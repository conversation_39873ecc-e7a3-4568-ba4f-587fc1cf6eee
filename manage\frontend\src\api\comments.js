import request from '@/utils/request'

// 获取评论列表
export function getComments(params) {
  return request({
    url: '/admin/comments',
    method: 'get',
    params
  })
}

// 删除评论
export function deleteComment(id) {
  return request({
    url: `/admin/comments/${id}/delete`,
    method: 'delete'
  })
}

// 获取举报列表
export function getReports(params) {
  return request({
    url: '/admin/reports',
    method: 'get',
    params
  })
}

// 处理举报
export function handleReportAction(id, data) {
  return request({
    url: `/admin/reports/${id}/handle/`,
    method: 'post',
    data
  })
}

// 获取赛车等级列表
export function getCarLevels() {
  return request({
    url: '/cars/levels',
    method: 'get'
  })
}

// 获取敏感词列表
export function getSensitiveWords(params) {
  return request({
    url: '/sensitive/list/',
    method: 'get',
    params
  })
}

// 添加敏感词
export function addSensitiveWord(data) {
  return request({
    url: '/sensitive/add/',
    method: 'post',
    data
  })
}

// 批量添加敏感词
export function batchAddSensitiveWords(formData) {
  return request({
    url: '/sensitive/batch_add/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除敏感词
export function deleteSensitiveWord(id) {
  return request({
    url: `/sensitive/${id}/delete/`,
    method: 'delete'
  })
}

// 启用/禁用敏感词
export function enableSensitiveWord(id) {
  return request({
    url: `/sensitive/${id}/enable/`,
    method: 'post'
  })
}

// 获取导出链接
export function getSensitiveExportUrl(params) {
  const queryString = new URLSearchParams(params).toString()
  // 使用与其他 API 请求相同的基础 URL
  const baseURL = request.defaults.baseURL || ''
  return `${baseURL}/sensitive/template/${queryString ? `?${queryString}` : ''}`
}

// 导出敏感词
export function exportSensitiveWords(params) {
  return request({
    url: '/sensitive/template/',
    method: 'get',
    params,
    responseType: 'blob'  // 设置响应类型为 blob
  })
} 