import axios from 'axios'
import { API_URL } from '@/config'

const api = axios.create({
  baseURL: API_URL,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: false
})

// 响应拦截器
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response) {
      // 服务器返回错误信息
      console.error('API Error:', error.response.data)
    } else if (error.request) {
      // 请求未收到响应
      console.error('Network Error:', error.request)
    } else {
      // 请求配置出错
      console.error('Request Error:', error.message)
    }
    return Promise.reject(error)
  }
)

export const carAPI = {
  // 获取赛车列表
  getCarList(params) {
    return api.get('/cars/', { params })
  },

  // 获取单个赛车详情
  getCarDetail(id) {
    return api.get(`/cars/${id}/`)
  },

  // 创建赛车
  createCar(data) {
    return api.post('/cars/', data)
  },

  // 更新赛车
  updateCar(id, data) {
    return api.put(`/cars/${id}/`, data)
  },

  // 删除赛车
  deleteCar(id) {
    return api.delete(`/cars/${id}/`)
  },

  // 导入Excel
  importExcel(file) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/cars/import_excel/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出Excel
  exportExcel() {
    return api.get('/cars/export_excel/', {
      responseType: 'blob'
    })
  }
}

export const feedbackAPI = {
  /**
   * 获取反馈列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.page_size - 每页条数
   * @returns {Promise} 返回反馈列表数据
   */
  getFeedbackList(params) {
    return api.get('/feedback/', { params })
  },

  /**
   * 更新反馈回复
   * @param {number} id - 反馈ID
   * @param {object} data - 回复数据
   * @returns {Promise} 返回更新后的反馈数据
   */
  updateFeedbackReply(id, data) {
    return api.post(`/feedback/${id}/reply/`, data)
  },

  /**
   * 删除反馈
   * @param {number} id - 反馈ID
   * @returns {Promise} 返回删除操作的Promise
   */
  deleteFeedback(id) {
    return api.delete(`/feedback/${id}/`)
  }
}

export default api
