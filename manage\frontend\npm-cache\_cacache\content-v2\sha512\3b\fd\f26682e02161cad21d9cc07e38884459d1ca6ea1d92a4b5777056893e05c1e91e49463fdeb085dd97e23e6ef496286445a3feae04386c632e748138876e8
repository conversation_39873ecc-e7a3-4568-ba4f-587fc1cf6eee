{"dist-tags": {"latest": "6.5.8"}, "modified": "2024-11-22T08:23:04.313Z", "name": "@codemirror/search", "versions": {"0.19.5": {"name": "@codemirror/search", "version": "0.19.5", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-9kbtCBpMDlzcj7AptMRBx9BZpC5wz+/tG8nIe4vdpOszP08ZY2AcxN5nlhCoUSZu+pd0b6fYiwjLXOf000rRpw==", "shasum": "cae88292a6b4a6d6e6a8b6218fe62355cf7f6055", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.5.tgz", "fileCount": 8, "unpackedSize": 100052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhu0sPCRA9TVsSAnZWagAAnxwQAIbecn93348BJkUeECjH\nZUOgMmirkXFyv70Hz+q9jMmesNZHYEWc/g8mMOu++Wtzm8R6RYO2kFXIY59n\nemDhII6FCCe1mfn6Z9urMyR/RFjpG51geQfWbb4lOCJmAq2IfxZmRAQ84S5N\nCSTB4jhIO69Cr+Wd6R5GU9ux/ohPw6+sdEe7Dhcz05vV0/bXB3V29ee9Zq7y\nD8wK/nvZxAeiTQwx6PTdWID22xzDTyTImHCxf7ePSmer4pLURsm815dajBwz\nJaeFcOMRBYdGrVftg8DtXeWzxiG5Sgwle2KtZiMgTodi8lcEscnVg9uSmeUB\n+IEfYRfoJ7ZgP7m2DNLR+7cX56SG5dsmQj6UxqL4G5FAeRzf0RDo2kfEuUz2\n8RZMuykRopRTKL7F7stni2CH2Tv+HHBgWEKu+FbQ97/Cw3cZOAf2nsifIaVt\nq0me5eX9ST2momwRoxoJ5jn9Qf4+FoTG0K4ZM6tHk+50qBokgXTqFN7j3miy\n2kwZvS+IuoczG5myeCtKYYe+iR8AMKE8luLWErhrZl9U5q9M1iUxZ3PZwbfR\n45lYhErngem/CgWyHwT+Q5Vrw+ahTsVWDH1+PYduHTYAKMXo4DxVJwjcxnRW\nOzaLTNPaAab1RpbE0ruhoIg7DjfIbtzzt60aURb0YzLc6a7RhCFa4gjJEJD+\nDTon\r\n=8T7W\r\n-----END PGP SIGNATURE-----\r\n", "size": 24827, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/search", "version": "0.19.4", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-<PERSON>ogbi/Ep7suM3Q6eCeRdaPBHyJtuFnYo287H9uXwrbS7nCCoqYBy272tUt0OMt3GhJ3yK0/g+XMDveb+HVKRtA==", "shasum": "89ca7750a0aae0245b8453abe8b15f441bcc2fb7", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.4.tgz", "fileCount": 8, "unpackedSize": 92800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqMzCCRA9TVsSAnZWagAAs3cP/28Akf14GhvsVfibfR6c\n0CFuiH0JE7KHzoAVnFnfxYuEip+El30wF4BbAByh3Ca+7kOqgeBfdbrQZUk2\nLKEc6PPxxIeWRci1kEFez7gMWjmbOHvmOK3IZSC4V4CS5osuJp7jCB6jmRkw\nTCFVMJutdhMmbUOL9rXzBFZFu7Ox+3Xfus1NVtDTikR1G75BuboNlOHf9f7M\nutpE9OvIOSLQJOnP/xKn50i77VuZddETSUcjQF9eI5h0FRKAJCcSUa0/9nGK\neu7rpZ3cIdgP9BCXEefJ5VCi5ZIfpYqscqtNvqfyJyQIGYL6lLkVzaftpyXk\n6OD2JA2zvKlIxoXQMjMBF7A0LGSMZLHr7Sd7sEa0Iymr2vI57HfnS/3SRcpG\nlYbqWET/y1VhwmNqafEMnqX4ynOqtgF5oR2ZEuED7CEPwT8cvP50azoZyXdd\ncnPZNn9OyQ1WZunzEspCr/qEtDkXgjDsoE9LZwAlf0wt4soViSNbtLmilWrk\nq+YNMBiNR2xzGp5ewDJ3E81fjCxx0L0S+l5qbiLOgVO9xwypmRyRYpHCPnOu\nEb45x0k1NUGviOkWwXtNfHAIdHBhflfs+PgFSNtavuSqXNUTUaXUHhwX/k4J\nMTOHNZKw/9+rArM20EMBu83fIrVI6KBLT/mVlwffzNN4eXgZ09QxgxD+Y8Cj\n/a59\r\n=B9uP\r\n-----END PGP SIGNATURE-----\r\n", "size": 23344, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.3": {"name": "@codemirror/search", "version": "0.19.3", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-T8AcSGNYuYtmOUXzI0VtCa9SMA0C90/Zk7fxnabgy0dpViwokucCMY2+aBZMPncIatkgojzbiRn7d5Qo1Ugx9w==", "shasum": "9c81f89e965e708a5095aeebb4a181c463d37f17", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.3.tgz", "fileCount": 8, "unpackedSize": 92521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm0OiCRA9TVsSAnZWagAAwGoP/iXB40gVs2yqhMbAU3kX\nk58XFEM9acEul/m9KT4S0/28P8BieS3x6krJawWcm2fXrS3AyrHP5/P+JLJP\nHXpygMzLHgxvIhWuc78QMt///hxiV5ViT352uCUJ2U5gN7Lq7NJxcrM5qyjb\nBABJxJv7kDuirY8xxVws+ILHvIWTsxZLNjvnuobyu2zIqDKIZ2ctXVqF/KRV\nd0jlnJsgFEDHS/PiHJGO4RGSRVHy74UUnEV8R5GLECk74yF73r4SSBrmkM1L\nOg3akW1rjqyeJV3CcO53mQm69ATdduyIKWUD4rzr4oQAAqCx/4BH5S0Y7+Fw\ng115sSCyH/EVSbTmSde9PLhMQ/JSRXmCpRtuvU9G13hhjviTmvv+Dg4w5WW4\nqlxQr7z6FSI5aQ48YyybX4HpwFog1f76nCCvxKACAtbuT/ITN0kcBDqBEs0w\nCaj3F8Z72yeH9k/GiiQr4Eq/SRekU2I6jIh9rUXdJnmSbR5FMqIxlnWFXBV/\nqUk50uy2KcAUtIEFQeTgE++uSD5vhzn+cE/cYKH4tSehrmPNHgzbyD+togEB\nIgGIR2DFByPRFSfNmQRmNSLzIkwJwjQM5tg5/ovSH5fM11cVgRgwQt1p6zxf\nSDUKWK5HPKZiMVfmOPDvZGSXjoolxrxIMsXj0x8TXxMAMMs0d6rlcRFPomeU\nQywb\r\n=kxB0\r\n-----END PGP SIGNATURE-----\r\n", "size": 23219, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/search", "version": "0.19.2", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.2", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "d549c3daa527e17c173cdfc90b7c1b02deab1502", "size": 22835, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.2.tgz", "integrity": "sha512-TrRxUxyJ/a7HXtUvMZhgkOUbKE1xO33UhXjn1XACEHKWhgovw1vEeEEti9dZejN8/QOOFJed39InUxmp7oQ8HA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/search", "version": "0.19.1", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "0d53f9db9f8099d2de1e370efff61053f7d7833e", "size": 22520, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.1.tgz", "integrity": "sha512-OBjaIvdFwiknmPoc/hFSistUN/hdumz7Wf0SyueL2tTIQ1MZDNnonC07+GEpiF6nTjcY6r+ifC0cf2X3o/FVqQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.0": {"name": "@codemirror/search", "version": "0.19.0", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "c7ffcefa654d7f17be1567f67e4eb0cbc2ee657b", "size": 22185, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.0.tgz", "integrity": "sha512-9yFHX3XENso1dzUPkHNZU96IMCs/cjDg3JTF6qiInaCOxo5cwuRn95NIem+jlpvx6fQsjstzSR55HIb5R5fb7A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.4": {"name": "@codemirror/search", "version": "0.18.4", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.6", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "cb258c994db78df8c74c0350b84a0c275cfa2711", "size": 22088, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.18.4.tgz", "integrity": "sha512-3chVkMPzl+pTUSqtimTicebhti4SLpvkj03pQx2aPZScXxIiYuDk4cLdIJK9omjmO1+oycRKbOrqvG7iZJJwMg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.3": {"name": "@codemirror/search", "version": "0.18.3", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.6", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "6c5f51eb7f973b45bd88a9913f03c1756fbdc606", "size": 21568, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.18.3.tgz", "integrity": "sha512-9s8ltRtBHcEZmE9lHBmYArfdj9bpsCkxLYjvrzOVqix3wv5DVAgcfk3Kd1WkahhkTzycYULC+r9KKmelUEyTbg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/search", "version": "0.18.2", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "7f6311ce4d5749d92aefb41b2f8628d28d90918c", "size": 20097, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.18.2.tgz", "integrity": "sha512-t90Ra34piJDF589hNDmuA1fVKCFDh0FTuTZTHDmmSaWS5OWq2++zAwRTQnOdQD+uGfEUwLQPiLJzu60NDhA5xw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/search", "version": "0.18.1", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "0d699825a2bdb3a352e9fcbfd2e5162368fd7641", "size": 10498, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.18.1.tgz", "integrity": "sha512-T1yAZHLmCzaXicyHoW/45f/7ImQs87kajcl4+9F/uBgzDjvQAL1sL5NVVONze2UxGHPAHRPTLwvkPxKFgQ0kGg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/search", "version": "0.18.0", "dependencies": {"@codemirror/panel": "^0.18.0", "@codemirror/rangeset": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "aabb53d4678196e82309241753cd67403149e35d", "size": 18803, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.18.0.tgz", "integrity": "sha512-gjg1yDz6gof0lhOEzjqofidd5tH2dwUwiyzym6E78n3tZBh/KygLKmw0B0T9a5s9JTVZtzup95i+TmcHrz2MQg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/search", "version": "0.17.1", "dependencies": {"@codemirror/panel": "^0.17.0", "@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "crelt": "^1.0.5"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "eb6ae529093b09f92b1d62c4d0ad8d09c4e218f7", "size": 18878, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.17.1.tgz", "integrity": "sha512-wY0KP9my/0uKQk9AU39EqmkY6zMVv2Erej5b1rRBksM78JZXzjNUl4gyhtx1/0om84IZ1ocmW8MRElkAY6r1rw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/search", "version": "0.17.0", "dependencies": {"@codemirror/panel": "^0.17.0", "@codemirror/rangeset": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "crelt": "^1.0.5"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "136767ee61df67e64a5a5163627d73810f625ce1", "size": 17315, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.17.0.tgz", "integrity": "sha512-ive9zue2d+VB9MkwJ1s/v4yThTUmBL035G0Tos4/RAWeZ4ZWMuipvPO+ajHHD6bsicx8xK6pn+pFCCbir1yZEA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.6": {"name": "@codemirror/search", "version": "0.19.6", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.34", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-Wx3AyZ5/hFv6+YklNMeW2sHbVmnhSNC027Rc/pk7Cy51g09ZBHqMMAwJlpNcWmGYjf+SYzomIUpd+DgkVCmvLw==", "shasum": "fa6eb9975baff3e063011ff47c41a885d3f1db55", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.6.tgz", "fileCount": 8, "unpackedSize": 100217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8nCmCRA9TVsSAnZWagAAcjsP/2SMH0CdD7GBJYIupBAZ\nvfF6CvG7vLe06D6Ci0Pk2vAAGC/znqOJfRQYu0KFHg+nndXO3chuarx6Uqor\nqLzGM7vOOpBhCAWhVzgn7Tcs3Uft41p75q47tdvO0Bf7fzaoqJyMb5Nkapq1\npqUUQH0enDpp4XeX2SaImlgDDLEtnDKLlMvH1fUt92G9v0dEvZDsJbM/do+I\nOkVk975BA2OSurO3xp31Vl8WF4OwC+OIb4RhVgcYgyI6Tm7UX8dWx1Jc0wNz\nm4y+xuwc2ywhIKn16T+kpk3Os0NicO92gqpFuQwgIxWMji+OaFbOIWffsExG\ncv5VFgZuh2WXuorj60sVTbI3gohuD2QrKXQwoGMKQwPcK6lEmyaKhtP+S+mQ\n2iAidi3mhG1W+ubmtOgAreBY+jpEia+WQ/v/C8TRjlfZS5dnFX12DdXxUZb+\nm70Z6Md95EgrlSQynh3Q+I/TvW79gYHdVTbyxOofu2uCaqWNekqyNaK4zk8O\nSpAi5qt8KD0A05cA2wvlU5ytFgieBwkA+xtxrrPRbI5tLXU0Ow6nAp1VyMk4\n/rEhBVNZA7mG3FGxyG5tUvYKnUPXYKUKn2/Z+WQN3vnWE1LK1UQ6jt129Q4k\nklRSos+iw6EsgUwsGw31C56qtsVtXRuqpUQfU/lUD7LR2sureqwh3+k/BL9+\ndaaT\r\n=LBN7\r\n-----END PGP SIGNATURE-----\r\n", "size": 24863}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.7": {"name": "@codemirror/search", "version": "0.19.7", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.34", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-/dhHi/HUHtHfmw7ULcTtsLAUTRauPZctGkqZ+yCA58QvzP2RVWsvq0BqBrlvFgZq2SwSbWwF3NO2MuxIGsQ4Sw==", "shasum": "77dcfde1008a38c8e2261776b10d6756ba2a8167", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.7.tgz", "fileCount": 8, "unpackedSize": 102104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiChT5CRA9TVsSAnZWagAA0a8P/iekug68OTjpzWIyahci\nnXMklX4a7NBp41AnO26cBP9qFOnSotvuGKvl4P/vafDlsVHGi6hu2ej/Lj/W\nTeuTrSMMVEWCZLs2FYU8atObd9GhtwHquwB4gOn+z/cOJhYKOk1d1hoyNlOw\nUcFIlGUSL2IDxyCd7yQmo+TEEjkIg/ZE/LVqmzIXim+DHjTncqzcXojWnXUq\n+G17Fl2KtFY3At9qLi+HQnqoZ8qU9IW8hu1b3kNB+9OgzHlgV2Jv2m8pa+m/\n6V8y2UiIcOqSDB0Y4X1dYi8lXOBR8nziLRv/5gNGJWd3h8t+A/n4eYjWSjuf\neNaLSAs5yDlWtrcq8FBPpHQjRtnA39uZasDi3wHkl1J3EAGnT36ARH3rvGu3\nqxngBdJ6XQf2X2ET1Jw7QRwa1yayPwzGNC3W9fE6vxXaFeUTFWjDLly85JF0\n8rxnkIIUD6W41jiTPxgpSZTKUb3OIb/LPzSlyPofvI1Yx6I/Iim73fuGhcOp\n3HOwsiBm8+ix6Z7Xfe4nOncNoXFaAJkNI5GG7mAW5cEF2hOIXl2Gd8dfUv5g\nkCl/dDPVBGONpDcKMEb6aDrFydkOygsEoftR7J5BT/tgcHHN6xPIyUIVhG7J\n86Ibu3yrsheefvbMwrxomqWfNX1tpbDxH2Xhn6CA6D40rgvk8J2dQgbFDeeM\nZPSZ\r\n=UY1l\r\n-----END PGP SIGNATURE-----\r\n", "size": 25352}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.8": {"name": "@codemirror/search", "version": "0.19.8", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.34", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-mMHx60bzbcbSI8Fu2WbxsGAZ2FCGkzslISsvyJXOxVoB7E+y1LDTBv0HUJ+srmfIsak6ceMxKgyI6RldRPMu5A==", "shasum": "20b5895ad6becd97e0b69b02369d69887ae15255", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.8.tgz", "fileCount": 8, "unpackedSize": 102227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCoBgCRA9TVsSAnZWagAAQ4IQAIQVf5SlLEa/4OeuYrtA\n4Fq55lOu1Ug33VpbKbRbsUlwtTB4voHOL2VoD4UdvoEZiM+xA5V/U3rrs/E4\nVahi+Qw7WIf5U1cZ9bgxiYaying4oCnqE3arvbYvlQjgXig5cqBvoSFZpg0b\nxdsiNYsc26Ox4F8axJ7wbI0yFXqFvq6H3zp3mdmrCa30+JQOcgUqqBqsEPdz\n0DyrdQ40MA7A8Z+3XwIrSh8uOfV2Aj8X5u4cAcpksCdlY21aNLGZCaSRmhgY\n8QdsZOYj1zdiVJwnV0hOzg3yic0/SxymtmpxIN5HRj14HbVGOW8HxK1tEvc8\nlzCHo7/vWg5Ham1MQDudOpQ9deZ3EO0OfxEvJi96sVnHPpSRRzsSbwOaoDKU\n48VKfBnpaGnXAsVSESD96ImTOHpBSJDv2ZWfDKSDZBFfYuv8e8OxzSWLc+qu\nUHuQc5/koYrGqVg/ONw0qZg4eX+i6gbCBx9zogQzuenXjK+ew/H7BVP4HTN4\nMgaJT4KdML377tXY4ApkJOx2jZ4dys9Gdri7WskmrtQ61vVlXlhA3crOY5YM\nm8EuNmANv5HXDAq6ZJO3ZNeI05GB2zy6haaf6+KfbIvb5qQrmvirHiF0AH/h\n25/VaJN113hcsCf05PaSGMw0+CBWfK8y99c7hC64QnP4LOd+dZ4hLNEziWGw\ngT53\r\n=iKFi\r\n-----END PGP SIGNATURE-----\r\n", "size": 25394}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.9": {"name": "@codemirror/search", "version": "0.19.9", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.34", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-h3MuwbUbiyOp6Np3IB5r4LH0w4inZvbtLO1Ipmz8RhElcGRiYr11Q6Bim8ocLfe08RmZT6B5EkTj1E8eNlugQQ==", "shasum": "b9548dfeb2028e6d5ecd1965f8f0ee50e2925603", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.9.tgz", "fileCount": 8, "unpackedSize": 105245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIIwQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9VA/7BvUhM3vpFQ3Rd9ogVdGi1AjRBirheX+t0FDbGVn1NUA/mCK4\r\nOOkosnKbz3wVMEjRK5D5Sv+g10XX2/zLRs3PxSkEVHYKwSIO8Jv126/iatzw\r\nDqnjAT0Bu3IvfxIoRP5eQ/g/ZisHbFhmQJ+XCCNyvfxhXpB48arrgoDr2yJr\r\nJYrVtbmXQFBXsRAVjk62f3EIboHA8pQT5/+H2cSjCZUmShvB5+RBHutHuRfg\r\n3LsebduJXe0erOvnyrEVIUDehNg86qH2/sAvImbKrhSyMzRVcy8hogpfXFHB\r\nhrjK8C4wr+PhoI58PFRKy6TKVyc8u1Fzq1V72NDxbLi4OmXkczISpOLGghrX\r\n4sz4ThGZz7Nrjw0LO0VZvft6trMzlhpAPpHc8bLPFEH3Kn8F1dfjvAZE6pr7\r\n99wBXOP+VjthI1H+BIdMNTjTJBB3eqbG8WhJzjE2+eAzTe0lM4sOIGMtG0s/\r\nHUkYvihP5JIOqwhjIOl/SD4SIW8jRhltERtE2zZd0SctKfC0Yg7LQarsCdHS\r\nuhWqtKPcvKS6Q5D489bx90w9UFxnDG1wAMbIbr+g2ZEl2XfAhEECaT6uClOo\r\nrUms7JtnrhVwzF7gJTDQvHHVwYopGFCuQ1X8omIUNLht875U1epeUagqdzEi\r\ntjzXV0/MeG1IwWb1mXdAbI4my+a3ZnPUkeM=\r\n=DDzC\r\n-----END PGP SIGNATURE-----\r\n", "size": 26093}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.10": {"name": "@codemirror/search", "version": "0.19.10", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.0", "@codemirror/state": "^0.19.3", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.34", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-qjubm69HJixPBWzI6HeEghTWOOD8NXiHOTRNvdizqs8xWRuFChq9zkjD3XiAJ7GXSTzCuQJnAP9DBBGCLq4ZIA==", "shasum": "4b0d91c53278db05088624ae04f164d66fd581cd", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.19.10.tgz", "fileCount": 8, "unpackedSize": 105418, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWM9Y4jR4XPm+SHQxO78Cuy9lId0rpVJmDyZqR5g8y4wIhAMtT+nuE98mab5IapxdXjK1tVfGpkVK/aWAbBcJUYGJM"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSqBQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3uxAAlmG8TDwp9BPuM6HdjIgjNekNOPFZYgwE+DmF5kBqE9z/IhLv\r\nrwB43IjiciPykt/MNLK1j5Z8BHEYNBF2SoJYmjHrU/q65ewACCVIDL3b03gc\r\nYLuZsr/iZhbod66K3cDkQRJ2gGAOalcfuHazTRc+rXp98WmkfSxhD6YSJGZz\r\nvYJPH+jW1BMVbaR7zr7hGVASpzbQtVqXBm6iZAMlJRohr7pBRQg0wclkiGCb\r\n3zWbluajTS45kQUqvgRXn83QN7NIOh7ZztVuysgZ48ptFN/qPLhiGm0LqSrT\r\n6UrYnSlmC8XEmhV8x2vHRW8Bt785pFHxi2H5gCt4gnXvrFOMEh/dCgNbM6YU\r\nvXj7WA0e6hNmGHj3eBRim3Jv8uAC2sCVxuyFz3x0yalUYiZb6rU6uz/lcy7i\r\nX/beivVAp/gmmT4UgHT+39VsCq2Gp9xhfcU2dVefoGHxZTSaXNY/1iqH+5Ha\r\nfJ51XGeFp6XHRFPFaAzBU0tEWosmcRo8rIGVI6h0S26w4P8E2++4Sgl+Scwc\r\no+i51s4/2U8DPM1h3fHNEpy3Fh4h7Dcn82Mk3CY6KsSrlkHB/qdcJlvAWutL\r\nOgoIqox8OdIHv1O1hBMyWLKJFL4SI/w3XPvmP2kXS8alZOMyW+d9R1UI7akR\r\n9yfWcKllggPDFtWvP90RZGj6BeNLleUm3fY=\r\n=I8PJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 26133}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/search", "version": "0.20.0", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-zgMHg9zXdaACL651ODmj6a5ierlCoxI1zBTF7WCkpL1pvqyaxOJXxlpzeFgJXZGSxUaRr1me6Py8LJIsHpMH6w==", "shasum": "5a830e58df7b086051c561686ef999deba7000af", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.20.0.tgz", "fileCount": 8, "unpackedSize": 105252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4oGtInU9LRUm/S2sbdXXjXchNm5va7F2mlJiVkyJuFQIhAI57Ve92MmCcJIwR2WG55d9Q+QQB9ubbnenb/bH8P7Xq"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBVCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKVQ//UFmFnoOcCJuHEs5JdrdtgX0J3BiTgcy5oM7dsdZ+VUbnbSOl\r\nFkT59E0CZj+e5rKHWuxo55Ja7ckYOflovaPmgnUDIQg8TzxfXSoRr7zamzY6\r\nd/ACZr+dATK3BDc37tNJ6Qod/90xrVcnePNEeIkmAJQKJcmxsQ33+cceagbx\r\nd0vts4dQJbHEDEvG67mf1pSSJYPfd4zog5QxByWXg4/FBX9FOFBnqyLMAFYM\r\nBuGad9NmE8MZO0f2WN++KAUfsUsneONn8g1TeewFkvXHJMo+vF9KO3Qs+i4Y\r\nDK/gzHfe9lFM7fzzgEXO30kSorgZ6OF6ihMwQoTNdOUyJpPyKe7FAIy5/5O0\r\nD/O/tUmlKYMHq4nRi26lPmrsy5FZxNLyFWT08lqV6Y/E+j77mpQsmWhlkeU+\r\nOtney9DNYOeetWRwzF5BrfmAn9JwgFIqQC5Zo8ZKxt3De8hXrmawjgc9vIn/\r\nFkJcsNCZKlZIqv38id9r1cC4LUn1L/UAW9Zu4r1u+aKlGtPDHGLcBuCy6d/z\r\nBQIv6hVUef36e5ys/iUAeGQd7NOhE9MDA3RrqwZqmtyNYywSmCZNsXqyXjoT\r\nz61QVcXtljBduLlAvhij5hpBZvBCFOE+sZj5j3RodLoYWEn6B0cW+gm/K9q4\r\ni9N3oeTv/v7gp8CLYZChPif3G3FBA/agYRU=\r\n=YLCq\r\n-----END PGP SIGNATURE-----\r\n", "size": 26072}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.1": {"name": "@codemirror/search", "version": "0.20.1", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-ROe6gRboQU5E4z6GAkNa2kxhXqsGNbeLEisbvzbOeB7nuDYXUZ70vGIgmqPu0tB+1M3F9yWk6W8k2vrFpJaD4Q==", "shasum": "9eba0514218a673e29501a889a4fcb7da7ce24ad", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-0.20.1.tgz", "fileCount": 8, "unpackedSize": 105692, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICDiFhI3deMKfJZ/43BpbqQ/dULeeVByJ9pCa8McN0XuAiBwPdhkpJnzez0Bu6RIVZjbo1kaLSkjyy750wLQPH2USQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYqf7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomexAAoo6GQHoPdc73WOlfZY2tNI4YOsH2px1KbA9mNRUlLnbgXOoa\r\nNrkF5JxW4FOpHD8pfuqyJ3uMEGl1yVDU8hhAN8CnSiYbM3axblBoBZ9FY/NT\r\ne81z6H8FAO8MsVP3DL6rl+YBqlOKXpvUIjWtOhGhKXz8GW944FVFjQyEf8h9\r\niblItBSS2tmPE06sM4slPUz+vgJvtVJNzSZle2lQwdSBiSlz/Qhu3zcsDkel\r\nxi3z/VUqPQASEvMrJ+kxKGmkkuFbmfLAbg1fTOLN6QGBh7xLCRiY5CtuPItq\r\nIxHQlI1EWrwmVyKTH+u6M8Cs5BSc46qxoHR/UsGYCPT/leA5R2fykJ/aDrwT\r\nfetlrRAtjAfRGgmKQ2h7Nz10Xp5ssYlniqKTk6FRm3g9PlzA1zMtSxpRz6RH\r\nF17bwFT3TI2kBcxEmeW8y7uKbhI1f71UfeLRvkvkLRHPG4+s46CYxKQjUzok\r\nCOpON73oYcBtfi4KPFH7nzbJrS724QfijPMw8HWmsWIg15GkZ0IJhCNQXEkK\r\nD13HyFGdE5KRvyHIPhXhJrG2w7cSlktPXjw7lHPkdm5rJj0+g0pykjRQCq1z\r\n/b7Skq+aE/fFU8dlAAJMLTDdoLT+kgqJH0kumAMO7TpEFwWcIdYrKAAfuGvY\r\n8jw1EpfEh/nYRPVO49FKDzpnyJ4neTI95pM=\r\n=arhd\r\n-----END PGP SIGNATURE-----\r\n", "size": 26229}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/search", "version": "6.0.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-rL0rd3AhI0TAsaJPUaEwC63KHLO7KL0Z/dYozXj6E7L3wNHRyx7RfE0/j5HsIf912EE5n2PCb4Vg0rGYmDv4UQ==", "shasum": "43bd6341d9aff18869386d2fce27519850e919e3", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.0.0.tgz", "fileCount": 8, "unpackedSize": 106527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCplmMsHNF02h+IfnGh/mDx4RvjzcoiKjLegn9W3KiSbgIhAI/0ss7NHdoZjS7fl0JVYCR2OIoyxSPmyneZH7y+hSPC"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrteA/+MbCx0ev3HMGb6KG2vVLXBr9K/R5976y1H5Z6QAL4FoePeVpr\r\nMtKnNA1rpp/6iK1bBl5lSD+ZNjtDndYcSuIA8MHd5zLgv8GN0ATFafPbqm6v\r\nGyeWDtIFZ3Shn0Zf0qp5mQjyPEM8los0MGWZVdKDDmgAftbUZ12IpW57dQZ1\r\n+NyKJFNJKq3+uayJ6dZjlGv7NlUvQs92jvxAA3xIxoueQLMSaX4mMr0QXxWy\r\n0QF1it8vdniI25lsTQuXq/yjSpVGfhNXy/WikUeTYnNoUuSIENnIWZPR83JM\r\nOJR1eT6wuBQ9+1/RP27tyB5Z0Tt89FZJx1sTUzARcT32Ore6YMI7XOBbDluO\r\nb8LabsD73PVSAUImlJ1WePPL+5sKmmdfzB60wWKfcV0gB0T6FIzugD6FUFQI\r\n5Ee+bqZSjUNjVzmjiNc6Q8sMHkVpPGcSVE+znLnpwZkCJxYWOZjDgdm2fbtR\r\nqn91s/O+KPc/3lNxV/eUd2GQtF6Bswm2uU7Yaxv6yvcGdePUUi4/+YskSPkx\r\n68D7mUi7Q5Uu241x4CRaBQQHI9a8fCRirXO073Qldk52Zc3d4x5V0xaXbeoD\r\ngz3urjm50OBM967worTGwHS8GxLXuSy5NBEbWfUxnXgAEOLt/UaLRzh0eSmt\r\n0zI8+qpaZyrOsUDGp0cUb4URVU52cZXi5a0=\r\n=671T\r\n-----END PGP SIGNATURE-----\r\n", "size": 26456}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.1": {"name": "@codemirror/search", "version": "6.0.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-uOinkOrM+daMduCgMPomDfKLr7drGHB4jHl3Vq6xY2WRlL7MkNsBE0b+XHYa/Mee2npsJOgwvkW4n1lMFeBW2Q==", "shasum": "12c20fdfb341fb4ca2125b2eaec35a8cf4b682f1", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.0.1.tgz", "fileCount": 8, "unpackedSize": 106683, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEUE2hS6/aUVw6m8OTblzU+sQzf11PYyyGqg3A/31+2TAiEA0wn+oQblraAIicT2iGUCFX4dWDWQFzrDlYO+/xNd3Io="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2kZcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV4BAAl1y3CAkoArJoo/TkU0KoITz8+MlFfzdEjNy5yKZGE94FdKdl\r\nPxRieVDwfp8xvLTI+8aMT6QbaYzA2nuDyIrmukKSRKssuG2if+HkIQeqMub8\r\ndhKSJA69cWSLSLvfeYJqsTLhNMGhY3/J1mTGt+3YLvfexXj79+z63hFd3E5u\r\nCBpFFvpdszwLEPd/bPiLfURATKqFa+GqpWubCTVqkf0qH7JptO9RUPnTuDvl\r\n9Jt1m6owm3XR83inzpKhBi/QKC6Ko9sLgFOGF/2QpHLKLm/6zYJJLv+J0c3k\r\n/ugP2ab82VHj8E8CK/QzMnobNkqOc9AH3gGmtkb1N2oy97yPa6BDRJB+n7Yc\r\nyPqQZa/7jdqiUGVYi8T3de4gdlzIiaHV4M8Dow1U0jYpEVg+VkwnxZqM5vKm\r\nqyX6FWnT8Rt5vkPobIg5LcsV2r9KtGXUwDz9MNgI82KZMOKtmbyqWZcTUdV3\r\nbfc+ySYqH3lOx/Qf3jjduAr6YHn5S8n4ncxJ/+7GppTPg1IYI4eSe3Fzb4ZY\r\nq7XkjZBv5bWi4n4kqYF/E+zQAGINVcEzpvftIsdDwBdmcl6Egd3sk1peTOGd\r\nmJHtwtTlYI5Rd3EhhTqAgMA61eA3Cz/PAX//EC6YkWrgsqWLZqgftBJj80hn\r\nOd8ygVgsfp+HBJJSHn1+7WoG9yN4Zmrz9nA=\r\n=9phj\r\n-----END PGP SIGNATURE-----\r\n", "size": 26544}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/search", "version": "6.1.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-ye6m0jFHSgQ4qnfWVwArvm7XrCMNppMYnL5f4M0WdBScslnckomf5eVacYCw8P0UBWeq72lCSXA0/eo1piZxLA==", "shasum": "2cf1d32f7b38d4b1fa2dadd26b4c1d23c59331eb", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.1.0.tgz", "fileCount": 8, "unpackedSize": 109766, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFUxxnFH75l2PIAY/g78qghQ3Ydggfrxs/xOuizVQWxpAiEAy5fXQzy0KfxtboFv1NzczJx7MckBoZEiEyukV8qP04U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+7P4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlvA//aQLZK/uyuNEdW3QbUC867dQSHh2Mo0zSPttkn/Re0w8ZQst+\r\nAY49hzqpBDyH5Kvu5fyf/8gg6cf5p4hrQadDh9sWrzQlX3omvZOIA5ZnUqhv\r\n7Qd+gexBAtEeDHueG1iiWaEf8B0IVu/ew7vXqZO+g7l8JUMIWk3gvX5LMqQj\r\n6j+veXLErpRyFM/bdcsGCOn0ZkTbu9evk5aUPf3xDkJfY5jAvL19S/5QcjCQ\r\nB1AyVFxAKwj70w/ApK1APt9oR0EVJgQWWzPkuKqGH7d1JavlWpMlfgWen4Kh\r\nJpCuHNNbBRDlGLyHl2RmSEazITHr7akGQEKHr1zGbN/ZzqRWOtabRJSZjzbe\r\nzEbeBBqWUTsqh2JoluvfSubNYPAeI+mnd8ODV6fQOk8FuBz9qPH0I24v19wx\r\nybNMZB+0EtszPF8zZ26QlqKTmqkwcx1mKh8+PjuCp4cpnuZx+qB1lHJXSyXM\r\na+Z70lQ/EtdgcBKBng6Z8aN7j0yEh6hOg6JE1MDWT5DqTLKhUU2TWbhZGxgN\r\na8y4O0qSGPGAfUsmzOA3IJX5p+QvchivHKJlBp0SUnRWUsgU7IJN+RkZ/Yh/\r\ng9B0IIueSlrD6RrM1yOxI4exnZ18Jma/QwBEke1qckgz5fFh7qanQx5C4STv\r\nlBtFBA0J7vcuISiHP2j1aOX0u3kiczsCXR4=\r\n=G4C+\r\n-----END PGP SIGNATURE-----\r\n", "size": 27289}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/search", "version": "6.2.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-FVhpUvPFUJe8lg2EQJTTcF4RNI9d/OC3PVitvOfhv5OuY7ZgtMfJl22o5eMkzOEsUY2Wxe7BKGLpe2UI5Wq3PQ==", "shasum": "05742292c8414dfcf86bd27ef2908d43620f79d2", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.2.0.tgz", "fileCount": 8, "unpackedSize": 115472, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnZxVYZoeTdPgU9W3BNw/sFr93WlI2jPr5oWFS4wPR6AIgfvEaek0nqL2p0Yw0MChlTOFyDbojHCfI8+hiq0qcpbU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBzdHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Nw//SGIJpew5bktwENwnTXTjuDZ9lVLt6VJdd8cbUEfq55ZLn6ZQ\r\nyNFYeroGI8qjXkGiAadiEyiS/B+s8kOs84gSYk/nlMbfDIzTVVaq9M+vL+1b\r\nuGkOpdwNl6/Ik6DJiFIKqMd2lKVU4J3nesCYO7ETzKkWVFEdli1nd6ouyff/\r\nuFYyJubxjxKnnOc1r7FkRGylgxHcNfCbmE1Z0wBXMdCgELycieHAlol87HEG\r\nNQBZuvIgsBj0aGlcs1FfAvpj/n0HOJEGRMtpfVT4orWo2rmd2UkXieJz+mjS\r\nAXrPyCJoxvk9xiip0jUr3lRYo8fAd3JQr4uTW43kCM5402gCMhErar4/xVpW\r\nU4/jNOX5YZ6VdQohDJ3FfEsSTHL/vxp/s0OuWCAt07YlEH54ZXIvnncMh50S\r\nxajN3T9/PgVHqns9tKV4CriN9JHcjxsHxM+0sAqu2C6TMWMi/2zWJ+mlis0z\r\nbDf6pWMUNpYl1Gecjic+ktiL8hyui5S5YsonIbPBug2d76lE7L46OGtp8m4G\r\naU4Fm5n+0ZzzWux8NSTZ+fpBvH9j0CGYNgz6oy9QNqrywfoZrId6jRgNiAu1\r\nEhH71V5x/E1clP6HPuTnVBjwrjr8HlJKgv5JfmyroL0yxOnhMDOhWexRkvMj\r\nP8/jZYfk7LgDprTzcR8z16bZt2vYlDMjXyc=\r\n=kdHR\r\n-----END PGP SIGNATURE-----\r\n", "size": 28380}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.1": {"name": "@codemirror/search", "version": "6.2.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-Q1JgUSBjQZRPIddlXzad/AVDigdhriLxQNFyP0gfrDTq6LDHNhr95U/tW3bpVssGenkaLzujtu/7XoK4kyvL3g==", "shasum": "953ca99e04877f1caa1b521a2b3180dfd1adc499", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.2.1.tgz", "fileCount": 8, "unpackedSize": 115691, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7E34+NGsoLOPmvK33khjY/vG1v7qNLXJg1sFRPRdtrQIgE47HFNVIASi3khlG71367bFH1KsQOFXn+33dNBjWhZY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMdLbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHnw/+KGSk5vu1rfQ6STfPb7UgucN3UdKls6TwSKcU63JtYKSiK8mS\r\n46m6Mg1M8TNoycila7y1yf7XY+WU+hhDPPx+eW/k48UvbdG6j7sb//3PdOBw\r\nRDw2fCVfyWTgLZXnSccq8dJ3JRaTHHScq3RihG3dfpzAx3/+vXtvIxUWkpn3\r\nttITz+9he1OhbFMtpXl5JY6cePMHvw2holvLzjOO5Nf/L5uLN05RSLsCoibG\r\nkzL2pOR3x1KB2kn7vASgO5zFXVTiwNHIp3wlNDJJgDtkMIzo2W/HmRgpBrem\r\n8yXb/7yMsdN2+gkSbVzseVmwA5jQcMWrk1xMNIaeERG3ayUEQk+30l5wdA29\r\nmWgIep0rY/c6h1e9ToN4YwZXSCwbXim6akztf02Ew7ebnUCgWKhDR5jpdYRW\r\n405ZE+wUBUhYJ4ZtYSIbTAyEGCai7oJ0L7en8nCCeY5tiepKSlLN1Iid3iVq\r\nn65HK7TGvqLLg3F+1+xiXrN9yavdWefA3SnZy7XLmWQQg+RFr7+vt2ynZBaN\r\n5TDvJ4ARmYq3zxQ9daE1T+1Mm5YI+AgDC7iBhSRbtc7s7FYcJjVJEGJFHCfx\r\n/Ooyue/CCbdyRyiAiZ1NFvMqLRLZ/r89qTKh+L7mJyoOok5uvJ3bMG70dqgm\r\nYI0di05yxwlmYTMUEcKqdGoZq7ebD32WjeM=\r\n=0aG8\r\n-----END PGP SIGNATURE-----\r\n", "size": 28425}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.2": {"name": "@codemirror/search", "version": "6.2.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-2pWY599zXk+lSoJ2iv9EuTO4gB7lhgBPLPwFb/zTbimFH4NmZSaKzJSV51okjABZ7/Rj0DYy5klWbIgaJh2LoQ==", "shasum": "278ac204bd19a038271595ce060ad32c13eb70a6", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.2.2.tgz", "fileCount": 8, "unpackedSize": 116612, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHzczpK24xrzvvd4JXs9+lmb/4O5+vSz2fAozIy0xRiSAiAuJvXiTfOv6GHR1OBQqIzECAR6/uFEpkK+SZETED4nuQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTku0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqA2g/9HwPAicROJbWsGNtQDTdr/9fGi57mBRz6dwnj5V8JVw9Yburo\r\ns04MG+oXsqJAH4TTLszvdaElzcnIgDusnW5YKIn/R4YR+Hhmm+F7BwyRegqX\r\n6DWwG4r1rXLqMpKrCCtnaUR5vUIg4DTLmmXrm3zT4kUltPZ3LjF1Ows3tntE\r\nGJjCVCJRe5Kw0sRhJNJey2sup1PIJUiTlGBhOcv79sc+AC2jScD5Fc94qnLB\r\nFOYI0f7o7tDds8pQip9vVWnpGWSgr+XbQmDzsQ22CE9zOON1aTlX3XOXZw3B\r\ncdRZ4Kzn40EfmSZSLzI1OVpWBz/Tz55E5bCVYWAHvfB/YD3c3Uj/kwtT3ofm\r\n56bmjPsVOATFjUStTIQkYvgBM/mxpQvk8k7Gmz407TgVQXFH1vHHQqF5a79i\r\n84Q0Tbm3nhYAg5rs0IjIpdoGQ2xkLziONER482GA9Tn5O1IBr692JuTfbH6y\r\nC2jWXpwHr7IMZpDj3SWiCApSPfUahhFtBeUoLUhmI5ZRJN6KUFkwZfmuDtCt\r\nnPli92KVpSVtWcCtlilwSF4do8fJhsn5oi7wioWHPcvfjNROivmO/ih2IrfR\r\nksPp1iMmOuGg0C+c1vCN4FzsT0Eb1undJuF7bnluTL5r5GvfkH3rMDRFCbBH\r\natWyY7yeyNYBdPh4uZscAMvdVQEqWcy4sD0=\r\n=omXA\r\n-----END PGP SIGNATURE-----\r\n", "size": 28638}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.3": {"name": "@codemirror/search", "version": "6.2.3", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-V9n9233lopQhB1dyjsBK2Wc1i+8hcCqxl1wQ46c5HWWLePoe4FluV3TGHoZ04rBRlGjNyz9DTmpJErig8UE4jw==", "shasum": "fab933fef1b1de8ef40cda275c73d9ac7a1ff40f", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.2.3.tgz", "fileCount": 8, "unpackedSize": 116686, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC3sFMCSpo6U/IhvFi2SyjCdi0L8HDFWrXMX25FRzL+eAiEAmgllSyO+AHeO6rLeEcKIPcH6vU8rmAfc+2Bws8vIu7s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcl6eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxQA/9FflR+TjvHkIB8P4pbbFbtEx29b/6WSONn+fEPTfby/AovpAX\r\nYO//B/wYI4JnnVg1NTRHjjVpLRbPiZj8fCV9rC16E7Hf7ylDgnhR5AdDDVdr\r\nH2RaxxTAq30ueaESrgCd67As52VvusLe2guL/oKJMNrr78N2wJmdRQukFwGk\r\n9Ky2qvHWzB8FxWtUnl+1KwWL5zddjfLPgszr9k2BsU8H9/LGt2IBgzFPWz9B\r\nwaHM+ohEMDafTXlXRziJuN6BFPCfMITrAaZ+LbYNE0LEvwnNhE7aB4SM3mp3\r\n27tNA/bQZuhw/D8Q+Sg3Xq7CT3KjNzFAb9dkkC0kG15NKoQuWyG7Aw5BunN+\r\nDMrVJ672NGvelEMh5DOUsbk6D8jsQWU2jUbuvfLc6omhFlYdIB3l6iX4pyux\r\nGknNJ47NKq1klSYTNSTQeekIXyxlEVasfOSY9dpRfrcwCSAAJ7uZ1cSb8S+9\r\npWFhSTtKtXurphs4LkkZNqIHSNXo1eS0FOWiRGjGwRwmfovh3rQv9XbXuKgx\r\nrJ7IMW1xmYjd6EBxgKLnM4LC+HpiTkcEp6Xo7HjZPlCG/QnXsYvCIszWK91d\r\nK7q//ptuZNciXzNlqycdp0wfAWPTa9Zwq8dyxoeRJx7eGiJMn52GOf2yjksZ\r\nIa/5c5h26tehuHggUH6UJzRS1bfsFNlIeDQ=\r\n=UW6J\r\n-----END PGP SIGNATURE-----\r\n", "size": 28627}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.0": {"name": "@codemirror/search", "version": "6.3.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-rBhZxzT34CarfhgCZGhaLBScABDN3iqJxixzNuINp9lrb3lzm0nTpR77G1VrxGO3HOGK7j62jcJftQM7eCOIuw==", "shasum": "d48b2d636fc7474613e240ba8e56c4c5e6e51822", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.3.0.tgz", "fileCount": 8, "unpackedSize": 117646, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAlfXFF5T6cSK4jSsn7u1DAGfVIhH6+QO20oDrs6X76EAiBZw674DEc2TN55a36dP97l3WvywYBKikFMxELA1uIoSQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGFCHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp09Q/8DfmMyw3lq5V2doLMgc1T5XEpgv4JqnZzEvo54XnF6LWLnkMY\r\nS6MyCgQuf0EHeIOBe6+ifK/ksFsxZXtAfZzEvc3p9mWsGMFRDjBBV1nZaQ2h\r\n2c3XnZHvmvRGCNlbBm6H2wRgvwWAcfFSYWkSALMsXB4VxdGB8szK0qTWAVvS\r\nyIOm1RdQdLmavClAXVIpeQEHOOoAGd3KdMlLNZjz9KDBX+1SWXYCZa/xnKLe\r\nSsanqNGGHwMx5wOJXCD79XA7zDfPOu9QjBsfvi28kSsQlV2E94hLwm9pfaDV\r\n52leY/PnY0oIB5s1j0qg/faNaVTmEiaaHz0eVDMJqEjHNnq4EmKnVBu3E5H3\r\nc0vXuzjW/87vVk8vzoiw0LFy2jhG1TNsIGeDWoy1RhlNT8ctnM8vujn+4Uv2\r\nGfgKAaSFLoSNgxDX+zmDCy8bjzeUgi06rIAu0HXeIHT5cCoJSoIes7PoaHGc\r\n+KElL2an5wud69EYDuVYftHdTKFWYX+SwDWgxC2y5iwHvJ0YrwagjT1xhxoz\r\n4eIL+NQOgnubE55RrUQ+wPRLyHkXPmFPqg+XYEQEn/dhBxlz9jUbaywbdSPV\r\nxSTtrK7jBjOOhyh17/FBpP2Dy1ftHqtrPqWOGjVITcx8hth/H/a9LjxAyja5\r\nExUEyBjr7+z5OoKCHqtFgOD1MySZG8jrCtk=\r\n=ya1p\r\n-----END PGP SIGNATURE-----\r\n", "size": 28811}, "_hasShrinkwrap": false, "publish_time": 1679315079892, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.0": {"name": "@codemirror/search", "version": "6.4.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-zMDgaBXah+nMLK2dHz9GdCnGbQu+oaGRXS1qviqNZkvOCv/whp5XZFyoikLp/23PM9RBcbuKUUISUmQHM1eRHw==", "shasum": "2b256a9e0eaa9317fb48e3cc81eb2735360a59b4", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.4.0.tgz", "fileCount": 8, "unpackedSize": 118406, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDAQcKX0Pol1bzmIg2aOxnMSvkdJUDRT/keD4txhkfnUAiB2/di1pgKkKeOMeh/OExms3mQMDaVl9lC6Q3yf6UjnJQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR6MhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmov1g/8Dk/J8S8gkbQES3XOb8zrbCuSctXJ9mG1P3urpyhVfutdK39y\r\nuRrlcIENp73WzCOZdKyWNdPdb9LqdjY5ohgce/fXC2tNC68HYI4GmeyWw5aZ\r\njtuHVhqkSZqdbuXDJLMd1tkG2wKWPXfbQtD0Pt2M9MYhX1KCcEi3kbDb9g3w\r\nRB4HmUZ9V6fDq+xtEuqyEau4VOKsjJmleQwSC5k9Ft4RQ5+WtSmld1ic30bA\r\ngjlPApsBdfI9TSswW3Rx3ah0R3vXGlDRo2K/MIrIwb/Jw8XnMsn5QEwxRV1m\r\nhCrQJRGXEUtxugQhAfdd56pZEA/WL4S//tfuidp4lO+PNaR4mgg1NF94cHar\r\nM0cy/SlGW7AB0DmZ5323Jxy/7QX1jVfcUEW2mbCCavEh8CnkvsUdu0sweHvW\r\nIsdb7cbJWUa3aU1VxkJ+x9TYC47Z8N/wStlacbj2EY2ZraqOGkAcT9/m3HSc\r\nmUQdpgxJRAiVCS52yClJOP1QwZV2U9nPRQsQE8tm6uA9qeCXp/CbhLczx3/P\r\nZ5QbtMFwXIt3+xtPxuwQmRhMnlO+jzx8lzaQ1xuXEwHDcrJM9qfdcdh5kWEp\r\n9DF6kf3krDSyTEiZtmed0CUbw1QJAVsjS/Gs6W5jm4GFThl4OQLtUTDLIyiV\r\nleEJ8mVjl7QYOrXlxLu91lg2V0tFFaFGX94=\r\n=Yvbt\r\n-----END PGP SIGNATURE-----\r\n", "size": 28984}, "_hasShrinkwrap": false, "publish_time": 1682416417123, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.0": {"name": "@codemirror/search", "version": "6.5.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-64/M40YeJPToKvGO6p3fijo2vwUEj4nACEAXElCaYQ50HrXSvRaK+NHEhSh73WFBGdvIdhrV+lL9PdJy2RfCYA==", "shasum": "308f9968434e0e6ed59c9ec36a0239eb1dfc5d92", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.0.tgz", "fileCount": 9, "unpackedSize": 131233, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyqKt9tBNbp0XR9HgDJEa4CI6DJlMrYiW+XSwBB686fAIgbh8W7XT57SGGAGFbB37HK0S75MKr8JTvJ9wPopoao34="}], "size": 31213}, "_hasShrinkwrap": false, "publish_time": 1685950626317, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.1": {"name": "@codemirror/search", "version": "6.5.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-4jupk4JwkeVbrN2pStY74q6OJEYqwosB4koA66nyLeVedadtX9MHI38j2vbYmnfDGurDApP3OZO46MrWalcjiQ==", "shasum": "bdd19ba54d314e55df87b7105ec440eaab4521f6", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.1.tgz", "fileCount": 9, "unpackedSize": 131644, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1617CUNQyOUT6U9FC2OPxpvSvEYuWd+rTjyFfCWmEeAiEAuA9rc2V+0GAB9Ideyr+DleaGcnmcbR1A5lD9zYfdj0I="}], "size": 31326}, "_hasShrinkwrap": false, "publish_time": 1691134651899, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.2": {"name": "@codemirror/search", "version": "6.5.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-WRihpqd0l9cEh9J3IZe45Yi+Z5MfTsEXnyc3V7qXHP4ZYtIYpGOn+EJ7fyLIkyAm/8S6QIr7/mMISfAadf8zCg==", "shasum": "01cc58f27c88e902971b9596d89a3af671b1ef9f", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.2.tgz", "fileCount": 9, "unpackedSize": 131281, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtNwQKG9rt5qc154cLUqxIYrgZlhjp9p6N5GI96qQQoAIgICCB0BWZU0VvZEfPE9Q+BzacKFod0Q0IfeTZ0nD57cA="}], "size": 31208}, "_hasShrinkwrap": false, "publish_time": 1693047971747, "_source_registry_name": "default"}, "6.5.3": {"name": "@codemirror/search", "version": "6.5.3", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-M1nGnpUTlOqp0Ywn6V30T8eFcuNFIDfx4+0ja5Wag+qQpL/HZgsIZ7FpE6qZatPziakgj+UXyZTrTUditrkwIQ==", "shasum": "75e53d6cf44400fc03c4470349daad4d08827793", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.3.tgz", "fileCount": 9, "unpackedSize": 131608, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHJCkii5NmbmVgz7jGCvGTagIuaPJMYbKH7Iz/NCYgq4AiEA0SQhgFWXE9zvpP9l40uOJ+vbwK/uYXms/Ql71F7j7xE="}], "size": 31293}, "_hasShrinkwrap": false, "publish_time": 1694669846954, "_source_registry_name": "default"}, "6.5.4": {"name": "@codemirror/search", "version": "6.5.4", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-YoTrvjv9e8EbPs58opjZKyJ3ewFrVSUzQ/4WXlULQLSDDr1nGPJ67mMXFNNVYwdFhybzhrzrtqgHmtpJwIF+8g==", "shasum": "54005697bf581f7dccbbb4a0c34d3a7aa25a513a", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.4.tgz", "fileCount": 9, "unpackedSize": 131755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD57LIahBtKMhKwuEARqIQAZ4gHOYZqS8MZ4RQR2MdkEwIgSF83J5lR08uWXbkhufIRtauSwqFV5BKIr+c2+oMewjk="}], "size": 31347}, "_hasShrinkwrap": false, "publish_time": 1695215657761, "_source_registry_name": "default"}, "6.5.5": {"name": "@codemirror/search", "version": "6.5.5", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-PIEN3Ke1buPod2EHbJsoQwlbpkz30qGZKcnmH1eihq9+bPQx8gelauUwLYaY4vBOuBAuEhmpDLii4rj/uO0yMA==", "shasum": "cf97e201da364da2285c2a250167af25bbd2a4a2", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.5.tgz", "fileCount": 9, "unpackedSize": 132088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFse5pfk5vMkRIX69AwTk/66dRSC9hiLAaerBcpq6JOfAiBNETtYipk/vCYR+Dh8/HBaRDtZY/vJFbKcnkzkTq7SmA=="}], "size": 31463}, "_hasShrinkwrap": false, "publish_time": 1701078549887, "_source_registry_name": "default"}, "6.5.6": {"name": "@codemirror/search", "version": "6.5.6", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-rpMgcsh7o0GuCDUXKPvww+muLA1pDJaFrpq/CCHtpQJYz8xopu4D1hPcKRoDD0YlF8gZaqTNIRa4VRBWyhyy7Q==", "shasum": "8f858b9e678d675869112e475f082d1e8488db93", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.6.tgz", "fileCount": 9, "unpackedSize": 132414, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkq9kqAeoncZqZvOBEx53+yeL8/Wl4JxKs93McWvV8QAIgVnxuts5LfX3ZMxczkSp5TNmS8slrZyB/4aBpvVLgNfM="}], "size": 31595}, "_hasShrinkwrap": false, "publish_time": 1707310275571, "_source_registry_name": "default"}, "6.5.7": {"name": "@codemirror/search", "version": "6.5.7", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-6+iLsXvITWKHYlkgHPCs/qiX4dNzn8N78YfhOFvPtPYCkuXqZq10rAfsUMhOq7O/1VjJqdXRflyExlfVcu/9VQ==", "shasum": "fb60f6637437a8264f86079621a56290dc3814c4", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.7.tgz", "fileCount": 9, "unpackedSize": 133454, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD20qojkCoYZU7lL54gCU4AoOFCmFcnI0zS+8JFYSvOnAIgTMatXGxaXODzGZQGvJ8On9cr9pIiKhW4vVpT7UWva6U="}], "size": 31902}, "_hasShrinkwrap": false, "publish_time": 1730435050787, "_source_registry_name": "default"}, "6.5.8": {"name": "@codemirror/search", "version": "6.5.8", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-PoWtZvo7c1XFeZWmmyaOp2G0XVbOnm+fJzvghqGAktBW3cufwJUWvSCcNG0ppXiBEM05mZu6RhMtXPv2hpllig==", "shasum": "b59b3659b46184cc75d6108d7c050a4ca344c3a0", "tarball": "https://registry.npmmirror.com/@codemirror/search/-/search-6.5.8.tgz", "fileCount": 9, "unpackedSize": 133693, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBm8W4qIRrt0tholIExhkaUu2rxlRWiNl0Mx78eW/kFwIhANEZF1kEqE8kOJgezlXQqYvsys5cQ/oaZMiFg5imC6qM"}], "size": 31972}, "_hasShrinkwrap": false, "publish_time": 1732263248044, "_source_registry_name": "default"}}, "_source_registry_name": "default"}