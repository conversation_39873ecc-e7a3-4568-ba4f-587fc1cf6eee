import { createRouter, createWebHistory } from 'vue-router'
import CarList from '@/views/CarList.vue'
import Login from '@/views/Login.vue'
import UserList from '@/views/UserList.vue'
import NotFound from '@/views/404.vue'
import FeedbackList from '@/views/FeedbackList.vue'
import { ElMessage } from 'element-plus'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    redirect: '/illustration/car'
  },
  {
    path: '/illustration',
    component: () => import('@/views/IllustrationLayout.vue'),
    children: [
      {
        path: 'car',
        name: 'CarList',
        component: () => import('@/views/CarList.vue'),
        meta: { requiresAuth: true, permission: 'car_illustration' }
      },
      {
        path: 'pet',
        name: 'PetList',
        component: () => import('@/views/PetList.vue'),
        meta: { requiresAuth: true, permission: 'pet_illustration' }
      }
    ],
    redirect: '/illustration/car'
  },
  {
    path: '/users',
    name: 'UserList',
    component: UserList,
    meta: { requiresAdmin: true }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound
  },
  {
    path: '/feedback',
    name: 'FeedbackList',
    component: FeedbackList,
    meta: { requiresAdmin: true }
  },
  {
    path: '/sql-executor',
    name: 'SqlExecutor',
    component: () => import('@/views/SqlExecutor.vue'),
    meta: {
      title: 'SQL执行器',
      requiresAuth: true,
      roles: ['admin']  // 确保只有管理员可以访问
    }
  },
  {
    path: '/comments',
    component: () => import('@/views/CommentManagement.vue'),
    meta: {
      requiresAdmin: true,
      title: '评论管理'
    }
  },
  {
    path: '/lottery',
    name: 'LotteryManagement',
    component: () => import('@/views/LotteryManagement.vue'),
    meta: {
      requiresAdmin: true,
      title: '抽奖活动管理'
    }
  },
  {
    path: '/car-treasure',
    name: 'CarTreasureManagement',
    component: () => import('@/views/CarTreasureManagement.vue'),
    meta: {
      requiresAdmin: true,
      title: '赛车夺宝管理'
    }
  },
  {
    path: '/vip-management',
    name: 'VipManagement',
    component: () => import('@/views/VipManagement.vue'),
    meta: {
      requiresAdmin: true,
      title: 'VIP管理'
    }
  },
  {
    path: '/prize-management',
    name: 'PrizeManagement',
    component: () => import('@/views/PrizeManagement.vue'),
    meta: {
      requiresAdmin: true,
      title: '道具与奖品管理'
    }
  },
  // 通配符路由，匹配所有未定义的路由
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFoundRedirect',
    /**
     * 使用beforeEnter直接重定向到404.html
     * 这样可以确保不会进入Vue应用的路由系统
     */
    beforeEnter: (to) => {
      // 检查是否是API请求
      if (to.path.startsWith('/api')) {
        // 如果是API请求，不做处理，让请求正常进行
        return true
      }

      // 直接重定向到404.html静态页面
      window.location.href = '/404.html'
      return false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  const token = localStorage.getItem('token')

  // 检查是否是API请求
  if (to.path.startsWith('/api')) {
    // 如果是API请求，不做处理，让请求正常进行
    next()
    return
  }

  // 检查是否是已定义的路由
  const isDefinedRoute = routes.some(route => {
    if (route.path === to.path) return true
    if (route.path === to.matched[0]?.path) return true
    return false
  })

  // 如果不是已定义的路由，重定向到404.html
  if (!isDefinedRoute && to.name !== 'NotFoundRedirect' && to.path !== '/404') {
    window.location.href = '/404.html'
    return
  }

  // 对于登录页和404页面，允许直接访问
  if (to.path === '/login' || to.path === '/404') {
    if (token) {
      if (to.path === '/login') {
        next('/')
      } else {
        next()
      }
    } else {
      next()
    }
    return
  }

  // 对于其他页面，检查登录状态
  if (!token) {
    next('/login')
    return
  }

  // 检查管理员权限
  if (to.meta.requiresAdmin) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      if (!payload.is_admin) {
        ElMessage.error('需要管理员权限')
        next('/')
        return
      }
    } catch (error) {
      console.error('解析token失败', error)
      localStorage.removeItem('token')
      next('/login')
      return
    }
  }

  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误', error)
  // 如果是动态导入的组件加载失败，可以重定向到404页面
  if (error.message.includes('Failed to load module') || error.message.includes('Failed to fetch dynamically imported module')) {
    router.push('/404')
  }
})

export default router
