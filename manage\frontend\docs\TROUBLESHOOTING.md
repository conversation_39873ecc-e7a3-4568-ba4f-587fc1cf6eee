# QQ飞车赛车图鉴前端问题跟踪文档

本文档用于记录在开发和维护过程中遇到的问题及其解决方案。

## 2024-01-19

### 1. 图片上传接口 405 错误

**问题描述：**
在赛车图片上传时遇到 405 Method Not Allowed 错误，提示 "方法 'POST' 不被允许"。

**原因分析：**
前端请求的接口路径与后端实际提供的接口路径不匹配。前端使用了 `/cars/upload-image/`，而实际后端接口是 `/cars/upload_image/`。

**解决方案：**
1. 修改 `src/api/car.js` 中的图片上传接口路径
2. 将 `upload-image` 改为 `upload_image`
3. 保持与后端接口路径一致

**相关代码：**
```javascript
// 上传图片
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('image', file)
  return request({
    url: '/cars/upload_image/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

**API 接口说明：**
- 请求方法：POST
- 请求路径：/api/cars/upload_image/
- Content-Type: multipart/form-data
- 请求参数：
  ```json
  {
    "image": "(binary file data)"
  }
  ```
- 响应格式：
  ```json
  {
    "success": true,
    "message": "图片上传成功",
    "image_id": "550e8400-e29b-41d4-a716-446655440000.jpg"
  }
  ```

### 最佳实践建议

1. API 接口命名规范：
   - 建议统一使用下划线命名法（snake_case）或连字符命名法（kebab-case），避免混用
   - 在项目初期就确定命名规范，并在前后端团队间达成一致

2. 接口文档维护：
   - 及时更新接口文档
   - 明确记录接口的请求方法、路径、参数和响应格式
   - 添加示例代码和响应示例 