<template>
  <div class="car-list">
    <div v-if="permissionChecking" class="loading-container">
      <el-loading :fullscreen="true" text="权限检查中..." />
    </div>
    <div v-else-if="!hasPermission" class="no-permission">
      <el-empty
        description="您没有查看赛车图鉴的权限"
      >
        <template #extra>
          <p>请联系管理员开通权限</p>
        </template>
      </el-empty>
    </div>
    <template v-else>
      <div class="content-container" v-loading="loading">
        <div class="header-operations">
          <div class="title-section">
            <h2 class="page-title">赛车图鉴管理</h2>
            <div class="operation-buttons">
              <el-upload
                class="excel-upload"
                accept=".xlsx, .xls"
                :show-file-list="false"
                :before-upload="handleExcelImport"
              >
                <el-button type="primary" class="action-button">
                  <el-icon><Upload /></el-icon>
                  导入Excel
                </el-button>
              </el-upload>
              <el-button 
                type="success" 
                @click="handleExcelExport"
                :loading="exporting"
                :disabled="exporting"
                class="action-button"
              >
                <el-icon><Download /></el-icon>
                {{ exporting ? '正在导出...' : '导出Excel' }}
              </el-button>
              <el-button type="primary" @click="showAddDialog" class="action-button add-button">
                <el-icon><Plus /></el-icon>
                添加赛车
              </el-button>
            </div>
          </div>
          <div class="search-filters">
            <div class="search-input-group">
              <label class="filter-label">搜索</label>
              <el-input
                v-model="searchQuery"
                placeholder="输入赛车名称或编号"
                class="search-input"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon class="search-icon"><Search /></el-icon>
                </template>
              </el-input>
            </div>
            
            <div class="filter-divider"></div>
            
            <div class="filter-group">
              <div class="filter-item">
                <label class="filter-label">级别</label>
                <el-select
                  v-model="levelFilter"
                  placeholder="选择级别"
                  clearable
                  class="filter-select"
                  @change="handleLevelChange"
                >
                  <template #prefix>
                    <el-icon><Medal /></el-icon>
                  </template>
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="option in levelOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>
              
              <div class="filter-item">
                <label class="filter-label">排序字段</label>
                <el-select
                  v-model="sortBy"
                  placeholder="选择排序字段"
                  clearable
                  class="filter-select sort-field-select"
                  @change="handleSortChange"
                >
                  <template #prefix>
                    <el-icon><Sort /></el-icon>
                  </template>
                  <el-option label="默认排序" value="" />
                  <el-option label="平跑极速" value="normal_speed" />
                  <el-option label="氮气极速" value="nitro_speed" />
                  <el-option label="夹角平跑极速" value="angle_normal_speed" />
                  <el-option label="夹角氮气极速" value="angle_nitro_speed" />
                  <el-option label="平跑180提速" value="normal_180_acceleration" />
                  <el-option label="平跑极速提速" value="normal_speed_acceleration" />
                  <el-option label="大喷250提速" value="nitro_250_acceleration" />
                  <el-option label="大喷290提速" value="nitro_290_acceleration" />
                  <el-option label="夹角平跑极速(推进40)" value="angle_normal_speed_advance40" />
                  <el-option label="夹角氮气极速(推进40)" value="angle_nitro_speed_advance40" />
                  <el-option label="平跑180提速(推进40)" value="normal_180_acceleration_advance40" />
                  <el-option label="平跑极速提速(推进40)" value="normal_speed_acceleration_advance40" />
                  <el-option label="大喷250提速(推进40)" value="nitro_250_acceleration_advance40" />
                  <el-option label="大喷290提速(推进40)" value="nitro_290_acceleration_advance40" />
                  <el-option label="燃料时长" value="fuel_duration" />
                  <el-option label="燃料强度" value="fuel_intensity" />
                  <el-option label="点火时长" value="ignition_duration" />
                  <el-option label="点火强度" value="ignition_intensity" />
                  <el-option label="原装进气系数" value="original_intake_coefficient" />
                  <el-option label="进气系数" value="intake_coefficient" />
                  <el-option label="漂移转向" value="drift_steering" />
                  <el-option label="漂移摆动" value="drift_swing" />
                  <el-option label="漂移反向" value="drift_reverse" />
                  <el-option label="漂移回正" value="drift_correction" />
                  <el-option label="超级喷强度" value="super_nitro_intensity" />
                  <el-option label="超级喷时长" value="super_nitro_duration" />
                  <el-option label="超级喷触发条件" value="super_nitro_trigger_condition" />
                  <el-option label="超级喷250提速" value="super_nitro_250_acceleration" />
                  <el-option label="超级喷290提速" value="super_nitro_290_acceleration" />
                  <el-option label="夹角超级喷极速" value="angle_super_nitro_speed" />
                  <el-option label="更新时间" value="updated_at" />
                </el-select>
              </div>
              
              <div class="filter-item">
                <label class="filter-label">排序方向</label>
                <el-select
                  v-model="sortOrder"
                  placeholder="排序方向"
                  class="filter-select direction-select"
                  @change="handleSortChange"
                >
                  <template #prefix>
                    <el-icon><ArrowDown v-if="sortOrder === 'desc'" /><ArrowUp v-else /></el-icon>
                  </template>
                  <el-option label="升序" value="asc">
                    <div class="sort-option">
                      <el-icon><ArrowUp /></el-icon>
                      <span>升序</span>
                    </div>
                  </el-option>
                  <el-option label="降序" value="desc">
                    <div class="sort-option">
                      <el-icon><ArrowDown /></el-icon>
                      <span>降序</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="3" v-for="car in cars" :key="car.id">
            <el-card :body-style="{ padding: '0px' }" class="car-card">
              <img 
                v-lazy="car.image || '/default-car.png'"
                class="car-image"
                loading="lazy"
                :alt="car.name"
                @error="handleImageError"
              >
              <div class="car-info">
                <h3>{{ car.name }}</h3>
                <p>编号: {{ car.car_id }}</p>
                <p>级别: {{ car.level }}</p>
                <div class="performance-info">
                  <p>平跑极速: {{ car.normal_speed }}</p>
                  <p>氮气极速: {{ car.nitro_speed }}</p>
                  <p>漂移速率: {{ car.drift_factor }}</p>
                  <p>摩擦系数: {{ car.friction_factor }}</p>
                  <p>车重: {{ car.weight }}</p>
                  <p>最大转向: {{ car.low_speed_steering }}</p>
                  <p>最小转向: {{ car.high_speed_steering }}</p>
                </div>
                <div class="update-time">更新于 {{ formatDateTime(car.updated_at) }}</div>
                <div class="card-operations">
                  <el-button type="primary" @click="handleEdit(car)">编辑</el-button>
                  <el-button type="danger" @click="handleDelete(car)">删除</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            @current-change="handlePageChange"
            layout="prev, pager, next"
          />
        </div>
      </div>

      <!-- 添加/编辑对话框 -->
      <el-dialog
        :title="dialogType === 'add' ? '添加赛车' : '编辑赛车'"
        v-model="dialogVisible"
        width="85%"
        top="5vh"
        :destroy-on-close="true"
        class="car-edit-dialog"
      >
        <el-form :model="carForm" label-width="110px" class="compact-form">
          <div class="form-container">
            <!-- 左侧区域：基本信息、车辆特性 -->
            <div class="form-left-column">
              <!-- 基本信息 -->
              <div class="form-section compact-section">
                <h3 class="section-title">基本信息</h3>
                <div class="base-info-grid">
                  <el-form-item label="编号" class="compact-form-item">
                    <el-input v-model="carForm.car_id" />
                  </el-form-item>
                  <el-form-item label="名称" required class="compact-form-item">
                    <el-input v-model="carForm.name" />
                  </el-form-item>
                  <el-form-item label="级别" required class="compact-form-item">
                    <el-select v-model="carForm.level" placeholder="请选择赛车级别">
                      <el-option
                        v-for="option in levelOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="宝石槽位" class="compact-form-item">
                    <el-input v-model="carForm.gem_slots" />
                  </el-form-item>
                </div>
              </div>

              <!-- 基础速度数据 -->
              <div class="form-section compact-section">
                <h3 class="section-title">基础速度</h3>
                <div class="base-info-grid">
                  <el-form-item label="平跑极速" class="compact-form-item">
                    <el-input v-model="carForm.normal_speed" />
                  </el-form-item>
                  <el-form-item label="氮气极速" class="compact-form-item">
                    <el-input v-model="carForm.nitro_speed" />
                  </el-form-item>
                </div>
              </div>

              <!-- 车辆特性 -->
              <div class="form-section compact-section">
                <h3 class="section-title">车辆特性</h3>
                <div class="base-info-grid">
                  <el-form-item label="漂移速率" class="compact-form-item">
                    <el-input v-model="carForm.drift_factor" />
                  </el-form-item>
                  <el-form-item label="摩擦系数" class="compact-form-item">
                    <el-input v-model="carForm.friction_factor" />
                  </el-form-item>
                  <el-form-item label="车重" class="compact-form-item">
                    <el-input v-model="carForm.weight" />
                  </el-form-item>
                  <el-form-item label="悬挂" class="compact-form-item">
                    <el-input v-model="carForm.suspension" />
                  </el-form-item>
                  <el-form-item label="最大转向" class="compact-form-item">
                    <el-input v-model="carForm.low_speed_steering" />
                  </el-form-item>
                  <el-form-item label="最小转向" class="compact-form-item">
                    <el-input v-model="carForm.high_speed_steering" />
                  </el-form-item>
                </div>
              </div>

              <!-- 引擎档位 -->
              <div class="form-section compact-section">
                <h3 class="section-title">引擎档位</h3>
                <div class="base-info-grid three-columns">
                  <el-form-item label="引擎1挡" class="compact-form-item">
                    <el-input v-model="carForm.engine_gear_1" />
                  </el-form-item>
                  <el-form-item label="引擎2挡" class="compact-form-item">
                    <el-input v-model="carForm.engine_gear_2" />
                  </el-form-item>
                  <el-form-item label="引擎3挡" class="compact-form-item">
                    <el-input v-model="carForm.engine_gear_3" />
                  </el-form-item>
                  <el-form-item label="引擎4挡" class="compact-form-item">
                    <el-input v-model="carForm.engine_gear_4" />
                  </el-form-item>
                  <el-form-item label="引擎5挡" class="compact-form-item">
                    <el-input v-model="carForm.engine_gear_5" />
                  </el-form-item>
                  <el-form-item label="引擎6挡" class="compact-form-item">
                    <el-input v-model="carForm.engine_gear_6" />
                  </el-form-item>
                </div>
              </div>

              <!-- 推进档位 -->
              <div class="form-section compact-section">
                <h3 class="section-title">原装推进档位</h3>
                <div class="base-info-grid three-columns">
                  <el-form-item label="原装推进1挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_1" />
                  </el-form-item>
                  <el-form-item label="原装推进2挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_2" />
                  </el-form-item>
                  <el-form-item label="原装推进3挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_3" />
                  </el-form-item>
                  <el-form-item label="原装推进4挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_4" />
                  </el-form-item>
                  <el-form-item label="原装推进5挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_5" />
                  </el-form-item>
                  <el-form-item label="原装推进6挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_6" />
                  </el-form-item>
                  <el-form-item label="原装推进7挡" class="compact-form-item">
                    <el-input v-model="carForm.original_propulsion_7" />
                  </el-form-item>
                </div>
              </div>
            </div>

            <!-- 中间区域：性能参数 -->
            <div class="form-middle-column">
              <!-- 夹角极速参数组 -->
              <div class="form-section compact-section">
                <h3 class="section-title">夹角极速</h3>
                <div class="param-group compact-group">
                  <div class="param-group-header">
                    <div class="param-label">夹角平跑极速</div>
                    <div class="param-label">夹角氮气极速</div>
                  </div>
                  <div class="param-group-content">
                    <div class="param-row">
                      <div class="param-type">0推进</div>
                      <div class="param-input">
                        <el-input v-model="carForm.angle_normal_speed" />
                      </div>
                      <div class="param-input">
                        <el-input v-model="carForm.angle_nitro_speed" />
                      </div>
                    </div>
                    <div class="param-row">
                      <div class="param-type">40推进</div>
                      <div class="param-input">
                        <el-input v-model="carForm.angle_normal_speed_advance40" />
                      </div>
                      <div class="param-input">
                        <el-input v-model="carForm.angle_nitro_speed_advance40" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 平跑提速参数组 -->
              <div class="form-section compact-section">
                <h3 class="section-title">平跑提速</h3>
                <div class="param-group compact-group">
                  <div class="param-group-header">
                    <div class="param-label">平跑180提速</div>
                    <div class="param-label">平跑极速提速</div>
                  </div>
                  <div class="param-group-content">
                    <div class="param-row">
                      <div class="param-type">0推进</div>
                      <div class="param-input">
                        <el-input v-model="carForm.normal_180_acceleration" />
                      </div>
                      <div class="param-input">
                        <el-input v-model="carForm.normal_speed_acceleration" />
                      </div>
                    </div>
                    <div class="param-row">
                      <div class="param-type">40推进</div>
                      <div class="param-input">
                        <el-input v-model="carForm.normal_180_acceleration_advance40" />
                      </div>
                      <div class="param-input">
                        <el-input v-model="carForm.normal_speed_acceleration_advance40" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 大喷提速参数组 -->
              <div class="form-section compact-section">
                <h3 class="section-title">大喷提速</h3>
                <div class="param-group compact-group">
                  <div class="param-group-header">
                    <div class="param-label">大喷250提速</div>
                    <div class="param-label">大喷290提速</div>
                  </div>
                  <div class="param-group-content">
                    <div class="param-row">
                      <div class="param-type">0推进</div>
                      <div class="param-input">
                        <el-input v-model="carForm.nitro_250_acceleration" />
                      </div>
                      <div class="param-input">
                        <el-input v-model="carForm.nitro_290_acceleration" />
                      </div>
                    </div>
                    <div class="param-row">
                      <div class="param-type">40推进</div>
                      <div class="param-input">
                        <el-input v-model="carForm.nitro_250_acceleration_advance40" />
                      </div>
                      <div class="param-input">
                        <el-input v-model="carForm.nitro_290_acceleration_advance40" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 新增：燃料系统 -->
              <div class="form-section compact-section">
                <h3 class="section-title">燃料数据</h3>
                <div class="base-info-grid">
                  <el-form-item label="燃料时长" class="compact-form-item">
                    <el-input v-model="carForm.fuel_duration" />
                  </el-form-item>
                  <el-form-item label="燃料强度" class="compact-form-item">
                    <el-input v-model="carForm.fuel_intensity" />
                  </el-form-item>
                </div>
              </div>
              
              <!-- 新增：点火系统 -->
              <div class="form-section compact-section">
                <h3 class="section-title">点火数据</h3>
                <div class="base-info-grid">
                  <el-form-item label="点火时长" class="compact-form-item">
                    <el-input v-model="carForm.ignition_duration" />
                  </el-form-item>
                  <el-form-item label="点火强度" class="compact-form-item">
                    <el-input v-model="carForm.ignition_intensity" />
                  </el-form-item>
                </div>
              </div>
            </div>

            <!-- 右侧区域：特殊参数与图片 -->
            <div class="form-right-column">

              
              <!-- 新增：漂移参数 -->
              <div class="form-section compact-section">
                <h3 class="section-title">漂移参数</h3>
                <div class="base-info-grid">
                  <el-form-item label="漂移转向" class="compact-form-item">
                    <el-input v-model="carForm.drift_steering" />
                  </el-form-item>
                  <el-form-item label="漂移摆动" class="compact-form-item">
                    <el-input v-model="carForm.drift_swing" />
                  </el-form-item>
                  <el-form-item label="漂移反向" class="compact-form-item">
                    <el-input v-model="carForm.drift_reverse" />
                  </el-form-item>
                  <el-form-item label="漂移回正" class="compact-form-item">
                    <el-input v-model="carForm.drift_correction" />
                  </el-form-item>
                </div>
              </div>

              <!-- 新增：超级喷 -->
              <div class="form-section compact-section">
                <h3 class="section-title">超级喷</h3>
                <div class="base-info-grid">
                  <el-form-item label="超级喷强度" class="compact-form-item">
                    <el-input v-model="carForm.super_nitro_intensity" />
                  </el-form-item>
                  <el-form-item label="超级喷时长" class="compact-form-item">
                    <el-input v-model="carForm.super_nitro_duration" />
                  </el-form-item>
                  <el-form-item label="超级喷250提速" class="compact-form-item">
                    <el-input v-model="carForm.super_nitro_250_acceleration" />
                  </el-form-item>
                  <el-form-item label="超级喷290提速" class="compact-form-item">
                    <el-input v-model="carForm.super_nitro_290_acceleration" />
                  </el-form-item>
                  <el-form-item label="夹角超级喷极速" class="compact-form-item">
                    <el-input v-model="carForm.angle_super_nitro_speed" />
                  </el-form-item>
                  <el-form-item label="触发条件" class="compact-form-item full-width">
                    <el-input 
                      v-model="carForm.super_nitro_trigger_condition"
                      placeholder="输入超级喷触发条件..."
                    />
                  </el-form-item>
                </div>
              </div>

              <!-- 赛车图片 -->
              <div class="form-section compact-section image-section">
                <h3 class="section-title">赛车图片</h3>
                <el-form-item class="image-form-item">
                  <el-upload
                    class="car-image-upload"
                    :show-file-list="false"
                    :before-upload="beforeImageUpload"
                    :http-request="handleImageUpload"
                  >
                    <img v-if="carForm.image" :src="carForm.image" class="preview-image">
                    <div v-else class="upload-placeholder">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-tip">点击上传图片</div>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 导入确认对话框 -->
      <el-dialog
        v-model="importDialogVisible"
        title="导入Excel"
        width="30%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div v-if="!importing">
          <p>确认导入文件: {{ selectedFile?.name }}</p>
          <p class="import-tip">注意：导入将覆盖已有的重复数据</p>
        </div>
        <div v-else class="importing-progress">
          <el-progress type="circle" :percentage="importProgress" />
          <p class="progress-text">正在导入数据，请稍候...</p>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelImport" :disabled="importing">取消</el-button>
            <el-button type="primary" @click="confirmImport" :loading="importing">
              {{ importing ? '导入中' : '确认导入' }}
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加修改密码对话框 -->
      <el-dialog
        v-model="passwordDialogVisible"
        title="修改密码"
        width="400px"
      >
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="100px"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              show-password
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="passwordDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCarList, createCar, updateCar, deleteCar, importCars, exportCars, uploadImage, getCarLevels } from '@/api/car'
import { IMAGE_URL, MEDIA_URL } from '@/config'
import { read as readExcelFile, utils as xlsxUtils } from 'xlsx'
import { useRouter } from 'vue-router'
import { testAuth } from '@/api/auth'
import { ArrowDown, User, SwitchButton, Key, Medal, Sort, ArrowUp } from '@element-plus/icons-vue'

const router = useRouter()
const searchQuery = ref('')
const dialogVisible = ref(false)
const dialogType = ref('add')
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(24)
const total = ref(0)
const cars = ref([])
const sortBy = ref('')
const sortOrder = ref('desc')

// 表单数据
const carForm = ref({
  car_id: '',
  name: '',
  level: '',
  gem_slots :'',
  normal_speed: 0,
  nitro_speed: 0,
  drift_factor: 0,
  friction_factor: 0,
  weight: 0,
  low_speed_steering: 0,
  high_speed_steering: 0,
  engine_gear_1: 0,
  engine_gear_2: 0,
  engine_gear_3: 0,
  engine_gear_4: 0,
  engine_gear_5: 0,
  engine_gear_6: 0,
  original_propulsion_1: 0,
  original_propulsion_2: 0,
  original_propulsion_3: 0,
  original_propulsion_4: 0,
  original_propulsion_5: 0,
  original_propulsion_6: 0,
  original_propulsion_7: 0,
  suspension: 0,
  angle_normal_speed: 0,
  angle_nitro_speed: 0,
  normal_180_acceleration: 0,
  normal_speed_acceleration: 0,
  nitro_250_acceleration: 0,
  nitro_290_acceleration: 0,
  angle_normal_speed_advance40: 0,
  angle_nitro_speed_advance40: 0,
  normal_180_acceleration_advance40: 0,
  normal_speed_acceleration_advance40: 0,
  nitro_250_acceleration_advance40: 0,
  nitro_290_acceleration_advance40: 0,
  fuel_duration: '',
  fuel_intensity: '',
  ignition_duration: '',
  ignition_intensity: '',
  original_intake_coefficient: '',
  intake_coefficient: '',
  drift_steering: '',
  drift_swing: '',
  drift_reverse: '',
  drift_correction: '',
  super_nitro_intensity: '',
  super_nitro_duration: '',
  super_nitro_trigger_condition: '',
  super_nitro_250_acceleration: '',
  super_nitro_290_acceleration: '',
  angle_super_nitro_speed: '',
  image_id: '',
  image: ''
})

// 获取图片 URL
const getImageUrl = (imageId) => {
  if (!imageId) return '/default-car.png'
  return `${IMAGE_URL}/${imageId}`
}

const loadCars = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      search: searchQuery.value || undefined,
      level: levelFilter.value || undefined,
      sort_by: sortBy.value || undefined,
      order: sortOrder.value || undefined
    }
    const response = await getCarList(params)
    cars.value = response.results.map(car => ({
      ...car,
      image: car.image_id ? getImageUrl(car.image_id) : null
    }))
    total.value = response.count
  } catch (error) {
    console.error('Failed to load cars:', error)
    ElMessage.error('加载赛车列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadCars()
}

// 显示添加对话框
const showAddDialog = () => {
  dialogType.value = 'add'
  carForm.value = {
    car_id: '',
    name: '',
    level: '',
    normal_speed: 0,
    nitro_speed: 0,
    drift_factor: 0,
    friction_factor: 0,
    weight: 0,
    low_speed_steering: 0,
    high_speed_steering: 0,
    engine_gear_1: 0,
    engine_gear_2: 0,
    engine_gear_3: 0,
    engine_gear_4: 0,
    engine_gear_5: 0,
    engine_gear_6: 0,
    original_propulsion_1: 0,
    original_propulsion_2: 0,
    original_propulsion_3: 0,
    original_propulsion_4: 0,
    original_propulsion_5: 0,
    original_propulsion_6: 0,
    original_propulsion_7: 0,
    suspension: 0,
    angle_normal_speed: 0,
    angle_nitro_speed: 0,
    normal_180_acceleration: 0,
    normal_speed_acceleration: 0,
    nitro_250_acceleration: 0,
    nitro_290_acceleration: 0,
    angle_normal_speed_advance40: 0,
    angle_nitro_speed_advance40: 0,
    normal_180_acceleration_advance40: 0,
    normal_speed_acceleration_advance40: 0,
    nitro_250_acceleration_advance40: 0,
    nitro_290_acceleration_advance40: 0,
    fuel_duration: '',
    fuel_intensity: '',
    ignition_duration: '',
    ignition_intensity: '',
    original_intake_coefficient: '',
    intake_coefficient: '',
    drift_steering: '',
    drift_swing: '',
    drift_reverse: '',
    drift_correction: '',
    super_nitro_intensity: '',
    super_nitro_duration: '',
    super_nitro_trigger_condition: '',
    super_nitro_250_acceleration: '',
    super_nitro_290_acceleration: '',
    angle_super_nitro_speed: '',
    image_id: '',
    image: ''
  }
  dialogVisible.value = true
}

// 编辑赛车
const handleEdit = (car) => {
  dialogType.value = 'edit'
  carForm.value = { ...car }
  dialogVisible.value = true
}

// 删除赛车
const handleDelete = (car) => {
  ElMessageBox.confirm(
    `确定要删除赛车 ${car.name} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteCar(car.id)
      ElMessage.success('删除成功')
      loadCars()
    } catch (error) {
      console.error('Failed to delete car:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 提交表单
const handleSubmit = async () => {
  try {
    if (dialogType.value === 'add') {
      await createCar(carForm.value)
      ElMessage.success('添加成功')
    } else {
      await updateCar(carForm.value.id, carForm.value)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    setTimeout(() => {
      loadCars()
    }, 300)
  } catch (error) {
    console.error('Form submission failed:', error)
    ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败')
  }
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  loadCars()
}

// Excel导入导出
const handleExcelImport = (file) => {
  selectedFile.value = file
  importDialogVisible.value = true
  return false // 阻止自动上传
}

// 添加导出加载状态
const exporting = ref(false)

const handleExcelExport = async () => {
  if (exporting.value) return // 防止重复点击
  
  try {
    exporting.value = true
    ElMessage({
      message: '正在导出数据，请稍候...',
      type: 'info',
      duration: 0
    })
    
    // 构建导出参数，与查询列表使用相同的参数
    const params = {
      search: searchQuery.value || undefined,
      level: levelFilter.value || undefined,
      sort_by: sortBy.value || undefined,
      order: sortOrder.value || undefined
    }
    
    const response = await exportCars(params)
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    // 根据筛选条件构建文件名
    let fileName = '赛车列表'
    if (params.search) fileName += `_搜索_${params.search}`
    if (params.level) fileName += `_${params.level}级`
    if (params.sort_by) fileName += `_排序_${params.sort_by}_${params.order}`
    fileName += '.xlsx'
    
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(link.href)
    
    // 关闭之前的消息
    ElMessage.closeAll()
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export Excel:', error)
    ElMessage.closeAll()
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 级别筛选
const levelFilter = ref('')
const handleLevelChange = () => {
  currentPage.value = 1
  loadCars()
}

// 排序处理
const handleSortChange = () => {
  currentPage.value = 1
  loadCars()
}

// 图片上传相关
const handleImageSuccess = async (response) => {
  if (response.success) {
    carForm.value.image_id = response.image_id
    carForm.value.image = getImageUrl(response.image_id)
    ElMessage.success(response.message || '图片上传成功')
    // 如果是编辑模式，立即更新卡片数据
    if (dialogType.value === 'edit') {
      try {
        await updateCar(carForm.value.id, carForm.value)
        // 等待一小段时间后刷新数据，确保服务器处理完成
        setTimeout(() => {
          loadCars()
        }, 300)
      } catch (error) {
        console.error('Failed to update car after image upload:', error)
        ElMessage.error('更新图片信息失败')
      }
    }
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleImageUpload = async (file) => {
  try {
    const response = await uploadImage(file.file)
    handleImageSuccess(response)
    return false
  } catch (error) {
    console.error('Failed to upload image:', error)
    ElMessage.error('图片上传失败')
    return false
  }
}

// 添加错误处理函数
const handleImageError = (e) => {
  console.error('Image load failed:', e.target.src)
  e.target.src = '/default-car.png'
  // 如果不是默认图片加载失败，尝试重新加载一次
  if (!e.target.src.endsWith('default-car.png')) {
    setTimeout(() => {
      const timestamp = new Date().getTime()
      const originalSrc = e.target.src.split('?')[0]
      e.target.src = `${originalSrc}?t=${timestamp}`
    }, 1000)
  }
}

// 添加权限状态
const hasPermission = ref(false)
const permissionChecking = ref(true)

// 检查权限
const checkPermission = async () => {
  try {
    permissionChecking.value = true
    // 从 localStorage 获取权限状态
    const hasCarPermission = localStorage.getItem('has_car_permission')
    console.log('Has car permission:', hasCarPermission)
    hasPermission.value = hasCarPermission === 'true' || hasCarPermission === true
    
    if (!hasPermission.value) {
      router.push('/404')
      return false
    }
    return true
  } catch (error) {
    console.error('Failed to check permission:', error)
    router.push('/404')
    return false
  } finally {
    permissionChecking.value = false
  }
}

// 初始加载
onMounted(() => {
  checkAuth().then(async () => {
    const hasAccess = await checkPermission()
    if (hasAccess) {
      loadLevels()
      loadCars()
    }
  })
})

// 图片懒加载指令
const vLazy = {
  mounted(el, binding) {
    useIntersectionObserver(el, ([{ isIntersecting }]) => {
      if (isIntersecting) {
        // 强制浏览器重新加载图片
        el.src = binding.value + (binding.value.includes('?') ? '&' : '?') + 'v=' + Date.now()
        el.style.opacity = 1
      }
    })
  },
  // 添加 updated 钩子以响应 binding.value 的变化
  updated(el, binding) {
    if (binding.value !== binding.oldValue) {
      el.src = binding.value + (binding.value.includes('?') ? '&' : '?') + 'v=' + Date.now()
      el.style.opacity = 1
    }
  }
}

// 在已有的 ref 声明后添加
const importDialogVisible = ref(false)
const importing = ref(false)
const importProgress = ref(0)
const selectedFile = ref(null)

// 添加新的方法
const cancelImport = () => {
  importDialogVisible.value = false
  selectedFile.value = null
  importProgress.value = 0
}

const confirmImport = async () => {
  if (!selectedFile.value) return
  
  try {
    importing.value = true
    
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (importProgress.value < 90) {
        importProgress.value += 10
      }
    }, 200)

    // 实际导入操作
    await importCars(selectedFile.value)
    
    // 完成导入
    clearInterval(progressInterval)
    importProgress.value = 100
    
    setTimeout(() => {
      importing.value = false
      importDialogVisible.value = false
      importProgress.value = 0
      selectedFile.value = null
      
      ElMessage.success('导入成功')
      loadCars()
    }, 500)
    
  } catch (error) {
    console.error('Import failed:', error)
    ElMessage.error('导入失败')
    importing.value = false
    importProgress.value = 0
  }
}

const username = ref('')
const isAdmin = ref(false)

// 检查用户登录状态
const checkAuth = async () => {
  try {
    const response = await testAuth()
    if (response.success) {
      username.value = response.user.username
      // 直接从响应中获取管理员状态
      isAdmin.value = response.user.is_admin
    } else {
      throw new Error('认证失败')
    }
  } catch (error) {
    console.error('Auth check failed:', error)
    localStorage.removeItem('token')
    router.push('/login')
  }
}

const passwordDialogVisible = ref(false)
const passwordLoading = ref(false)
const passwordFormRef = ref(null)
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    // 从 token 中获取用户 ID
    const token = localStorage.getItem('token')
    const payload = JSON.parse(atob(token.split('.')[1]))
    const userId = payload.id
    
    await updatePassword({
      userId,
      data: {
        old_password: passwordForm.value.oldPassword,
        new_password: passwordForm.value.newPassword
      }
    })
    
    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
    passwordForm.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  } catch (error) {
    console.error('Failed to change password:', error)
    ElMessage.error(error.response?.data?.message || '修改密码失败')
  } finally {
    passwordLoading.value = false
  }
}

// 添加等级列表的响应式引用
const levelOptions = ref([])

// 添加获取等级列表的方法
const loadLevels = async () => {
  try {
    const response = await getCarLevels()
    if (response.success) {
      levelOptions.value = response.levels
    }
  } catch (error) {
    console.error('Failed to load car levels:', error)
    ElMessage.error('加载赛车等级列表失败')
  }
}

// 添加日期格式化函数
const formatDateTime = (dateString) => {
  if (!dateString) return '暂无记录'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  }).replace(/\//g, '-')
}
</script>

<style scoped>
.car-list {
  padding: 0;
  background-color: transparent;
}

.content-container {
  padding: 0;
}

.header-operations {
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.page-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #409eff;
  border-radius: 2px;
}

.action-button {
  transition: all 0.2s;
}

.add-button {
  background-color: #67c23a;
  border-color: #67c23a;
}

.add-button:hover {
  background-color: #85ce61;
  border-color: #85ce61;
}

.search-filters {
  display: flex;
  gap: 20px;
  align-items: center;
  background-color: #f8fafb;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  flex-wrap: wrap;
}

.search-input-group {
  display: flex;
  align-items: center;
  min-width: 300px;
  position: relative;
}

.search-input-group::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #ebeef5;
  display: none;
}

@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .search-input-group {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .search-input-group::after {
    display: block;
  }
  
  .filter-divider {
    display: none;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-item {
    width: 100%;
  }
  
  .filter-select, 
  .sort-field-select, 
  .direction-select,
  .search-input {
    width: 100%;
  }
}

:deep(.el-select__tags) {
  max-width: 70%;
}

.filter-select :deep(.el-input__prefix) {
  color: #909399;
}

.search-input :deep(.el-input__inner) {
  padding-left: 30px;
}

.filter-select :deep(.el-input__inner) {
  padding-left: 30px;
}

.operation-buttons {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 0 8px;
}

.filter-divider {
  width: 1px;
  height: 24px;
  background-color: #ebeef5;
  margin: 0 16px;
}

.filter-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  font-size: 13px;
  white-space: nowrap;
}

.search-input {
  width: 240px;
}

.search-icon {
  color: #409eff;
}

.filter-select {
  width: 160px;
}

.sort-field-select {
  width: 180px;
}

.direction-select {
  width: 120px;
}

.sort-option {
  display: flex;
  align-items: center;
  gap: 6px;
}

:deep(.el-input__wrapper), :deep(.el-select__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 6px;
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover), :deep(.el-select__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus), :deep(.el-select__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset !important;
}

.car-card {
  margin-bottom: 25px;
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
}

.car-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.car-image {
  width: 100%;
  height: 140px;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0;
  background-color: #f5f7fa;
}

.car-card:hover .car-image {
  transform: scale(1.05);
}

.car-info {
  padding: 16px;
  background: white;
  display: flex;
  flex-direction: column;
  height: 380px;
}

.car-info h3 {
  margin: 0;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.car-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.performance-info {
  margin: 8px 0;
  padding: 8px;
  background-color: #f8f9fb;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  font-size: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.performance-info p {
  margin: 3px 0;
  color: #5c6b7f;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 18px;
}

.card-operations {
  margin-top: auto;
  padding-top: 12px;
  display: flex;
  gap: 8px;
  border-top: 1px solid #ebeef5;
}

.card-operations .el-button {
  flex: 1;
  padding: 8px 12px;
}

.pagination {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  margin-top: 6px;
  border-top: 1px solid #ebeef5;
}

.image-section {
  height: calc(100% - 15px);
  display: flex;
  flex-direction: column;
}

.image-section .el-form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  margin-bottom: 0;
}

.image-section .el-form-item__content {
  flex: 1;
}

.car-image-upload {
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: 260px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.car-image-upload:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.car-image-upload :deep(.el-upload) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 240px;
}

.upload-icon {
  font-size: 28px;
  color: #909399;
}

.upload-tip {
  font-size: 13px;
  color: #909399;
}

.form-section .el-row {
  margin-bottom: 0;
}

.form-section .el-col {
  padding-bottom: 0;
}

.import-tip {
  color: #e6a23c;
  font-size: 14px;
  margin-top: 8px;
}

.importing-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.progress-text {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

:deep(.el-progress__text) {
  font-size: 14px !important;
}

:deep(.el-progress-circle) {
  width: 120px !important;
  height: 120px !important;
}

.user-info {
  position: absolute;
  top: 20px;
  right: 20px;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-permission {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.no-permission :deep(.el-empty__description) {
  margin-top: 20px;
  font-size: 16px;
  color: #909399;
}

.no-permission :deep(.el-empty__extra) {
  margin-top: 10px;
  color: #f56c6c;
}

.update-time {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 6px 0;
  border-top: 1px solid #ebeef5;
  margin-top: auto;
}

/* 新增样式 - 表单分组 */
.form-section {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 2px;
}

/* 参数分组样式 */
.param-group {
  margin-top: 16px;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fafafa;
}

.param-group-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  padding: 0;
}

.param-label {
  flex: 1;
  padding: 8px 12px;
  font-weight: 500;
  font-size: 13px;
  color: #606266;
  text-align: center;
  position: relative;
}

.param-label:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 8px;
  bottom: 8px;
  width: 1px;
  background-color: #ebeef5;
}

.param-group-content {
  padding: 8px 0;
}

.param-row {
  display: flex;
  align-items: center;
  padding: 4px 12px;
}

.param-row:not(:last-child) {
  margin-bottom: 4px;
}

.param-type {
  width: 60px;
  font-size: 13px;
  color: #606266;
  text-align: center;
  background-color: #f5f7fa;
  padding: 4px 0;
  border-radius: 4px;
  margin-right: 8px;
}

.param-row:first-child .param-type {
  color: #409eff;
  font-weight: 500;
}

.param-row:last-child .param-type {
  color: #67c23a;
  font-weight: 500;
}

.param-input {
  flex: 1;
  padding: 0 8px;
}

.param-input :deep(.el-input__wrapper) {
  padding: 1px 11px;
}

.param-input :deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
}

.image-section {
  height: calc(100% - 15px);
  display: flex;
  flex-direction: column;
}

.image-section .el-form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  margin-bottom: 0;
}

.image-section .el-form-item__content {
  flex: 1;
}

.car-image-upload {
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: 260px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.car-image-upload:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.car-image-upload :deep(.el-upload) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 240px;
}

.upload-icon {
  font-size: 28px;
  color: #909399;
}

.upload-tip {
  font-size: 13px;
  color: #909399;
}

.form-section .el-row {
  margin-bottom: 0;
}

.form-section .el-col {
  padding-bottom: 0;
}

.import-tip {
  color: #e6a23c;
  font-size: 14px;
  margin-top: 8px;
}

.importing-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.progress-text {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

:deep(.el-progress__text) {
  font-size: 14px !important;
}

:deep(.el-progress-circle) {
  width: 120px !important;
  height: 120px !important;
}

.user-info {
  position: absolute;
  top: 20px;
  right: 20px;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-permission {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.no-permission :deep(.el-empty__description) {
  margin-top: 20px;
  font-size: 16px;
  color: #909399;
}

.no-permission :deep(.el-empty__extra) {
  margin-top: 10px;
  color: #f56c6c;
}

.update-time {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 6px 0;
  border-top: 1px solid #ebeef5;
  margin-top: auto;
}

/* 新增样式 - 表单分组 */
.form-section {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 2px;
}

/* 参数分组样式 */
.param-group {
  margin-top: 16px;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fafafa;
}

.param-group-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  padding: 0;
}

.param-label {
  flex: 1;
  padding: 8px 12px;
  font-weight: 500;
  font-size: 13px;
  color: #606266;
  text-align: center;
  position: relative;
}

.param-label:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 8px;
  bottom: 8px;
  width: 1px;
  background-color: #ebeef5;
}

.param-group-content {
  padding: 8px 0;
}

.param-row {
  display: flex;
  align-items: center;
  padding: 4px 12px;
}

.param-row:not(:last-child) {
  margin-bottom: 4px;
}

.param-type {
  width: 60px;
  font-size: 13px;
  color: #606266;
  text-align: center;
  background-color: #f5f7fa;
  padding: 4px 0;
  border-radius: 4px;
  margin-right: 8px;
}

.param-row:first-child .param-type {
  color: #409eff;
  font-weight: 500;
}

.param-row:last-child .param-type {
  color: #67c23a;
  font-weight: 500;
}

.param-input {
  flex: 1;
  padding: 0 8px;
}

.param-input :deep(.el-input__wrapper) {
  padding: 1px 11px;
}

.param-input :deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
}

.image-section {
  height: calc(100% - 15px);
  display: flex;
  flex-direction: column;
}

.image-section .el-form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  margin-bottom: 0;
}

.image-section .el-form-item__content {
  flex: 1;
}

.car-image-upload {
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: 260px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.car-image-upload:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.car-image-upload :deep(.el-upload) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 240px;
}

.upload-icon {
  font-size: 28px;
  color: #909399;
}

.upload-tip {
  font-size: 13px;
  color: #909399;
}

.form-section .el-row {
  margin-bottom: 0;
}

.form-section .el-col {
  padding-bottom: 0;
}

.import-tip {
  color: #e6a23c;
  font-size: 14px;
  margin-top: 8px;
}

.importing-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.progress-text {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

:deep(.el-progress__text) {
  font-size: 14px !important;
}

:deep(.el-progress-circle) {
  width: 120px !important;
  height: 120px !important;
}

.user-info {
  position: absolute;
  top: 20px;
  right: 20px;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-permission {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.no-permission :deep(.el-empty__description) {
  margin-top: 20px;
  font-size: 16px;
  color: #909399;
}

.no-permission :deep(.el-empty__extra) {
  margin-top: 10px;
  color: #f56c6c;
}

.update-time {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 6px 0;
  border-top: 1px solid #ebeef5;
  margin-top: auto;
}

/* 新增样式 - 表单分组 */
.form-section {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 2px;
}

/* 参数分组样式 */
.param-group {
  margin-top: 16px;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fafafa;
}

.param-group-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  padding: 0;
}

.param-label {
  flex: 1;
  padding: 8px 12px;
  font-weight: 500;
  font-size: 13px;
  color: #606266;
  text-align: center;
  position: relative;
}

.param-label:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 8px;
  bottom: 8px;
  width: 1px;
  background-color: #ebeef5;
}

.param-group-content {
  padding: 8px 0;
}

.param-row {
  display: flex;
  align-items: center;
  padding: 4px 12px;
}

.param-row:not(:last-child) {
  margin-bottom: 4px;
}

.param-type {
  width: 60px;
  font-size: 13px;
  color: #606266;
  text-align: center;
  background-color: #f5f7fa;
  padding: 4px 0;
  border-radius: 4px;
  margin-right: 8px;
}

.param-row:first-child .param-type {
  color: #409eff;
  font-weight: 500;
}

.param-row:last-child .param-type {
  color: #67c23a;
  font-weight: 500;
}

.param-input {
  flex: 1;
  padding: 0 8px;
}

.param-input :deep(.el-input__wrapper) {
  padding: 1px 11px;
}

.param-input :deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
}

.image-section {
  height: calc(100% - 15px);
  display: flex;
  flex-direction: column;
}

.image-section .el-form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  margin-bottom: 0;
}

.image-section .el-form-item__content {
  flex: 1;
}

.car-image-upload {
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: 260px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.car-image-upload:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.car-image-upload :deep(.el-upload) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.car-image-upload img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 280px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  padding-right: 6px;
  color: #606266;
  text-align: right;
  width: 110px !important;
  box-sizing: border-box;
  font-size: 13px;
}

:deep(.el-form-item) {
  margin-bottom: 10px;
  height: 36px;
}

:deep(.el-input__inner) {
  border-radius: 4px;
  height: 32px;
  line-height: 32px;
  font-size: 13px;
}

:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

:deep(.el-input__wrapper) {
  width: 100%;
}

.form-section .el-row {
  margin-bottom: 0;
}

.form-section .el-col {
  padding-bottom: 0;
}

:deep(.el-dialog__body) {
  padding: 15px 20px;
  background-color: #f9fafc;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding: 15px 20px;
  margin: 0;
  background-color: #f5f7fa;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 12px 20px;
}

/* 紧凑型表单容器 */
.form-container {
  display: flex;
  gap: 12px;
  height: 100%;
}

.form-left-column {
  flex: 2;
}

.form-middle-column {
  flex: 2;
}

.form-right-column {
  flex: 1;
  min-width: 200px;
}

/* 紧凑型表单区块 */
.compact-section {
  margin-bottom: 10px;
  padding: 10px 12px 5px;
}

.compact-section .section-title {
  font-size: 14px;
  margin: 0 0 8px 0;
  padding-bottom: 6px;
}

/* 紧凑型表单项 */
.compact-form :deep(.el-form-item) {
  margin-bottom: 8px;
}

.compact-form-item {
  margin-bottom: 5px !important;
}

.compact-form :deep(.el-form-item__label) {
  font-size: 12px;
  line-height: 28px;
  padding-right: 5px;
}

.compact-form :deep(.el-form-item__content) {
  margin-left: 0 !important;
  margin-right: 0;
}

.compact-form :deep(.el-input__wrapper) {
  padding: 0 10px;
}

.compact-form :deep(.el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}

/* 网格布局 */
.base-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.three-columns {
  grid-template-columns: repeat(3, 1fr);
}

/* 参数组紧凑型样式 */
.compact-group {
  margin-top: 8px;
  margin-bottom: 10px;
}

.compact-group .param-label {
  padding: 6px 8px;
  font-size: 12px;
}

.compact-group .param-row {
  padding: 3px 8px;
}

.compact-group .param-type {
  width: 50px;
  font-size: 12px;
  padding: 3px 0;
}

/* 图片上传区域 */
.image-form-item {
  height: calc(100% - 35px);
  margin-bottom: 0 !important;
}

.car-image-upload {
  min-height: 180px;
  height: 100%;
  max-height: 260px;
}

.preview-image {
  max-height: 230px;
}

/* 响应式调整 */
@media (max-height: 768px) {
  :deep(.el-dialog__body) {
    padding: 8px;
    max-height: 70vh;
  }
  
  .compact-section {
    margin-bottom: 6px;
    padding: 6px 8px 3px;
  }
  
  .compact-section .section-title {
    font-size: 13px;
    margin: 0 0 6px 0;
    padding-bottom: 4px;
  }
  
  .compact-form :deep(.el-form-item) {
    margin-bottom: 3px;
  }
  
  .compact-form :deep(.el-input__inner) {
    height: 26px;
    line-height: 26px;
  }
  
  .param-group {
    margin-bottom: 5px;
  }
  
  .car-image-upload {
    min-height: 140px;
  }
}

@media (max-width: 1366px) {
  .form-container {
    flex-wrap: wrap;
  }
  
  .form-left-column,
  .form-middle-column {
    flex: 0 0 48%;
  }
  
  .form-right-column {
    flex: 0 0 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .form-right-column .form-section {
    flex: 1;
    min-width: 300px;
  }
  
  .image-section {
    flex: 0 0 100%;
    height: 200px;
  }
}

.param-input.rowspan-2 {
  grid-row: span 2;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.param-input.rowspan-2 .el-input,
.param-input.rowspan-2 .el-input__wrapper {
  height: 100%;
}

.param-input.rowspan-2 .el-input__inner {
  height: 100%;
  align-items: flex-start;
  padding-top: 5px;
}

.param-input.rowspan-2 textarea.el-textarea__inner {
  height: 100% !important;
  min-height: 58px;
  padding: 5px 11px;
  font-size: 12px;
  resize: none !important;
  border: none;
  box-shadow: none;
  background-color: transparent;
}

.special-section {
  background-color: #f8fafe;
  border-left: 3px solid #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.special-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.special-section .section-title {
  color: #409eff;
}

.special-section .section-title::before {
  background-color: #409eff;
  width: 30px;
  height: 4px;
}

.wide-input {
  grid-column: span 2;
}

.special-cell {
  background-color: #f0f9eb;
  border-radius: 4px;
}

.param-input.special-cell .el-input__wrapper {
  box-shadow: 0 0 0 1px #67c23a inset !important;
  background-color: rgba(103, 194, 58, 0.05);
}

.param-row .param-type.highlight {
  color: #409eff;
  font-weight: 600;
  background-color: #ecf5ff;
}

.special-row {
  background-color: rgba(103, 194, 58, 0.05);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.special-row:hover {
  background-color: rgba(103, 194, 58, 0.1);
}

/* 对话框整体样式优化 */
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);
  padding: 14px 20px;
}

:deep(.el-dialog__title) {
  color: rgb(0, 0, 0);
  font-weight: 600;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(0, 0, 0, 0.9);
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: rgb(0, 0, 0);
}

/* 修改表单边框样式 */
.form-section {
  border-radius: 6px;
}

.param-group {
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.full-width {
  grid-column: span 2;
}

.full-width :deep(.el-textarea__inner) {
  min-height: 60px;
}

.car-edit-dialog {
  --primary-color: #409eff;
  --section-border-color: #ebeef5;
  --hover-bg-color: #f1f5fd;
  --section-bg-color: #f8f9fb;
}

.car-edit-dialog .form-container {
  display: flex;
  gap: 15px;
}

.car-edit-dialog .form-left-column,
.car-edit-dialog .form-middle-column,
.car-edit-dialog .form-right-column {
  flex: 1;
  min-width: 0;
}

.car-edit-dialog .form-section {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  transition: all 0.2s ease;
}

.car-edit-dialog .section-title {
  margin: 0 0 10px;
  padding: 0 0 8px 12px;
  font-size: 14px;
  color: #303133;
  border-bottom: 1px solid var(--section-border-color);
  position: relative;
}

.car-edit-dialog .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.car-edit-dialog .compact-section {
  padding: 12px 15px 8px;
}

.car-edit-dialog .base-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.car-edit-dialog .three-columns {
  grid-template-columns: repeat(3, 1fr);
}

.car-edit-dialog .compact-form-item {
  margin-bottom: 8px;
}

.car-edit-dialog .full-width {
  grid-column: 1 / -1;
}

.car-edit-dialog .image-section {
  margin-top: auto;
  height: calc(100% - 320px);
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.car-edit-dialog .image-form-item {
  margin-bottom: 0;
  height: 100%;
}

.car-edit-dialog .car-image-upload {
  height: 100%;
  min-height: 150px;
}

@media (max-width: 1366px) {
  .car-edit-dialog .form-container {
    flex-wrap: wrap;
  }
  
  .car-edit-dialog .form-left-column,
  .car-edit-dialog .form-middle-column {
    flex: 0 0 48%;
  }
  
  .car-edit-dialog .form-right-column {
    flex: 0 0 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .car-edit-dialog .form-right-column .form-section {
    flex: 1;
    min-width: 300px;
  }
  
  .car-edit-dialog .image-section {
    flex: 0 0 100%;
    height: 200px;
  }
}

/* 优化表单区域样式 */
.car-edit-dialog .form-section {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.03);
  margin-bottom: 16px;
  padding: 16px 18px 10px;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.car-edit-dialog .section-title {
  margin: 0 0 16px;
  padding: 0 0 10px 14px;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  position: relative;
  letter-spacing: 0.2px;
}

.car-edit-dialog .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.car-edit-dialog .base-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 20px;
}

.car-edit-dialog .three-columns {
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 14px;
}

.car-edit-dialog :deep(.el-form-item__label) {
  font-size: 13px;
  line-height: 1.4;
  padding-right: 10px;
  color: #606266;
  font-weight: 500;
  text-align: right;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.car-edit-dialog :deep(.el-form-item__content) {
  line-height: 32px;
}

.car-edit-dialog :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  padding: 0 12px;
  transition: all 0.2s;
  background-color: #fcfcfc;
}

.car-edit-dialog :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
  background-color: #ffffff;
}

.car-edit-dialog :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset !important;
  background-color: #ffffff;
}

.car-edit-dialog :deep(.el-input__inner) {
  height: 32px;
  font-size: 13px;
  color: #333;
}

.car-edit-dialog .compact-form-item {
  margin-bottom: 12px;
}

/* 表单容器布局优化 */
.car-edit-dialog .form-container {
  display: flex;
  gap: 18px;
  margin-top: 5px;
}

/* 图片上传区域优化 */
.car-edit-dialog .image-section {
  margin-top: 16px;
  height: auto;
  min-height: 240px;
}

.car-edit-dialog .car-image-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
  height: 100%;
  min-height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.car-edit-dialog .car-image-upload:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.02);
}

.car-edit-dialog .image-form-item :deep(.el-form-item__content) {
  width: 100%;
  height: 100%;
}

.car-edit-dialog .upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20px;
}

.car-edit-dialog .upload-icon {
  font-size: 32px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.car-edit-dialog .upload-tip {
  font-size: 14px;
  color: #909399;
}

/* 对话框样式优化 */
.car-edit-dialog :deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 9px 28px rgba(0, 0, 0, 0.05);
}

.car-edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #409eff 0%, #55acff 100%);
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.car-edit-dialog :deep(.el-dialog__title) {
  color: rgb(0, 0, 0);
  font-weight: 600;
  font-size: 17px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.3px;
}

.car-edit-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
  background-color: #f9fafc;
  max-height: 76vh;
  overflow-y: auto;
}

.car-edit-dialog :deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 14px 20px;
  background-color: #f9fafc;
}

.car-edit-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.9);
}

.car-edit-dialog :deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: white;
}

.car-edit-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.car-edit-dialog .dialog-footer .el-button {
  min-width: 88px;
  font-weight: 500;
  padding: 9px 16px;
  border-radius: 4px;
  letter-spacing: 0.3px;
}

/* 响应式布局优化 */
@media (max-width: 1366px) {
  .car-edit-dialog .form-container {
    gap: 16px;
  }
  
  .car-edit-dialog .form-left-column,
  .car-edit-dialog .form-middle-column {
    flex: 0 0 calc(50% - 8px);
  }
  
  .car-edit-dialog .form-right-column {
    flex: 0 0 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 6px;
  }
  
  .car-edit-dialog .form-right-column .form-section {
    flex: 1;
    min-width: 280px;
    margin-bottom: 10px;
  }
  
  .car-edit-dialog .image-section {
    flex: 0 0 100%;
    min-height: 200px;
    height: auto;
  }
  
  .car-edit-dialog .base-info-grid {
    gap: 12px 16px;
  }
}

@media (max-width: 1100px) {
  .car-edit-dialog :deep(.el-dialog__body) {
    padding: 16px 18px;
  }
  
  .car-edit-dialog .form-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .car-edit-dialog .form-left-column,
  .car-edit-dialog .form-middle-column,
  .car-edit-dialog .form-right-column {
    flex: 0 0 100%;
    width: 100%;
  }
  
  .car-edit-dialog .form-section {
    padding: 14px 16px 8px;
    margin-bottom: 12px;
  }
  
  .car-edit-dialog .form-right-column {
    display: block;
  }
  
  .car-edit-dialog .section-title {
    margin: 0 0 12px;
    padding: 0 0 8px 12px;
    font-size: 14px;
  }
  
  .car-edit-dialog .car-image-upload {
    min-height: 180px;
  }
}

/* 参数组样式优化 */
.car-edit-dialog .param-group {
  margin-top: 12px;
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.25s;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
}

.car-edit-dialog .param-group:hover {
  border-color: #d9e3f8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.car-edit-dialog .param-group-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  padding: 0;
}

.car-edit-dialog .param-label {
  flex: 1;
  padding: 9px 12px;
  font-weight: 500;
  font-size: 13px;
  color: #606266;
  text-align: center;
  position: relative;
  transition: all 0.2s;
}

.car-edit-dialog .param-label:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 8px;
  bottom: 8px;
  width: 1px;
  background-color: #ebeef5;
}

.car-edit-dialog .param-group-content {
  padding: 10px 6px;
  background-color: rgba(255, 255, 255, 0.6);
}

.car-edit-dialog .param-row {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.car-edit-dialog .param-row:hover {
  background-color: rgba(64, 158, 255, 0.04);
}

.car-edit-dialog .param-row:not(:last-child) {
  margin-bottom: 6px;
}

.car-edit-dialog .param-type {
  width: 64px;
  font-size: 13px;
  color: #606266;
  text-align: center;
  background-color: #f5f7fa;
  padding: 5px 0;
  border-radius: 4px;
  margin-right: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.car-edit-dialog .param-row:hover .param-type {
  background-color: #ecf5ff;
}

.car-edit-dialog .param-input {
  flex: 1;
  padding: 0 6px;
}

.car-edit-dialog .param-input :deep(.el-input__wrapper) {
  padding: 1px 10px;
  transition: all 0.25s;
}

/* 表单组件样式优化 */
.car-edit-dialog :deep(.el-select .el-input__wrapper) {
  padding-right: 30px;
}

.car-edit-dialog :deep(.el-textarea__inner) {
  padding: 6px 10px;
  font-size: 13px;
  color: #333;
  border-radius: 4px;
}

.car-edit-dialog :deep(.el-select .el-input .el-select__caret) {
  color: #909399;
  font-size: 14px;
  transition: transform 0.3s;
}

.car-edit-dialog :deep(.el-select .el-select__popper .el-select-dropdown__item) {
  padding: 0 16px;
  height: 34px;
  line-height: 34px;
  font-size: 13px;
}

.car-edit-dialog .full-width :deep(.el-textarea__inner) {
  min-height: 56px;
  line-height: 1.5;
}

/* 调整间距和对齐 */
.car-edit-dialog :deep(.el-form-item) {
  margin-bottom: 14px;
}

.car-edit-dialog .compact-form-item:last-child {
  margin-bottom: 8px;
}
</style>


