import request from '@/utils/request'

// 检查赛车权限
export function checkCarPermission() {
  return request({
    url: '/cars/check_permission/',
    method: 'get'
  })
}

// 获取赛车列表
export function getCarList(params) {
  return request({
    url: '/cars/',
    method: 'get',
    params
  })
}

// 创建赛车
export function createCar(data) {
  return request({
    url: '/cars/',
    method: 'post',
    data
  })
}

// 更新赛车
export function updateCar(id, data) {
  return request({
    url: `/cars/${id}/`,
    method: 'put',
    data
  })
}

// 删除赛车
export function deleteCar(id) {
  return request({
    url: `/cars/${id}/`,
    method: 'delete'
  })
}

// 导入Excel
export function importCars(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/cars/import_excel/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出Excel
 * @param {Object} params - 查询参数
 * @param {string} [params.search] - 搜索关键词，匹配赛车编号和名称
 * @param {string} [params.level] - 赛车级别，精确匹配
 * @returns {Promise} 返回二进制文件流
 */
export function exportCars(params) {
  return request({
    url: '/cars/export_excel/',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 上传图片
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('image', file)
  return request({
    url: '/cars/upload_image/',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取赛车等级列表
export function getCarLevels() {
  return request({
    url: '/cars/levels/',
    method: 'get'
  })
}

// 搜索赛车
export function searchCars(query) {
  console.log(`调用搜索赛车API，查询参数:`, { query });
  return request({
    url: '/cars/search/',
    method: 'get',
    params: { query }
  }).then(response => {
    console.log('搜索赛车API返回结果:', response);
    return response;
  }).catch(error => {
    console.error('搜索赛车API错误:', error);
    throw error;
  });
}
