{"dist-tags": {"latest": "6.5.1"}, "modified": "2025-01-10T07:26:20.452Z", "name": "@codemirror/state", "versions": {"0.19.6": {"name": "@codemirror/state", "version": "0.19.6", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-sqIQZE9VqwQj7D4c2oz9mfLhlT1ElAzGB5lO1lE33BPyrdNy1cJyCIOecT4cn4VeJOFrnjOeu+IftZ3zqdFETw==", "shasum": "d631f041d39ce41b7891b099fca26cb1fdb9763e", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.6.tgz", "fileCount": 8, "unpackedSize": 221322, "size": 55548, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.5": {"name": "@codemirror/state", "version": "0.19.5", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-a3bJnkFuh4Z36nuOzAYobWViQ9eq5ux2wOb/46jUl+0Sj2BGrdz+pY1L+y2NUZhwPyWGcIrBtranr5P0rEEq8A==", "shasum": "af56e3af50224d748a992e56d1e31a52287594a5", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.5.tgz", "fileCount": 8, "unpackedSize": 219154, "size": 55074, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/state", "version": "0.19.4", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "db6ee73ec3eb5a237d0d04652a8d8e7b4cb7758b", "size": 55019, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.4.tgz", "integrity": "sha512-PLsY1PvuGynylzv4FVEi1vDz4gituIxJR4XdEVT4+UK6C2l14Y2TGLkUTw3D8xlq+xmY1J1TR7UbicZtJIxO6A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.3": {"name": "@codemirror/state", "version": "0.19.3", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "b225653251dea02559c1a3b2bcd47f5a62cfbbd0", "size": 55069, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.3.tgz", "integrity": "sha512-mMCOQWB4Kua/XPYRsY95IwX+uDcoD+n4G0FTDi0jNJHYoyWTkPpHjT6zt6mfl4kpqwqBJ8/WQBuztvpstblvvQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/state", "version": "0.19.2", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "81de81f89e87b9362b8bc6d51135637dddd4d33d", "size": 54608, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.2.tgz", "integrity": "sha512-dDqCrtkb0c/LYUlvQBLyLfkISEskbZnhvBbcVOF4j2AusJ1ptJ3EGMxBL9G16GP1TOdC1T613gA1J1qc3pbfGQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/state", "version": "0.19.1", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "cf0ee90b4fb45c295f8bbe2d6196d890ec81d92a", "size": 53911, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.1.tgz", "integrity": "sha512-5NR6/h+pVp7d77F4rLpNWcYvARCvqXa1uayoNx06d1aEm6wy/K+3lxTg7taB/qaEKd+oCsSHjRL8oSE1Fm+nsw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.0": {"name": "@codemirror/state", "version": "0.19.0", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "db3b111fbbab1bd4d7bb74b4c85f131b7004925c", "size": 53875, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.0.tgz", "integrity": "sha512-yS4oUzaYggUCsDA5cVqMGFNe2CjA2x1YQp68A8/Nnczxr5PctIPuzSTUfBQCi/IKNRouJxMr+gDPweEErJi7fw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.7": {"name": "@codemirror/state", "version": "0.18.7", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"shasum": "3339a732387bb2c034987c57ccf0649ef2f7c4c1", "size": 52488, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.7.tgz", "integrity": "sha512-cVyTiAC9vv90NKmGOfNtBjyIem3BqKui1L5Hfcxurp8K9votQj2oH9COcgWPnQ2Xs64yC70tEuTt9DF1pj5PFQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.6": {"name": "@codemirror/state", "version": "0.18.6", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "46f4cabd7e635dd0f229d0ff1136d79b071354f8", "size": 52928, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.6.tgz", "integrity": "sha512-jBY4KFY6RGPkuRUFXSZtgxpKebju8CJq7SkKYf+NsD8OZzDSauxPPYAL7V2z8ubvw74qLPIKIX2hERvY6WBdbg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.5": {"name": "@codemirror/state", "version": "0.18.5", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "db936f80c40f329fb803bd284cbb5aa556dafb88", "size": 52347, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.5.tgz", "integrity": "sha512-lHR+yE08jEz7MqA5hgNvK4/ksF2mQsJJ/pedKKfB94CUobMX20tsFQ27lZbXCxZDQcz5lO0AZuFuRqrbDlRtKA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.4": {"name": "@codemirror/state", "version": "0.18.4", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "1b6009439b53e4d1a7b8ae021cff4c57287c520f", "size": 52289, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.4.tgz", "integrity": "sha512-UG8OThgMzhGoUDK8ujTDkY+OM2fNmd7vSU947Sc3zGCws1mEDq7GJbW2uvvIE7HDpySnojmqq0odGzLYHGQkJg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.3": {"name": "@codemirror/state", "version": "0.18.3", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "f275293b077d6c3867c0343320d6b29c10e54f84", "size": 51912, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.3.tgz", "integrity": "sha512-LQlQWFAw+OLd1ufdf8o8yU/iJd88ixw8kOQ8KgEHDZJnegJOz0NafXvkFoWPiLNaiIUxif02KlZXJAPqDThZ4g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/state", "version": "0.18.2", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "cf0df2710698f98cf0a57528d5d83db2ed7e44d4", "size": 51666, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.2.tgz", "integrity": "sha512-fzd9duIkRjSZFsz9lie0V6wa4zZac8LtjZYd9pSmNneDAoJx0AigFEswJ+KDdYuiPmsKd8NB0wXzoiGLLjP6MA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/state", "version": "0.18.1", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "6d2910c6fd4f0751212be9e62a361227c0e3b636", "size": 51494, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.1.tgz", "integrity": "sha512-rVSY4Yi9FNRnaYbQ6deuj6yQ8RYQrKD6HsJi7KYUrWhHZF2MnPD3K2hdj177DgcZKy40U8kVz33X3irWU0dD5w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/state", "version": "0.18.0", "dependencies": {"@codemirror/text": "^0.18.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "5bb5600c5bf32d4103b6576cd33bdb8cf926d602", "size": 72730, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.18.0.tgz", "integrity": "sha512-E5Tgx/CVmQQdiIOoJ4xX4hy2RGqTjxbpzDifGn8wUu3Driq/uZ+ncr7M4ojWs1T83yYFevNBmPtT5vprD25vYA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.2": {"name": "@codemirror/state", "version": "0.17.2", "dependencies": {"@codemirror/text": "^0.17.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "b94846def08c2258bfdf09839359c31823e663ff", "size": 71771, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.17.2.tgz", "integrity": "sha512-3WwDsBTMkiyFKJntIZjCiyq0KKo1OaKhq8k9vG/eGRm6bYL8rPOAEvufW6AKwuK3BSAftOAHFNXXq+GRNoL7zg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/state", "version": "0.17.1", "dependencies": {"@codemirror/text": "^0.17.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "ef8db9af29a4485572d380237df5bb0b4d50e7d5", "size": 71600, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.17.1.tgz", "integrity": "sha512-ELgxzackFVC+wBWpGJGFN6OJ+uIdHrgj4BEh6qooju7U5Mdd1bM594QPGzNMRu3uXjfGj7ANHTATRPc0nnQRfA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/state", "version": "0.17.0", "dependencies": {"@codemirror/text": "^0.17.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "212f4af07a9aa2f6a9ff210ec18468f79059b626", "size": 52503, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.17.0.tgz", "integrity": "sha512-hY0O/Kf1bQMQt5kbv0mXl4tHL+3/bEu1h3T67jBbiYFZyE9fXUWe8GVXE8x8CvfFooSc0+fU9bTJWpx3wUEQ8Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.7": {"name": "@codemirror/state", "version": "0.19.7", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-UB7OqJSiUZXmwbsBu7wRoMBrXwOHwSs1J9RORB2oz0oA5LfPVbLYIKl5223qCcSSKoM5cSc3bWpMUaHo8WOVnA==", "shasum": "32ab9913d8db89b9518d43de1e85f6e270b0869d", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.7.tgz", "fileCount": 8, "unpackedSize": 222544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBpWBCRA9TVsSAnZWagAAkyoP+wSrpyh5xiuBtpZAPpDV\nxzr74s6/fslBwEMp/cJ1VCMQoFgqzyObDPnUJC+sG7pC3ltGEUz8PjJuR9Tp\nEm8Ud1kZdnejKs+z+wP5qKq7ijiz0jPCnKHN7C0hY2i5WCbms9fNn6PMHHxU\n+pyYxhoXWpLqa1zbU4YOv+SsRO/FdxkNJ/hYmZR3j1FQy8/7XsGlqfdkffSu\nY47uoJfRPr0AhlT0uv6no3/8IdQRpFr4Tn+tiLoZVzpH1tI0y0Qk+xpbRFNP\nEHakVF0uI3Il01l/eMF6RQNeBnn7El5Ijn28laRbMkVbRUrCMC95IZ81BOSk\n1y7A+OkuyviZ7gIyEqfk0PcjFqBJwklLjnLQ7BAn997Z+PvVbjtSkzQHOy96\nAyAZxnocSBHY9ohRuDDkwyd4JX/h/PG9NriKz4yX10aE0kCI2Jco9z65VWjj\nN8hL2C+ND1cF74fNMNavIYiwGeDYj7S6YyV6p4UI+paeevpUar3tUN2yjhOv\nT7ECHw9d6XbF7mw+iLRlRRYUQiuToeYTLAphaZLWaSqhwRvce1M2RfnMccO1\nnDxphBub7yx0tL1N6TMjJa+Xcz7ltu8uW3CPjc7bqs+gOtgjjK0jb6dYWQrM\nDmLWqnbpM6+vPD6dI+dklLLrkQB4742vRp4OPfQH4GBjlJjRHy3zXsaWv/kK\nC7AJ\r\n=Dtvh\r\n-----END PGP SIGNATURE-----\r\n", "size": 55824}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.8": {"name": "@codemirror/state", "version": "0.19.8", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-f8iZJe+1czZNOdt/H6FnuiYnzLF9OKxc7PsKhdEDq0478U2kak5CBbS7JwxkJekeguTyaagoJbnZveqPY2wlmQ==", "shasum": "b5692dc9ad16adb032efd0cf8cb245ca90007202", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.8.tgz", "fileCount": 8, "unpackedSize": 223427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC3KBCRA9TVsSAnZWagAAEOcP+wRp5g5gf1S2WMJj0GlB\nzxNbGAVfDuBYkHnRFuD23/ScfIM4fJ2rf9E/tYhkpUk0zOAT0dtJZSvq6FoR\n0xABqZ0vXCKkaQIl/bThTWVdktbiNkf9Fj2LnnTdUz84Vws8d2dvvzYxCRg5\ngvskUYLxI0X/mhlkUkJ8aQJWX1aqIC/vmRwJJzERHnm+HzOpnGHDURYxbS8S\nWf44T0PVWDf4HQY58fEMpwxAwrDEII+o5pGumpJLd+Ylp7REOrGeslULnvw2\n3ORDC1kjtRpZ8FgMkFgoZppRT99RXJHw/K3RJnldq2CWHkycKNF5ksu+MKV3\nwxdtHtxpnPVMlhcvpKUNSDEC1wdwP3Y9FCp4+2uAJZmNZQ1afIx84qk/vR2M\nC6DOFKDASMFgLZRHyh6OkCWaS/ACglTlOBLGomIpbv9vkjDiVq3wFOF1UjDa\njSw2CYzIGLIp/XcNk6hJ/gO1+EVPi6o0QFd2bn9IVMCwjQ5iDV8yefQqjJBy\nKJr1tLR/zZ06JRfWW31t/6eH46tb13ol/URwFMp3qD07b43b+947SwVUwyY9\nV/GGaSIZPeyCPiWP/ja2VFa5D1dV3Nvfpdx2oQIEI4tPrVroA0Zjg3B/QA08\nF0LhRzYTMeDOGUdt00y3rXf6kxYHX4wm5gkUg/gS+lBq2WfM1rzgJZu31CN4\ny461\r\n=3feJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 55774}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.9": {"name": "@codemirror/state", "version": "0.19.9", "dependencies": {"@codemirror/text": "^0.19.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-psOzDolKTZkx4CgUqhBQ8T8gBc0xN5z4gzed109aF6x7D7umpDRoimacI/O6d9UGuyl4eYuDCZmDFr2Rq7aGOw==", "shasum": "b797f9fbc204d6dc7975485e231693c09001b0dd", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.19.9.tgz", "fileCount": 8, "unpackedSize": 223890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDSzcCRA9TVsSAnZWagAAmp4P/2gNVQ4TtY7dGGEi0Hx+\nLSMdksUEkN+2sObsB6WwhCHQr+fLwVOuAEfquoUIbnaPO8pf7dmOb81n3GZ4\nI7axIJ0i6ljngyx8ivV6hoUH0AA3MilgLC3VYD39Vs29lbGmXTBBmG2J+9OZ\nEyLbpEWvPrqQZB5ZCGlPurjnPIkTJOzenVfd2A6ZBtY5IBbRW5YvjROXsiAM\nO05oBOitdZ1BPCQ9ESBXYD91vPApbZMM5gySGlizfpmQ1dYHiOPbH94dkq0z\nAfCP8EnpyRsDyEii1100BcU/sf6seZW33bzDFQJvUSpovdbygY+O87Xrprgt\nZsTOm/9m3hIKirwhK1JUgQ3PyubV9/jJNkJa8N8+C4chhHEjibXP9D2kgKFV\nmy7jDwQNx1b7Uu38CHkuKp5YrnYhynu2OSZXRcsazN+6teGEpoAyg46dEwl/\nbiT5cA75DlSs+GGDBvPyy99hzPEFqrowDFqobUm/Sh0JhzGbKhNAot+t+pKP\nhVmd8UoybcZOX6Jh3ED+zP8Xb1bfMLcpAVEF4rYQqhSPLDQ6UfMpL7uUBBRo\nhg9c5u76Odof975L8IqXzMcArRYziOgnRSezzxBi/u57RT+6X488j6QIFHaU\nN+WIylOBXwEdSDRe0FDrfm3XX2o8Uq2ZaihFuqIO2uGuADbV8eI8Lz+agaCR\noPZT\r\n=HknV\r\n-----END PGP SIGNATURE-----\r\n", "size": 55898}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/state", "version": "0.20.0", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-R3XrAWCS5Xm9lx+4pDET4EUPEg+8bDfAa5zoOFIhx+VChsfew9Vy33dAjCXS5ES4Q8UecW4WM4UudmUFpZ+86A==", "shasum": "3343d209169813b0152b77361cd78bea01549a73", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.20.0.tgz", "fileCount": 8, "unpackedSize": 353104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICQsfavtbRi+0X1J29vYUNaaFsPwQ2gvMivt7Tt6DsTYAiEA+mPlYoBrl6ST3V3X3olbLnUoykEr0mZpJ6KK/clNSak="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBUmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmodzg/+OjGpTNctfKxcQRGLGDOWxEaogWKdd2k6JMmnElcPj6LXmn+k\r\nY73V4PDvn/qfPDFcp+LuwZq9vVZDAHeLHD/ZYkSFhnGWeM/aU234uaKCQJEW\r\n1LwMAepiVBzID5TRdMoRT1LAMT4h35+RD/4vo4XebGNN42/16U4dnsy78+iN\r\n48tLZFziu6kO3np3bN+lohI5pKT5gfchsQ7YdLPd/MX1CbIM/M7aukYm8jIw\r\nll7LXWCJBWwHwjvrvPR7KvALSaH7IuZqISCmfF6jn9iwrUTIfVIHW60UBYXK\r\nNIlDu2AASXoB8gI52APHLy8mw+nYSZi/0imjt8GAGoDjyIUanaTqx6zF5tKX\r\nq8zxSwkuHraP0G0nbg50/KgSIHifaSZUDkYS4exbJAozIjLOS2pQq8xqi3ks\r\n5yBg5YFslck9fSMogL/6X9+NJyJBGLh6jtqIUqEru/aXMFmfb5G/pErdst2X\r\notWiJ2K14stLqLXa5gG12mWiI2zCdUU1bV582DfDtyaGS8pIM39AG+WIGYDP\r\ncjbPNzlk5qEisB/QSnSaDn7HLS6pNgcQB8XuVvi0zrUlAblA6vzaj7ZUbVKL\r\n75JrgxarGCJaGBd6sTZ5nKScccaht4tH0YAiHDHxrJ6M8dZYmnuw9K/ALK3/\r\nRvJrwNho/4p4iNDRd391RtfVehiPfMC5T0E=\r\n=QcD2\r\n-----END PGP SIGNATURE-----\r\n", "size": 87674}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.1": {"name": "@codemirror/state", "version": "0.20.1", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-ms0tlV5A02OK0pFvTtSUGMLkoarzh1F8mr6jy1cD7ucSC2X/VLHtQCxfhdSEGqTYlQF2hoZtmLv+amqhdgbwjQ==", "shasum": "de5c6dc0de3e216eaa3a9ee9391c926b766f6b46", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-0.20.1.tgz", "fileCount": 8, "unpackedSize": 356424, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDk4sjQYxEUCQWSALpFH/aBDEOwDGLe9vLe3TlcDap4HQIgBtDa7mpWMU3/JOjLjx5yBcjfXtXXBF4buM/C/4MqZkY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimFsTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMZRAAm886AdnquHeo5owk9Jg34prYGgIZ94PbSUYoxGiKTjW73ync\r\na5UmhH0LsaVMlK+N3tXLVicm+ZuY69YX6m/KQsCMz6QPc1xtjILD4rLgmIMr\r\n3V72VbpYYB7KPL/0nswPOcp+zb1HDMP7oAUKDaTC8YE6a3gdLINJIqvK4Tyb\r\nlnf/26To4a9kUF7/bws5tGdKajhdKOaqRp2ow/++KxucKCf6cHUH0D7OHRW+\r\nIJCyCpxgTVmE8L5XSSUjYPy5jvo7EP/Y1edng0vovVFTItBOcr5J7Trq7t4p\r\nl/KHZRlAhemfu7v7uP8vP3UNAX/3nKlzo9A9iMKGANypCRgwdwim4N6TEWUX\r\nwfkeb50PkUKC5Nmqgn37rbqxUALJKs7m2RumMqTqQEuZ55hibYLldyV/Dwt7\r\nOzWfxdcgCcXkD4GK7Vq4x0CeT633rC2JFsaq1kVreKpmacwnDDRJ+nm8EB/C\r\n+1VIp5zZ3V3Yya35O8aZmetkJBrQ5lfW5LXegcYpBadu6ru6npdYpoQhhyGC\r\nw7Eql+p4upAt5BxEacgu+cWwKjnKLN+s0vB6AOJ/AaxMTpbGFK1AAXb1dZPp\r\nXzNU1DOCcHDYKLFk56X++AWk3uGzM+Mjk99wkEOwRiA7ci5EU/cW1GiK76lb\r\ntCBAtsFFgoGdaPs7TgHGIfN9iK4sAj8iufg=\r\n=lxxs\r\n-----END PGP SIGNATURE-----\r\n", "size": 88555}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/state", "version": "6.0.0", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-UjP/jB2dz7B+1L+eiCm8YWhM1zBkFM2zUyp8hhtpvgmuOFmoWjwqHnICQmM34HNPBqMPcVY9ZcqJcOhDrB+dBQ==", "shasum": "baac0d050ce9271259b0997e1693469c044deb3f", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.0.0.tgz", "fileCount": 8, "unpackedSize": 356031, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDe203w1+1ArSAKVU6jw7N9TVZSSn7mYmO9uq6+4rzs7wIgPIBDWGASaDs5Io686scXUhdrWBsHDwAFTkj6JM/khAA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpioBAAgqsO7vKjn/kWCIsqEetv9FJ93CgurpapXNWDPMXfufExmr2v\r\n8vOK85Qkyb5HV3EqFrHDGVCgAl+qrZ10AT8Yb1mUM/8aUBCLFVlX5gmakoff\r\nwo6fFSMwV0GhidvS+aBUC8Jj1y63+LmZ7r/Hix/6Op3rI7xImHmMsDDQW5Tz\r\nS+y7wXs5ebNUMf89dje9eNKZOoqSrEbIHvbHUYPQXV43M8cJOcoiWp4KYrTk\r\n/E2VMC1zjaPWzBYwd/fNVaEdTDeyJdBViy82e2tUM7MMAJ7tzE16UnUGBTx5\r\nwtSPPffaO2b9WwFjAJJxFfiRED7qGAwQLB2DOj0Pux/UVf+mmBEKAmVhDUx9\r\nP9BsW90LXnFB7wmFDJMlCq4UB1QDOnaCit534e0lhulZghZJ2ER/Q0g04CcA\r\nASADEo5CkuPG5roVI3HBXAFcHx7reemlrOWQjmxFdHNYIIjPhLYysWwX9UXL\r\nyCr5Yb3Q1JoiWSTCd/Wf/6MZJ4ZyDMNgcvc4fUsz32gzURHA6V/MALoBr/vC\r\nLeKrAAqc3CmIfJflJJw4RZlfCNTkW+GuZMwDhP39aOOHKQ7faXkZYXchdILd\r\nWJ+BeTtzhjDq42miigXuBG6wM3KY1KgheyaBRBZA8lamObMvu0jLMMH2+cnt\r\nzdcgr4LPZlybBoE73a8Ju61lxbo/n1r6WA8=\r\n=gwky\r\n-----END PGP SIGNATURE-----\r\n", "size": 88380}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.1": {"name": "@codemirror/state", "version": "6.0.1", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-6vYgaXc4KjSY0BUfSVDJooGcoswg/RJZpq/ZGjsUYmY0KN1lmB8u03nv+jiG1ncUV5qoggyxFT5AGD5Ak+5Zrw==", "shasum": "a1994f14c49e2f77cb9e26aef35f63a8b3707c6c", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.0.1.tgz", "fileCount": 8, "unpackedSize": 356213, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYc7HhFshJSE6pR2ojPhRsxo5ye6KaA7oFxzCqVf45fgIgOvIIeYgoOcEKeOh1S2aHQXDdPC5BjR8gMy8xk3UT70U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirCwaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKhw//cvnJuLgYrQwVjqFqCMlh5NFyC4VRIQsfqM1kUq8IH9UdmF4Y\r\ny80+PNE1Msq+QdovfbzrMkrCUf++11NfAERXiWrBpfgAqZyd7+DNOAyrw+w2\r\ncbGpXTvfohBOZUfxXUDvFHs1lykfjd9jLpnh1EqNVqqvsQDrVOUM8Lf7Pmm0\r\n2K/8BaQFBj8QkJYYQGi9UyrC4LMUIWUSk6+H8rSi+BqdvYEgalUuE2R2D/SR\r\nW1p3jKIbCAicNuAJBP+a4tsSiYIsdXWczYjVVRQOfSLBQ6mWP7vpBa7Pzr+i\r\n9mDS6FsbN0UWXS43fcdSS1t1yfMw/45TRr2rSHhPuIYbWmZgcLdOkyJygOu/\r\n2Y1ruSqai0HKqJb79NoWMgD5T2tSoLg04Rrf86316+iudjmOzOH8LVzhM1fc\r\nHKaIBIIF3UhaoWnoIQpEnXay6JrzJzTrVaajgbrPTjp1OYPkMQ6VPgoYRt6w\r\ncsBNHEzegfEfqwNZqWwtUJ+6u8yqP5TlMmRz5H9vWEMsMfm3rvgCkQcwSjkJ\r\n0SZty3Es1e1ee9qJSAjX/CeNnYb8k+Z7g9dU9t6NcmSQpuPWO5ozhSYceCyi\r\nLPvSvkbG8w7wKof5JRS8xNq/UiLXmyZ24bjY7Op/VPSwzt3yJGdhz6tEJcJn\r\nkVeFNEkhwxr0mywLAuCJmvlh7VfoNnAVxV8=\r\n=VFCY\r\n-----END PGP SIGNATURE-----\r\n", "size": 88433}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/state", "version": "6.1.0", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-qbUr94DZTe6/V1VS7LDLz11rM/1t/nJxR1El4I6UaxDEdc0aZZvq6JCLJWiRmUf95NRAnDH6fhXn+PWp9wGCIg==", "shasum": "c0f1d80f61908c9dcf5e2a3fe931e9dd78f3df8a", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.1.0.tgz", "fileCount": 8, "unpackedSize": 358737, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNLc+YkeqaCfTYF/ClkNf0a+2Jv9T+Kn2Qa8fb9Kg/eAiEA7LOplLIhusoDf+rglClB1qMjgJV1EREqGjoL/JomL8M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivbY2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNUQ//RXdFh9jNRs8F8Faq4e10DURb60q+aIdCwTlTAAH5+LiSeDIB\r\n5CvqpMya/vbYBPRLg0TfTRTo93oc8/PC8sYSRmIglOH14x8APHA4CCcxVg4a\r\niIzwuaJnS417riIqX8AA3iMH2NRWtv2QsWtt8BwDxtt76PPhiZ4AkLfPiuJy\r\n9CchAJj9mfrGnfx68qsXV1wKKp/GNsy7QUx6na6tosIw9LaG3Jf7PBJfJpjh\r\nkrHRfk32RHc72G7ry7uyfVKqegPxrWtl6L5H3SkiZ2Q4kXFg3CnNz4R9Ar8U\r\nRvY+FfraGiAqcssYRY18JojbjxslGLFpyTssSF46fIU0hEFU3Ly9GW8U3Pcr\r\nVMvn7B6j8mYeh98a5le7VqkiD0RRDewn8pJP/3bMWKEfVkc5OG+/ZVfEZIpe\r\nm556Jsg3eP649M5+yutnXCJ/oNKZASbGE664q+4QwJGk9yXPq2xl/cxYv9d8\r\nQd0uaxW0ffCbNKg45v+xtLooP6Jy5Ssbd2dbmWM+l99xMoOd2mbu01bf69az\r\nr4trwxRn0/o7unMV6Vzw6pCHLzw0llMdWiq75AYZ0mPOPk4qOb3IGUMuZpoF\r\nSlZXJYster/w21r3kxWpclK10ktjrO4L2teMmIUFqF7Og2snptcjMqAVYLnm\r\nUKh5K5LbHsqWbke4rt2dmO+1vuHW2grsDfw=\r\n=SZ5r\r\n-----END PGP SIGNATURE-----\r\n", "size": 89212}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.1": {"name": "@codemirror/state", "version": "6.1.1", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-2s+aXsxmAwnR3Rd+JDHPG/1lw0YsA9PEwl7Re88gHJHGfxyfEzKBmsN4rr53RyPIR4lzbbhJX0DCq0WlqlBIRw==", "shasum": "4f512e5e34ea23a5e10b2c1fe43f6195e90417bb", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.1.1.tgz", "fileCount": 8, "unpackedSize": 359133, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgb0qUf7aBSBC1CdB6z9MZNBP9okr5SbX1GXWrQyfB3QIhAPlXSu4PqBe/lkrNe0EXziC6lWzCS2ATP26cNCQ6rn/N"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6kfIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNrw/9H800tqXZOp69IYAg0X1NdckMVgLC+pb65FXWseDe5z8j0CC/\r\nZAd1y5YTYejbOSsI1mSEuirK2zYpIBzPsBI7i2TccWdYx6X5JpzDiG27Unf9\r\ngd/JpbvqpMrziEsqNgAVrTHqLS9uVMwxigdTG9ycaqOnJsyowzR6c7XFEHII\r\nJeoKvuseGW3EEKtKrmkATccraBLguH3t/xekuI1G9bLD1aZhNTz2AEWMS1tj\r\nqBoHNbnoSrChJJG3xxxziqtGmmNbVKY6As4VDTcnKXrLyT9uCIdUcMaLnKyT\r\nSG2Joab82E8dD6heZBypVr9GYxWVFGIS5qo5Hc1nYkBJdA/2N+/XptdMCBIn\r\nw32HlgBB5zfCBXLu0MmvarYWxiODPwWg6Cm0rponPaxyDLjTW/AQizSUAxlk\r\noI4C35q4G3Ddjgo47nbr+a+KYtTsmnQQ0jwGxLDoo3msDVHa4RecjqPc1TpZ\r\n9EqdpwDHVWZyo3Y0rc7kiRf3h3HhCgGfHLpf/YIGL+5y+Aps+mo32CX2jnCB\r\noujy/Tub0eV3VjUZcKbBEougFAT40Re/xyaX9d8KijQs6hHm+dWomMBO0caK\r\ndxrfrwcufKxoytfD4c4wQaJ2LP0iIgS2WM5ESF2fQwe6huVEwYTKPyoyCOrt\r\n9AgrfHYL89EDjKJvggiZdJzFBZjdAGb1YKI=\r\n=vns+\r\n-----END PGP SIGNATURE-----\r\n", "size": 89307}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.2": {"name": "@codemirror/state", "version": "6.1.2", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-Mxff85Hp5va+zuj+H748KbubXjrinX/k28lj43H14T2D0+4kuvEFIEIO7hCEcvBT8ubZyIelt9yGOjj2MWOEQA==", "shasum": "182d46eabcc17c95508984d6add5a5a641dcd517", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.1.2.tgz", "fileCount": 8, "unpackedSize": 360343, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCzx9LESdk6Iml7DGmgOlHRFjvfy+smOvaRYXU2u20EAiB74rz5MZSC0yPCE43HXl5ExGDXpfkdW3VIiFxrMUAP0w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKuUFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYZQ/7BKtRrRRVYNOn5ovXtoe9k5/UYbrCVtVkiA0hw25d+b3Fo36j\r\n2aREj8uE9TDnFnRnT0t+OtOUM+hmJ8vbHbu+J1cgisRcistI4G/4Ko6s8Bfy\r\nEoL6ijbKf+ylI8BohjL6GXERi3kRQ8oj5Rju7nPFgRMflIytiwo1HF/wnP0m\r\npAKxZxJytPIiLT+celH+u90wYtPYvy4p4x0BHl3Jn+K3NihIcqTAPVuJX0K2\r\nuXs1RsfEXMfTJXJ1wf158WBlABT6Ourz7u0mtFMBOH+lri4WAw8w6JLby+o1\r\nX10W8WML0OKM+drjwD3W5dhr6/11SFRMvxhe3SpvuRLD2DCsP/7bNEnp0Os5\r\nsBHXHO+Q0AjEQ/yH/D+PVGJI46tQJd07V693N6WqsHziWOcm6ct9SilwsYXZ\r\n45wbXU6VIz0MpZmIZZXZn7LP2BVm9f23JNkWbJwrmJUbQ3deO65qTlG9H2ZN\r\nvsUZeNrAFYjeDyCMji+jvd6Hz9Gf2LaXqLH9T3Ivj/KeO/hJzvM32nJmhh6l\r\nBUCQB0dHBpVPmxVSP9r+YokARzRxjMIFO2Q4y5ENjXo0ZGdmbQvYRIzYSGcv\r\nDjmLZ7M43RZ7kuG5SdJWwi8nRt30KvQb0Z2wQLlESsOymUD1HrT6aOPKnXD8\r\nRmSSXmhOwk2JPDdJU/9Rnk4MiKqgKxksmuQ=\r\n=6tvz\r\n-----END PGP SIGNATURE-----\r\n", "size": 89442}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.3": {"name": "@codemirror/state", "version": "6.1.3", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-0Rn7vadZ6EgHaKdIOwyhBWLdPDh1JM5USYqXjxwrvpmTKWu4wQ77twgAYEg1MU282XcrnV4ZqFf+00bu6UPCyg==", "shasum": "39bf545c589f51b6978716d188d34f7d7c483350", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.1.3.tgz", "fileCount": 8, "unpackedSize": 362731, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdSjwUkYsBgE/Mxkl0Dq8U1pN6UXuuGQXVdi/G96GLWgIgdqy6ZZPI5X3mK0IZkloC+ihW0yEeXgZhaTcQPXjQ7q4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbR05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvOA/+KcxdzzFi+UWMunGIyDDq7IsB5D5rBAvZSGudC7xbqvlS4nnK\r\nngkfJv4dY0o9trUzRyS+wBt0hfdInPSu8oYzbDqEHLht6xUxZt2s3mbconNF\r\nqnJ3FIkg7Ycv31avlvPnglD5EGP0S1iv+T3zrJAdhfv+Lj8rnfoFxhXFOHwi\r\ngqlPN+ZqTdsjst8QSSiStsekyfDkXTKa0Wcct2ckiPq/EfvDtCmSnEuld7KH\r\nrmRM+TVEcraqDje+8+EUPg3pLLp3MG1qwihNeCtT8JlhkSrn4vIObSEucRf8\r\nKF3fWT+8ed2O1fSeCN2gIVtjkTECSF6bTy63MsZvYi/FhTjHCTGoyqvipKc5\r\nyg79bxqKo1XqcYB1Tu5fGN8wDEUn6HQzYlz1M68sdTNa9I7idmqMkYgnR5yT\r\nGdv+qPNZEuebLF1NFfRLP2+DhFI5l8+6DB283G6whyBuqhRVIHNgbtVUQIHm\r\n6b9j+lgHbPQepfaeNWeNP3siDbp4X+W0oaxvfgJP7wSNO3amldLXl3qPxj5r\r\nMtiZAF7FqJRfVjn2JQQQmn6bC5iSH3q+Q+THEO5tQTtplXo+SUaTUgX+55GJ\r\n888QoW31VRcBEAQS+ITSFJGqtdFCQ+YlpGpp1eIOomTXIxfeZJzOtxOfH05L\r\nD8URpzxAF/WYH3dyZGyf4+2h79en7MxaW5k=\r\n=mdNe\r\n-----END PGP SIGNATURE-----\r\n", "size": 90104}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.4": {"name": "@codemirror/state", "version": "6.1.4", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-g+3OJuRylV5qsXuuhrc6Cvs1NQluNioepYMM2fhnpYkNk7NgX+j0AFuevKSVKzTDmDyt9+Puju+zPdHNECzCNQ==", "shasum": "2b654ae233ac4f41ee89ce095509ea35ecdf1031", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.1.4.tgz", "fileCount": 8, "unpackedSize": 363005, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhIHG1Cs2eFckicWzatLhq+4WGqYp1qPoMdgXtGBdBrAiEAnaCZEroG8b9upQ8NjjG3y8OnmEy/R3iwxgptTVzYiBM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc7qFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFARAAgvnMrBhGTjg1nvpXMAvx9qhYpJ0+sgQDETv5Il4fFhcGH50W\r\nhQH4GvuQV7xNTqxGX6PGOiBcF4xY0+P1I4ed1rHQs8kDanpmrr4Ks9sfCRQF\r\nKlPYeBNtXGPgP5vxTuSVtq0Tpy1mOK8P4La14+MRCx0Kw/isb2B6l7HXQLJ1\r\nMfwYWV5kB4DMu7Yf3VQSLiFNlSIH3tIBte+hTMpeBdDkhkNBne9hUUjdzVrn\r\nnLp/SkyadQjs51zqHpjxcQ63gPPpmoDW/im7LByb0w5XkT65pOKprYT8ym5I\r\n+pkNGOvVL8G5dTUz57OKGCh4z0Qv7WTpw5HH0aGXQId7R1M2kW4PvdPTuDwh\r\nas+N2utUsblUAo8TukCkovJAoqmwzDQQb2KtMxmwVjg2BYl6Tf/ClqNTqy0Z\r\n7+j4fnby3PpUmJJg4qhcT3XynloM5bo3oIGmrhWgknquDhTo7MehGJiU0Ibp\r\nF+mYlRZFtx9chlCzHwJdqC8/hhU0pBwLPTxOQKOO3+EbORgFX6T9Cx4/US42\r\n6zMFUhajb4NfA6bjibILF55lpGWTwuY3eRz8dxCdpPnjDFadYLgVlFeRs3IK\r\nuh7qd2S1B5Obr8UZVqM/IsFZoGAkTtU1ih8q1pwsITfATxst7J6Xgw/Kd4Jy\r\n8n+tC9lgjBY11rBhus1nSL3H5C5gwu6NAng=\r\n=xu4W\r\n-----END PGP SIGNATURE-----\r\n", "size": 90235}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/state", "version": "6.2.0", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-69QXtcrsc3RYtOtd+GsvczJ319udtBf1PTrr2KbLWM/e2CXUPnh0Nz9AUo8WfhSQ7GeL8dPVNUmhQVgpmuaNGA==", "shasum": "a0fb08403ced8c2a68d1d0acee926bd20be922f2", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.2.0.tgz", "fileCount": 8, "unpackedSize": 363342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYA9PmdS7a3Mq+JCa87T4hArTAd83Vi6/xcZotwuHD9AiEAykTEfXcPgD5RXvC6WsXovWwx6+VKpZ/MgV5H9ZIhpdY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqXnBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9IRAAgKd5RAJWeIX6hWMwz6jv3Rnucchveyml/069rn3KlCxcSdTg\r\n/41HZ44n6TABSFo3WqIUEPre+S6vjkrF6I+Kii7QX8XC22aFenQvI/GUgPDW\r\n+LJsDg9sWK9CgXZQJ/wa51w6OtuDGa07vTbWRT2DHUJyiKM863iFmzqyRFGi\r\nGebLDHeAldcD452+FktFa9HFLdZVF45bLeaiYkI+0FbtaHRSJYuvRolDQB0h\r\nb6ZWiTaq6GglV+qsKuU8BabF7S/uk5R7DX9WwlPxGijMsAso66+hstE/U9Wq\r\nMUB1uiQGnp432cpskfaWAPz+uqP5M0papG1Ee4tH5572Rav0pInP4Eei/zN3\r\nTGthjymtzAJJ2eP18cWv/xdv9o3SOytg2el6noUiTqhIvcOVv1cLgMyHX3Cx\r\nxf+7heAdOlJLY+XCKoADYswrimr0oY2svOXA3AlJuujkla7nce0i25wxa9Ta\r\nkT9MZlC2Fmt90BmVuzBy4X10IlyIn492OT9xN5vicjfhL2DL6UXgdlsX9y0Y\r\n9bJhJGLAYRwfF9MrQvuA/H7pGkYEMY41USAFklr1wXd7wgMBMlrTptcpYRa0\r\nM1TSl4QLUBvzfwJq8UcQpzIsREK2MCxTXoI0yrx8RuZjwWiZyRieGTFdP52l\r\nC3ikEbTjzKuQDP8mY58236Sp7FVZT9vBkWU=\r\n=TJVC\r\n-----END PGP SIGNATURE-----\r\n", "size": 90298}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.1": {"name": "@codemirror/state", "version": "6.2.1", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "directories": {}, "dist": {"integrity": "sha512-Rup<PERSON>Z8+OjNT38zU9fKH2sv+Dnlr8Eb8sl4NOnnqz95mCFTZUaiRP8Xv5MeeaG0px2b8Bnfe7YGwCV3nsBhbuw==", "shasum": "6dc8d8e5abb26b875e3164191872d69a5e85bd73", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.2.1.tgz", "fileCount": 8, "unpackedSize": 364058, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDX7BUhBd08K8DH3T6M+n5pTYDbgwLo8bJ7R+9vVD+CtgIhAJazMX9toWOHKQ6SW6S8bz3SSUkCXKFrls9Foiv6nU54"}], "size": 90488}, "_hasShrinkwrap": false, "publish_time": 1684832368348, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.0": {"name": "@codemirror/state", "version": "6.3.0", "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-5fIS19U46PEqczbBL6gBAtju9MFDT9TjIC/q2MYblHCEKiU8jhV3cRFhvQu5tQvbtxc5KLWxSnzMNh3ZqeaXVg==", "shasum": "cc4045ec89b33a12d16184717086955892d1cf06", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.3.0.tgz", "fileCount": 9, "unpackedSize": 430542, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBNRikz4zJAGZ3M+OoSCCY6yOmTKsz78eVg5BzuuVN+eAiBj0GKFi5rOxvIE/vFj67BDo2OF+W8dlDAhz6XsGuAKXA=="}], "size": 107649}, "_hasShrinkwrap": false, "publish_time": 1697088746609, "_source_registry_name": "default"}, "6.3.1": {"name": "@codemirror/state", "version": "6.3.1", "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-88e4HhMtKJyw6fKprGaN/yZfiaoGYOi2nM45YCUC6R/kex9sxFWBDGatS1vk4lMgnWmdIIB9tk8Gj1LmL8YfvA==", "shasum": "acabbbaeedcbfd31680704aba22102a75104f434", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.3.1.tgz", "fileCount": 9, "unpackedSize": 430716, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQe1D83+ngYllNEyy59k4K2S1qJTuL5rBKddXiKEm2+gIhAIWydeZ8XRntmEHqzu5j4IRCkWNpW7YtGAbOH77EdutS"}], "size": 107680}, "_hasShrinkwrap": false, "publish_time": 1697614609243, "_source_registry_name": "default"}, "6.3.2": {"name": "@codemirror/state", "version": "6.3.2", "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-5jEikOfU0r9y+OTlZn5AEQB15mibu3deLBUp+GnLzVUNezEEuPt/JdSeniQNi+0YviblAvOPO2JQAlgJ3SYYaA==", "shasum": "a5679cbf02b334bbdbf79bd2f60641da45cb06a6", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.3.2.tgz", "fileCount": 9, "unpackedSize": 431415, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQDWGGMMp55uqsDhA0L5D3LAQEgPhRQn4u2Zgw9lAiXmKgIfJPM3jhFtTjsqnBSDidMpkE4YtKtff2hli6LX4SnHZw=="}], "size": 107828}, "_hasShrinkwrap": false, "publish_time": 1701078892367, "_source_registry_name": "default"}, "6.3.3": {"name": "@codemirror/state", "version": "6.3.3", "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-0wufKcTw2dEwEaADajjHf6hBy1sh3M6V0e+q4JKIhLuiMSe5td5HOWpUdvKth1fT1M9VYOboajoBHpkCd7PG7A==", "shasum": "6a647c2fa62b68604187152de497e91aabf43f82", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.3.3.tgz", "fileCount": 9, "unpackedSize": 432430, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEQclMz0UwFptklKa0H414bgh8oEL4uRiXJ1Wx0feXvNAiBJm4UdY5RYXz6n9iDXxJKVJDuf8rdgeSzTXP9FVmsm6w=="}], "size": 108003}, "_hasShrinkwrap": false, "publish_time": 1701853810482, "_source_registry_name": "default"}, "6.4.0": {"name": "@codemirror/state", "version": "6.4.0", "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-hm8XshYj5Fo30Bb922QX9hXB/bxOAVH+qaqHBzw5TKa72vOeslyGwd4X8M0c1dJ9JqxlaMceOQ8RsL9tC7gU0A==", "shasum": "8bc3e096c84360b34525a84696a84f86b305363a", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.4.0.tgz", "fileCount": 9, "unpackedSize": 435662, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUEbiNRLZw63ZvSModGtNEMOH7nmhetmGOWHjkL8z2YAIgcU6Fx/e8uVWneqtGfdpd8U7MlNLRRmgn/QMSpRaeo+0="}], "size": 108964}, "_hasShrinkwrap": false, "publish_time": 1703784401874, "_source_registry_name": "default"}, "6.4.1": {"name": "@codemirror/state", "version": "6.4.1", "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-QkEyUiLhsJoZkbumGZlswmAhA7CBU02Wrz7zvH4SrcifbsqwlXShVXg65f3v/ts57W3dqyamEriMhij1Z3Zz4A==", "shasum": "da57143695c056d9a3c38705ed34136e2b68171b", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.4.1.tgz", "fileCount": 9, "unpackedSize": 436023, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDN9Hvu8iA4/e3v+p4q3RIaEzDGqhZnMfcz9iZm8IVe6wIhANNNv2rsaLktjW0kN54GoQY1qWQpKEH1Hc/oFg/Ym4I8"}], "size": 109079}, "_hasShrinkwrap": false, "publish_time": 1708351246394, "_source_registry_name": "default"}, "6.5.0": {"name": "@codemirror/state", "version": "6.5.0", "dependencies": {"@marijn/find-cluster-break": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-MwBHVK60IiIHDcoMet78lxt6iw5gJOGSbNbOIVBHWVXIH4/Nq1+GQgLLGgI1KlnN86WDXsPudVaqYHKBIx7Eyw==", "shasum": "e98dde85620618651543152fe1c2483300a0ccc9", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.5.0.tgz", "fileCount": 9, "unpackedSize": 430769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfXe/UQz12f3vjcGrDhOzmqrMSXNC+a/49qL4cbEErkAIgU1D701vdyq5GXrz2dqX06rpzZGxxiTz2O2Uxp9sNOa0="}], "size": 106677}, "_hasShrinkwrap": false, "publish_time": 1733747188029, "_source_registry_name": "default"}, "6.5.1": {"name": "@codemirror/state", "version": "6.5.1", "dependencies": {"@marijn/find-cluster-break": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-3rA<PERSON>lcwciEB47ZevqvD8qgbzhM9qMb8vCcQCNmDfVRPQG4JT9mSb0Jg8H7YjKGGQcFnLN323fj9jdnG59Kx6bg==", "shasum": "e5c0599f7b43cf03f19e05861317df5425c07904", "tarball": "https://registry.npmmirror.com/@codemirror/state/-/state-6.5.1.tgz", "fileCount": 9, "unpackedSize": 431005, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLMTaGvunN+pu1ya4orUimTX+iwxwJJp5fVzrNekoHXgIgbPWUg2THm5kXUIBbw43pJRCrX9V1aWjeABhSlRMh/24="}], "size": 106746}, "_hasShrinkwrap": false, "publish_time": 1736493073057, "_source_registry_name": "default"}}, "_source_registry_name": "default"}