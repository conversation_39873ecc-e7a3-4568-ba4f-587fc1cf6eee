{"source": 1102341, "name": "esbuild", "dependency": "esbuild", "title": "esbuild enables any website to send any requests to the development server and read the response", "url": "https://github.com/advisories/GHSA-67mh-4wv8-2f99", "severity": "moderate", "versions": ["0.0.0", "0.0.4", "0.0.11", "0.0.12", "0.0.13", "0.0.14", "0.0.15", "0.0.16", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.1.5", "0.1.6", "0.1.7", "0.1.8", "0.1.9", "0.1.10", "0.1.11", "0.1.12", "0.1.13", "0.1.14", "0.1.15", "0.1.16", "0.1.17", "0.1.18", "0.1.19", "0.1.20", "0.1.21", "0.1.22", "0.1.23", "0.1.24", "0.1.25", "0.1.26", "0.2.0", "0.2.1", "0.2.2", "0.2.3", "0.2.4", "0.2.5", "0.2.6", "0.2.7", "0.2.8", "0.2.9", "0.2.10", "0.2.11", "0.2.12", "0.3.0", "0.3.1", "0.3.2", "0.3.3", "0.3.4", "0.3.5", "0.3.6", "0.3.7", "0.3.8", "0.3.9", "0.4.0", "0.4.1", "0.4.2", "0.4.3", "0.4.4", "0.4.5", "0.4.6", "0.4.7", "0.4.8", "0.4.9", "0.4.10", "0.4.11", "0.4.12", "0.4.13", "0.4.14", "0.5.0", "0.5.1", "0.5.2", "0.5.3", "0.5.4", "0.5.5", "0.5.6", "0.5.7", "0.5.8", "0.5.9", "0.5.10", "0.5.11", "0.5.12", "0.5.13", "0.5.14", "0.5.15", "0.5.16", "0.5.17", "0.5.18", "0.5.19", "0.5.20", "0.5.21", "0.5.22", "0.5.23", "0.5.24", "0.5.25", "0.5.26", "0.6.0", "0.6.1", "0.6.2", "0.6.3", "0.6.4", "0.6.5", "0.6.6", "0.6.7", "0.6.8", "0.6.9", "0.6.10", "0.6.11", "0.6.12", "0.6.13", "0.6.14", "0.6.15", "0.6.16", "0.6.17", "0.6.18", "0.6.19", "0.6.20", "0.6.21", "0.6.22", "0.6.23", "0.6.24", "0.6.25", "0.6.26", "0.6.27", "0.6.28", "0.6.29", "0.6.30", "0.6.31", "0.6.32", "0.6.33", "0.6.34", "0.7.0", "0.7.1", "0.7.2", "0.7.3", "0.7.4", "0.7.5", "0.7.6", "0.7.7", "0.7.8", "0.7.9", "0.7.10", "0.7.11", "0.7.12", "0.7.13", "0.7.14", "0.7.15", "0.7.16", "0.7.17", "0.7.18", "0.7.19", "0.7.20", "0.7.21", "0.7.22", "0.8.0", "0.8.1", "0.8.2", "0.8.3", "0.8.4", "0.8.5", "0.8.6", "0.8.7", "0.8.8", "0.8.9", "0.8.10", "0.8.11", "0.8.12", "0.8.13", "0.8.14", "0.8.15", "0.8.16", "0.8.17", "0.8.18", "0.8.19", "0.8.20", "0.8.21", "0.8.22", "0.8.23", "0.8.24", "0.8.25", "0.8.26", "0.8.27", "0.8.28", "0.8.29", "0.8.30", "0.8.31", "0.8.32", "0.8.33", "0.8.34", "0.8.35", "0.8.36", "0.8.37", "0.8.38", "0.8.39", "0.8.40", "0.8.41", "0.8.42", "0.8.43", "0.8.44", "0.8.45", "0.8.46", "0.8.47", "0.8.48", "0.8.49", "0.8.50", "0.8.51", "0.8.52", "0.8.53", "0.8.54", "0.8.55", "0.8.56", "0.8.57", "0.9.0", "0.9.1", "0.9.2", "0.9.3", "0.9.4", "0.9.5", "0.9.6", "0.9.7", "0.10.0", "0.10.1", "0.10.2", "0.11.0", "0.11.1", "0.11.2", "0.11.3", "0.11.4", "0.11.5", "0.11.6", "0.11.7", "0.11.8", "0.11.9", "0.11.10", "0.11.11", "0.11.12", "0.11.13", "0.11.14", "0.11.15", "0.11.16", "0.11.17", "0.11.18", "0.11.19", "0.11.20", "0.11.21", "0.11.22", "0.11.23", "0.12.0", "0.12.1", "0.12.2", "0.12.3", "0.12.4", "0.12.5", "0.12.6", "0.12.7", "0.12.8", "0.12.9", "0.12.10", "0.12.11", "0.12.12", "0.12.13", "0.12.14", "0.12.15", "0.12.16", "0.12.17", "0.12.18", "0.12.19", "0.12.20", "0.12.21", "0.12.22", "0.12.23", "0.12.24", "0.12.25", "0.12.26", "0.12.27", "0.12.28", "0.12.29", "0.13.0", "0.13.1", "0.13.2", "0.13.3", "0.13.4", "0.13.5", "0.13.6", "0.13.7", "0.13.8", "0.13.9", "0.13.10", "0.13.11", "0.13.12", "0.13.13", "0.13.14", "0.13.15", "0.14.0", "0.14.1", "0.14.2", "0.14.3", "0.14.4", "0.14.5", "0.14.6", "0.14.7", "0.14.8", "0.14.9", "0.14.10", "0.14.11", "0.14.12", "0.14.13", "0.14.14", "0.14.15", "0.14.16", "0.14.17", "0.14.18", "0.14.19", "0.14.20", "0.14.21", "0.14.22", "0.14.23", "0.14.24", "0.14.25", "0.14.26", "0.14.27", "0.14.28", "0.14.29", "0.14.30", "0.14.31", "0.14.32", "0.14.33", "0.14.34", "0.14.35", "0.14.36", "0.14.37", "0.14.38", "0.14.39", "0.14.40", "0.14.41", "0.14.42", "0.14.43", "0.14.44", "0.14.45", "0.14.46", "0.14.47", "0.14.48", "0.14.49", "0.14.50", "0.14.51", "0.14.52", "0.14.53", "0.14.54", "0.15.0", "0.15.1", "0.15.2", "0.15.3", "0.15.4", "0.15.5", "0.15.6", "0.15.7", "0.15.8", "0.15.9", "0.15.10", "0.15.11", "0.15.12", "0.15.13", "0.15.14", "0.15.15", "0.15.16", "0.15.17", "0.15.18", "0.16.0", "0.16.1", "0.16.2", "0.16.3", "0.16.4", "0.16.5", "0.16.6", "0.16.7", "0.16.8", "0.16.9", "0.16.10", "0.16.11", "0.16.12", "0.16.13", "0.16.14", "0.16.15", "0.16.16", "0.16.17", "0.17.0", "0.17.1", "0.17.2", "0.17.3", "0.17.4", "0.17.5", "0.17.6", "0.17.7", "0.17.8", "0.17.9", "0.17.10", "0.17.11", "0.17.12", "0.17.13", "0.17.14", "0.17.15", "0.17.16", "0.17.17", "0.17.18", "0.17.19", "0.18.0", "0.18.1", "0.18.2", "0.18.3", "0.18.4", "0.18.5", "0.18.6", "0.18.7", "0.18.8", "0.18.9", "0.18.10", "0.18.11", "0.18.12", "0.18.13", "0.18.14", "0.18.15", "0.18.16", "0.18.17", "0.18.18", "0.18.19", "0.18.20", "0.19.0", "0.19.1", "0.19.2", "0.19.3", "0.19.4", "0.19.5", "0.19.6", "0.19.7", "0.19.8", "0.19.9", "0.19.10", "0.19.11", "0.19.12", "0.20.0", "0.20.1", "0.20.2", "0.21.0", "0.21.1", "0.21.2", "0.21.3", "0.21.4", "0.21.5", "0.22.0", "0.23.0", "0.23.1", "0.24.0", "0.24.1", "0.24.2", "0.25.0", "0.25.1", "0.25.2", "0.25.3", "0.25.4"], "vulnerableVersions": ["0.0.0", "0.0.4", "0.0.11", "0.0.12", "0.0.13", "0.0.14", "0.0.15", "0.0.16", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.1.5", "0.1.6", "0.1.7", "0.1.8", "0.1.9", "0.1.10", "0.1.11", "0.1.12", "0.1.13", "0.1.14", "0.1.15", "0.1.16", "0.1.17", "0.1.18", "0.1.19", "0.1.20", "0.1.21", "0.1.22", "0.1.23", "0.1.24", "0.1.25", "0.1.26", "0.2.0", "0.2.1", "0.2.2", "0.2.3", "0.2.4", "0.2.5", "0.2.6", "0.2.7", "0.2.8", "0.2.9", "0.2.10", "0.2.11", "0.2.12", "0.3.0", "0.3.1", "0.3.2", "0.3.3", "0.3.4", "0.3.5", "0.3.6", "0.3.7", "0.3.8", "0.3.9", "0.4.0", "0.4.1", "0.4.2", "0.4.3", "0.4.4", "0.4.5", "0.4.6", "0.4.7", "0.4.8", "0.4.9", "0.4.10", "0.4.11", "0.4.12", "0.4.13", "0.4.14", "0.5.0", "0.5.1", "0.5.2", "0.5.3", "0.5.4", "0.5.5", "0.5.6", "0.5.7", "0.5.8", "0.5.9", "0.5.10", "0.5.11", "0.5.12", "0.5.13", "0.5.14", "0.5.15", "0.5.16", "0.5.17", "0.5.18", "0.5.19", "0.5.20", "0.5.21", "0.5.22", "0.5.23", "0.5.24", "0.5.25", "0.5.26", "0.6.0", "0.6.1", "0.6.2", "0.6.3", "0.6.4", "0.6.5", "0.6.6", "0.6.7", "0.6.8", "0.6.9", "0.6.10", "0.6.11", "0.6.12", "0.6.13", "0.6.14", "0.6.15", "0.6.16", "0.6.17", "0.6.18", "0.6.19", "0.6.20", "0.6.21", "0.6.22", "0.6.23", "0.6.24", "0.6.25", "0.6.26", "0.6.27", "0.6.28", "0.6.29", "0.6.30", "0.6.31", "0.6.32", "0.6.33", "0.6.34", "0.7.0", "0.7.1", "0.7.2", "0.7.3", "0.7.4", "0.7.5", "0.7.6", "0.7.7", "0.7.8", "0.7.9", "0.7.10", "0.7.11", "0.7.12", "0.7.13", "0.7.14", "0.7.15", "0.7.16", "0.7.17", "0.7.18", "0.7.19", "0.7.20", "0.7.21", "0.7.22", "0.8.0", "0.8.1", "0.8.2", "0.8.3", "0.8.4", "0.8.5", "0.8.6", "0.8.7", "0.8.8", "0.8.9", "0.8.10", "0.8.11", "0.8.12", "0.8.13", "0.8.14", "0.8.15", "0.8.16", "0.8.17", "0.8.18", "0.8.19", "0.8.20", "0.8.21", "0.8.22", "0.8.23", "0.8.24", "0.8.25", "0.8.26", "0.8.27", "0.8.28", "0.8.29", "0.8.30", "0.8.31", "0.8.32", "0.8.33", "0.8.34", "0.8.35", "0.8.36", "0.8.37", "0.8.38", "0.8.39", "0.8.40", "0.8.41", "0.8.42", "0.8.43", "0.8.44", "0.8.45", "0.8.46", "0.8.47", "0.8.48", "0.8.49", "0.8.50", "0.8.51", "0.8.52", "0.8.53", "0.8.54", "0.8.55", "0.8.56", "0.8.57", "0.9.0", "0.9.1", "0.9.2", "0.9.3", "0.9.4", "0.9.5", "0.9.6", "0.9.7", "0.10.0", "0.10.1", "0.10.2", "0.11.0", "0.11.1", "0.11.2", "0.11.3", "0.11.4", "0.11.5", "0.11.6", "0.11.7", "0.11.8", "0.11.9", "0.11.10", "0.11.11", "0.11.12", "0.11.13", "0.11.14", "0.11.15", "0.11.16", "0.11.17", "0.11.18", "0.11.19", "0.11.20", "0.11.21", "0.11.22", "0.11.23", "0.12.0", "0.12.1", "0.12.2", "0.12.3", "0.12.4", "0.12.5", "0.12.6", "0.12.7", "0.12.8", "0.12.9", "0.12.10", "0.12.11", "0.12.12", "0.12.13", "0.12.14", "0.12.15", "0.12.16", "0.12.17", "0.12.18", "0.12.19", "0.12.20", "0.12.21", "0.12.22", "0.12.23", "0.12.24", "0.12.25", "0.12.26", "0.12.27", "0.12.28", "0.12.29", "0.13.0", "0.13.1", "0.13.2", "0.13.3", "0.13.4", "0.13.5", "0.13.6", "0.13.7", "0.13.8", "0.13.9", "0.13.10", "0.13.11", "0.13.12", "0.13.13", "0.13.14", "0.13.15", "0.14.0", "0.14.1", "0.14.2", "0.14.3", "0.14.4", "0.14.5", "0.14.6", "0.14.7", "0.14.8", "0.14.9", "0.14.10", "0.14.11", "0.14.12", "0.14.13", "0.14.14", "0.14.15", "0.14.16", "0.14.17", "0.14.18", "0.14.19", "0.14.20", "0.14.21", "0.14.22", "0.14.23", "0.14.24", "0.14.25", "0.14.26", "0.14.27", "0.14.28", "0.14.29", "0.14.30", "0.14.31", "0.14.32", "0.14.33", "0.14.34", "0.14.35", "0.14.36", "0.14.37", "0.14.38", "0.14.39", "0.14.40", "0.14.41", "0.14.42", "0.14.43", "0.14.44", "0.14.45", "0.14.46", "0.14.47", "0.14.48", "0.14.49", "0.14.50", "0.14.51", "0.14.52", "0.14.53", "0.14.54", "0.15.0", "0.15.1", "0.15.2", "0.15.3", "0.15.4", "0.15.5", "0.15.6", "0.15.7", "0.15.8", "0.15.9", "0.15.10", "0.15.11", "0.15.12", "0.15.13", "0.15.14", "0.15.15", "0.15.16", "0.15.17", "0.15.18", "0.16.0", "0.16.1", "0.16.2", "0.16.3", "0.16.4", "0.16.5", "0.16.6", "0.16.7", "0.16.8", "0.16.9", "0.16.10", "0.16.11", "0.16.12", "0.16.13", "0.16.14", "0.16.15", "0.16.16", "0.16.17", "0.17.0", "0.17.1", "0.17.2", "0.17.3", "0.17.4", "0.17.5", "0.17.6", "0.17.7", "0.17.8", "0.17.9", "0.17.10", "0.17.11", "0.17.12", "0.17.13", "0.17.14", "0.17.15", "0.17.16", "0.17.17", "0.17.18", "0.17.19", "0.18.0", "0.18.1", "0.18.2", "0.18.3", "0.18.4", "0.18.5", "0.18.6", "0.18.7", "0.18.8", "0.18.9", "0.18.10", "0.18.11", "0.18.12", "0.18.13", "0.18.14", "0.18.15", "0.18.16", "0.18.17", "0.18.18", "0.18.19", "0.18.20", "0.19.0", "0.19.1", "0.19.2", "0.19.3", "0.19.4", "0.19.5", "0.19.6", "0.19.7", "0.19.8", "0.19.9", "0.19.10", "0.19.11", "0.19.12", "0.20.0", "0.20.1", "0.20.2", "0.21.0", "0.21.1", "0.21.2", "0.21.3", "0.21.4", "0.21.5", "0.22.0", "0.23.0", "0.23.1", "0.24.0", "0.24.1", "0.24.2"], "cwe": ["CWE-346"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:N/A:N"}, "range": "<=0.24.2", "id": "jKdCIzePj7J1488qgRXvF8Srh9wutSuR1exB8YooNX62hT+qhtrUXYbArLNoIhgwbaCqKpmduh18syZU7BR9SA=="}