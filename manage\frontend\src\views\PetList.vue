<template>
  <div class="pet-list">
    <!-- 复用大部分 CarList.vue 的结构,修改相关字段 -->
    <div v-if="permissionChecking" class="loading-container">
      <el-loading :fullscreen="true" text="权限检查中..." />
    </div>
    <div v-else-if="!hasPermission" class="no-permission">
      <el-empty description="您没有查看宠物图鉴的权限">
        <template #extra>
          <p>请联系管理员开通权限</p>
        </template>
      </el-empty>
    </div>
    <template v-else>
      <div class="content-container" v-loading="loading">
        <div class="header-operations">
          <div class="search-filters">
            <el-input
              v-model="searchQuery.name"
              placeholder="搜索宠物名称"
              class="search-input"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <div class="filter-group">
              <el-input
                v-model="searchQuery.pet_id"
                placeholder="搜索宠物代码"
                class="search-input"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Document /></el-icon>
                </template>
              </el-input>
              <el-input
                v-model="searchQuery.form"
                placeholder="搜索宠物形态"
                class="search-input"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Collection /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
          <div class="operation-buttons">
            <el-upload
              class="excel-upload"
              accept=".xlsx, .xls"
              :show-file-list="false"
              :before-upload="handleExcelImport"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                导入Excel
              </el-button>
            </el-upload>
            <el-button 
              type="success" 
              @click="handleExcelExport"
              :loading="exporting"
              :disabled="exporting"
            >
              <el-icon><Download /></el-icon>
              {{ exporting ? '正在导出...' : '导出Excel' }}
            </el-button>
            <el-button type="primary" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              添加宠物
            </el-button>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="4" v-for="pet in pets" :key="pet.id">
            <el-card :body-style="{ padding: '0px' }" class="pet-card">
              <img 
                v-lazy="pet.image || '/default-pet.png'"
                class="pet-image"
                loading="lazy"
                :alt="pet.name"
                @error="handleImageError"
                @load="handleImageLoad"
              >
              <div class="pet-info">
                <h3>{{ pet.name }}</h3>
                <p>代码: {{ pet.pet_id }}</p>
                <p>形态: {{ pet.form }}</p>
                <div class="skill-info">
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="pet.basic_skill"
                    placement="top"
                  >
                    <p class="truncate">
                      <span class="skill-label">基本技能:</span>
                      {{ pet.basic_skill }}
                    </p>
                  </el-tooltip>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="pet.enhanced_skill"
                    placement="top"
                  >
                    <p class="truncate">
                      <span class="skill-label">强化技能:</span>
                      {{ pet.enhanced_skill }}
                    </p>
                  </el-tooltip>
                </div>
                <div class="attribute-info">
                  <p>战斗力: {{ pet.combat_power }}</p>
                  <p>资质: {{ pet.aptitude }}</p>
                  <p>主属性: {{ pet.main_attribute }}</p>
                </div>
                <div class="update-time">更新于 {{ formatDateTime(pet.updated_at) }}</div>
                <div class="card-operations">
                  <el-button type="primary" @click="handleEdit(pet)">编辑</el-button>
                  <el-button type="danger" @click="handleDelete(pet)">删除</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            @current-change="handlePageChange"
            layout="prev, pager, next"
          />
        </div>
      </div>

      <!-- 添加/编辑对话框 -->
      <el-dialog
        :title="dialogType === 'add' ? '添加宠物' : '编辑宠物'"
        v-model="dialogVisible"
        width="80%"
        :close-on-click-modal="false"
        class="pet-dialog"
        top="5vh"
      >
        <el-form :model="petForm" label-width="100px">
          <el-row :gutter="15">
            <!-- 基本信息分组 -->
            <el-col :span="12">
              <div class="form-section">
                <el-form-item label="代码" required>
                  <el-input v-model="petForm.pet_id" />
                </el-form-item>
                <el-form-item label="名称" required>
                  <el-input v-model="petForm.name" />
                </el-form-item>
                <el-form-item label="形态">
                  <el-input v-model="petForm.form" />
                </el-form-item>
                <el-form-item label="战斗力">
                  <el-input v-model.number="petForm.combat_power" />
                </el-form-item>
                <el-form-item label="资质">
                  <el-input v-model="petForm.aptitude" />
                </el-form-item>
                <el-form-item label="主属性">
                  <el-input v-model="petForm.main_attribute" />
                </el-form-item>
                <el-form-item label="宠物图片">
                  <el-upload
                    class="pet-image-upload"
                    :show-file-list="false"
                    :before-upload="beforeImageUpload"
                    :http-request="handleImageUpload"
                  >
                    <template v-if="petForm.image">
                      <img 
                        :src="petForm.image" 
                        class="preview-image"
                        @error="handlePreviewImageError"
                      >
                    </template>
                    <div v-else>
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-tip">点击上传图片</div>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </el-col>
            <!-- 技能信息分组 -->
            <el-col :span="12">
              <el-form-item label="基本技能">
                <el-input 
                  type="textarea" 
                  v-model="petForm.basic_skill"
                  :rows="2"
                  resize="none"
                />
              </el-form-item>
              <el-form-item label="强化技能">
                <el-input 
                  type="textarea" 
                  v-model="petForm.enhanced_skill"
                  :rows="4"
                  resize="none"
                />
              </el-form-item>
              <el-form-item label="普通技能">
                <el-input 
                  type="textarea" 
                  v-model="petForm.normal_skill"
                  :rows="2"
                  resize="none"
                />
              </el-form-item>
              <el-form-item label="怒气技能">
                <el-input 
                  type="textarea" 
                  v-model="petForm.rage_skill"
                  :rows="2"
                  resize="none"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 导入确认对话框 -->
      <el-dialog
        v-model="importDialogVisible"
        title="导入Excel"
        width="30%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div v-if="!importing">
          <p>确认导入文件: {{ selectedFile?.name }}</p>
          <p class="import-tip">注意：导入将覆盖已有的重复数据</p>
        </div>
        <div v-else class="importing-progress">
          <el-progress type="circle" :percentage="importProgress" />
          <p class="progress-text">正在导入数据，请稍候...</p>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelImport" :disabled="importing">取消</el-button>
            <el-button type="primary" @click="confirmImport" :loading="importing">
              {{ importing ? '导入中' : '确认导入' }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createIllustrationAPI } from '@/api/illustration'
import { PET_IMAGE_URL } from '@/config'
import { useRouter } from 'vue-router'
import { testAuth } from '@/api/auth'
import { useIntersectionObserver } from '@vueuse/core'

const petAPI = createIllustrationAPI('pets')
const router = useRouter()

// 基础数据
const pets = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(24)
const total = ref(0)
const exporting = ref(false)

// 搜索条件
const searchQuery = ref({
  name: '',
  pet_id: '',
  form: '',
})

// 表单数据
const dialogVisible = ref(false)
const dialogType = ref('add')
const petForm = ref({
  pet_id: '',
  name: '',
  basic_skill: '',
  enhanced_skill: '',
  form: '',
  combat_power: '',
  aptitude: '',
  main_attribute: '',
  normal_skill: '',
  rage_skill: '',
  image_id: '',
  image: ''
})

// 权限检查
const permissionChecking = ref(true)
const hasPermission = ref(false)

// 导入相关
const importDialogVisible = ref(false)
const importing = ref(false)
const importProgress = ref(0)
const selectedFile = ref(null)

// 添加 v-lazy 指令定义
const vLazy = {
  mounted(el, binding) {
    useIntersectionObserver(el, ([{ isIntersecting }]) => {
      if (isIntersecting) {
        // 强制浏览器重新加载图片
        el.src = binding.value + (binding.value.includes('?') ? '&' : '?') + 'v=' + Date.now()
        el.style.opacity = 1
      }
    })
  },
  updated(el, binding) {
    if (binding.value !== binding.oldValue) {
      el.src = binding.value + (binding.value.includes('?') ? '&' : '?') + 'v=' + Date.now()
      el.style.opacity = 1
    }
  }
}

// 加载宠物列表
const loadPets = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      ...searchQuery.value
    }
    const response = await petAPI.getList(params)
    pets.value = response.results.map(pet => ({
      ...pet,
      image: pet.image_id ? `${PET_IMAGE_URL}/${pet.image_id}.png` : null
    }))
    total.value = response.count
  } catch (error) {
    console.error('Failed to load pets:', error)
    ElMessage.error('加载宠物列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadPets()
}

// 显示添加对话框
const showAddDialog = () => {
  dialogType.value = 'add'
  petForm.value = {
    pet_id: '',
    name: '',
    basic_skill: '',
    enhanced_skill: '',
    form: '',
    combat_power: '',
    aptitude: '',
    main_attribute: '',
    normal_skill: '',
    rage_skill: '',
    image_id: '',
    image: ''
  }
  dialogVisible.value = true
}

// 编辑宠物
const handleEdit = (pet) => {
  dialogType.value = 'edit'
  petForm.value = { ...pet }
  dialogVisible.value = true
}

// 删除宠物
const handleDelete = (pet) => {
  ElMessageBox.confirm(
    `确定要删除宠物 ${pet.name} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await petAPI.delete(pet.id)
      ElMessage.success('删除成功')
      loadPets()
    } catch (error) {
      console.error('Failed to delete pet:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 提交表单
const handleSubmit = async () => {
  try {
    if (dialogType.value === 'add') {
      await petAPI.create(petForm.value)
      ElMessage.success('添加成功')
    } else {
      await petAPI.update(petForm.value.id, petForm.value)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    loadPets()
  } catch (error) {
    console.error('Form submission failed:', error)
    ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败')
  }
}

// 图片上传相关
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleImageUpload = async ({ file }) => {
  if (!petForm.value.id) {
    ElMessage.error('请先保存宠物信息')
    return
  }

  try {
    const response = await petAPI.uploadImage(petForm.value.id, file)
    
    if (response.success) {
      petForm.value.image_id = response.image_id
      petForm.value.image = `${PET_IMAGE_URL}/${response.image_id}.png`
      ElMessage.success('图片上传成功')
      
      if (dialogType.value === 'edit') {
        await petAPI.update(petForm.value.id, {
          ...petForm.value,
          image_id: response.image_id
        })
        loadPets()
      }
    } else {
      ElMessage.error(response.message || '图片上传失败')
    }
  } catch (error) {
    console.error('Failed to upload image:', error)
    ElMessage.error('图片上传失败')
  }
}

// Excel导入导出
const handleExcelImport = (file) => {
  selectedFile.value = file
  importDialogVisible.value = true
  return false
}

const confirmImport = async () => {
  if (!selectedFile.value) return
  
  try {
    importing.value = true
    const progressInterval = setInterval(() => {
      if (importProgress.value < 90) {
        importProgress.value += 10
      }
    }, 200)

    await petAPI.import(selectedFile.value)
    
    clearInterval(progressInterval)
    importProgress.value = 100
    
    setTimeout(() => {
      importing.value = false
      importDialogVisible.value = false
      importProgress.value = 0
      selectedFile.value = null
      
      ElMessage.success('导入成功')
      loadPets()
    }, 500)
  } catch (error) {
    console.error('Import failed:', error)
    ElMessage.error('导入失败')
    importing.value = false
    importProgress.value = 0
  }
}

const cancelImport = () => {
  importDialogVisible.value = false
  selectedFile.value = null
  importProgress.value = 0
}

const handleExcelExport = async () => {
  if (exporting.value) return
  
  try {
    exporting.value = true
    ElMessage({
      message: '正在导出数据，请稍候...',
      type: 'info',
      duration: 0
    })
    
    const params = { ...searchQuery.value }
    const response = await petAPI.export(params)
    
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    
    let fileName = '宠物列表'
    if (params.name) fileName += `_搜索_${params.name}`
    if (params.form) fileName += `_${params.form}`
    fileName += '.xlsx'
    
    link.download = fileName
    link.click()
    window.URL.revokeObjectURL(link.href)
    
    ElMessage.closeAll()
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Failed to export Excel:', error)
    ElMessage.closeAll()
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  loadPets()
}

// 图片加载处理
const handleImageLoad = (e) => {
  e.target.style.opacity = 1
}

// 图片错误处理
const handleImageError = (e) => {
  console.error('Image load failed:', e.target.src)
  e.target.src = '/default-pet.png'
  // 如果不是默认图片加载失败，尝试重新加载一次
  if (!e.target.src.endsWith('default-pet.png')) {
    setTimeout(() => {
      const timestamp = new Date().getTime()
      const originalSrc = e.target.src.split('?')[0]
      e.target.src = `${originalSrc}?t=${timestamp}`
    }, 1000)
  }
}

// 添加预览图片错误处理
const handlePreviewImageError = (e) => {
  console.error('Preview image load failed:', e.target.src)
  // 当预览图片加载失败时，清空图片地址，显示上传界面
  petForm.value.image = ''
  petForm.value.image_id = ''
}

// 日期格式化
const formatDateTime = (dateString) => {
  if (!dateString) return '暂无记录'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  }).replace(/\//g, '-')
}

// 初始化
onMounted(async () => {
  try {
    const response = await testAuth()
    if (response.success) {
      hasPermission.value = true
      loadPets()
    }
  } catch (error) {
    console.error('Auth check failed:', error)
    router.push('/login')
  } finally {
    permissionChecking.value = false
  }
})
</script>

<style scoped>
.pet-list {
  padding: 0;
  background-color: transparent;
}

.content-container {
  padding: 0;
}

.header-operations {
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.filter-group {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-input {
  width: 200px;
}

.operation-buttons {
  display: flex;
  gap: 15px;
}

.pet-card {
  margin-bottom: 25px;
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.pet-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.pet-image {
  width: 100%;
  height: 160px;
  object-fit: contain;
  transition: transform 0.3s ease;
  opacity: 0;
  background-color: #f5f7fa;
  position: relative;
  display: block;
}

.pet-image[src] {
  opacity: 1;
}

.pet-card:hover .pet-image {
  transform: scale(1.05);
}

.pet-info {
  padding: 16px;
  background: white;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 280px;
}

.pet-info h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.pet-info p {
  margin: 6px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.6;
}

.attribute-info {
  margin: 12px 0;
  padding: 10px;
  background-color: #f8f9fb;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  
  p {
    margin: 6px 0;
    font-size: 13px;
    line-height: 1.6;
  }
}

.skill-info {
  margin: 12px 0;
  padding: 10px;
  background-color: #f8f9fb;
  border-radius: 6px;
  border-left: 3px solid #67c23a;
  
  .truncate {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.6;
    margin: 8px 0;
  }
  
  .skill-label {
    color: #409eff;
    font-weight: 500;
    font-size: 13px;
    margin-right: 4px;
  }
}

.update-time {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 6px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 8px;
}

.card-operations {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.card-operations .el-button {
  flex: 1;
  padding: 8px 12px;
}

.pet-image-upload {
  width: 240px;
  height: 240px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  margin-top: 0;
  
  &:hover {
    border-color: #409eff;
    background-color: rgba(64, 158, 255, 0.05);
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
    z-index: 1;  /* 确保图片不会阻挡点击事件 */
  }
  
  /* 空状态容器样式 */
  div:not(.upload-tip) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  
  .upload-icon {
    font-size: 36px;
    color: #909399;
    margin-bottom: 8px;
  }
  
  .upload-tip {
    font-size: 13px;
    color: #909399;
  }
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.importing-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.progress-text {
  margin-top: 16px;
  color: #606266;
}

.import-tip {
  color: #e6a23c;
  font-size: 14px;
  margin-top: 8px;
}

:deep(.el-input-number) {
  width: 200px;
}

/* 添加新的样式 */
:deep(.el-textarea__inner) {
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 12px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.el-form {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
}

/* 调整表单项间距 */
.el-row {
  margin-bottom: 15px;
}

/* 美化文本框样式 */
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6;
  
  &.is-focus {
    box-shadow: 0 0 0 1px #409eff !important;
  }
  
  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc;
  }
}

:deep(.el-textarea__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6;
  
  &.is-focus {
    box-shadow: 0 0 0 1px #409eff !important;
  }
  
  &:hover {
    box-shadow: 0 0 0 1px #c0c4cc;
  }
}

/* 对话框样式优化 */
:deep(.pet-dialog) {
  /* 对话框最大尺寸限制 */
  max-width: 1200px;
  min-width: 800px;
  
  /* 对话框内容区域样式 */
  .el-dialog__body {
    padding: 20px 30px;
    max-height: 80vh;
    overflow-y: auto;
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }
  
  /* 对话框头部样式 */
  .el-dialog__header {
    padding: 20px 30px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  /* 对话框底部样式 */
  .el-dialog__footer {
    padding: 20px 30px;
    border-top: 1px solid #ebeef5;
    
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      
      .el-button {
        min-width: 100px;
      }
    }
  }
}

/* 表单布局优化 */
.el-form {
  .el-row {
    margin-bottom: 0;
    
    /* 表单项分组容器 */
    .form-section {
      padding: 15px;
      
      :deep(.el-form-item) {
        margin-bottom: 25px;
        
        /* 图片上传表单项特殊处理 */
        &:last-child {
          margin-bottom: 10px;
          margin-top: 35px;  /* 给图片上传添加一些顶部间距 */
        }
      }
      
      :deep(.el-form-item__label) {
        padding-bottom: 8px;  /* 标签下方间距 */
        line-height: 1.5;
      }
      
      :deep(.el-input__wrapper) {
        padding: 4px 8px;  /* 输入框内边距 */
      }
    }

    /* 响应式布局调整 */
    @media screen and (max-width: 1200px) {
      .el-col {
        width: 100% !important;
        margin-bottom: 20px;
      }
    }
  }
  
  /* 表单项组样式 */
  .form-group {
    background-color: #f8f9fb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    
    .group-title {
      font-size: 15px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 3px solid #409eff;
    }
  }
}

/* 文本域样式优化 */
:deep(.el-textarea__inner) {
  min-height: 100px !important;
  font-family: system-ui, -apple-system, sans-serif;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
}

/* 输入框样式优化 */
:deep(.el-input__inner) {
  border-radius: 4px;
}

/* 响应式布局优化 */
@media screen and (max-width: 1600px) {
  .el-col {
    width: 16.66% !important;  /* 6列布局 */
  }
}

@media screen and (max-width: 1200px) {
  .el-col {
    width: 20% !important;  /* 5列布局 */
  }
}

@media screen and (max-width: 768px) {
  .el-col {
    width: 25% !important;  /* 4列布局 */
  }
}

@media screen and (max-width: 480px) {
  .el-col {
    width: 50% !important;  /* 2列布局 */
  }
}
</style> 