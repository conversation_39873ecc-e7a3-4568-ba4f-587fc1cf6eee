{"source": 1103516, "name": "vite", "dependency": "vite", "title": "Vite bypasses server.fs.deny when using ?raw??", "url": "https://github.com/advisories/GHSA-x574-m823-4x7w", "severity": "moderate", "versions": ["0.1.0", "0.1.1", "0.1.2", "0.2.0", "0.3.0", "0.3.1", "0.3.2", "0.4.0", "0.5.0", "0.5.1", "0.5.2", "0.5.3", "0.6.0", "0.6.1", "0.7.0", "0.8.0", "0.8.1", "0.9.0", "0.9.1", "0.10.0", "0.10.1", "0.10.2", "0.10.3", "0.11.0", "0.11.1", "0.11.2", "0.11.3", "0.11.4", "0.11.5", "0.12.0", "0.13.0", "0.13.1", "0.13.2", "0.14.0", "0.14.1", "0.14.2", "0.14.3", "0.14.4", "0.15.0", "0.15.1", "0.15.2", "0.15.3", "0.15.4", "0.15.5", "0.15.6", "0.16.0", "0.16.1", "0.16.2", "0.16.3", "0.16.4", "0.16.5", "0.16.6", "0.16.7", "0.16.8", "0.16.9", "0.16.10", "0.16.11", "0.16.12", "0.17.0", "0.17.1", "0.17.2", "0.18.0", "0.18.1", "0.19.0", "0.19.1", "0.19.2", "0.19.3", "0.20.0", "0.20.1", "0.20.2", "0.20.3", "0.20.4", "0.20.5", "0.20.6", "0.20.7", "0.20.8", "0.20.9", "0.20.10", "1.0.0-beta.1", "1.0.0-beta.2", "1.0.0-beta.3", "1.0.0-beta.4", "1.0.0-beta.5", "1.0.0-beta.6", "1.0.0-beta.7", "1.0.0-beta.8", "1.0.0-beta.9", "1.0.0-beta.10", "1.0.0-beta.11", "1.0.0-beta.12", "1.0.0-rc.1", "1.0.0-rc.2", "1.0.0-rc.3", "1.0.0-rc.4", "1.0.0-rc.5", "1.0.0-rc.6", "1.0.0-rc.7", "1.0.0-rc.8", "1.0.0-rc.9", "1.0.0-rc.10", "1.0.0-rc.11", "1.0.0-rc.12", "1.0.0-rc.13", "2.0.0-alpha.1", "2.0.0-alpha.2", "2.0.0-alpha.3", "2.0.0-alpha.4", "2.0.0-alpha.5", "2.0.0-beta.1", "2.0.0-beta.2", "2.0.0-beta.3", "2.0.0-beta.4", "2.0.0-beta.5", "2.0.0-beta.6", "2.0.0-beta.7", "2.0.0-beta.8", "2.0.0-beta.9", "2.0.0-beta.10", "2.0.0-beta.11", "2.0.0-beta.12", "2.0.0-beta.13", "2.0.0-beta.14", "2.0.0-beta.15", "2.0.0-beta.16", "2.0.0-beta.17", "2.0.0-beta.18", "2.0.0-beta.19", "2.0.0-beta.20", "2.0.0-beta.21", "2.0.0-beta.22", "2.0.0-beta.23", "2.0.0-beta.24", "2.0.0-beta.25", "2.0.0-beta.26", "2.0.0-beta.27", "2.0.0-beta.28", "2.0.0-beta.29", "2.0.0-beta.30", "2.0.0-beta.31", "2.0.0-beta.32", "2.0.0-beta.33", "2.0.0-beta.34", "2.0.0-beta.35", "2.0.0-beta.36", "2.0.0-beta.37", "2.0.0-beta.38", "2.0.0-beta.39", "2.0.0-beta.40", "2.0.0-beta.41", "2.0.0-beta.42", "2.0.0-beta.43", "2.0.0-beta.44", "2.0.0-beta.45", "2.0.0-beta.46", "2.0.0-beta.47", "2.0.0-beta.48", "2.0.0-beta.49", "2.0.0-beta.50", "2.0.0-beta.51", "2.0.0-beta.52", "2.0.0-beta.53", "2.0.0-beta.54", "2.0.0-beta.55", "2.0.0-beta.56", "2.0.0-beta.57", "2.0.0-beta.58", "2.0.0-beta.59", "2.0.0-beta.60", "2.0.0-beta.61", "2.0.0-beta.62", "2.0.0-beta.63", "2.0.0-beta.64", "2.0.0-beta.65", "2.0.0-beta.66", "2.0.0-beta.67", "2.0.0-beta.68", "2.0.0-beta.69", "2.0.0-beta.70", "2.0.0", "2.0.1", "2.0.2", "2.0.3", "2.0.4", "2.0.5", "2.1.0", "2.1.1", "2.1.2", "2.1.3", "2.1.4", "2.1.5", "2.2.0", "2.2.1", "2.2.2", "2.2.3", "2.2.4", "2.3.0", "2.3.1", "2.3.2", "2.3.3", "2.3.4", "2.3.5", "2.3.6", "2.3.7", "2.3.8", "2.4.0-beta.0", "2.4.0-beta.1", "2.4.0-beta.2", "2.4.0-beta.3", "2.4.0", "2.4.1", "2.4.2", "2.4.3", "2.4.4", "2.5.0-beta.0", "2.5.0-beta.1", "2.5.0-beta.2", "2.5.0-beta.3", "2.5.0", "2.5.1", "2.5.2", "2.5.3", "2.5.4", "2.5.5", "2.5.6", "2.5.7", "2.5.8", "2.5.9", "2.5.10", "2.6.0-beta.0", "2.6.0-beta.1", "2.6.0-beta.2", "2.6.0-beta.3", "2.6.0-beta.4", "2.6.0", "2.6.1", "2.6.2", "2.6.3", "2.6.4", "2.6.5", "2.6.6", "2.6.7", "2.6.8", "2.6.9", "2.6.10", "2.6.11", "2.6.12", "2.6.13", "2.6.14", "2.7.0-beta.0", "2.7.0-beta.1", "2.7.0-beta.2", "2.7.0-beta.3", "2.7.0-beta.4", "2.7.0-beta.5", "2.7.0-beta.6", "2.7.0-beta.7", "2.7.0-beta.8", "2.7.0-beta.9", "2.7.0-beta.10", "2.7.0-beta.11", "2.7.0", "2.7.1", "2.7.2", "2.7.3", "2.7.4", "2.7.5", "2.7.6", "2.7.7", "2.7.8", "2.7.9", "2.7.10", "2.7.11", "2.7.12", "2.7.13", "2.8.0-beta.0", "2.8.0-beta.1", "2.8.0-beta.2", "2.8.0-beta.3", "2.8.0-beta.4", "2.8.0-beta.5", "2.8.0-beta.6", "2.8.0-beta.7", "2.8.0", "2.8.1", "2.8.2", "2.8.3", "2.8.4", "2.8.5", "2.8.6", "2.9.0-beta.0", "2.9.0-beta.1", "2.9.0-beta.2", "2.9.0-beta.3", "2.9.0-beta.4", "2.9.0-beta.6", "2.9.0-beta.7", "2.9.0-beta.8", "2.9.0-beta.9", "2.9.0-beta.10", "2.9.0-beta.11", "2.9.0", "2.9.1", "2.9.2", "2.9.3", "2.9.4", "2.9.5", "2.9.6", "2.9.7", "2.9.8", "2.9.9", "2.9.10", "2.9.11", "2.9.12", "2.9.13", "2.9.14", "2.9.15", "2.9.16", "2.9.17", "2.9.18", "3.0.0-alpha.0", "3.0.0-alpha.1", "3.0.0-alpha.2", "3.0.0-alpha.3", "3.0.0-alpha.4", "3.0.0-alpha.5", "3.0.0-alpha.6", "3.0.0-alpha.7", "3.0.0-alpha.8", "3.0.0-alpha.9", "3.0.0-alpha.10", "3.0.0-alpha.11", "3.0.0-alpha.12", "3.0.0-alpha.13", "3.0.0-alpha.14", "3.0.0-beta.0", "3.0.0-beta.1", "3.0.0-beta.2", "3.0.0-beta.3", "3.0.0-beta.4", "3.0.0-beta.5", "3.0.0-beta.6", "3.0.0-beta.7", "3.0.0-beta.8", "3.0.0-beta.9", "3.0.0-beta.10", "3.0.0", "3.0.1", "3.0.2", "3.0.3", "3.0.4", "3.0.5", "3.0.6", "3.0.7", "3.0.8", "3.0.9", "3.1.0-beta.0", "3.1.0-beta.1", "3.1.0-beta.2", "3.1.0", "3.1.1", "3.1.2", "3.1.3", "3.1.4", "3.1.5", "3.1.6", "3.1.7", "3.1.8", "3.2.0-beta.0", "3.2.0-beta.1", "3.2.0-beta.2", "3.2.0-beta.3", "3.2.0-beta.4", "3.2.0", "3.2.1", "3.2.2", "3.2.3", "3.2.4", "3.2.5", "3.2.6", "3.2.7", "3.2.8", "3.2.10", "3.2.11", "4.0.0-alpha.0", "4.0.0-alpha.1", "4.0.0-alpha.2", "4.0.0-alpha.3", "4.0.0-alpha.4", "4.0.0-alpha.5", "4.0.0-alpha.6", "4.0.0-beta.0", "4.0.0-beta.1", "4.0.0-beta.2", "4.0.0-beta.3", "4.0.0-beta.4", "4.0.0-beta.5", "4.0.0-beta.6", "4.0.0-beta.7", "4.0.0", "4.0.1", "4.0.2", "4.0.3", "4.0.4", "4.0.5", "4.1.0-beta.0", "4.1.0-beta.1", "4.1.0-beta.2", "4.1.0", "4.1.1", "4.1.2", "4.1.3", "4.1.4", "4.1.5", "4.2.0-beta.0", "4.2.0-beta.1", "4.2.0-beta.2", "4.2.0", "4.2.1", "4.2.2", "4.2.3", "4.3.0-beta.0", "4.3.0-beta.1", "4.3.0-beta.2", "4.3.0-beta.3", "4.3.0-beta.4", "4.3.0-beta.5", "4.3.0-beta.6", "4.3.0-beta.7", "4.3.0-beta.8", "4.3.0", "4.3.1", "4.3.2", "4.3.3", "4.3.4", "4.3.5", "4.3.6", "4.3.7", "4.3.8", "4.3.9", "4.4.0-beta.0", "4.4.0-beta.1", "4.4.0-beta.2", "4.4.0-beta.3", "4.4.0-beta.4", "4.4.0", "4.4.1", "4.4.2", "4.4.3", "4.4.4", "4.4.5", "4.4.6", "4.4.7", "4.4.8", "4.4.9", "4.4.10", "4.4.11", "4.4.12", "4.5.0", "4.5.1", "4.5.2", "4.5.3", "4.5.5", "4.5.6", "4.5.7", "4.5.8", "4.5.9", "4.5.10", "4.5.11", "4.5.12", "4.5.13", "4.5.14", "5.0.0-beta.0", "5.0.0-beta.1", "5.0.0-beta.2", "5.0.0-beta.3", "5.0.0-beta.4", "5.0.0-beta.5", "5.0.0-beta.6", "5.0.0-beta.7", "5.0.0-beta.8", "5.0.0-beta.9", "5.0.0-beta.10", "5.0.0-beta.11", "5.0.0-beta.12", "5.0.0-beta.13", "5.0.0-beta.14", "5.0.0-beta.15", "5.0.0-beta.16", "5.0.0-beta.17", "5.0.0-beta.18", "5.0.0-beta.19", "5.0.0-beta.20", "5.0.0", "5.0.1", "5.0.2", "5.0.3", "5.0.4", "5.0.5", "5.0.6", "5.0.7", "5.0.8", "5.0.9", "5.0.10", "5.0.11", "5.0.12", "5.0.13", "5.1.0-beta.0", "5.1.0-beta.1", "5.1.0-beta.2", "5.1.0-beta.3", "5.1.0-beta.4", "5.1.0-beta.5", "5.1.0-beta.6", "5.1.0-beta.7", "5.1.0", "5.1.1", "5.1.2", "5.1.3", "5.1.4", "5.1.5", "5.1.6", "5.1.7", "5.1.8", "5.2.0-beta.0", "5.2.0-beta.1", "5.2.0", "5.2.1", "5.2.2", "5.2.3", "5.2.4", "5.2.5", "5.2.6", "5.2.7", "5.2.8", "5.2.9", "5.2.10", "5.2.11", "5.2.12", "5.2.13", "5.2.14", "5.3.0-beta.0", "5.3.0-beta.1", "5.3.0-beta.2", "5.3.0", "5.3.1", "5.3.2", "5.3.3", "5.3.4", "5.3.5", "5.3.6", "5.4.0-beta.0", "5.4.0-beta.1", "5.4.0", "5.4.1", "5.4.2", "5.4.3", "5.4.4", "5.4.5", "5.4.6", "5.4.7", "5.4.8", "5.4.9", "5.4.10", "5.4.11", "5.4.12", "5.4.13", "5.4.14", "5.4.15", "5.4.16", "5.4.17", "5.4.18", "5.4.19", "6.0.0-alpha.0", "6.0.0-alpha.1", "6.0.0-alpha.2", "6.0.0-alpha.3", "6.0.0-alpha.4", "6.0.0-alpha.5", "6.0.0-alpha.6", "6.0.0-alpha.7", "6.0.0-alpha.8", "6.0.0-alpha.9", "6.0.0-alpha.10", "6.0.0-alpha.11", "6.0.0-alpha.13", "6.0.0-alpha.14", "6.0.0-alpha.15", "6.0.0-alpha.16", "6.0.0-alpha.17", "6.0.0-alpha.18", "6.0.0-alpha.19", "6.0.0-alpha.20", "6.0.0-alpha.21", "6.0.0-alpha.22", "6.0.0-alpha.23", "6.0.0-alpha.24", "6.0.0-beta.0", "6.0.0-beta.1", "6.0.0-beta.2", "6.0.0-beta.3", "6.0.0-beta.4", "6.0.0-beta.5", "6.0.0-beta.6", "6.0.0-beta.7", "6.0.0-beta.8", "6.0.0-beta.9", "6.0.0-beta.10", "6.0.0", "6.0.1", "6.0.2", "6.0.3", "6.0.4", "6.0.5", "6.0.6", "6.0.7", "6.0.8", "6.0.9", "6.0.10", "6.0.11", "6.0.12", "6.0.13", "6.0.14", "6.0.15", "6.1.0-beta.0", "6.1.0-beta.1", "6.1.0-beta.2", "6.1.0", "6.1.1", "6.1.2", "6.1.3", "6.1.4", "6.1.5", "6.1.6", "6.2.0-beta.0", "6.2.0-beta.1", "6.2.0", "6.2.1", "6.2.2", "6.2.3", "6.2.4", "6.2.5", "6.2.6", "6.2.7", "6.3.0-beta.0", "6.3.0-beta.1", "6.3.0-beta.2", "6.3.0", "6.3.1", "6.3.2", "6.3.3", "6.3.4", "6.3.5"], "vulnerableVersions": ["0.1.0", "0.1.1", "0.1.2", "0.2.0", "0.3.0", "0.3.1", "0.3.2", "0.4.0", "0.5.0", "0.5.1", "0.5.2", "0.5.3", "0.6.0", "0.6.1", "0.7.0", "0.8.0", "0.8.1", "0.9.0", "0.9.1", "0.10.0", "0.10.1", "0.10.2", "0.10.3", "0.11.0", "0.11.1", "0.11.2", "0.11.3", "0.11.4", "0.11.5", "0.12.0", "0.13.0", "0.13.1", "0.13.2", "0.14.0", "0.14.1", "0.14.2", "0.14.3", "0.14.4", "0.15.0", "0.15.1", "0.15.2", "0.15.3", "0.15.4", "0.15.5", "0.15.6", "0.16.0", "0.16.1", "0.16.2", "0.16.3", "0.16.4", "0.16.5", "0.16.6", "0.16.7", "0.16.8", "0.16.9", "0.16.10", "0.16.11", "0.16.12", "0.17.0", "0.17.1", "0.17.2", "0.18.0", "0.18.1", "0.19.0", "0.19.1", "0.19.2", "0.19.3", "0.20.0", "0.20.1", "0.20.2", "0.20.3", "0.20.4", "0.20.5", "0.20.6", "0.20.7", "0.20.8", "0.20.9", "0.20.10", "1.0.0-beta.1", "1.0.0-beta.2", "1.0.0-beta.3", "1.0.0-beta.4", "1.0.0-beta.5", "1.0.0-beta.6", "1.0.0-beta.7", "1.0.0-beta.8", "1.0.0-beta.9", "1.0.0-beta.10", "1.0.0-beta.11", "1.0.0-beta.12", "1.0.0-rc.1", "1.0.0-rc.2", "1.0.0-rc.3", "1.0.0-rc.4", "1.0.0-rc.5", "1.0.0-rc.6", "1.0.0-rc.7", "1.0.0-rc.8", "1.0.0-rc.9", "1.0.0-rc.10", "1.0.0-rc.11", "1.0.0-rc.12", "1.0.0-rc.13", "2.0.0-alpha.1", "2.0.0-alpha.2", "2.0.0-alpha.3", "2.0.0-alpha.4", "2.0.0-alpha.5", "2.0.0-beta.1", "2.0.0-beta.2", "2.0.0-beta.3", "2.0.0-beta.4", "2.0.0-beta.5", "2.0.0-beta.6", "2.0.0-beta.7", "2.0.0-beta.8", "2.0.0-beta.9", "2.0.0-beta.10", "2.0.0-beta.11", "2.0.0-beta.12", "2.0.0-beta.13", "2.0.0-beta.14", "2.0.0-beta.15", "2.0.0-beta.16", "2.0.0-beta.17", "2.0.0-beta.18", "2.0.0-beta.19", "2.0.0-beta.20", "2.0.0-beta.21", "2.0.0-beta.22", "2.0.0-beta.23", "2.0.0-beta.24", "2.0.0-beta.25", "2.0.0-beta.26", "2.0.0-beta.27", "2.0.0-beta.28", "2.0.0-beta.29", "2.0.0-beta.30", "2.0.0-beta.31", "2.0.0-beta.32", "2.0.0-beta.33", "2.0.0-beta.34", "2.0.0-beta.35", "2.0.0-beta.36", "2.0.0-beta.37", "2.0.0-beta.38", "2.0.0-beta.39", "2.0.0-beta.40", "2.0.0-beta.41", "2.0.0-beta.42", "2.0.0-beta.43", "2.0.0-beta.44", "2.0.0-beta.45", "2.0.0-beta.46", "2.0.0-beta.47", "2.0.0-beta.48", "2.0.0-beta.49", "2.0.0-beta.50", "2.0.0-beta.51", "2.0.0-beta.52", "2.0.0-beta.53", "2.0.0-beta.54", "2.0.0-beta.55", "2.0.0-beta.56", "2.0.0-beta.57", "2.0.0-beta.58", "2.0.0-beta.59", "2.0.0-beta.60", "2.0.0-beta.61", "2.0.0-beta.62", "2.0.0-beta.63", "2.0.0-beta.64", "2.0.0-beta.65", "2.0.0-beta.66", "2.0.0-beta.67", "2.0.0-beta.68", "2.0.0-beta.69", "2.0.0-beta.70", "2.0.0", "2.0.1", "2.0.2", "2.0.3", "2.0.4", "2.0.5", "2.1.0", "2.1.1", "2.1.2", "2.1.3", "2.1.4", "2.1.5", "2.2.0", "2.2.1", "2.2.2", "2.2.3", "2.2.4", "2.3.0", "2.3.1", "2.3.2", "2.3.3", "2.3.4", "2.3.5", "2.3.6", "2.3.7", "2.3.8", "2.4.0-beta.0", "2.4.0-beta.1", "2.4.0-beta.2", "2.4.0-beta.3", "2.4.0", "2.4.1", "2.4.2", "2.4.3", "2.4.4", "2.5.0-beta.0", "2.5.0-beta.1", "2.5.0-beta.2", "2.5.0-beta.3", "2.5.0", "2.5.1", "2.5.2", "2.5.3", "2.5.4", "2.5.5", "2.5.6", "2.5.7", "2.5.8", "2.5.9", "2.5.10", "2.6.0-beta.0", "2.6.0-beta.1", "2.6.0-beta.2", "2.6.0-beta.3", "2.6.0-beta.4", "2.6.0", "2.6.1", "2.6.2", "2.6.3", "2.6.4", "2.6.5", "2.6.6", "2.6.7", "2.6.8", "2.6.9", "2.6.10", "2.6.11", "2.6.12", "2.6.13", "2.6.14", "2.7.0-beta.0", "2.7.0-beta.1", "2.7.0-beta.2", "2.7.0-beta.3", "2.7.0-beta.4", "2.7.0-beta.5", "2.7.0-beta.6", "2.7.0-beta.7", "2.7.0-beta.8", "2.7.0-beta.9", "2.7.0-beta.10", "2.7.0-beta.11", "2.7.0", "2.7.1", "2.7.2", "2.7.3", "2.7.4", "2.7.5", "2.7.6", "2.7.7", "2.7.8", "2.7.9", "2.7.10", "2.7.11", "2.7.12", "2.7.13", "2.8.0-beta.0", "2.8.0-beta.1", "2.8.0-beta.2", "2.8.0-beta.3", "2.8.0-beta.4", "2.8.0-beta.5", "2.8.0-beta.6", "2.8.0-beta.7", "2.8.0", "2.8.1", "2.8.2", "2.8.3", "2.8.4", "2.8.5", "2.8.6", "2.9.0-beta.0", "2.9.0-beta.1", "2.9.0-beta.2", "2.9.0-beta.3", "2.9.0-beta.4", "2.9.0-beta.6", "2.9.0-beta.7", "2.9.0-beta.8", "2.9.0-beta.9", "2.9.0-beta.10", "2.9.0-beta.11", "2.9.0", "2.9.1", "2.9.2", "2.9.3", "2.9.4", "2.9.5", "2.9.6", "2.9.7", "2.9.8", "2.9.9", "2.9.10", "2.9.11", "2.9.12", "2.9.13", "2.9.14", "2.9.15", "2.9.16", "2.9.17", "2.9.18", "3.0.0-alpha.0", "3.0.0-alpha.1", "3.0.0-alpha.2", "3.0.0-alpha.3", "3.0.0-alpha.4", "3.0.0-alpha.5", "3.0.0-alpha.6", "3.0.0-alpha.7", "3.0.0-alpha.8", "3.0.0-alpha.9", "3.0.0-alpha.10", "3.0.0-alpha.11", "3.0.0-alpha.12", "3.0.0-alpha.13", "3.0.0-alpha.14", "3.0.0-beta.0", "3.0.0-beta.1", "3.0.0-beta.2", "3.0.0-beta.3", "3.0.0-beta.4", "3.0.0-beta.5", "3.0.0-beta.6", "3.0.0-beta.7", "3.0.0-beta.8", "3.0.0-beta.9", "3.0.0-beta.10", "3.0.0", "3.0.1", "3.0.2", "3.0.3", "3.0.4", "3.0.5", "3.0.6", "3.0.7", "3.0.8", "3.0.9", "3.1.0-beta.0", "3.1.0-beta.1", "3.1.0-beta.2", "3.1.0", "3.1.1", "3.1.2", "3.1.3", "3.1.4", "3.1.5", "3.1.6", "3.1.7", "3.1.8", "3.2.0-beta.0", "3.2.0-beta.1", "3.2.0-beta.2", "3.2.0-beta.3", "3.2.0-beta.4", "3.2.0", "3.2.1", "3.2.2", "3.2.3", "3.2.4", "3.2.5", "3.2.6", "3.2.7", "3.2.8", "3.2.10", "3.2.11", "4.0.0-alpha.0", "4.0.0-alpha.1", "4.0.0-alpha.2", "4.0.0-alpha.3", "4.0.0-alpha.4", "4.0.0-alpha.5", "4.0.0-alpha.6", "4.0.0-beta.0", "4.0.0-beta.1", "4.0.0-beta.2", "4.0.0-beta.3", "4.0.0-beta.4", "4.0.0-beta.5", "4.0.0-beta.6", "4.0.0-beta.7", "4.0.0", "4.0.1", "4.0.2", "4.0.3", "4.0.4", "4.0.5", "4.1.0-beta.0", "4.1.0-beta.1", "4.1.0-beta.2", "4.1.0", "4.1.1", "4.1.2", "4.1.3", "4.1.4", "4.1.5", "4.2.0-beta.0", "4.2.0-beta.1", "4.2.0-beta.2", "4.2.0", "4.2.1", "4.2.2", "4.2.3", "4.3.0-beta.0", "4.3.0-beta.1", "4.3.0-beta.2", "4.3.0-beta.3", "4.3.0-beta.4", "4.3.0-beta.5", "4.3.0-beta.6", "4.3.0-beta.7", "4.3.0-beta.8", "4.3.0", "4.3.1", "4.3.2", "4.3.3", "4.3.4", "4.3.5", "4.3.6", "4.3.7", "4.3.8", "4.3.9", "4.4.0-beta.0", "4.4.0-beta.1", "4.4.0-beta.2", "4.4.0-beta.3", "4.4.0-beta.4", "4.4.0", "4.4.1", "4.4.2", "4.4.3", "4.4.4", "4.4.5", "4.4.6", "4.4.7", "4.4.8", "4.4.9", "4.4.10", "4.4.11", "4.4.12", "4.5.0", "4.5.1", "4.5.2", "4.5.3", "4.5.5", "4.5.6", "4.5.7", "4.5.8", "4.5.9"], "cwe": ["CWE-200", "CWE-284"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:N/A:N"}, "range": "<4.5.10", "id": "LrGCNVWv9HdICCEmuCuHM3iSq4nqgx3JJ3/PLAz3cygu4U/ST2WflCFRBc1EjBtriuozXAGQBOs1Q6w4UIljyg=="}