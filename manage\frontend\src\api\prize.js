/**
 * 道具和奖品管理API服务
 */
import request from '@/utils/request'

const BASE_URL = '/treasure'

/**
 * 获取奖品来源(道具)列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回API响应
 */
export function getPrizeSources(params) {
  return request({
    url: `${BASE_URL}/prize-sources/`,
    method: 'get',
    params
  })
}

/**
 * 获取奖品来源(道具)详情
 * @param {number|string} id - 奖品来源ID
 * @returns {Promise} - 返回API响应
 */
export function getPrizeSourceDetail(id) {
  return request({
    url: `${BASE_URL}/prize-sources/${id}/`,
    method: 'get'
  })
}

/**
 * 创建奖品来源(道具)
 * @param {Object} data - 奖品来源数据
 * @returns {Promise} - 返回API响应
 */
export function createPrizeSource(data) {
  return request({
    url: `${BASE_URL}/prize-sources/`,
    method: 'post',
    data
  })
}

/**
 * 更新奖品来源(道具)
 * @param {number|string} id - 奖品来源ID
 * @param {Object} data - 奖品来源数据
 * @returns {Promise} - 返回API响应
 */
export function updatePrizeSource(id, data) {
  return request({
    url: `${BASE_URL}/prize-sources/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除奖品来源(道具)
 * @param {number|string} id - 奖品来源ID
 * @returns {Promise} - 返回API响应
 */
export function deletePrizeSource(id) {
  return request({
    url: `${BASE_URL}/prize-sources/${id}/`,
    method: 'delete'
  })
}

/**
 * 获取特定来源的所有奖品
 * @param {number|string} id - 奖品来源ID
 * @returns {Promise} - 返回API响应
 */
export function getSourcePrizes(id) {
  return request({
    url: `${BASE_URL}/prize-sources/${id}/prizes/`,
    method: 'get'
  })
}

/**
 * 获取来源类型选项
 * @returns {Promise} - 返回API响应
 */
export function getSourceTypes() {
  return request({
    url: `${BASE_URL}/prize-sources/source_types/`,
    method: 'get'
  })
}

/**
 * 导入Excel数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回API响应
 */
export function importExcel(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: `${BASE_URL}/prize-sources/import_excel/`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取奖品列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回API响应
 */
export function getPrizes(params) {
  return request({
    url: `${BASE_URL}/prizes/`,
    method: 'get',
    params
  })
}

/**
 * 获取奖品详情
 * @param {number|string} id - 奖品ID
 * @returns {Promise} - 返回API响应
 */
export function getPrizeDetail(id) {
  return request({
    url: `${BASE_URL}/prizes/${id}/`,
    method: 'get'
  })
}

/**
 * 创建奖品
 * @param {Object} data - 奖品数据
 * @returns {Promise} - 返回API响应
 */
export function createPrize(data) {
  return request({
    url: `${BASE_URL}/prizes/`,
    method: 'post',
    data
  })
}

/**
 * 更新奖品
 * @param {number|string} id - 奖品ID
 * @param {Object} data - 奖品数据
 * @returns {Promise} - 返回API响应
 */
export function updatePrize(id, data) {
  return request({
    url: `${BASE_URL}/prizes/${id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除奖品
 * @param {number|string} id - 奖品ID
 * @returns {Promise} - 返回API响应
 */
export function deletePrize(id) {
  return request({
    url: `${BASE_URL}/prizes/${id}/`,
    method: 'delete'
  })
}

/**
 * 获取稀有度类型选项
 * @returns {Promise} - 返回API响应
 */
export function getRarityTypes() {
  return request({
    url: `${BASE_URL}/prizes/rarity_types/`,
    method: 'get'
  })
}

/**
 * 获取奖品类型选项
 * @param {Object} params - 查询参数，如 {search: '赛车'}
 * @returns {Promise} - 返回API响应
 */
export function getPrizeTypes(params) {
  return request({
    url: `${BASE_URL}/prizes/prize_types/`,
    method: 'get',
    params
  })
}

/**
 * 上传图片
 * @param {File} file - 图片文件
 * @param {string} type - 图片类型，'prize'或'property'
 * @returns {Promise} - 返回API响应
 */
export function uploadImage(file, type = 'prize') {
  const formData = new FormData()
  formData.append('image', file)
  formData.append('type', type)
  return request({
    url: `${BASE_URL}/upload_image/`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
