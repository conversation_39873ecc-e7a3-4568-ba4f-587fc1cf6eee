#!/bin/bash

# 配置
LOG_DIR="logs"
LOG_FILE="$LOG_DIR/npm-dev-$(date +%Y%m%d_%H%M%S).log"

# 创建日志目录
mkdir -p $LOG_DIR

# 输出时间戳函数
timestamp() {
    date "+%Y-%m-%d %H:%M:%S"
}

# 开始运行
echo "=== Development Server Started at $(timestamp) ===" | tee -a $LOG_FILE

# 检查 node 和 npm 版本
echo "Node version: $(node -v)" | tee -a $LOG_FILE
echo "NPM version: $(npm -v)" | tee -a $LOG_FILE

# 运行开发服务器
echo "Starting development server..." | tee -a $LOG_FILE
npm run dev --verbose 2>&1 | tee -a $LOG_FILE

# 检查运行结果
if [ $? -eq 0 ]; then
    echo "=== Server Stopped Successfully at $(timestamp) ===" | tee -a $LOG_FILE
else
    echo "=== Server Failed at $(timestamp) ===" | tee -a $LOG_FILE
fi 