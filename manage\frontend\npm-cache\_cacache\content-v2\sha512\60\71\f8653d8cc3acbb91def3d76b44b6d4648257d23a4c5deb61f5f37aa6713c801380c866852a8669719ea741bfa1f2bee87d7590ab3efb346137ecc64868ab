{"dist-tags": {"latest": "6.8.4"}, "modified": "2024-11-28T09:32:38.279Z", "name": "@codemirror/lint", "versions": {"0.19.3": {"name": "@codemirror/lint", "version": "0.19.3", "dependencies": {"@codemirror/gutter": "^0.19.4", "@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.1", "@codemirror/state": "^0.19.4", "@codemirror/tooltip": "^0.19.5", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-+c39s05ybD2NjghxkPFsUbH/qBL0cdzKmtHbzUm0RVspeL2OiP7uHYJ6J5+Qr9RjMIPWzcqSauRqxfmCrctUfg==", "shasum": "84101d0967fea8df114a8f0f79965c22ccd3b3cc", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.3.tgz", "fileCount": 8, "unpackedSize": 63423, "size": 11589, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/lint", "version": "0.19.2", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "1c3932cffd635da796bea3384256b0a315b6fe1e", "size": 9808, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.2.tgz", "integrity": "sha512-477qvXWwuf24YsBi1DzjrGyzM+qfPe5L4xEHGxQTGOMq6R0+QAFKppOJsxN3y7gzDpLrZSYZdhJzWevOuliZQg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/lint", "version": "0.19.1", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "8035abd867d85b0bcbcea72db0077b2d738c7702", "size": 9752, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.1.tgz", "integrity": "sha512-Ef0TnXpF4Q4B+lvBdgHexe1dHbvkKBvBHsLB8HwTYffwjekg1YvEM+6zyjyjG9m6s1Ru0VaCNlNwotLaVZjHGQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.0": {"name": "@codemirror/lint", "version": "0.19.0", "dependencies": {"@codemirror/panel": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/tooltip": "^0.19.0", "@codemirror/view": "^0.19.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "689d900aa13094093b41e87bf8b97943d6e9c62a", "size": 9495, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.0.tgz", "integrity": "sha512-XjCIVJi2cUNlppwz0eGu0F93uFKJq1t2fupBQlDMr3hLmIxIRWjhvy66+Pw/ziH98XA8meLEGQX29umBAvym4w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.6": {"name": "@codemirror/lint", "version": "0.18.6", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "e0fe533f1fe76bb0f6543280124b793d427b081a", "size": 9472, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.6.tgz", "integrity": "sha512-juE05YyDoLp9WCcR0hQagphOCIZ0r4WRocRFu9tbFwsMjfuForjn4m+wsLSDaDgp2Z9secMyOSGDpBNtVwM9lQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.5": {"name": "@codemirror/lint", "version": "0.18.5", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "c8985a93fb4862a07a7672729c99d6325222b479", "size": 9426, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.5.tgz", "integrity": "sha512-zbXbRZFI6FyO1s3n0L4k2uoK96thLozntx5Hfu3UVzNXcdV4ecgcaJaEJ/Gg02wFY+R53HApUhUae+2MmXAHVg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.4": {"name": "@codemirror/lint", "version": "0.18.4", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "4cdbb4fd6fee3ac8384df62a99efefe099932feb", "size": 9207, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.4.tgz", "integrity": "sha512-H77qYfZOmo1kKf0ZQagzk/JRGVhIpwP0hq1TSO6DFC1WLjW6gcsFJO5NDMS86enm0KX0w4/IkA7PItz2mjmHhQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.3": {"name": "@codemirror/lint", "version": "0.18.3", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "31c629485e910b3f145e2b819d2f012cd17be016", "size": 8909, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.3.tgz", "integrity": "sha512-ORD8bGQRv5FATJ/LUmhaVPz69ucNjMCQ53WF8TqYMw6KtlTwAFsi+GvEhYyav7CdXPAXS/Z/j0C0Op8d1Dp+lQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/lint", "version": "0.18.2", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "d80adb1767b486894e921785b5e82fa435d28ecf", "size": 8820, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.2.tgz", "integrity": "sha512-WUZdlWNqwF6OIJFD/n3Pfsi1R3eIYrAmnnFhfEBUI/ftBh8RqjqOoPJvctHm++re85cwWzXSSZgSWxzse11PSA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/lint", "version": "0.18.1", "dependencies": {"@codemirror/panel": "^0.18.1", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.4", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"shasum": "ef5502d3bc27eaf23c670fa888bd23d09b59af55", "size": 8691, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.1.tgz", "integrity": "sha512-2Ueg/ojU56vF5thxMdS67XQzvHNcBnPKw2zgbDVmL/Z+84SMjP7EKvHV5FlbrKFNGZiwnaAKk5MZRYUwBY3f0g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/lint", "version": "0.18.0", "dependencies": {"@codemirror/panel": "^0.18.0", "@codemirror/state": "^0.18.0", "@codemirror/tooltip": "^0.18.0", "@codemirror/view": "^0.18.0", "crelt": "^1.0.5"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3"}, "directories": {}, "dist": {"shasum": "b8e2ee7acefbf87197afd34c1269e5c709cac943", "size": 15231, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.18.0.tgz", "integrity": "sha512-kRkSGMGXN9wi4j0/xxJy7uePE/vPq0nGKeoYFgue2KZbpVtNUC7teYjrj7QbYe9KRv/2NrO9+pwIzwgjpuwlJw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/lint", "version": "0.17.1", "dependencies": {"@codemirror/panel": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "crelt": "^1.0.5"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3"}, "directories": {}, "dist": {"shasum": "970dbfdb52cef35ff0210d22230dbff1c7dab7a4", "size": 15049, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.17.1.tgz", "integrity": "sha512-Jzhf3g+hIr8frQaLsRSYJgM3BFVt7qQ59J0l/oqkjx8LsqYn0zbCM6zHnKMC0lOUdZXf7O8D24jTmVJb/8n4YQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/lint", "version": "0.17.0", "dependencies": {"@codemirror/panel": "^0.17.0", "@codemirror/state": "^0.17.0", "@codemirror/tooltip": "^0.17.0", "@codemirror/view": "^0.17.0", "crelt": "^1.0.5"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3"}, "directories": {}, "dist": {"shasum": "412003a6194e773c875883a7322733145f73a0b8", "size": 13787, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.17.0.tgz", "integrity": "sha512-psG+YuzRqRD0ataXY5uy7sGxeyKCQa3GJuhUCpSUyBzsEJxVUmPt4pkG5e7tZMzgBLi7RQarsKncz12hNHmXaA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/lint", "version": "0.19.4", "dependencies": {"@codemirror/gutter": "^0.19.4", "@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.1", "@codemirror/state": "^0.19.4", "@codemirror/tooltip": "^0.19.5", "@codemirror/view": "^0.19.22", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-29/Wid7X58PMpuD1OrdsIUZYOftCBfIzJwZJUa917CTYbP8WdrZulVtWW2U0J68/6Xzq48d58OxIGa/y7vxjqQ==", "shasum": "d23310bc0a5e9523e647f680666d210038d92e22", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.4.tgz", "fileCount": 8, "unpackedSize": 63994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGL5zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaIA//VyAKLW4ra4en2a1/a2Sh7ZDxOkr1ctxNm0eE9O8UT7UEyXVf\r\nePI0zovAJd69AEIoJGZwxpFYcSMqaL8ympMhFoMnVJg4Ll+IqxscrLxVnfGl\r\n5AffhfU+DpQFSjpVTEKFNQRV3LkGvX1cVyde5UJgFfh/B0s4HlGX1c+BrO3E\r\nnE7TpNR9NeQYe+h4TVAngoeb0KlYHts9qvqrQTYrAzYpqUM73ZWeYylNQcZo\r\n4yPIsd07XMhQnitCrgjiSILr3ZK5Ll1cvekva51tgiIcL48g8ePMVFDkhoYN\r\naGlH+Xtd7dRu4iB2TexJz+ldkjrFlDyyDXO52KLJ3tkmxIdccRD49wXUT2wO\r\nPVUNtTh1chDnD1MepWR8rNivRXQqkynBYbMX6Q9hhWcx0GjzDvEO4hlRJDuB\r\nxoRdgVxvDX4Ic1brCrTDi9e6nb1RWOIwtj08g4s88TcpCmuHh9gtmL8yYeOZ\r\nR6VdxVyts0vZityPJmZzfseF9C/oEb28UqvByyikneL/5XuhzzPl3RDCZCAi\r\nwIDAqaOXRDlTF4z9NccA2jGqBr7PeXLHFHCx4CcdrrLFhXWiZ14vqgqtRuYO\r\nKyZ6eruxvaD8bCwknP/UXldb/9bJ3mwgixo/mmR0zJwjk9639Bj2LATot/bx\r\nEmFPcHtZF+HZsJoJs/ApNghKkNcSi370C4s=\r\n=/22m\r\n-----END PGP SIGNATURE-----\r\n", "size": 11746}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.5": {"name": "@codemirror/lint", "version": "0.19.5", "dependencies": {"@codemirror/gutter": "^0.19.4", "@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.1", "@codemirror/state": "^0.19.4", "@codemirror/tooltip": "^0.19.16", "@codemirror/view": "^0.19.22", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-J6AnEj2WyQfejWmlm4TS5ItVDAe2R+1cO79138HEUJnAN0CX6pVG6zI9cUhoy6V6dsV0hTwFmu6TzGe6vNhkXA==", "shasum": "e5e4171df98ea66a7acb7c76084498e72a8c09c3", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.5.tgz", "fileCount": 8, "unpackedSize": 63820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGQ+sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonow//fbzTxjlgnVSzZgDg4NVHd7SHMNK4cC1cGeihQlznlTfuTQxA\r\npYDm0yhCZA/K7AV+o/NEMqh+gR5aIaHQJT9ztWxtwbL2eXxTTGC1YruWv+5D\r\n6aQIf/BdNlNdp7xznHf3SgLdLs06GvxS93HNQPDLWKlub9247XmqEVWF4MOC\r\n1BtUrHhrylpXr/UFSsCJmnGbHfbof8V7w5QRD3wxgLSnlSL2nPuPuOoV+TL+\r\ne2k+t2KZpNeSY847y2qMR5iAcxOBBQoVC+rFS3fz5mleUNyF+LSuv89kO5k7\r\nU3avGTwOKc1Q7vpRjyn1NtSHaw+ky5lXbseEelQDyXxWi5jDZVHvnT+RLsfQ\r\nTXVYpAjQbRpRGG4PCxaO40t7/oVyW01Y2qXXUAZmxtCxXXu5QiAnoFKfJp1H\r\njl8tdcdO++3sTpg4fdUFD8WYpqwSdQvhHVAWIqve6QvMHkkdtxNNcIXbz56j\r\ndQI/BU9goRKcpz3VSWiDZnsMZzjnYTXhupM9SBn1YS1UEgj8vADB682um5U2\r\nN8oYHnTWFRhLgT912kJxAjqxhjBKOUIxDrBo3BQy6wgsfXH0BKwvjfWXZIPX\r\nraLsbB+ciV4FpwfhYxyFztDCmDWBHkbOXHr2qXc3Hs1WitB+9Hi+QmFOBbdo\r\nZTRtDgLYSvJSahAdXAQ19XihCDhxSlOuPbU=\r\n=UVo/\r\n-----END PGP SIGNATURE-----\r\n", "size": 11758}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.6": {"name": "@codemirror/lint", "version": "0.19.6", "dependencies": {"@codemirror/gutter": "^0.19.4", "@codemirror/panel": "^0.19.0", "@codemirror/rangeset": "^0.19.1", "@codemirror/state": "^0.19.4", "@codemirror/tooltip": "^0.19.16", "@codemirror/view": "^0.19.22", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-Pbw1Y5kHVs2J+itQ0uez3dI4qY9ApYVap7eNfV81x1/3/BXgBkKfadaw0gqJ4h4FDG7OnJwb0VbPsjJQllHjaA==", "shasum": "0379688da3e16739db4a6304c73db857ca85d7ec", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.19.6.tgz", "fileCount": 8, "unpackedSize": 63990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIh0dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp01g/+Iz1xteOsxu/ITE4o+Br5+ti0spi1rheNNINeqGrdlU5CvpMj\r\n3l2ytLR8ORPmBMTYvU5Ens0IiqC++PKb1lrHWAFqrMqO2eVVqu2B2BGmWhTO\r\nRo3uIQpwKV1G9RXp92dkQ6Po3TNOPxm0+B5DcSmhx9dG6qYB2vdE0+GZmrx1\r\nVW3LSwk2uw4swjQw+R9ZBNlhiFoNQLZGg6v/0GIe3U0W1HfuWTwIf9FHBVXl\r\nVfNS1I7Jvi5ld+b6FqxQTrdxV2T9WvT6NaUVmhw+mVBc6kGDI0VC7mJerd9i\r\nSisJm8KmTJ29moACQGMxES037MQPxdZZXRS3e1ZI8wgemdEjiHa4kSSgg+XJ\r\nhNla8GDqMYAM1xzphLUzWw1LP5BL/g4P4OORWk218xZLrcKWTUl17mXk8hpg\r\ndvEF1AcdE8Q+BqgsStEdwB/eioFsoQrNNXPpWVyrrQIilKdhPZJnwV1ythle\r\nmTiWcTvqJczqUdOn1i+XnQ94ESIMmpzfZEXNvDVGsRITaC/6U1K6eFUGHCeq\r\ndZ0/iVQYN0TF6DpWb4vryJbr2bMo/SUqnL694/XIrhULfLtn4tZ67PgLQuNN\r\nm2WIGTQSGba/2X6FimScy9o8oW2O9FgX77VIKEZc5rn/AHnE8hJTQBW4vIMB\r\nT+WUIQ7funToMC+zvgMk7ld0dV1IbJ84DPw=\r\n=OQlk\r\n-----END PGP SIGNATURE-----\r\n", "size": 11806}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/lint", "version": "0.20.0", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-JLWWpNEfIeuCGJNMauyYs3xqaYkkpeluuwgdPZgWrD/BjyqkJaYFScA3UVyYaQwGIXQKMnzhZXnuIa4q9cx0mA==", "shasum": "9826034ee18265000d6349f53b70dbf0d851ff08", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.20.0.tgz", "fileCount": 8, "unpackedSize": 63572, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+lIs4LL+xRy+AusqrRfIUoN2QU1csrTKBHSNH2b37pAiEAyYhuzpIDFQ8MGbk935VhsWdiqbKlQjhm3oQc843v130="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBVGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokJBAAn2QzRXVOjIaJ6qyCcZ4cV3mWNKipO1nNBN1AXHVSxBMatxcu\r\nF/B4/hMbHoG/aWCQ/hgs7rb0ddSSL3xoE/1GP7v4amr3hwUwmIVcNFSeEVyS\r\n8curt97Otps3Svq1XELLZvD/+fYA9hiVUCt/WfkpWWin+iBMF7HjjtKfFHNi\r\ndLle17zs+u4ovVJvIQRO0qXDMj3yLzK6nY+oSewj4tngiahBpvhWe0OjuGw9\r\ndhlnMqeUSUbE7IF+01n2B9utPD1JWxJhUqhOGBptMsJV5TR1OneUPCC+h6lL\r\n4QF6EKBZubkW4fNWakSlVI8DWOBpF8F1Qjvh/1sR/4eJpw1SS7f6YJ7N4vIU\r\nJHiYkXvpqKriBXma90P5dGpvNSVvj89sGHn8ySrsf2OgkcV0xFNMcQZJ9d1n\r\nKX/zVTyh2lJM8VOBe63SyNvgvLLwg4SaTco+OGPCHN5WC1pKVR+h8jfdMViC\r\nANZRQtrFF3uZpXtWk3hvIrc3EDBUAGKoQTljEH06mcuuWZoXNJEq7qTyzpnK\r\nP5sT1c0DnMahyoH4BGMCmCZe91rqKvpLI9j7EbkJv71GiPh+jeftVoSaxA+0\r\n4Ddsw4WeLFeI2Ok67btWyouxdC3BG6j2JfPLZegOded6aZOPez4tY4mZAvB6\r\nvFdSz3Qqi0MsrwtpJSsVxckXU5RR0rt8vv8=\r\n=cgAG\r\n-----END PGP SIGNATURE-----\r\n", "size": 11708}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.1": {"name": "@codemirror/lint", "version": "0.20.1", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.2", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-aWbeicDiNe5tb2aZuXs7sKk3hQ89Z1YcUuUX8ngQu23tT6EY4kcY2cayDjizvkLn8iHkObNf97uSudjNmUon2Q==", "shasum": "791c83642f76103bbf6b8bfdc8020eff2b78b5e2", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.20.1.tgz", "fileCount": 8, "unpackedSize": 64065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2PnJRjXaoj94lhOkPLC8KzdcG/8S/PBUGiMGrr8Xg3gIgTFJanKiCm478KoALZUvIpw2k3W5Pd705fMJlnmNq/BI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYoeAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0CRAAliBYZ9g/i0yHulpa1bmiqoibkEcgwr9nABfjprdO/LBJGyVW\r\nrqYCX2WDXPFhPx/nN0yCdGDCoG8E80bqQs9VRDVQXzUTjmyAXAWMHFvF6g2e\r\nvy9pFPbrnUmoenNLye1/SuIptVgdBUJ3xxX5pNeSpr6FZKw7oU3JF7dTPLLg\r\nA3Ue1i0ZGSatQde80QXakMetsl1Y4rPgt2WJRcd+d7sbINuiFE4BX82ptkAb\r\nwVzCXsfGHWLotdgnb05j+A9TxKVVFUGL2QaKxS6IukCQYT0cOXJgOee4EMCG\r\nYEYBb6vDTnKoz3TUC3SRow1PVi5NLueBhxz4/qMZTQjzKRbNRFS3INS/vDT1\r\n11A8AeyITGsF7wLepRfw+Yd1l2GEiaklZIcCmDaeDeZP0rjmENrI0LcKOVLW\r\nXOBTPIsR8QGi5tn+MueFm1AWugHNOd4Dp0ddvvMf1p4AxssvwSCaxqKCOKOU\r\nBMhpYt0uuaDWAxDqTE+6NfPRx+K8xxXTeXTgUsFmFdBkAvl/p481JydkQ89i\r\nLcpP3c+r1fT/bK5S3OJ3QFFD73y5WzK6fLwtbKed5K7khhZLj56krtKD6eTx\r\n9zWw942cVbt6Bx8savok20zz7KeRcjhmfMyFXTDVY5L1a/dX3cUDXdMUxw1R\r\nYBKXrKfcSmdUHBOSEbwhhIl1N2v3KXq5FCI=\r\n=DACD\r\n-----END PGP SIGNATURE-----\r\n", "size": 11810}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.2": {"name": "@codemirror/lint", "version": "0.20.2", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.2", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-xEH3wlzoFLEhPEeMVRNoQIhoTCMEtXhVxemGh3FYjLfl/CL3B2Wz+CU7ooP5SKhN1le7JqUNSfiTArFP+IzFuw==", "shasum": "e47307167ff7e5bb14b717f4084a386dd7f8954e", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.20.2.tgz", "fileCount": 8, "unpackedSize": 67192, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICyMtKozSdP1T6iXcXgj0iyFjZ5f7B0QaN3pBJvMZXvZAiEAreIDP3s6f3JDYZzFhEVW7UFwrhX4OFh483z9JecrlAA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib3oTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5KRAAndeIPsqBPptZ2egG4SZDKFW5v3tF0zkODgRNxAfRBwW//iz/\r\nutu/wt01VQgnyfRlnAwXLEEMiVc0BKwmbTDi3e9aQCGzP8RU7cQ88xuxpNDh\r\nwttSsxziUyP3M0FmPhSeqRCLCBBHI14e7UiwYsGpiaTyBMncI9edpUF9Dipf\r\n7UBHq0xdpp5IKeXfoUCjufc65HDbR93npRUdeGut/ueRsvvpdkAk1e+vAn17\r\no4Pqa9HdmHvHiJxwhTPW5z7vG6O8/MDkk52AheDyLnWbsVOI+jrGqICt71vf\r\navKfRxhCQIBewBa0G26Y6ag5VWESi6/7bhc0MpGtWbHshdXW54NRfNFnjqjt\r\n39JopOgGWvWPHHCQg8jw6QyvaOOePgsNjR8QeAA6P11yu4rL1C7b2a+oTeIJ\r\nopKSuELztUMOEUHIPbfVeO6mD7UwHZJ2u7vB0+OxzGbxySgUyyRe6dTbe6u+\r\n4FFufTUDQ8vIOhgz1O0QGHSBRYBOdHlV+Ft0OogP8+OXi3fwsJE+fxXrccYi\r\nQiqiGfd3vz+jZCbmn7X9uAybiIS5oDz/VceqaQNAY8X5ppg5rOKxiaWRAvXn\r\nfjb1vznNqFkP4QGT/AU2zSwLakY/nPkjzAOiOqM+pJXaCXnKWCDQixfh/EzE\r\nzzL3rnqKgNhl2lGcfIAvN/MpHSaDN3xEY84=\r\n=JOTR\r\n-----END PGP SIGNATURE-----\r\n", "size": 12282}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.3": {"name": "@codemirror/lint", "version": "0.20.3", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.2", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-06xUScbbspZ8mKoODQCEx6hz1bjaq9m8W8DxdycWARMiiX1wMtfCh/MoHpaL7ws/KUMwlsFFfp2qhm32oaCvVA==", "shasum": "34c0fd45c5acd522637f68602e3a416162e03a15", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-0.20.3.tgz", "fileCount": 8, "unpackedSize": 67574, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQg5zPs7TkttXadWtccd+lHh52EvGLCxTActrOd8VQAAiBMiMKtzK2SX8mP/lv5mSrNGrYm4eEgWscRdwXpyBGHyA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijcNFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRLQ//WccPxg9UztTzP8K/3cqINYKXhrJ+YJ/nWTNijc5gickkFKVE\r\nQYbt00EPpgDMIGnFojR3tk0eQWq52gAixzG9/i5/53qF1QPqoqnhJJzin01n\r\nATN8zrjp+i+hJiXVW1x/jjDNw67y8ALXXZNv938tGD8eK0V0h6sNz6EnRfIJ\r\naBcgqHoo6V0XYbJT9lPo0ZujrBKDXYUFBQxcMLmmtv532dWTi4JPeGmYYud8\r\n3HGF26WAJrQ0LfcJ58pp68fib7N79+bBPzeLb4UkBu1iLy7zSdEfii85IHAk\r\nHvX8aHz+Czb4T2tUAsYtRMHMNcZa+KDQQqywcVxUEPlTOYwSbQ5wMCuUImR1\r\n9NhMi2m+Qm/vZjFScphb+6yWvyfk2pGq1oQJ7ea9FsKJybc/zbTWHSHynDsd\r\nztsfWSllsYMOTSGKPLFKOUXgFAFSRpB3Hh/ikly4+W7+9KDqNrxHzwMNGjj4\r\nOvoF9vUDXYhcD5KURyWTtYZQtQH7m1+i+bTqyh744ZjF6F06V3B6M7Ms6ZLH\r\nVncX9FnMYbUYJ3mtHnvBvg5gDMJXSckHueBt790vc4xSEmbzriL8QS1GFR+n\r\n5Xw9B2u14Z+YpYwjEXqkBBNwT1QeQxFHZgCE/zjxMAt5l84Q+7qXKJ8/GKYF\r\nwl5wJia3MzOYrh8qBwIuwCQcoa5gJctIsQA=\r\n=ZE9q\r\n-----END PGP SIGNATURE-----\r\n", "size": 12389}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/lint", "version": "6.0.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-nUUXcJW1Xp54kNs+a1ToPLK8MadO0rMTnJB8Zk4Z8gBdrN0kqV7uvUraU/T2yqg+grDNR38Vmy/MrhQN/RgwiA==", "shasum": "a249b021ac9933b94fe312d994d220f0ef11a157", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.0.0.tgz", "fileCount": 8, "unpackedSize": 67646, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmqsg1o8HsKBgrNIg5vNA5yNRD8HnOlQ9YBV1daqEJLAIgJwm07XKVhejZ+mGblB9LQQNKL9JievgwIWkqHh7APkY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5zxAAkjzC0syxznJyOt/NKU7QCSm4vI2bp1T1GAs6xsaHantI9jca\r\nzNZ3ITZzXMxIhIKc+ynA8eJ86Mb756SNyzOvuoYuoV5y+0LtdwQrPnqF+KL3\r\nSxa47dUQi7yWDwZLiDTU9MUI7Z1trFyUgf3P66hMsHBPUnR+k8+lhtiy5N+z\r\njMHhRhhpPtEP2OLIweg22rTtRNp9H0KuEkefJ9BVXxKIYWK3yVNkx82pLWih\r\nVhYfZrFQfu285eYfvh8zj6lM2WkdpjyqGfHlQ7NgSkCN4OfSYAbkHHVN3e+X\r\ny5tC2BZSvPEGUxAgsD/FpwzLprfm4GgBzj+3z42r/6C4Hl4tCV/SfNIcr1FT\r\nsUfKRdtzBKjs09t2VmH2SjkDzGfPnS6mxIJWnXA6Ssni3Wug2fAukjMIHU37\r\nsp6VQrjcw1FLjG7SnodzwvUdsS8LmM/ZuIRWT0xR78wcZmuxiq93HNLZmAsz\r\n9tG3jhRpUaRM46a0Kb9sPdOMHNk1Dmm/iHbe6dc0A6LzEgENpmHFcl1DQKVj\r\nOOSBNyWW0gPalon2LEIJvzv+diTZEMcF8gDz3KkVBMMHWBJy3Ey+MNexf8B5\r\nEOa/56FSEV9ik/RYVwLmoQ8aS8/aDTeR/GSxhGSxqLA/qKh0mB07yw/YKkKV\r\nvCby1zncS/ARA02Jjd0HTqAxfvWTRB8HLmk=\r\n=V0/w\r\n-----END PGP SIGNATURE-----\r\n", "size": 12404}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/lint", "version": "6.1.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-mdvDQrjRmYPvQ3WrzF6Ewaao+NWERYtpthJvoQ3tK3t/44Ynhk8ZGjTSL9jMEv8CgSMogmt75X8ceOZRDSXHtQ==", "shasum": "f006142d3a580fdb8ffc2faa3361b2232c08e079", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.1.0.tgz", "fileCount": 8, "unpackedSize": 69561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBF6rL8d6Ts70K8TxTakxh0Atrx7nE/jBFVbQGvZqQCpAiEAvFfJRYsTO6UU7HYcEGEzPhJlLt/4UIb/NkTJADIpgzs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc1qhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCXw/9GPAXPivfgxB8xAfSJzjVjxinnMm3EpnNnf/nPk6BTyoHXFSH\r\nbHvNBwWrA8PSr79hv1FkIo8Fg3n9EKva+eBE8nvx/HHVHtoWjV6qO8dcKSJD\r\nyXdeXmwiiaAFSsZ3cbRtV0Q5XaBDXzq5V4/aiEof48PUhCZt+u2xuIsXtJXP\r\nNEpyQsDSVufXysgGJ5LANfnC6HDWaK93awJkmbbtpngXz5e84qXFM4kqKZIw\r\n7xvt5KOZj4X5RXPWH44ZXCFWIleway3OHwDtXHn/X3k0G9i6CVMBqJXnMGPR\r\nGUkZR7WR40Wx+xPQFKHVtVmWei9YqxLk8i2AP61EuVtyHtAmc7LaGsBYwyjF\r\nyUmSx5AzxpOXYLRKVC7aQ4URXWsWJkCykdZ5SNLfg7ukCOd/X5/2wiUH1g88\r\nxw2OXDE+I/qJZX0oRiuKv8dCyRKV4RwMCkZcCQ7E99CsiZxhdrt+L4Zi4CBS\r\nrcYzlyaaRM7FqrhicRRZx40rNLKwvvpIxZqlkHPEg7Pg4/CcRk5AroocIktD\r\nWadEcjXcgGCFKmatYqjgtBsqgIufuOafNJBuYaDTpv67ahRZHmzK/x0v0S1l\r\ncTnbhvNmyGilaad7JbUCrkDZJi2mWaW4yJTGmjdd1FNW73pyIg+Tlr9E+764\r\nzJdlpxq57+XDqD1aHMJlct/vmjeaIW3VsWQ=\r\n=7Rv5\r\n-----END PGP SIGNATURE-----\r\n", "size": 12748}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.1": {"name": "@codemirror/lint", "version": "6.1.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-e+M543x0NVHGayNHQzLP4XByJsvbu/ojY6+0VF2Y4Uu66Rt1nADuxNflZwECLf7gS009smIsptSUa6bUj/U/rw==", "shasum": "b30741e714a43a11cb78feb2c220b4971ad175f3", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.1.1.tgz", "fileCount": 8, "unpackedSize": 69995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxjxgBod0VwSysPDp9j87Ur1vR2ed8gZz6EhQepqvklQIhAPe3nJIAUrYSknwjm3t3FkjslhVoQhP8+wA/2mBJ2Pfm"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7SGPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqenA/+JlAPM/CMxHTlzKGjhJ5f81W68Q0DXU2DkN0p/adhFXFpCDaY\r\nikk+bgxWQKdWwfEKuQYghtGqkHxxYJ1Qq3tQHT5b//nPt6B+Lc9j9T93S/Du\r\nGblHgJ19DEZF+tW+C/Lbve14HCvJR5Q5OL6pHSglSv5MDHywm6UqkSX6XppB\r\nYtLOiU+yPeIZTTgQDsmbXIgVX+ii1fAEtmYQ8mHa/9No27BbQRVOsiEGa20I\r\nLixXt8CTstMYQy+yJHvCJGDG2vlrL/gxngM1vcVpseSIHJiEUjvRtf7wYzKt\r\n6fRTmIhv2TzeUKMrS6ENwT8WyCS/xl9/g7Q6sGVNniPOYeW3Kol4IZoi9m50\r\ntoLNL1MBfg+AxqP0Mg1T+/WfBNskRCdQC/1mIgyv0dgl15jiZ4ioMyygb+/S\r\nOJAQcoIltvWYeZc3BxoYL9NIQ7r1PoDy+vuuhMrwxyWdt2dlZcA/DWtQzbDZ\r\nvEX0Dwm1FxHm1o2v/xLlFS/I2EplmI4WH9+ViJZ+wikFuxnQOYG0gyjlcbI8\r\ntfzEvQfc0KJw6waCQMFHLf05sAOVD3b0ZFR6vbThSFM6+sVN4jdlK+iCCKcq\r\n2Pb/cS+f56MdKaeBS66bh8GfjM96A5asKtWsq0pYx6ifxtaik679UvxIgtT6\r\nNlmRz/6ekmYU281z71bDKP+TPl6CtxSLr88=\r\n=N59N\r\n-----END PGP SIGNATURE-----\r\n", "size": 12858}, "_hasShrinkwrap": false, "publish_time": 1676485007448, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/lint", "version": "6.2.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-KVCECmR2fFeYBr1ZXDVue7x3q5PMI0PzcIbA+zKufnkniMBo1325t0h1jM85AKp8l3tj67LRxVpZfgDxEXlQkg==", "shasum": "25cdab7425fcda1b38a9d63f230f833c8b6b369f", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.2.0.tgz", "fileCount": 8, "unpackedSize": 70766, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmKEF3v2dKscDH1X2ET9C3BRVrQ9ODBUi0cVHfffRiqAiAsjtpKIqxGyxpbDpYf5b/8F/oHp3KBVUc/oOaO1w5/DQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/NULACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0qA//Ws5nsWm2SyZiRlaf6fV5uYk/ZrnHziLaspZ07UYLSTe+mxic\r\nwNjPYQ3g15eOj8LFE3ADCFZsP55DLSgmqB8X9qb23KaiimvGPiiVM9Q0y468\r\nNa6x2eCpi1cqoSmOBxMLHcMG2fxNXfNffVQ26yLWjFb9Mo8oBHZPVs7TQxlV\r\nSJ1pdeoVvJDJtvbPqwsKTKGpNjrmsr91OlcVZmvcs9sJg21ekkuOPaB5d+CP\r\nQNeitmyHHX+bDpHE9wYeVAzuQavIOf0RYnwtBUBDx6ru3JoNJ4TEZG+hLCx9\r\n8Ytq+dmH8QhIzYgO+osFTEntbPhOHt9jyS3gI/tP090n4yWt2IirG7xxkcGc\r\nvwFALD1pfAkbSGfNHYWh/6unuRIbWKgrq8LQNbaTZFYVT6U28xug9Yl/ntCi\r\nwgNVNiaYT2xMe0hjJN9yy25iDqHwh+ZpW5BT0FUrzHlWVj/KeK/PsYkSrP6W\r\nGFHR7BqqgUli3HEef14RIsLmw/LhIU7uXfVNZ3DSbZe1U8W48uuz7YytOqOo\r\nK1SFpUaY+DxhZwpPFsZouiMSDgCt+rBMqRbrpnP+LNWxd5ZMrtl02WmKMBol\r\nTs+Q3les9l8xJZJL5PaKj+fK8GuR+BWmQ+Tved/gfO1xqku5iq8jTszzW5dm\r\nSumSElXv8opBdN2siZcJyQ/b+CUFRk/4/kY=\r\n=Jyk6\r\n-----END PGP SIGNATURE-----\r\n", "size": 13055}, "_hasShrinkwrap": false, "publish_time": 1677513995181, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.1": {"name": "@codemirror/lint", "version": "6.2.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "directories": {}, "dist": {"integrity": "sha512-y1muai5U/uUPAGRyHMx9mHuHLypPcHWxzlZGknp/U5Mdb5Ol8Q5ZLp67UqyTbNFJJ3unVxZ8iX3g1fMN79S1JQ==", "shasum": "654581d8cc293c315ecfa5c9d61d78c52bbd9ccd", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.2.1.tgz", "fileCount": 8, "unpackedSize": 71119, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4BRdx/XkuDQxZvCueH/xgXPhVfN/8A5XK7uVIciXttQIgRr7qZ2odgjJ/27/m3HP/cuKMnaCdJ04WyrSuWvurBkc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkN5O2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwiA/8D0tNyT8aTeuT89SXgBMJKoJx4D5FQrTgV+uDuBTzF1RSg00E\r\nxzgKMuanwXH4rjo4b4i3NxzVpwSDBY+3TE+dF6AkTk5AQpvGoRQXCr1zkeOh\r\n89n3ANE6UUe9N3lsfDeWDOzQDixW66VumT1wDwcUAzo92hMoeMtzV25etigV\r\ng8LDb1RaTfy7EzWQw4GeKlschU1k7a2bmFtnLaoVChm4NwgoFUT+ogXkmMHQ\r\n5pPkIMqoLCyqg0z7To84yGp13xtoM78+eeaUUC6ZNArcFfrDHzxX6j8rAu0t\r\nj3ZtivsmP63emLuPB4u0wOFgxZEoa8stlva+PP1BAaZ4kPSQ5iuZ9xL5/4YL\r\noeHDTKCVmxJEMdcFclEpX18QGY4VBAlJE7onCICoi84gGkBgv3MH/Y8PEMa+\r\nq4hH4zZ4dnpOzf28hbPsXDd/SuPT7aMbAloAKehPerLUtu6EBjm71fnI6pLX\r\nkABermKiy1056cYPd5CSfexLDY5L08FwBa4WN5sr1smsAtSv7hsAPORfSpzt\r\nht4kXsjTqJXdZSKwXA1ip2MgA0qj2+4RuvsNh3lTidLrPcsLrXdJ4P54Nj6e\r\nOGFifP5KXhekOY6HdGobxB0TFi6SP80Gh9F9lg42NyCOlyp83V+O7M2H/cPQ\r\nRt6iqBrc6PpG00XipN8tGCTQmZdprQBZ3t8=\r\n=pIGh\r\n-----END PGP SIGNATURE-----\r\n", "size": 13264}, "_hasShrinkwrap": false, "publish_time": 1681363894063, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.2": {"name": "@codemirror/lint", "version": "6.2.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-kHGuynBHjqinp1Bx25D2hgH8a6Fh1m9rSmZFzBVTqPIXDIcZ6j3VI67DY8USGYpGrjrJys9R52eLxtfERGNozg==", "shasum": "a3b9817836d2a42af5c181104e252d9054a03a3f", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.2.2.tgz", "fileCount": 9, "unpackedSize": 76831, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEkDrGcot+16YsiVj0wlhJgGrQE68BmEa2jDWjVlZt7EAiEAuiZq09woM06qwgNuE6xkDOgftEaYSSfd11J7xfUAs4I="}], "size": 19972}, "_hasShrinkwrap": false, "publish_time": 1685950566099, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.0": {"name": "@codemirror/lint", "version": "6.3.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-tzxOVQNoDhhwFNfcTO2IB74wQoWarARcH6gv3YufPpiJ9yhcb7zD6JCkO5+FWARskqRFc8GFa6E+wUyOvADl5A==", "shasum": "bd36fb85094cf5735e5426e48f1a8e575a7b70df", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.3.0.tgz", "fileCount": 9, "unpackedSize": 79076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyTV8K6eHfTFqasr9xM/BfBNZavODawGqaECbQsGZDqAiB1R48MdUyFyNwd00sbmBnHDYc3k9RgS45zmLBlMx43Pg=="}], "size": 20428}, "_hasShrinkwrap": false, "publish_time": 1687505826916, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.0": {"name": "@codemirror/lint", "version": "6.4.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-6VZ44Ysh/Zn07xrGkdtNfmHCbGSHZzFBdzWi0pbd7chAQ/iUcpLGX99NYRZTa7Ugqg4kEHCqiHhcZnH0gLIgSg==", "shasum": "3507e937aa9415ef0831ff04734ef0e736e75014", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.4.0.tgz", "fileCount": 9, "unpackedSize": 80278, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGxBlXJb/8pivYOAcfbuUZHPNsHNY0cLRo4klWnOaqw2AiAfN8uGSSti0LtxvNlQYbjCMp1oiNruvGWm5uQ/T9sQ0A=="}], "size": 20788}, "_hasShrinkwrap": false, "publish_time": 1688375974199, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.1": {"name": "@codemirror/lint", "version": "6.4.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-2Hx945qKX7FBan5/gUdTM8fsMYrNG9clIgEcPXestbLVFAUyQYFAuju/5BMNf/PwgpVaX5pvRm4+ovjbp9D9gQ==", "shasum": "e4626ff8890c745209c6c24840843ff7933bc64f", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.4.1.tgz", "fileCount": 9, "unpackedSize": 80018, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC06YTV8Q/GulqxVd/5E8yeq7RfCz3cnk7E3ep3gKfyjwIhANN07D++ur7+d59L4z/nfbSHv6GkOX6ymvU3/M6efMuU"}], "size": 20666}, "_hasShrinkwrap": false, "publish_time": 1693048001859, "_source_registry_name": "default"}, "6.4.2": {"name": "@codemirror/lint", "version": "6.4.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wzRkluWb1ptPKdzlsrbwwjYCPLgzU6N88YBAmlZi8WFyuiEduSd05MnJYNogzyc8rPK7pj6m95ptUApc8sHKVA==", "shasum": "c13be5320bde9707efdc94e8bcd3c698abae0b92", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.4.2.tgz", "fileCount": 9, "unpackedSize": 80332, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDsqxNu+eyV1Rm/O5pdsnCZXD3GlJoWtYA4NdVdqlLNQIgIzOIJKdCT21bxouYTh54L0N7yQT28XIOXNdB1V8b0ag="}], "size": 20790}, "_hasShrinkwrap": false, "publish_time": 1694669872587, "_source_registry_name": "default"}, "6.5.0": {"name": "@codemirror/lint", "version": "6.5.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-+5Yyi<PERSON><PERSON>aaAZKU8K43IQi8TBy6mF6giGeWAH7N96Z5LC30Wm5JMjqxOYIE9mxwMG1NbhT2mA3l9hA4uuKUM3E5g==", "shasum": "ea43b6e653dcc5bcd93456b55e9fe62e63f326d9", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.5.0.tgz", "fileCount": 9, "unpackedSize": 81390, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcuj+XVW7UjyOSHU1Bd3x8zuVLdng0DinrTmnuLQlcUQIhAIsfXU+gAtd/cV1nUtSmvx3xaowXIQ5Ylzo9AehM1oVX"}], "size": 21094}, "_hasShrinkwrap": false, "publish_time": 1706647159451, "_source_registry_name": "default"}, "6.6.0": {"name": "@codemirror/lint", "version": "6.6.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-RDWLZqvnTlU2k7Xc2QbeySS7hdClfca76BFsdh9iE5OgszlHRenCLLufH8SCO+CCbod0XLaukxs5DesHrxQATQ==", "shasum": "9efa8bbb416fc9aed8bf229c6495dc3b84c40f59", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.6.0.tgz", "fileCount": 9, "unpackedSize": 82744, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5GPk9fuygWGI8mAGJXRJJU+Yi97CdvXo9UnHmiZSXCAiAsFj7oUqNLEVhv2ognJgaLFml1rPk5ukpHyPGOhLjsZw=="}], "size": 21512}, "_hasShrinkwrap": false, "publish_time": 1714424101105, "_source_registry_name": "default"}, "6.7.0": {"name": "@codemirror/lint", "version": "6.7.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-LTLOL2nT41ADNSCCCCw8Q/UmdAFzB23OUYSjsHTdsVaH0XEo+orhuqbDNWzrzodm14w6FOxqxpmy4LF8Lixqjw==", "shasum": "b252169512f826d5d742f1d82b478d53548c9ba6", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.7.0.tgz", "fileCount": 9, "unpackedSize": 82909, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDvVa0TCU6PcvoJxv8u0x7r4ZCWYlHfSzeKUfkBGIsewAiBLNDygBipcYJob8y5C0bo/SHPvgi+cVbw+i8dSs3+Bpw=="}], "size": 21540}, "_hasShrinkwrap": false, "publish_time": 1714455961509, "_source_registry_name": "default"}, "6.7.1": {"name": "@codemirror/lint", "version": "6.7.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-rELba6QJD20/bNXWP/cKTGLrwVEcpa2ViwULCV03ONcY1Je85++7sczVRUlnE4TJMjatx3IJTz6HX4NXi+moXw==", "shasum": "bed4b3a38785678efbe683efe0e61d8ccf478d58", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.7.1.tgz", "fileCount": 9, "unpackedSize": 83113, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5nFFyq30TQGTrlCCfPjwR07bYgkdhL8cPZlr2PnBP+QIgCYEsEleyQTIpYlsp1M2oGQeb97NYmgULOxXxZBhHUDc="}], "size": 21712}, "_hasShrinkwrap": false, "publish_time": 1715752073986, "_source_registry_name": "default"}, "6.8.0": {"name": "@codemirror/lint", "version": "6.8.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-lsFofvaw0lnPRJlQylNsC4IRt/1lI4OD/yYslrSGVndOJfStc58v+8p9dgGiD90ktOfL7OhBWns1ZETYgz0EJA==", "shasum": "cf9067c7041c1f6c9f20bab411dac9323aab54f0", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.8.0.tgz", "fileCount": 9, "unpackedSize": 84284, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHi9XsbfCJK2rqdmRUQcIltI2imDyd/a0kyDf1agHjTrAiEAu4CTPL4dDqdIJmgZSyeufJPDW/kv1ak8ldMgE6ardGk="}], "size": 22006}, "_hasShrinkwrap": false, "publish_time": 1716460723375, "_source_registry_name": "default"}, "6.8.1": {"name": "@codemirror/lint", "version": "6.8.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-IZ0Y7S4/bpaunwggW2jYqwLuHj0QtESf5xcROewY6+lDNwZ/NzvR4t+vpYgg9m7V8UXLPYqG+lu3DF470E5Oxg==", "shasum": "6427848815baaf68c08e98c7673b804d3d8c0e7f", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.8.1.tgz", "fileCount": 9, "unpackedSize": 84316, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEnfRkiyipnoanDY2edK8Vkhc41P8CzLZ3t9o5/VUZ8BAiB9zVl7Furnka/2DDeiLFhV/0ik/ux4Zjo0m/uM6A5mWA=="}], "size": 22023}, "_hasShrinkwrap": false, "publish_time": 1718779904507, "_source_registry_name": "default"}, "6.8.2": {"name": "@codemirror/lint", "version": "6.8.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-PDFG5DjHxSEjOXk9TQYYVjZDqlZTFaDBfhQixHnQOEVDDNHUbEh/hstAjcQJaA6FQdZTD1hquXTK0rVBLADR1g==", "shasum": "7864b03583e9efd18554cff1dd4504da10338ab1", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.8.2.tgz", "fileCount": 9, "unpackedSize": 85858, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0cITSdhzuYqp2hD3+HoGoP1h1lNeRrGpM/xfUOxNM1QIgNAY3ZVdhTUAtOYcl+26/GmJIl7ABXMYAth6XPr0pEn0="}], "size": 22431}, "_hasShrinkwrap": false, "publish_time": 1727165578191, "_source_registry_name": "default"}, "6.8.3": {"name": "@codemirror/lint", "version": "6.8.3", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.35.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-GSGfKxCo867P7EX1k2LoCrjuQFeqVgPGRRsSl4J4c0KMkD+k1y6WYvTQkzv0iZ8JhLJDujEvlnMchv4CZQLh3Q==", "shasum": "5988b4afc0717da2ccec6ddd33c5cfc0de5ba4d9", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.8.3.tgz", "fileCount": 9, "unpackedSize": 86042, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA+7+Oh0lJsXTmnkKjT3rXVITQJqOPreS+C2zmTyjeKZAiEAjB+wszpYDZIT7uenvy0iEyMjcwiMtqNx0cP3Ajvw9Os="}], "size": 22470}, "_hasShrinkwrap": false, "publish_time": 1732183379261, "_source_registry_name": "default"}, "6.8.4": {"name": "@codemirror/lint", "version": "6.8.4", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.35.0", "crelt": "^1.0.5"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-u4q7PnZlJUojeRe8FJa/njJcMctISGgPQ4PnWsd9268R4ZTtU+tfFYmwkBvgcrK2+QQ8tYFVALVb5fVJykKc5A==", "shasum": "7d8aa5d1a6dec89ffcc23ad45ddca2e12e90982d", "tarball": "https://registry.npmmirror.com/@codemirror/lint/-/lint-6.8.4.tgz", "fileCount": 9, "unpackedSize": 91532, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHST/cwEqbS3+yR2wH2TNUQqiNJC7wpxWoWJyG9orjFmAiEAw2s8vjzPUGDuyKcRrFrv/f6/Ip3MWQ/+CRaQisoG06E="}], "size": 23910}, "_hasShrinkwrap": false, "publish_time": 1732785414139, "_source_registry_name": "default"}}, "_source_registry_name": "default"}