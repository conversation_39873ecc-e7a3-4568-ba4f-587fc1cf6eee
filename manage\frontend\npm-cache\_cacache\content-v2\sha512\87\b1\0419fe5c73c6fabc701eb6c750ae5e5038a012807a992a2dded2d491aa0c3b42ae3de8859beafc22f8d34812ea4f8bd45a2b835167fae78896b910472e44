{"dist-tags": {"latest": "3.0.3"}, "modified": "2024-05-21T09:32:52.307Z", "name": "braces", "versions": {"3.0.2": {"name": "braces", "version": "3.0.2", "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "directories": {}, "dist": {"shasum": "3454e1a462ee8d599e236df336cd9ea4f8afe107", "size": 15637, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "3.0.1": {"name": "braces", "version": "3.0.1", "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "directories": {}, "dist": {"shasum": "dd8f330ba1c895e39de73ec33e99275443ff0fed", "size": 15971, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-3.0.1.tgz", "integrity": "sha512-Vmyh3JAr5DRUKCdRrC+WyAAsWBez8HLnBmVb6Ux2VYbvC8DjqMC228WHx24fiQG5BiDOVo+otK1scdkK5S6YNg=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.0": {"name": "braces", "version": "3.0.0", "dependencies": {"fill-range": "^7.0.1"}, "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "directories": {}, "dist": {"shasum": "1d9a1a84daea4606d3a6a03d1fd97cc6bd37d543", "size": 15953, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-3.0.0.tgz", "integrity": "sha512-JrED+3ZoiTW3KmWkrajE5zm5Pl69XD1DjItKTX9KS+dsfrge66nho5fAOC+tBwMH5p5bPv97EYm8loTb7mxPKg=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.2": {"name": "braces", "version": "2.3.2", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^2.0.0", "brace-expansion": "^1.1.8", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^8.0.0"}, "directories": {}, "dist": {"shasum": "5979fd3f14cd531565e5fa2df1abfff1dfaee729", "size": 17531, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.3.2.tgz", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.1": {"name": "braces", "version": "2.3.1", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "kind-of": "^6.0.2", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^2.0.0", "brace-expansion": "^1.1.8", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^8.0.0"}, "directories": {}, "dist": {"shasum": "7086c913b4e5a08dbe37ac0ee6a2500c4ba691bb", "size": 19901, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.3.1.tgz", "integrity": "sha512-SO5lYHA3vO6gz66erVvedSCkp7AKWdv6VcQ2N4ysXfPxdAlxAMMAdwegGGcv1Bqwm7naF1hNdk5d6AAIEHV2nQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.0": {"name": "braces", "version": "2.3.0", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^2.0.0", "brace-expansion": "^1.1.8", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^8.0.0"}, "directories": {}, "dist": {"shasum": "a46941cb5fb492156b3d6a656e06c35364e3e66e", "size": 17931, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.3.0.tgz", "integrity": "sha512-P4O8UQRdGiMLWSizsApmXVQDBS6KCt7dSexgLKBmH5Hr1CZq7vsnscFh8oR1sP1ab1Zj0uCHCEzZeV6SfUf3rA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.2.2": {"name": "braces", "version": "2.2.2", "dependencies": {"arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^2.1.0", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "brace-expansion": "^1.1.7", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^5.0.0"}, "directories": {}, "dist": {"shasum": "241f868c2b2690d9febeee5a7c83fbbf25d00b1b", "size": 17129, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.2.2.tgz", "integrity": "sha512-LsV+Z9xUYWR9XX0fnDceeu4+X+RCZ18P8YHJlgSUEzRjsOFfMl1gjzIg7vfbW6cJg8mfJIuoWpVbgAkimkAWAA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.2.1": {"name": "braces", "version": "2.2.1", "dependencies": {"arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^2.1.0", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "brace-expansion": "^1.1.7", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^5.0.0"}, "directories": {}, "dist": {"shasum": "cccce9e092a93faa0bba2c1174cd262d2369794d", "size": 16982, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.2.1.tgz", "integrity": "sha512-saw1qqYe6p9B1zaZJco2fuIk1xl+NvrvXg29DYjUEv4S1FvSFZFzjHMtfDFbKkXHKKZsQDLBgASKRaz5mnV2nw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.2.0": {"name": "braces", "version": "2.2.0", "dependencies": {"arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^2.1.0", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "brace-expansion": "^1.1.7", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^5.0.0"}, "directories": {}, "dist": {"shasum": "9edd1d6ee0acd760f1d1e5c13bc66d428ad0a3c0", "size": 16726, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.2.0.tgz", "integrity": "sha512-RZWGwDQ3cdGQTqajHnk4KXjmTdpdwzlotZ1nZa9GCoyG/fkzyjuMvDXGlkgzNDSvcxGfApZmIwVtpXUzht6vLA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.1": {"name": "braces", "version": "2.1.1", "dependencies": {"arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "split-string": "^2.1.0", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "brace-expansion": "^1.1.7", "cross-spawn": "^5.1.0", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^5.0.0"}, "directories": {}, "dist": {"shasum": "b33581be8553a651fdc79012760a7e767f82b834", "size": 15644, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.1.1.tgz", "integrity": "sha512-hUsQnnSix9sYcu2hgn/CwF7LR5YP+ICow571lAORgz3R++N0zA51MV5z41skZdE1XsEU9+4eHUro/K8/SVLh2A=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.0": {"name": "braces", "version": "2.1.0", "dependencies": {"arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "split-string": "^2.0.0", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^1.0.0", "brace-expansion": "^1.1.7", "cross-spawn": "^5.1.0", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^5.0.0"}, "directories": {}, "dist": {"shasum": "27dc86dfb1add13b2974fc4276e01941a246e14a", "size": 15639, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.1.0.tgz", "integrity": "sha512-qrKq9EBrp6asnvy70BJGJzjtIoirGrpteVrEQwWkzxFFbVy6z7vgijSXsI2z3mcjsCnjrJE4huxvlIcyQWBlVw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.4": {"name": "braces", "version": "2.0.4", "dependencies": {"arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "fill-range": "^3.1.1", "isobject": "^3.0.0", "regex-not": "^1.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "split-string": "^2.0.0", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "brace-expansion": "^1.1.7", "cross-spawn": "^5.1.0", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^5.0.0"}, "directories": {}, "dist": {"shasum": "6381bfe9154fbc2b0b1b420aab574b1ac87534ee", "size": 15630, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.0.4.tgz", "integrity": "sha512-+fxIrbtFLL6T/b1N86npW5J3WFiGNo6mKBel0tLT6zLDRK5uggXgaPW3T3Bua4JCUTmktzENYjY6P82Dcv40kw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.3": {"name": "braces", "version": "2.0.3", "dependencies": {"arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "fill-range": "^3.0.3", "isobject": "^3.0.0", "regex-not": "^1.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "brace-expansion": "^1.1.6", "cross-spawn": "^5.0.1", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.11", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "is-windows": "^1.0.0", "minimatch": "^3.0.3", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^4.2.0"}, "directories": {}, "dist": {"shasum": "7ac586e593bdc43a262daf45673661464844198f", "size": 15535, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.0.3.tgz", "integrity": "sha512-vp/DHMo3GvQgTvOp5Nvm+Rcd5DAWCYT+vDL9A3ZTm6flVOw0WH8IjcyBleC9H5BrNu2JhEQmyPUYgFHExUUY/w=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.2": {"name": "braces", "version": "2.0.2", "dependencies": {"arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "fill-range": "^3.0.3", "isobject": "^2.1.0", "regex-not": "^1.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "brace-expansion": "^1.1.6", "cross-spawn": "^4.0.2", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.11", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "is-windows": "^0.2.0", "minimatch": "^3.0.3", "mocha": "^3.1.2", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^4.0.2"}, "directories": {}, "dist": {"shasum": "d4c9b44820e63a7b79437a4ef094b8ab53a4ae44", "size": 15519, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.0.2.tgz", "integrity": "sha512-WhhjRmZ3ksdEwciX8KbK3E0FjExG4aSetO70d3X4hM/g0elE8XYJNLBq1ov82HTPXEGg0/0DGaSqpvqwzwXyGw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.1": {"name": "braces", "version": "2.0.1", "dependencies": {"arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "fill-range": "^3.0.3", "isobject": "^2.1.0", "regex-not": "^1.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "brace-expansion": "^1.1.6", "cross-spawn": "^4.0.2", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.11", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "is-windows": "^0.2.0", "minimatch": "^3.0.3", "mocha": "^3.1.2", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^4.0.2"}, "directories": {}, "dist": {"shasum": "aef8fb862f7f429d9840a931cbbbbf8355107905", "size": 14407, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.0.1.tgz", "integrity": "sha512-qRHNcprYklsNT+kuUQLUxluiz3eFb7utwCuTBhrlfA4tYFG4cyUSw0vgTs8Z6yFgpssXiQiDHsoeU0wDaW+5Jw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0": {"name": "braces", "version": "2.0.0", "dependencies": {"arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "fill-range": "^3.0.3", "isobject": "^2.1.0", "regex-not": "^1.0.0", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "brace-expansion": "^1.1.6", "cross-spawn": "^4.0.2", "fs-exists-sync": "^0.1.0", "gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.11", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "is-windows": "^0.2.0", "minimatch": "^3.0.3", "mocha": "^3.1.2", "noncharacters": "^1.1.0", "pretty-bytes": "^4.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^4.0.2"}, "directories": {}, "dist": {"shasum": "3d809da0a3e2024761d48651955fd9452e25ec85", "size": 13944, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-2.0.0.tgz", "integrity": "sha512-oUsVNHRH2pbD2nhJuU6oHgMTpwH/V4ABbzRshMPcIc9GZeYi57sfTrPGGXyLfBreFlGlbvv7aFrQd+GYLwo8yw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.8.5": {"name": "braces", "version": "1.8.5", "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "devDependencies": {"benchmarked": "^0.1.5", "brace-expansion": "^1.1.3", "chalk": "^1.1.3", "gulp-format-md": "^0.1.8", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "should": "^8.3.1"}, "directories": {}, "dist": {"shasum": "ba77962e12dff969d6b76711e914b737857bf6a7", "size": 5981, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.8.5.tgz", "integrity": "sha512-xU7bpz2ytJl1bH9cgIurjpg/n8Gohy9GTw81heDYLJQ4RU60dlyJsa+atVF2pI0yMMvKxI9HkKwjePCj5XI1hw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.8.4": {"name": "braces", "version": "1.8.4", "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "devDependencies": {"benchmarked": "^0.1.5", "brace-expansion": "^1.1.3", "chalk": "^1.1.3", "gulp-format-md": "^0.1.8", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "should": "^8.3.1"}, "directories": {}, "dist": {"shasum": "75e2d6456d48b06dbb5205ed63442a3bfc5eefce", "size": 6001, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.8.4.tgz", "integrity": "sha512-Ed5g6san2X6MCCzrwAPTAfe9Ak9zkgXQ9jUhgSRiUsb5+/ceptlRtHNbHOlc2e2SVkrGW1Jayf6AMR8YFyMoQg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.8.3": {"name": "braces", "version": "1.8.3", "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "35d4e7dda632b33e215d38a8a9cf4329c9c75d2c", "size": 5673, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.8.3.tgz", "integrity": "sha512-a1Y3ZnTdzkI10NdmKkDpgJGq4pXLTy/QCvsyHFEhMM+8fMnx2+SB8r+7HoKYMmqlSv8tgbs3nKypNEEZSTC4uA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.8.2": {"name": "braces", "version": "1.8.2", "dependencies": {"expand-range": "^1.8.1", "lazy-cache": "^0.2.3", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "036e024051d4bbc7096428b4d6f20ea1f137a794", "size": 5777, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.8.2.tgz", "integrity": "sha512-SY1uQ2Z0f8nLzUp6jJIvNVMLmzmzpVb6C13ipLEJlPL4XbFc7fEhVYJxMNpqt05SKg46oX3h1clGdqEbfBtwyA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "1.8.1": {"name": "braces", "version": "1.8.1", "dependencies": {"expand-range": "^1.8.1", "lazy-cache": "^0.2.3", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "2d195b85a0a997ec21be78a7f1bc970480b12a1a", "size": 5745, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.8.1.tgz", "integrity": "sha512-92peuXcKVAXcbTGlsftd1PHiVAi363Ijh/IkNA6+7tWCupPvZDsNn9p5/7ecs7SUz1SrJrAPf0WM2vU8Vl8qqw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.8.0": {"name": "braces", "version": "1.8.0", "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "3a4a005aae0391817c17b074dca8f08e6fc9e4c4", "size": 5746, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.8.0.tgz", "integrity": "sha512-zmetXdAbxy7iu++21YqfIvdufiV+ndJzUck78ObHHMMiM/HRNLWGiI052nUT5A623rRudpld0hfkOBiwoIiCIw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.7.0": {"name": "braces", "version": "1.7.0", "dependencies": {"arr-filter": "^1.0.2", "arr-map": "^1.0.0", "expand-range": "^1.8.0", "preserve": "^0.2.0", "repeat-element": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "ed954c25e8a170971202316c1899fa98fdae6d6d", "size": 5691, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.7.0.tgz", "integrity": "sha512-vUUn0ZC0UApO70cQQfi6Pd7HNU6drH7opymAFr3gYtZ+Go45nimpawAdnDnKj3wrPDFsmqqHc7JUE7PZHznmvA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.6.0": {"name": "braces", "version": "1.6.0", "dependencies": {"arr-filter": "^1.0.2", "expand-range": "^1.8.0", "preserve": "^0.2.0", "repeat-element": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "438c23185022e8fa968af9feeaa056723a3efe55", "size": 5437, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.6.0.tgz", "integrity": "sha512-bq8Ul/s4vyD8GX2jSqUT/Db+BIPktohKewWzaczlB9+BVMVx0Y423mbsPlbRussx5F5n0OH6n0PJODyqWugCBg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.5.1": {"name": "braces", "version": "1.5.1", "dependencies": {"arr-filter": "^1.0.2", "expand-range": "^1.5.0", "preserve": "^0.2.0", "repeat-element": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "faefcd7e5251c84c21127e8f079b14775b6c06ce", "size": 5438, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.5.1.tgz", "integrity": "sha512-PhxDD2jLYwNFNO0hYkYGTMIh+n+ioNXi6cM+TYyWaqruoGOpjWTLGj5XFnIHciR5snxwtQtLzfOU85gEqR0QPA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.5.0": {"name": "braces", "version": "1.5.0", "dependencies": {"arr-filter": "^1.0.2", "expand-range": "^1.5.0", "preserve": "^0.2.0", "repeat-element": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "3600d82f04390dfc7c6016f091657b2d86189a15", "size": 4455, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.5.0.tgz", "integrity": "sha512-lxYGtXXPqFES/5rDs1l01Fy6GM3hlZm65uxHgbcHvkcz0TenXPm7rbye2JaKl/RSJ6/cJwW+tC1ax34rKrk3IQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.0": {"name": "braces", "version": "1.4.0", "dependencies": {"arr-filter": "^1.0.2", "expand-range": "^1.5.0", "preserve": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "c95b1b8fd045b765703c2c0cd95e0369e5696492", "size": 4864, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.4.0.tgz", "integrity": "sha512-A7mrTs9OAQrOpAHne8Ukiit7Aue9TNkcg0QO/hw/U8T43VUWPvIbhCCbgpoCgCEza8Y50YpKpC89+0SlWa9jWQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.3.0": {"name": "braces", "version": "1.3.0", "dependencies": {"arr-filter": "^1.0.2", "expand-range": "^1.4.0", "preserve": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "e0495cf4796c137fb0d80a922ffef431fbde6bef", "size": 4683, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.3.0.tgz", "integrity": "sha512-5T2hlMwxt1IhrjcL6wdbCi/wOTwtdEFc58D/G0ER1MkO8syeMF0TcCf0KTSUpuEdrbN3FOCf+SN/xl8vWRHBAA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.2.0": {"name": "braces", "version": "1.2.0", "dependencies": {"arr-filter": "^1.0.2", "expand-range": "^1.2.0", "preserve": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "b86f8f3898d11dc1008d29176a45b4863190e7b9", "size": 5219, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.2.0.tgz", "integrity": "sha512-LrWXOZAaORkFo9btgFznnPWxqe0xfUUIse+7OLWzt84ddPX0747PZS+BV86W1F3VyIeOhckQ4CRBhp6DnPhgYw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.1.0": {"name": "braces", "version": "1.1.0", "dependencies": {"arr-filter": "^1.0.0", "expand-range": "^1.1.0", "preserve": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "1fd01ee8420a7196d234f2a29dc1bfe6e259854b", "size": 5198, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.1.0.tgz", "integrity": "sha512-/41xAQljVWmYtUjUrfjRGdEAUD/Z4fEJKziIDuQN20hniJX/opnWHGjyvNhf7u920PinSkh58l7vsjM43HpcmA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0": {"name": "braces", "version": "1.0.0", "dependencies": {"expand-range": "^0.3.1"}, "devDependencies": {"benchmarked": "^0.1.3", "brace-expansion": "^1.1.0", "minimatch": "^2.0.1", "mocha": "^2.1.0", "should": "^4.4.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "991c57970805294a6f1a8aade96c9037fcea1246", "size": 3153, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-1.0.0.tgz", "integrity": "sha512-96tjnHiAQnke1h8ibSkJXk60GMG2JFSad0zwnM2duZDfWtJXEyJ8xDlRoMLvPPotk/0uJtAk2XIp0VwLrJ+GTg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.5": {"name": "braces", "version": "0.1.5", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "lodash": "^2.4.1", "minimatch": "^1.0.0", "mocha": "*", "should": "^4.1.0"}, "directories": {}, "dist": {"shasum": "c085711085291d8b75fdd74eab0f8597280711e6", "size": 6007, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-0.1.5.tgz", "integrity": "sha512-EIMHIv2UXHWFY2xubUGKz+hq9hNkENj4Pjvr7h58cmJgpkK2yMlKA8I484f7MSttkzVAy/lL7X9xDaILd6avzA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.4": {"name": "braces", "version": "0.1.4", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "lodash": "^2.4.1", "minimatch": "^1.0.0", "mocha": "*", "should": "^4.1.0"}, "directories": {}, "dist": {"shasum": "45ed440222debb0fec60b06bb75059e421c590fd", "size": 5951, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-0.1.4.tgz", "integrity": "sha512-5ZaWT3BXeL4rfMmOgZUktJ3YExoTc/tfHFV8xJRb5At1uATRxLwXJY/cItMFzCF4e33yPZWOR08mbmUJneCQIw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.2": {"name": "braces", "version": "0.1.2", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "lodash": "^2.4.1", "minimatch": "^1.0.0", "mocha": "*", "should": "^4.1.0", "verb": ">= 0.2.6", "verb-tag-jscomments": ">= 0.2.0"}, "directories": {}, "dist": {"shasum": "f794ff9aabe60eaf557e9607cc850c8f531a12be", "size": 5685, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-0.1.2.tgz", "integrity": "sha512-tQ1ltvLIdYGIpKDwnjDqqJ7ZYxDOHsJiBNgp2N9RsEAtPzTcKq3aJlQ40+qXWYHFpy4cC5wthznUE2M89xlMSw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.1": {"name": "braces", "version": "0.1.1", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "lodash": "^2.4.1", "minimatch": "^1.0.0", "mocha": "*", "should": "^4.1.0", "verb": ">= 0.2.6", "verb-tag-jscomments": ">= 0.2.0"}, "directories": {}, "dist": {"shasum": "9b3c273b426d1a7b3c9265094a9434ab84643ba3", "size": 5594, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-0.1.1.tgz", "integrity": "sha512-biKFllQsz6LzOEwAFWX/CMy8NtDzGwXYUXFgNWWRuehPC3X0okAb5lEFf2PtnHwHYe6MoJZD0fuy+BFtoJC5Sg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.0": {"name": "braces", "version": "0.1.0", "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "lodash": "^2.4.1", "minimatch": "^1.0.0", "mocha": "*", "should": "^4.1.0", "verb": ">= 0.2.6", "verb-tag-jscomments": ">= 0.2.0"}, "directories": {}, "dist": {"shasum": "2ea567d1f9622ba2395135bbf81fe140da7fefeb", "size": 5316, "noattachment": false, "tarball": "https://registry.npmmirror.com/braces/-/braces-0.1.0.tgz", "integrity": "sha512-59/QQZW1FIbvnxuRWTNaulRIvblwjvGKwz88r/HJIN1TE0N12M2pbRNZpYUWuE7DAyl8C2ucWeX9M7DYWnZVpg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.3": {"name": "braces", "version": "3.0.3", "dependencies": {"fill-range": "^7.1.1"}, "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "directories": {}, "dist": {"integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "shasum": "490332f40919452272d55a8480adc0c441358789", "tarball": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "fileCount": 10, "unpackedSize": 44635, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfWLKNcFCGtU07ikrPJ+s1dTDH3VZTE+vQZzC22s6o+AiAEVUE/YXRqHYgctcwHqNawvsOviSDFZPvgNm8+SUXD3w=="}], "size": 13972}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "publish_time": 1716281951390, "_source_registry_name": "default"}}, "_source_registry_name": "default"}