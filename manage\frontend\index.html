<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ飞车赛车图鉴管理系统</title>
    <!-- 404页面处理脚本 -->
    <script>
      /**
       * 严格的路由匹配逻辑，确保任何不存在的路由都能被正确识别并重定向到404页面
       * 这个脚本会在Vue应用加载之前执行，可以拦截所有不存在的路由
       */
      (function() {
        // 定义应用的有效路由前缀（精确匹配）
        const exactRoutes = [
          '/',
          '/login',
          '/404'
        ];

        // 定义应用的有效路由前缀（前缀匹配）
        const prefixRoutes = [
          '/illustration/',
          '/users/',
          '/feedback/',
          '/sql-executor/',
          '/comments/',
          '/lottery/',
          '/car-treasure/',
          '/vip-management/',
          '/prize-management/'
        ];

        // 定义静态资源路径前缀
        const staticPrefixes = [
          '/assets/',
          '/favicon.ico',
          '/robots.txt'
        ];

        // 定义API路由前缀
        const apiPrefix = '/api/';

        // 获取当前路径
        const path = window.location.pathname;

        // 检查是否是API请求
        if (path.startsWith(apiPrefix)) {
          // 如果是API请求，不做处理，让请求正常进行
          return;
        }

        // 检查是否是静态资源
        if (staticPrefixes.some(prefix => path.startsWith(prefix))) {
          // 如果是静态资源，不做处理，让请求正常进行
          return;
        }

        // 检查是否是精确匹配的路由
        if (exactRoutes.includes(path)) {
          // 如果是精确匹配的路由，不做处理，让请求正常进行
          return;
        }

        // 检查是否是前缀匹配的路由
        if (prefixRoutes.some(prefix => path.startsWith(prefix))) {
          // 如果是前缀匹配的路由，不做处理，让请求正常进行
          return;
        }

        // 如果不是以上任何一种情况，则重定向到404.html
        console.log('路由不存在，重定向到404页面:', path);
        window.location.href = '/404.html';
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
