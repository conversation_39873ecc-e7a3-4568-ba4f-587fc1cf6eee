RewriteEngine On
RewriteBase /

# 处理API请求 - 不重写API请求
RewriteRule ^api/ - [L]

# 处理静态文件
RewriteRule ^index\.html$ - [L]
RewriteRule ^404\.html$ - [L]
RewriteRule ^(assets|css|js|img|fonts|static|favicon\.ico|robots\.txt)/ - [L]

# 对于静态资源，直接访问
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule . - [L]

# 处理根路径
RewriteRule ^$ /index.html [L]

# 处理应用路由 - 精确匹配
RewriteRule ^(login|404)$ /index.html [L]

# 处理应用路由 - 前缀匹配
RewriteRule ^(illustration|users|feedback|sql-executor|comments|lottery|car-treasure|vip-management|prize-management)(/.*)?$ /index.html [L]

# 对于所有其他路由，直接返回404页面
RewriteRule . /404.html [R=404,L]

# 设置错误页面
ErrorDocument 404 /404.html
ErrorDocument 500 /404.html
