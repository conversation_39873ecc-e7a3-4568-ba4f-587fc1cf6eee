{"dist-tags": {"latest": "4.0.8"}, "modified": "2024-08-23T18:53:44.899Z", "name": "micromatch", "versions": {"4.0.4": {"name": "micromatch", "version": "4.0.4", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.3"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^3.0.4", "mocha": "^7.2.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "896d519dfe9db25fce94ceb7a500919bf881ebf9", "size": 16616, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.4.tgz", "integrity": "sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "danez", "email": "<EMAIL>"}}, "4.0.3": {"name": "micromatch", "version": "4.0.3", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.1"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^3.0.4", "mocha": "^5.2.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "fdad8352bf0cbeb89b391b5d244bc22ff3dd4ec8", "size": 16578, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.3.tgz", "integrity": "sha512-ueuSaP4i67F/FAUac9zzZ0Dz/5KeKDkITYIS/k4fps+9qeh1SkeH6gbljcqz97mNBOsaWZ+iv2UobMKK/yD+aw=="}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "danez", "email": "<EMAIL>"}}, "4.0.2": {"name": "micromatch", "version": "4.0.2", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.5"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^3.0.4", "mocha": "^5.2.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259", "size": 16675, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.2.tgz", "integrity": "sha512-y7FpHSbMUMoyPbYUSzO6PaZ6FyRnQOpHuKwbo1G+Knck95XVU4QAiKdGEnj5wwoS7PlOgthX/09u5iFJ+aYf5Q=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.1": {"name": "micromatch", "version": "4.0.1", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.3"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^3.0.4", "mocha": "^5.2.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "1bebd1a6e8dd8cff4669f3a687bdad62fb18571e", "size": 16615, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.1.tgz", "integrity": "sha512-6yawNHAc4S9Dh81xZCkZ5sXKH0/ly0t1DiOc+rnqzi0OvwS4DgRZU+HYTNDIgULgZXTNw5N8Vhxh2va8nEO6BA=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.0": {"name": "micromatch", "version": "4.0.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.3"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^3.0.4", "mocha": "^5.2.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"shasum": "3d9e7a815fabfb009a10fa5adc268242c6d6088e", "size": 16592, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.0.tgz", "integrity": "sha512-THzpRAtp/NcyqnAzYwvP9V1bMAM4zFs2AR02wwxNLzEbi6Mn2suaQ6lhiD8Ug+X3L3g9grohOe1NGb2m+72eeA=="}, "engines": {"node": ">=8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.10": {"name": "micromatch", "version": "3.1.10", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "70859bc95c9840952f359a068a3fc49f9ecfac23", "size": 19260, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.9": {"name": "micromatch", "version": "3.1.9", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "15dc93175ae39e52e93087847096effc73efcf89", "size": 19347, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.9.tgz", "integrity": "sha512-SlIz6sv5UPaAVVFRKodKjCg48EbNoIhgetzfK/Cy0v5U52Z6zB136M8tp0UC9jM53LYbmIRihJszvvqpKkfm9g=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.8": {"name": "micromatch", "version": "3.1.8", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "5c8caa008de588eebb395e8c0ad12c128f25fff1", "size": 19376, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.8.tgz", "integrity": "sha512-/XeuOQqYg+B5kwjDWekXseSwGS7CzE0w9Gjo4Cjkf/uFitNh47NrZHAY2vp/oS2YQVfebPIdbEIvgdy+kIcAog=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.7": {"name": "micromatch", "version": "3.1.7", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "ffbb25a79585d9cdbf9cb134ac21c5853dba0eb3", "size": 19373, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.7.tgz", "integrity": "sha512-uQTIoHx8MmOgQ/ZKAh9Oa4sGmn+wia5/QLQ5zBR5WCcPrnchTgUJCCEcZerQec67XqUSfE1OUEtUps/gRFYDSg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.6": {"name": "micromatch", "version": "3.1.6", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "8d7c043b48156f408ca07a4715182b79b99420bf", "size": 19346, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.6.tgz", "integrity": "sha512-6hezhKgmSIRZeSCiVB84GOmH1Ajvo8XgnaEq/uPQ/wv0g+MQlaVonSEru7VMDZXzRWFoclakpADfInbg/5FGjw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.5": {"name": "micromatch", "version": "3.1.5", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.0", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^6.0.0", "nanomatch": "^1.2.5", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.2.0", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "d05e168c206472dfbca985bfef4f57797b4cd4ba", "size": 19324, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.5.tgz", "integrity": "sha512-ykttrLPQrz1PUJcXjwsTUjGoPJ64StIGNE2lGVD1c9CuguJ+L7/navsE8IcDNndOoCMvYV0qc/exfVbMHkUhvA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.4": {"name": "micromatch", "version": "3.1.4", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.0", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^6.0.0", "nanomatch": "^1.2.5", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.2.0", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "bb812e741a41f982c854e42b421a7eac458796f4", "size": 19413, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.4.tgz", "integrity": "sha512-kFRtviKYoAJT+t7HggMl0tBFGNAKLw/S7N+CO9qfEQyisob1Oy4pao+geRbkyeEd+V9aOkvZ4mhuyPvI/q9Sfg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.3": {"name": "micromatch", "version": "3.1.3", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.0", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^6.0.0", "nanomatch": "^1.2.5", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.2.0", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "ae1ee52aff9c990a83ff8fb69891aeba2847c85f", "size": 19414, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.3.tgz", "integrity": "sha512-gVCSW2StFfuHZYfh/p/HJpdTyB/YX/mr/EATvmw9zMQa6BSUioG4hg4duKEKc47OaXioikzhgFYS/m4EyLmXXg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.2": {"name": "micromatch", "version": "3.1.2", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.0", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^6.0.0", "nanomatch": "^1.2.4", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.2.0", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "2737f3b16f4a7f12af3591bc30da4aa4dfbaf23e", "size": 19410, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.2.tgz", "integrity": "sha512-dIgY4Cr0Xq1NzICdDCPB9KQvjqNm23VfWXTZOSysk/1SzdjwkjnGozvLHS589VG07iGHOyHL6uYzvvhZ+Pc1pQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.1": {"name": "micromatch", "version": "3.1.1", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.0", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^6.0.0", "nanomatch": "^1.2.4", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^4.0.1", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "1c491537524916fb5b60c8f6cccdf25fceaa0def", "size": 19413, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.1.tgz", "integrity": "sha512-wSN8CKMA+JP1FEwVDKZBQDz9VaMfA8wNCRG/HxUHu7Mb5TjXq9UvA+Ss1iuUkXUhcCDWtwORNf2mZHJTW9fPBw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.0": {"name": "micromatch", "version": "3.1.0", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.2.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^5.0.2", "nanomatch": "^1.2.1", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.0", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "5102d4eaf20b6997d6008e3acfe1c44a3fa815e2", "size": 19316, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.0.tgz", "integrity": "sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.5": {"name": "micromatch", "version": "3.0.5", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.2.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.0", "fragment-cache": "^0.2.1", "kind-of": "^5.0.2", "nanomatch": "^1.2.1", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.0", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "99717da26f83c050cd712ab5ea59f71821f758f9", "size": 18608, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.0.5.tgz", "integrity": "sha512-NszdHwRzt/Kj4ee6W0TArIZ0kPN48pUN8jXTdzz6iDpJ16fxiYjB1KjMI138S450+9iKqXcmpY4MTT5pB317Rg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.4": {"name": "micromatch", "version": "3.0.4", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.2.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^1.1.0", "fragment-cache": "^0.2.1", "kind-of": "^4.0.0", "nanomatch": "^1.2.0", "object.pick": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^4.3.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.4.2", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "1543f1d04813447ac852001c5f5a933401786d1d", "size": 18801, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.0.4.tgz", "integrity": "sha512-W07uHmC2/7xleHWcYgg4iASgcUSY9TCBnap3gAn+331X7hMQKyRDGzcHzMsadQKa5KNcM/55hFocVggIyTuVFQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "3.0.3": {"name": "micromatch", "version": "3.0.3", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.2.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^1.1.0", "fragment-cache": "^0.2.1", "kind-of": "^4.0.0", "nanomatch": "^1.2.0", "object.pick": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^4.3.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.4.2", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "af3339640157ddad39b81a09956d8877cc4b421a", "size": 18709, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.0.3.tgz", "integrity": "sha512-uTjtugrI0sC0KbJNLZtCmyrg5oFESH++4QqLUdHLrKugDbCz+9V5983YfjpfJhmxqf5f2H+JV9JJhS0brPS9TA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.2": {"name": "micromatch", "version": "3.0.2", "dependencies": {"arr-diff": "^3.0.0", "array-unique": "^0.3.2", "braces": "^2.0.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "extglob": "^1.1.0", "fragment-cache": "^0.2.1", "kind-of": "^3.1.0", "nanomatch": "^1.1.1", "object.pick": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^0.2.0", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^0.1.12", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.3", "minimist": "^1.2.0", "mocha": "^3.4.2", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "bf77b8c860f342d73ac96bf4c495ed3cec05875c", "size": 18667, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.0.2.tgz", "integrity": "sha512-DJPmTvzNtZ7PExARW6ovsLRP6rxkv6IkdZLVhD8hpfduvMKNRjl2k7+CwqEvURC3R45BVex8TTPeA/YDVEoggg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.1": {"name": "micromatch", "version": "3.0.1", "dependencies": {"arr-diff": "^3.0.0", "array-unique": "^0.3.2", "braces": "^2.0.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "extglob": "^1.1.0", "fragment-cache": "^0.2.1", "kind-of": "^3.1.0", "nanomatch": "^1.1.1", "object.pick": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^0.2.0", "extend-shallow": "^2.0.1", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.3", "minimist": "^1.2.0", "mocha": "^3.4.2", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "2e1bd0ceda6cf78d04b14d281c922129e48ef323", "size": 18644, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.0.1.tgz", "integrity": "sha512-NzCifOxXgufa3ZUdTdOqvPlFsSoqn9CwSEbID5lM5HhvBKg+NozKqkSA39yuP7EE2qlRtNf4qji0Iw4WpVvvZQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.0": {"name": "micromatch", "version": "3.0.0", "dependencies": {"arr-diff": "^3.0.0", "array-unique": "^0.3.2", "braces": "^2.0.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "extglob": "^1.1.0", "fragment-cache": "^0.2.1", "kind-of": "^3.1.0", "nanomatch": "^1.1.1", "object.pick": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^0.2.0", "extend-shallow": "^2.0.1", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.3", "minimist": "^1.2.0", "mocha": "^3.4.2", "multimatch": "^2.1.0"}, "directories": {}, "dist": {"shasum": "c926f11cd75e887dc3b82968909575280731049d", "size": 18628, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-3.0.0.tgz", "integrity": "sha512-jXbEGkzAwEzFuofLH4JGjM+o1puUK0KlJcLYc5t7zXuutGC0+mRKVqKU264ZQzY6nf/lwTqX2JxOMc7D5lRIjw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.11": {"name": "micromatch", "version": "2.3.11", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-format-md": "^0.1.8", "gulp-istanbul": "^0.10.1", "gulp-mocha": "^2.1.3", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2", "multimatch": "^2.0.0", "should": "^8", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "86677c97d1720b363431d04d0d15293bd38c1565", "size": 14623, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha512-LnU2XFEk9xxSJ6rfgAry/ty5qwUTyHYOBU0g4R6tIw5ljwgGIBmiKhRWLw5NpMOnrgUNcDJ4WMp8rl3sYVHLNA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.10": {"name": "micromatch", "version": "2.3.10", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-format-md": "^0.1.8", "gulp-istanbul": "^0.10.1", "gulp-mocha": "^2.1.3", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2", "multimatch": "^2.0.0", "should": "^8", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "f4fb3175beec62795a7b8c24d5f745c3680660ab", "size": 9190, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.10.tgz", "integrity": "sha512-s4QloPs9pCkHMwjH3NMvZAXpCXEizLcR7QN1+fkoPmmAFx34ukor4ZdN9onkfQrOOye+Z44n2DSJ1vw4Vr7Atw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.9": {"name": "micromatch", "version": "2.3.9", "dependencies": {"arr-diff": "^3.0.0", "array-unique": "^0.2.1", "braces": "^1.8.4", "expand-brackets": "^0.1.5", "extglob": "^0.3.2", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.3", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.3"}, "devDependencies": {"benchmarked": "^0.2.5", "chalk": "^1.1.3", "gulp": "^3.9.1", "gulp-eslint": "^2.0.0", "gulp-format-md": "^0.1.9", "gulp-istanbul": "^0.10.4", "gulp-mocha": "^2.2.0", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "multimatch": "^2.1.0", "should": "^8.3.1", "write": "^0.3.1"}, "directories": {}, "dist": {"shasum": "26370d1989d8029f91034ab667f5f020ccd4f8fd", "size": 14727, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.9.tgz", "integrity": "sha512-QZU30YOYs0E+4Y/CC1QgqBqmeY3RmH2PYwPpyS5e43+I1xvSnJF/p6iBoBzrnLDVm1vjKLOHx8fnXU3T4u3OlQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.8": {"name": "micromatch", "version": "2.3.8", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-format-md": "^0.1.8", "gulp-istanbul": "^0.10.1", "gulp-mocha": "^2.1.3", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2", "multimatch": "^2.0.0", "should": "^8", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "94fbf8f37ed9edeca06bf1c8f7b743fb5f6f5854", "size": 14434, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.8.tgz", "integrity": "sha512-PQOHX68M7tTh3MiVCxD0ZqaFLlFCr2akgLvAaMCW1aqSi+eaCJbPsJvRNZ95aC3TsQUUziKVKPjMDXJbIInohA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.7": {"name": "micromatch", "version": "2.3.7", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-mocha": "^2.1.3", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "*", "multimatch": "^2.0.0", "should": "*", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "2f2e85ef46140dbea6cb55e739b6b11b30eaa509", "size": 14138, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.7.tgz", "integrity": "sha512-G0mcWeWiyuIQIrb1q+PW2IavR10QlVEYCKpw7R8FjkzhAOSwtj633GCAhQrx1uLQtfc1h3CQWazD8zv+Rf9C4A=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.6": {"name": "micromatch", "version": "2.3.6", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.0", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-mocha": "^2.1.3", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "*", "multimatch": "^2.0.0", "should": "*", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "67ced00422d36c9f8bb01d391fe69637b45aa866", "size": 14233, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.6.tgz", "integrity": "sha512-hee<PERSON><PERSON><PERSON>cib3225WcK6d822rW5wlbDkdhjkZbPWkGNeJQ/EvCSlkFBEdnDY5lGn3AW8TGcS0lkmOXOR/KVXcWcLw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.5": {"name": "micromatch", "version": "2.3.5", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.1", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^0.2.3", "normalize-path": "^2.0.0", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-istanbul": "^0.10.1", "gulp-jshint": "^1.11.2", "gulp-mocha": "^2.1.3", "jshint-stylish": "^2.0.1", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.3.3", "multimatch": "^2.0.0", "should": "^7.1.0", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "d8dfed89e28419d073489be55c33f0b05c273217", "size": 14226, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.5.tgz", "integrity": "sha512-Xm9hdaMOJrGA+hxR6IpiBxrMDkrzYXgaNJMz8Vst/wb1kv0SigQwFS4wWBvQ0+Hb0afwitHYNe23qTcO3PCz1w=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.4": {"name": "micromatch", "version": "2.3.4", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.1", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "normalize-path": "^2.0.0", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-istanbul": "^0.10.1", "gulp-jshint": "^1.11.2", "gulp-mocha": "^2.1.3", "jshint-stylish": "^2.0.1", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.3.3", "multimatch": "^2.0.0", "should": "^7.1.0", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "b17874eb365b371065fcd44c27d6e1585638ae4c", "size": 14233, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.4.tgz", "integrity": "sha512-TaQx3Nw4E5HTt//Q1YlwVBFfckcwTfDD9BLGpnx5jBIqkPe0DWa4P+KpVHuMdsJ53SfoK8gxjyiJDwASR1ycxw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "2.3.3": {"name": "micromatch", "version": "2.3.3", "dependencies": {"arr-diff": "^1.1.0", "array-unique": "^0.2.1", "braces": "^1.8.1", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "normalize-path": "^2.0.0", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-istanbul": "^0.10.1", "gulp-jshint": "^1.11.2", "gulp-mocha": "^2.1.3", "jshint-stylish": "^2.0.1", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.3.3", "multimatch": "^2.0.0", "should": "^7.1.0", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "f8357e265f5768f2ca799a3241d1995788d31648", "size": 14206, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.3.tgz", "integrity": "sha512-2ijwu2YhIOQ9FCmdZaLdtq8BvE4MTek7RuRu/wdg206/IpJKFmFxJU7TRyJ+vE9J0MauMUAShWUFykLlKl7pRg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.2": {"name": "micromatch", "version": "2.3.2", "dependencies": {"arr-diff": "^1.1.0", "array-unique": "^0.2.1", "braces": "^1.8.1", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "normalize-path": "^2.0.0", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-istanbul": "^0.10.1", "gulp-jshint": "^1.11.2", "gulp-mocha": "^2.1.3", "jshint-stylish": "^2.0.1", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.3.3", "multimatch": "^2.0.0", "should": "^7.1.0", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "fec97df21776b01ed72bdff7295f64072b6e2d42", "size": 14236, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.2.tgz", "integrity": "sha512-7lDNvYe0dzH7fsyYpLtjCV7Pz9aDrpAUjyFeCeBwLiaSWBnxbAfBLmc3pB/VyoqVEVja4PrAZdmr22P473ZiTw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.1": {"name": "micromatch", "version": "2.3.1", "dependencies": {"arr-diff": "^1.1.0", "array-unique": "^0.2.1", "braces": "^1.8.1", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "normalize-path": "^2.0.0", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "browserify": "^11.2.0", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-istanbul": "^0.10.1", "gulp-jshint": "^1.11.2", "gulp-mocha": "^2.1.3", "jshint-stylish": "^2.0.1", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.3.3", "multimatch": "^2.0.0", "should": "^7.1.0", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "f9cadd05bf513511288f0f22b89b84e0e93c1646", "size": 14217, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.1.tgz", "integrity": "sha512-hjBW51RnqXaFrjCSAe+8Ligdp21bDPTh4i+rlQ4qWXYP2eygnrQeKuCa4l8e2Fymmrm3eaCPH36jUCa3RkSYlA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.3.0": {"name": "micromatch", "version": "2.3.0", "dependencies": {"arr-diff": "^1.1.0", "array-unique": "^0.2.1", "braces": "^1.8.1", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "normalize-path": "^2.0.0", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "browserify": "^11.2.0", "chalk": "^1.1.1", "gulp": "^3.9.0", "gulp-istanbul": "^0.10.1", "gulp-jshint": "^1.11.2", "gulp-mocha": "^2.1.3", "jshint-stylish": "^2.0.1", "minimatch": "^3.0.0", "minimist": "^1.2.0", "mocha": "^2.3.3", "multimatch": "^2.0.0", "should": "^7.1.0", "write": "^0.2.1"}, "directories": {}, "dist": {"shasum": "951b2c4468c5d77885a6df558ea9a33823e7d238", "size": 14211, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.3.0.tgz", "integrity": "sha512-imzLEnZs3omGptcUytU1GCt7XsA/WZwDg/TIX4ApSCl+JwqFFJedlHRK2yLHj9uPo1vlL8Nw2DR8zFcgUoR8+g=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.2.0": {"name": "micromatch", "version": "2.2.0", "dependencies": {"arr-diff": "^1.0.1", "array-unique": "^0.2.1", "braces": "^1.8.0", "expand-brackets": "^0.1.1", "extglob": "^0.3.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "object.omit": "^1.1.0", "parse-glob": "^3.0.1", "regex-cache": "^0.4.2"}, "devDependencies": {"benchmarked": "^0.1.4", "browserify": "^9.0.8", "chalk": "^1.0.0", "minimatch": "^2.0.4", "minimist": "^1.1.1", "mocha": "^2.2.4", "multimatch": "^2.0.0", "should": "^6.0.1", "write": "^0.2.0"}, "directories": {}, "dist": {"shasum": "e7281bf971100827b890e375d994f12034898ff5", "size": 13802, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.2.0.tgz", "integrity": "sha512-C+bwIqRp177ofuqmibBPxudfy9J4IHY+I/U60ocDbQOoY1H1nj9nL8uN+D9M402/qnQ4LZgv9BLOIWDVxxwaHA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.6": {"name": "micromatch", "version": "2.1.6", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.8.0", "debug": "^2.1.3", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.4.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "51a65a9dcbfb92113292a071e04da35a81e9050e", "size": 12942, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.6.tgz", "integrity": "sha512-b6Pr6gJmAZciSjQjFSLXQs9lEs3iY2D6S/uS14gYW9lWYVmCybBPx5XbvTF10BXH8B+jXgWfI5jzsvFxqC8P7w=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "2.1.5": {"name": "micromatch", "version": "2.1.5", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.8.0", "debug": "^2.1.3", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "e356977873e69f94de02439355978f4a26e8849b", "size": 12953, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.5.tgz", "integrity": "sha512-FYjzTY02TJXwvxfzEdELtZC+G/wz/KbHUZVnCorE6DNbbNZsxLGZkTXhjmbz5I2ROJtCIdAgiJKVZrB/Q3grMw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "2.1.4": {"name": "micromatch", "version": "2.1.4", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.8.0", "debug": "^2.1.3", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "4e23f89ae0fa4fd72eac59261fbf7bb41970cdc8", "size": 12965, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.4.tgz", "integrity": "sha512-Td1DYyBCj7ugwWF+IZRtiwImrkNFUEZ4w1WHKEMknKWQJlzxmL4Zya5wtiMFHgbUUEeb81m0QG/Mj38fUbObTA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.3": {"name": "micromatch", "version": "2.1.3", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.8.0", "debug": "^2.1.3", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "8439bc163e8e12949b67b7e3382675e6c4448892", "size": 12947, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.3.tgz", "integrity": "sha512-gdLZ5V+NVHKDZ764rFMfqw8x1qZnCWN4y1hgu8eDsW/5L3OzKYKgec7ABVlpGBNmDteleUmgFbYKzxSshThZvg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.2": {"name": "micromatch", "version": "2.1.2", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.8.0", "debug": "^2.1.3", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "d86316f5b713cce7ac07474fb55971918fb4f48c", "size": 12904, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.2.tgz", "integrity": "sha512-Q0JVZ8YUWBFZGTZ96v50RSMvrsh0al8tLQFXg5jW0JmwrWj6t2XdFCu7hcaLN6Bp5XJtI6KawmmkPSgr2GhLsg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.1": {"name": "micromatch", "version": "2.1.1", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "debug": "^2.1.2", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "545300a2564bfbd579fc499c95f99d8a7aed19d9", "size": 12725, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.1.tgz", "integrity": "sha512-hL6jjzR+N4/ODDcUPdGV8+I/kPw7zfoGQvOgukHc7i3K1LuAp2Cn4v+Ec8WLC0GC1xO1iFSZLqRr0cq63tfbIA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.0": {"name": "micromatch", "version": "2.1.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "debug": "^2.1.2", "expand-brackets": "^0.1.1", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^3.0.0", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "48e749678e84b51616045c63c8c1dd1f1773495a", "size": 12737, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.1.0.tgz", "integrity": "sha512-zKMrPQVCAx6xEiewqWy5qbv+cMlYC1RgUfTvq9E5oUdtnhdfVEabV/5dYXuCoLeHFUDQvLJ1trKV/NY90UZyrw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0": {"name": "micromatch", "version": "2.0.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "debug": "^2.1.2", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.1.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "fa102f0e510e0b7861987cdb53e22526448da1a4", "size": 12686, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-2.0.0.tgz", "integrity": "sha512-CU9ZaGKPEkL7hUtzaBYporDZkYCOjviXVJ6gwByyRpnSNgcZdMh2HwRlG6XKPt1tXRM6ErXD272xvMn1Et5OGw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.6.2": {"name": "micromatch", "version": "1.6.2", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "debug": "^2.1.2", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.1.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "38fab47076aacece6ead77ef38472392f7b4bfb9", "size": 12447, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.6.2.tgz", "integrity": "sha512-aq9dbJx8tG1narL9gpZKV5QoVobLgwNreir/X7tqZpS+b+7RjAYUqLYk+8DP5iZ8bw7iHmpNiuQ87dbU4WpeuQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.6.1": {"name": "micromatch", "version": "1.6.1", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "debug": "^2.1.2", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.0.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "be9c756f85a6c04f2839625936f37eebd1aa3231", "size": 12444, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.6.1.tgz", "integrity": "sha512-6xfdEs0lyvcXVjOa6egEb+LyIRux0OuiimDKnMtzYLZuD6FiYDorTGQgg30XrsMVGm3ZIYMmLkFY+2zgg+kd1w=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.6.0": {"name": "micromatch", "version": "1.6.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "debug": "^2.1.2", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.0.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "browserify": "^9.0.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "0e5c87d8ea63a02da6d5706c9c4cc3c753129bfc", "size": 11925, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.6.0.tgz", "integrity": "sha512-RvJfr0+4VSJFc2LhJu+VOaSjQwvMbDoYFssaJspunSOHHjnn7C57JPNY7SBd8wUBLiAhBKjYiZ/cyw81se9QGg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.5.0": {"name": "micromatch", "version": "1.5.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.0.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^1.0.0", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "50bd67d41604ad1b749249c055abadee914a5ebb", "size": 11685, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.5.0.tgz", "integrity": "sha512-FHsVECj5JHEYp/XTU8bJGxJ30YUpBMY1hMMTLxQMCriDFjvox5O3S6Ze/m6rM71D9J24hL3iK3NHsw9cFoIi6A=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.5": {"name": "micromatch", "version": "1.4.5", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.0.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "eced3fa4cc87a8a8f32c142e238c17670600769f", "size": 11125, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.4.5.tgz", "integrity": "sha512-BgoJz5iwA+8cw3XUZL2eIFuZcpcPUcZDCfGwhyOCoUnem5yhAq3hDNCfU9h0ma8spNZxBbuCM6JvKd7z56OVtA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.4": {"name": "micromatch", "version": "1.4.4", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.0.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "24b2fe56e81ff7701e6290f39a1e88d143402b1f", "size": 11019, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.4.4.tgz", "integrity": "sha512-C6mVKf8OPUIqdVGlShQ+5d8jjicC72Qe2maAg7Treiwr6uEwDsPsAF/gG4hmv0GWo6te0g3M0ui9z6ld1s5SbA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.3": {"name": "micromatch", "version": "1.4.3", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "object.omit": "^0.2.1", "parse-glob": "^2.0.1", "regex-cache": "^0.3.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^5.0.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "853731f751b2ca52e04fdf8ee429e997c1d488f5", "size": 11034, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.4.3.tgz", "integrity": "sha512-QrM7/3azDMr6wx++iAlfJOCIXdm1Esk4dpkyRCR1/ApMgu3wO5XjCfjCJmIc8btt7HFjaATKAqAeKZ3BIS1Wiw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.2": {"name": "micromatch", "version": "1.4.2", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "1b1d1b9ef357c01a33239b170894709b037fc085", "size": 10334, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.4.2.tgz", "integrity": "sha512-VNu46T/+GcM841nBmivGaH/j7h/U4IYvlZXgBTD2SjQtj5D59MhjPDStWfggqeflZZqqSCaw1BcyA8ZzDkMysQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.1": {"name": "micromatch", "version": "1.4.1", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "cc7a81a85441657fdc36de1db9235724e55a6124", "size": 10236, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.4.1.tgz", "integrity": "sha512-zzEWNHeBUbNWPhCFz5r7VZ4npFiTEPVgREtAT/KhRHnVvK14dCs9n+tYZE+o3jeG48Ld6apXkny5w0DvckUzWQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.0": {"name": "micromatch", "version": "1.4.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "13f9f50b37d31a7138f5c1cdc1e72769083505c5", "size": 10229, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.4.0.tgz", "integrity": "sha512-ibVVM8/ZXS3HN6rQPF7dgsnn25E9aVF+1kNNqJ9JqQuOksYicAXznF/MOu2gxSa2BvKLpW119+8ileZpqEpdjQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.3.3": {"name": "micromatch", "version": "1.3.3", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "^2.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "86985e4d3dc01d71cab4ec63a3e317c2d129d268", "size": 10427, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.3.3.tgz", "integrity": "sha512-k0wScZxxJwPc5on6eBJn0Z3m1LfbSEYpZVWnisV8je1xUlzQm7Og2RFfSDM0FzKbIGU+38IW9rW5QKCZK5mJNA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.3.2": {"name": "micromatch", "version": "1.3.2", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.7.0", "expand-brackets": "^0.1.1", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "5706c4cb24c7040fc3f57a2b464f76f112a5b6b9", "size": 10419, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.3.2.tgz", "integrity": "sha512-317eXxI7Ql8ucklUPqigrllSwFOYfQHKWrFMZMhPYj69JfxUaKyIeEFbjX5l8oMxg3J7CXrzTM/emt8FP2HLeA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.3.1": {"name": "micromatch", "version": "1.3.1", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.6.0", "expand-brackets": "^0.1.0", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "f96117bc7c34062da5cebd721af5c40b402052cd", "size": 10003, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.3.1.tgz", "integrity": "sha512-z5/VwgKwSZNcjsVqtaqgGamNYJzxOM1EKHICHkWUjhBe+P3Z+/EUf0Ak0KnbCBCTt0U4CCZksyurrspGaXxgsA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.3.0": {"name": "micromatch", "version": "1.3.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.6.0", "expand-brackets": "^0.1.0", "extglob": "^0.2.0", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "62099439ad54fcad188dc013e184eb7261c68f55", "size": 9922, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.3.0.tgz", "integrity": "sha512-b1TBX7YDexNCqZwtxrRy5yTXbBxA8O1GEv1i4d218OdIdIjOBr7z+xQzkn27Qaxm5FbkB8tN4t5mxtO0RZzOVQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.2.2": {"name": "micromatch", "version": "1.2.2", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.6.0", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0", "is-glob": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "d38c0ea77731a1714c46d273469cd31322ea02ef", "size": 8566, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.2.2.tgz", "integrity": "sha512-ZLecoVApC2mZVyf++chmRd0c9j7Csj/xMQv7gX7PRdC5CWkmn8aRRvilazTp3ZEJ+Ma8xpa9p6jbDx3AFYrRXg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.2.0": {"name": "micromatch", "version": "1.2.0", "dependencies": {"arr-diff": "^1.0.1", "braces": "^1.4.0", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0", "is-glob": "^1.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "helper-reflinks": "^0.4.0", "minimatch": "^2.0.1", "minimist": "^1.1.0", "multimatch": "^2.0.0", "should": "^4.6.1", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "771a95449e4c2573f4c62df684c9a3fe430ad226", "size": 8193, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.2.0.tgz", "integrity": "sha512-jg3y77ZoUZtsAs/OPzIRoSx75piKdZymF2L8sz0uSUJG18w5LsmaQNgV9ZdGsJunyKndAFa/fgJFFazKN3yxUA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.1": {"name": "micromatch", "version": "1.0.1", "dependencies": {"arr-diff": "^1.0.1", "arr-union": "^1.0.0", "braces": "^1.0.0", "filename-regex": "^0.1.0", "unixify": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "d22539682fdd903b70df220018d8ac0defeb8434", "size": 5799, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.0.1.tgz", "integrity": "sha512-juRHcNORljLfREsoh0AW9oTq8ViIssD4VQzhwiPJEMWyOHLXC9y5CEIUQ72TIjgQQPrPWnXIoL17Or4Z9KeKOg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0": {"name": "micromatch", "version": "1.0.0", "dependencies": {"arr-diff": "^1.0.1", "arr-union": "^1.0.0", "braces": "^1.0.0", "filename-regex": "^0.1.0", "unixify": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "minimatch": "^2.0.1", "minimist": "^1.1.0", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "57ddb18dc0edfb6ee882a77581afb61970906b85", "size": 5509, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-1.0.0.tgz", "integrity": "sha512-5XdaJhe3HhSWkvvHy729IhIaLqccppEckCBAXXmOOzeimGekqLOXCisJpXh6Yek+QdvVXgOtdqK0BRPUI5wUdA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.2": {"name": "micromatch", "version": "0.2.2", "dependencies": {"arr-diff": "^0.2.2", "arr-union": "^1.0.0", "braces": "^1.0.0", "unixify": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "minimatch": "^2.0.1", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "228dc16354377e1b2dc2e4114b7fc53219b8ae57", "size": 4319, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-0.2.2.tgz", "integrity": "sha512-E7LJruEuTXuqjc8g1trRpxh1Ak9DQOj3CedFRFhpNi6KRwYPC5xDJg/dudWkkTsnNYdmonoC5d1X64hGsjP/sg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.1": {"name": "micromatch", "version": "0.2.1", "dependencies": {"arr-diff": "^0.2.2", "arr-union": "^1.0.0", "braces": "^0.1.5", "unixify": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "minimatch": "^2.0.1", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "2a8684d31401b7d4d4fd70890eda6ab53b3f8260", "size": 4254, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-0.2.1.tgz", "integrity": "sha512-zsOlEJzty2Qa2FwqDmLb1n9bTx22omw617rWbejid4ikoFQy+aMhYuty11CBvUvspABurrCsL2ql2xZCeTrOGg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.0": {"name": "micromatch", "version": "0.2.0", "dependencies": {"arr-diff": "^0.2.2", "arr-union": "^1.0.0", "braces": "^0.1.5", "unixify": "^0.1.0"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "minimatch": "^2.0.1", "mocha": "*", "should": "*", "write": "^0.1.1"}, "directories": {}, "dist": {"shasum": "e0b82107687dfd6e4ff8f085c62adf8a61a24ae3", "size": 4142, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-0.2.0.tgz", "integrity": "sha512-vM+tW7aGPXW3tipt1SKW9rUnkAbcXVn8k+qlM2LFD4V3n9RdkdQoslfKwaXWfqaFRQ8NmVH5PUrW5czOewn/WQ=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.0": {"name": "micromatch", "version": "0.1.0", "devDependencies": {"mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "f58fc1198dc1ba5b97cc2e301d28f0f3ee05ca2f", "size": 2135, "noattachment": false, "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-0.1.0.tgz", "integrity": "sha512-GQRzYCrGSoSGGU3QgckEij+psNPgh2N/7EGbW7q92WE9jMmRjAsrEH4pcioy62cNFsOJP2jxURvfQ2O6jQbEMA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.5": {"name": "micromatch", "version": "4.0.5", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^5.0.1", "mocha": "^9.2.2", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "shasum": "bc8999a7cbbf77cdc89f132f6e467051b49090c6", "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "fileCount": 4, "unpackedSize": 55947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPMcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1vBAAloppMNdWgbswe+RXiqrcsSpra8OFTxbwOOd8l0+tydhwJ8yS\r\n9tANTGLGX8w14fAdSV8IebVjjq1QNWQLlH0ofpRb4TOAYBclFkx4WMPp8s9T\r\n+vUC4fZyJsAd3QzfuWGMcUya9h8AX/S4Np3wwGnkQfg0eqpjxtkUNNyjJ0wi\r\nZeogQsi4SajN4SaJMs6Uef4PqJiyhLYyZVFMMPfXKtFud2q6hlp6LW+/v3k4\r\nV/T31dRooeUuCwADQSynf0Y05Zf9I6aEBk0FuDty6HtWf9pYZrFKGCK9JVBT\r\namijpFImSgxjL1zkytZ+p8d/+L0+IImCBVyhqH7MBEgO7IkVrjo6nMiJU/+A\r\nnh9AeYFZ0Hv0hAUZEtG+f8bKZ7egl3odWFOB9zH6+hfuJe2TwPIGGnQZAUK0\r\n+7n5i5SKsjwbAkpeIgm/1bgqW6QBPHnfGgTF9PyUgn6Hko8/688z2sfmiwbb\r\n7sZckXtWTo/vZ2K/364k19Vr4FI+HIp4BndAk2TA/zxfij99Pfu/I0c8fthV\r\nJq5AEgYu9HFWz7nFHRMHLQF0Y5cQwtMrWOy990USCEYyaQNjkJIL6oVZvNHS\r\nx+WUEmpVBB9BjtxH/ADQ8SAspnEFIs4/8mEOrsp49rF4joloFAMNXfKeSKWv\r\n8GCopBneL1eOO6NG/cXZ/M41/o9lUxbVBrQ=\r\n=lDr1\r\n-----END PGP SIGNATURE-----\r\n", "size": 14433}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.6": {"name": "micromatch", "version": "4.0.6", "dependencies": {"braces": "^3.0.3", "picomatch": "^4.0.2"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^9.0.3", "mocha": "^10.4.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-Y4Ypn3oujJYxJcMacVgcs92wofTHxp9FzfDpQON4msDefoC0lb3ETvQLOdLcbhSwU1bz8HrL/1sygfBIHudrkQ==", "shasum": "ab4e37c42726b9cd788181ba4a2a4fead8e394a3", "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.6.tgz", "fileCount": 4, "unpackedSize": 57044, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbYyO7EgwmokmY6Vs+PiniiJAHS7rpu7RijTE8hhOBXgIgSp95sJVjqMVu8e9W3JwuS4Q2uvqAIqeLKQZrG2OUQlE="}], "size": 14797}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "publish_time": 1716282939179, "_source_registry_name": "default"}, "4.0.7": {"name": "micromatch", "version": "4.0.7", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^5.0.1", "mocha": "^9.2.2", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==", "shasum": "33e8190d9fe474a9895525f5618eee136d46c2e5", "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.7.tgz", "fileCount": 4, "unpackedSize": 56265, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoqmLDH3dRKVCJvEJ3ReCeqMKCKVg7rGGyiumTxoU3SQIgEDFoR6C5ze70X6ZhoFpQsadAVMROyLvvy0GYhBM2QNc="}], "size": 14635}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "publish_time": 1716352429109, "_source_registry_name": "default"}, "4.0.8": {"name": "micromatch", "version": "4.0.8", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^5.0.1", "mocha": "^9.2.2", "time-require": "github:jonschlink<PERSON>/time-require"}, "directories": {}, "dist": {"integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "shasum": "d66fa18f3a47076789320b9b1af32bd86d9fa202", "tarball": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz", "fileCount": 4, "unpackedSize": 56599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbM5vTauiWZIyEboHJF/YXuneSxEyEJ+2VHaEdyLD4zQIgU0seoGyOpfB4e80kPjxYidBvIgonDpZpZzNN8w25BUU="}], "size": 14663}, "engines": {"node": ">=8.6"}, "_hasShrinkwrap": false, "publish_time": 1724430678748, "_source_registry_name": "default"}}, "_source_registry_name": "default"}