<template>
  <div class="comment-management">
    <!-- 添加页面头部 -->
    <div class="page-header">
      <h2>评论管理</h2>
      <div class="header-actions">
        <el-button @click="router.push('/')" type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <el-card class="comment-table">
      <!-- 评论列表和举报信息的标签页 -->
      <el-tabs v-model="activeTab">
        <el-tab-pane label="评论列表" name="comments">
          <!-- 搜索栏 -->
          <el-form :inline="true" class="search-form">
            <div class="search-form-item-group">
              <!-- 关键词搜索 -->
              <el-form-item label="关键词">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="评论内容"
                  clearable
                  :prefix-icon="Search"
                  style="width: 200px"
                />
              </el-form-item>
              
              <!-- 赛车名称 -->
              <el-form-item label="赛车名称">
                <el-input
                  v-model="searchForm.car_name"
                  placeholder="请输入赛车名称"
                  clearable
                  style="width: 180px"
                />
              </el-form-item>
              
              <!-- 赛车等级 -->
              <el-form-item label="赛车等级">
                <el-select 
                  v-model="searchForm.car_level" 
                  placeholder="全部等级"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="level in carLevels"
                    :key="level.value"
                    :label="level.label"
                    :value="level.value"
                  />
                </el-select>
              </el-form-item>
              
              <!--赛车编号--> 
              <el-form-item label="赛车编号">
                <el-input
                  v-model="searchForm.car_id"
                  placeholder="请输入赛车编号"
                  clearable
                  style="width: 180px"
                />
              </el-form-item>
            </div>
            
            <div class="search-form-item-group">
              <!-- 用户ID
              <el-form-item label="用户ID">
                <el-input
                  v-model="searchForm.openid"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 180px"
                />
              </el-form-item> -->
              
              <!-- 评论状态 -->
              <el-form-item label="状态">
                <el-select 
                  v-model="searchForm.is_deleted" 
                  placeholder="全部" 
                  clearable
                  style="width: 120px"
                >
                  <el-option label="正常" :value="false" />
                  <el-option label="已删除" :value="true" />
                </el-select>
              </el-form-item>
              
              <!-- 时间范围 -->
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  style="width: 320px"
                  :teleported="false"
                  format="YYYY年MM月DD日"
                  :popper-class="'custom-date-picker'"
                />
              </el-form-item>
            </div>
            
            <!-- 操作按钮 -->
            <div class="search-form-buttons">
              <el-form-item>
                <el-button type="primary" @click="handleSearch" :icon="Search">
                  搜索
                </el-button>
                <el-button @click="resetSearch" :icon="RefreshRight">
                  重置
                </el-button>
              </el-form-item>
            </div>
          </el-form>

          <!-- 评论列表表格 -->
          <el-table
            v-loading="loading"
            :data="comments"
            border
            style="width: 100%"
          >
            <el-table-column prop="car_name" label="赛车名称" width="200">
              <template #default="scope">
                <div class="car-name-cell">
                  <el-icon><Van /></el-icon>
                  <span>{{ scope.row.car_name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="car_level" label="等级" width="100" align="center">
              <template #default="{ row }">
                <el-tag 
                  size="small" 
                  effect="plain"
                  :style="{ 
                    backgroundColor: getCarLevelColor(row.car_level),
                    borderColor: getCarLevelColor(row.car_level),
                    color: '#fff'
                  }"
                >
                  {{ row.car_level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="评论内容" show-overflow-tooltip />
            <el-table-column prop="openid" label="用户ID" width="120">
              <template #default="scope">
                <div class="username-cell">
                  <el-icon><User /></el-icon>
                  <span>{{ scope.row.openid }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="发布时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="is_deleted" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.is_deleted ? 'danger' : 'success'" size="small">
                  {{ row.is_deleted ? '已删除' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template #default="{ row }">
                <el-button
                  v-if="!row.is_deleted"
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="10"
              :total="total"
              :pager-count="7"
              @current-change="handleCurrentChange"
              layout="prev, pager, next"
              background
            />
          </div>
        </el-tab-pane>

        <!-- 举报管理标签页 -->
        <el-tab-pane label="举报管理" name="reports">
          <!-- 添加搜索表单 -->
          <el-form :inline="true" class="search-form">
            <div class="search-form-item-group">
              <!-- 关键词搜索 -->
              <el-form-item label="关键词">
                <el-input
                  v-model="reportSearchForm.keyword"
                  placeholder="评论内容"
                  clearable
                  :prefix-icon="Search"
                  style="width: 200px"
                />
              </el-form-item>
              
              <!-- 赛车名称 -->
              <el-form-item label="赛车名称">
                <el-input
                  v-model="reportSearchForm.car_name"
                  placeholder="请输入赛车名称"
                  clearable
                  style="width: 180px"
                />
              </el-form-item>
              
              <!-- 赛车编号 -->
              <el-form-item label="赛车编号">
                <el-input
                  v-model="reportSearchForm.car_id"
                  placeholder="请输入赛车编号"
                  clearable
                  style="width: 180px"
                />
              </el-form-item>
            </div>
            
            <div class="search-form-item-group">
              <!-- 举报状态 -->
              <el-form-item label="状态">
                <el-select 
                  v-model="reportSearchForm.status"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  placeholder="全部状态"
                  clearable
                  style="width: 220px"
                >
                  <el-option label="待处理" value="pending" />
                  <el-option label="处理中" value="processing" />
                  <el-option label="已处理" value="resolved" />
                  <el-option label="已驳回" value="rejected" />
                </el-select>
              </el-form-item>
              
              <!-- 举报人ID -->
              <el-form-item label="举报人">
                <el-input
                  v-model="reportSearchForm.reporter_openid"
                  placeholder="请输入举报人ID"
                  clearable
                  style="width: 180px"
                />
              </el-form-item>
              
              <!-- 时间范围 -->
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="reportDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  style="width: 320px"
                  :teleported="false"
                  format="YYYY年MM月DD日"
                  :popper-class="'custom-date-picker'"
                />
              </el-form-item>
            </div>
            
            <!-- 操作按钮 -->
            <div class="search-form-buttons">
              <el-form-item>
                <el-button type="primary" @click="handleReportSearch" :icon="Search">
                  搜索
                </el-button>
                <el-button @click="resetReportSearch" :icon="RefreshRight">
                  重置
                </el-button>
              </el-form-item>
            </div>
          </el-form>

          <!-- 举报列表表格 -->
          <el-table
            v-loading="loading"
            :data="reports"
            border
            style="width: 100%"
          >
            <el-table-column prop="car_name" label="赛车名称" width="200">
              <template #default="scope">
                <div class="car-name-cell">
                  <el-icon><Van /></el-icon>
                  <span>{{ scope.row.car_name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="car_level" label="等级" width="100" align="center">
              <template #default="{ row }">
                <el-tag 
                  size="small" 
                  effect="plain"
                  :style="{ 
                    backgroundColor: getCarLevelColor(row.car_level),
                    borderColor: getCarLevelColor(row.car_level),
                    color: '#fff'
                  }"
                >
                  {{ row.car_level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="comment_content" label="评论内容" show-overflow-tooltip />
            <el-table-column prop="reporter_openid" label="举报人" width="120">
              <template #default="scope">
                <div class="username-cell">
                  <el-icon><User /></el-icon>
                  <span>{{ scope.row.reporter_openid }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="举报原因" width="180" show-overflow-tooltip />
            <el-table-column prop="created_at" label="举报时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getReportStatusType(row.status)" size="small">
                  {{ getReportStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center">
              <template #default="{ row }">
                <el-button
                  v-if="row.status === 'pending'"
                  type="primary"
                  size="small"
                  @click="handleReport(row, 'resolve')"
                >
                  处理
                </el-button>
                <el-button
                  v-if="row.status === 'pending'"
                  type="info"
                  size="small"
                  @click="handleReport(row, 'reject')"
                >
                  驳回
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 举报列表分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="reportCurrentPage"
              :page-size="10"
              :total="reportTotal"
              :pager-count="7"
              @current-change="handleReportCurrentChange"
              layout="prev, pager, next"
              background
            />
          </div>
        </el-tab-pane>

        <!-- 敏感词库标签页 -->
        <el-tab-pane label="敏感词库" name="sensitive">
          <div class="sensitive-words">
            <el-form :inline="true" class="search-form">
              <div class="search-form-item-group">
                <el-form-item label="关键词">
                  <el-input
                    v-model="sensitiveSearchForm.keyword"
                    placeholder="请输入敏感词"
                    clearable
                    :prefix-icon="Search"
                    style="width: 220px"
                  />
                </el-form-item>
                <el-form-item label="来源">
                  <el-select 
                    v-model="sensitiveSearchForm.source" 
                    placeholder="全部来源"
                    clearable
                    style="width: 140px"
                  >
                    <el-option label="手动添加" value="manual" />
                    <el-option label="腾讯云" value="tencent" />
                  </el-select>
                </el-form-item>
                <el-form-item label="状态">
                  <el-select 
                    v-model="sensitiveSearchForm.is_active" 
                    placeholder="全部状态"
                    clearable
                    style="width: 140px"
                  >
                    <el-option label="启用" :value="true" />
                    <el-option label="禁用" :value="false" />
                  </el-select>
                </el-form-item>
              </div>
              
              <!-- 操作按钮 -->
              <div class="search-form-buttons">
                <el-form-item>
                  <el-button type="primary" @click="handleSensitiveSearch" :icon="Search">
                    搜索
                  </el-button>
                  <el-button @click="resetSensitiveSearch" :icon="RefreshRight">
                    重置
                  </el-button>
                </el-form-item>
              </div>
            </el-form>

            <!-- 添加按钮组 -->
            <div class="sensitive-actions">
              <el-button type="primary" @click="showAddDialog">
                <el-icon><Plus /></el-icon>添加敏感词
              </el-button>
              <el-button type="success" @click="handleUploadExcel">
                <el-icon><Upload /></el-icon>导入Excel
              </el-button>
              <el-button type="warning" @click="handleExport">
                <el-icon><Download /></el-icon>导出Excel
              </el-button>
            </div>

            <!-- 敏感词列表 -->
            <el-table
              v-loading="sensitiveLoading"
              :data="sensitiveWords"
              border
              style="width: 100%"
            >
              <el-table-column prop="word" label="敏感词" min-width="150" />
              <el-table-column prop="source" label="来源" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.source === 'manual' ? 'primary' : 'warning'" size="small">
                    {{ row.source === 'manual' ? '手动添加' : '腾讯云' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="is_active" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.is_active"
                    :loading="row.switching"
                    @change="handleStatusChange(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" show-overflow-tooltip />
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="{ row }">
                  <el-button
                    type="danger"
                    link
                    @click="handleDeleteSensitive(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="sensitiveCurrentPage"
                :page-size="10"
                :total="sensitiveTotal"
                :pager-count="7"
                @current-change="handleSensitiveCurrentChange"
                layout="prev, pager, next"
                background
              />
            </div>
          </div>

          <!-- 添加敏感词对话框 -->
          <el-dialog
            v-model="addDialogVisible"
            title="添加敏感词"
            width="500px"
          >
            <el-form :model="addForm" label-width="80px">
              <el-form-item label="敏感词" required>
                <el-input v-model="addForm.word" placeholder="请输入敏感词" />
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  v-model="addForm.remark"
                  type="textarea"
                  rows="3"
                  placeholder="请输入备注说明"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="addDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitAdd" :loading="submitting">
                  确定
                </el-button>
              </span>
            </template>
          </el-dialog>

          <!-- 添加文件上传输入框（隐藏） -->
          <input
            type="file"
            ref="fileInput"
            accept=".xlsx,.xls"
            style="display: none"
            @change="handleFileChange"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 处理举报对话框 -->
    <el-dialog
      v-model="reportDialogVisible"
      :title="reportAction === 'resolve' ? '处理举报' : '驳回举报'"
      width="500px"
    >
      <el-form :model="reportForm" label-width="80px">
        <el-form-item label="处理备注">
          <el-input
            v-model="reportForm.note"
            type="textarea"
            rows="3"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReportHandle">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshRight, Back, User, Van, Plus, Upload, Download } from '@element-plus/icons-vue'
import { 
  getComments, 
  deleteComment, 
  getReports, 
  handleReportAction,
  getCarLevels,
  getSensitiveWords,
  addSensitiveWord,
  batchAddSensitiveWords,
  deleteSensitiveWord,
  enableSensitiveWord,
  getSensitiveExportUrl,
  exportSensitiveWords
} from '@/api/comments'

const router = useRouter()
const loading = ref(false)

// 评论列表相关数据
const comments = ref([])
const currentPage = ref(1)
const total = ref(0)

// 举报列表相关数据
const reports = ref([])
const reportCurrentPage = ref(1)
const reportTotal = ref(0)

// 当前激活的标签页
const activeTab = ref('comments')

// 搜索表单数据
const searchForm = ref({
  keyword: '',
  car_name: '',
  car_id: '',
  car_level: '',
  openid: '',
  is_deleted: null,
  start_time: '',
  end_time: ''
})

// 日期范围选择器
const dateRange = ref([])

// 添加赛车等级列表数据
const carLevels = ref([])

// 获取赛车等级列表
const fetchCarLevels = async () => {
  try {
    const { success, levels } = await getCarLevels()
    if (success) {
      carLevels.value = levels
    }
  } catch (error) {
    console.error('获取赛车等级列表失败:', error)
  }
}

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal) {
    searchForm.value.start_time = newVal[0]
    searchForm.value.end_time = newVal[1]
  } else {
    searchForm.value.start_time = ''
    searchForm.value.end_time = ''
  }
})

// 举报处理相关数据
const reportDialogVisible = ref(false)
const reportAction = ref('')
const reportForm = ref({
  note: ''
})
const currentReportId = ref(null)

// 举报搜索表单数据
const reportSearchForm = ref({
  keyword: '',
  car_name: '',
  car_id: '',
  reporter_openid: '',
  status: [],  // 使用数组存储多选的状态
  start_time: '',
  end_time: ''
})

// 举报时间范围
const reportDateRange = ref([])

// 监听举报时间范围变化
watch(reportDateRange, (newVal) => {
  reportSearchForm.value.start_time = newVal?.[0] || ''
  reportSearchForm.value.end_time = newVal?.[1] || ''
})

// 获取评论列表
const fetchComments = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: 10,  // 直接使用固定值，不再使用 pageSize
      ...searchForm.value
    }
    const { data } = await getComments(params)
    if (data && data.list) {  // 添加数据检查
      comments.value = data.list
      total.value = data.total
    } else {
      comments.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取评论列表失败:', error)
    ElMessage.error('获取评论列表失败')
    comments.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取举报列表
const fetchReports = async () => {
  try {
    const params = {
      page: reportCurrentPage.value,
      page_size: 10,
      ...reportSearchForm.value,
      status: reportSearchForm.value.status.join(',')  // 将状态数组转换为逗号分隔的字符串
    }
    const { data } = await getReports(params)
    if (data && data.list) {
      reports.value = data.list
      reportTotal.value = data.total
    } else {
      reports.value = []
      reportTotal.value = 0
    }
  } catch (error) {
    console.error('获取举报列表失败:', error)
    ElMessage.error('获取举报列表失败')
    reports.value = []
    reportTotal.value = 0
  }
}

// 处理评论删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      type: 'warning'
    })
    await deleteComment(row.id)
    ElMessage.success('删除成功')
    await fetchComments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理举报
const handleReport = (row, action) => {
  reportAction.value = action
  currentReportId.value = row.id
  reportForm.value.note = ''
  reportDialogVisible.value = true
}

// 提交举报处理
const submitReportHandle = async () => {
  try {
    await handleReportAction(currentReportId.value, {
      action: reportAction.value,
      note: reportForm.value.note
    })
    ElMessage.success('处理成功')
    reportDialogVisible.value = false
    await fetchReports()
  } catch (error) {
    ElMessage.error('处理失败')
  }
}

// 搜索和重置
const handleSearch = () => {
  currentPage.value = 1
  fetchComments()
}

const resetSearch = () => {
  searchForm.value = {
    keyword: '',
    car_name: '',
    car_id: '',
    car_level: '',
    openid: '',
    is_deleted: null,
    start_time: '',
    end_time: ''
  }
  dateRange.value = []
  handleSearch()
}

// 举报搜索
const handleReportSearch = () => {
  reportCurrentPage.value = 1
  fetchReports()
}

// 重置举报搜索
const resetReportSearch = () => {
  reportSearchForm.value = {
    keyword: '',
    car_name: '',
    car_id: '',
    reporter_openid: '',
    status: [],
    start_time: '',
    end_time: ''
  }
  reportDateRange.value = []
  handleReportSearch()
}

// 添加日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  }
]

// 修改日期格式化方法
const formatDateTime = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取举报状态样式
const getReportStatusType = (status) => {
  const types = {
    pending: 'warning',
    resolved: 'success',
    rejected: 'info'
  }
  return types[status] || 'info'
}

// 获取举报状态文本
const getReportStatusText = (status) => {
  const texts = {
    pending: '待处理',
    resolved: '已处理',
    rejected: '已驳回'
  }
  return texts[status] || '未知'
}

// 分页和搜索相关方法
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchComments()
}

const handleReportCurrentChange = (val) => {
  reportCurrentPage.value = val
  fetchReports()
}

// 添加等级颜色映射函数
const getCarLevelColor = (level) => {
  const colors = {
    'S': 'rgba(147, 112, 219, 0.9)',  // 紫色
    'A': 'rgba(255, 165, 0, 0.9)',    // 橙色
    'B': 'rgba(102, 187, 106, 0.9)',  // 绿色
    'C': 'rgba(30, 144, 255, 0.9)',   // 蓝色
    'D': 'rgba(169, 169, 169, 0.9)',  // 灰色
    'T': 'rgba(255, 140, 0, 0.9)'     // 深橙色
  }
  
  // 获取等级的第一个字符来匹配颜色
  const prefix = level?.charAt(0)
  return colors[prefix] || 'rgba(144, 238, 144, 0.9)'  // 默认浅草绿色
}

// 敏感词相关数据
const sensitiveWords = ref([])
const sensitiveCurrentPage = ref(1)
const sensitiveTotal = ref(0)
const sensitiveLoading = ref(false)
const addDialogVisible = ref(false)
const batchAddDialogVisible = ref(false)
const submitting = ref(false)

const sensitiveSearchForm = ref({
  keyword: '',
  source: '',
  is_active: null
})

const addForm = ref({
  word: '',
  remark: ''
})

const batchAddForm = ref({
  words: '',
  remark: ''
})

// 获取敏感词列表
const fetchSensitiveWords = async () => {
  try {
    sensitiveLoading.value = true
    const params = {
      page: sensitiveCurrentPage.value,
      page_size: 10,
      ...sensitiveSearchForm.value
    }
    const { data } = await getSensitiveWords(params)
    if (data && data.list) {
      sensitiveWords.value = data.list
      sensitiveTotal.value = data.total
    }
  } catch (error) {
    console.error('获取敏感词列表失败:', error)
    ElMessage.error('获取敏感词列表失败')
  } finally {
    sensitiveLoading.value = false
  }
}

// 搜索敏感词
const handleSensitiveSearch = () => {
  sensitiveCurrentPage.value = 1
  fetchSensitiveWords()
}

// 重置搜索
const resetSensitiveSearch = () => {
  sensitiveSearchForm.value = {
    keyword: '',
    source: '',
    is_active: null
  }
  handleSensitiveSearch()
}

// 显示添加对话框
const showAddDialog = () => {
  addForm.value = {
    word: '',
    remark: ''
  }
  addDialogVisible.value = true
}

// 显示批量添加对话框
const showBatchAddDialog = () => {
  batchAddForm.value = {
    words: '',
    remark: ''
  }
  batchAddDialogVisible.value = true
}

// 提交添加
const submitAdd = async () => {
  if (!addForm.value.word.trim()) {
    ElMessage.warning('请输入敏感词')
    return
  }
  
  try {
    submitting.value = true
    await addSensitiveWord({
      word: addForm.value.word.trim(),
      source: 'manual',
      remark: addForm.value.remark
    })
    ElMessage.success('添加成功')
    addDialogVisible.value = false
    fetchSensitiveWords()
  } catch (error) {
    ElMessage.error('添加失败')
  } finally {
    submitting.value = false
  }
}

// 提交批量添加
const submitBatchAdd = async () => {
  if (!batchAddForm.value.words.trim()) {
    ElMessage.warning('请输入敏感词')
    return
  }
  
  try {
    submitting.value = true
    const words = batchAddForm.value.words
      .split('\n')
      .map(word => word.trim())
      .filter(word => word)
      .map(word => ({
        word,
        remark: batchAddForm.value.remark
      }))
    
    await batchAddSensitiveWords({ words })
    ElMessage.success('批量添加成功')
    batchAddDialogVisible.value = false
    fetchSensitiveWords()
  } catch (error) {
    ElMessage.error('批量添加失败')
  } finally {
    submitting.value = false
  }
}

// 删除敏感词
const handleDeleteSensitive = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个敏感词吗？', '提示', {
      type: 'warning'
    })
    await deleteSensitiveWord(row.id)
    ElMessage.success('删除成功')
    fetchSensitiveWords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页切换
const handleSensitiveCurrentChange = (val) => {
  sensitiveCurrentPage.value = val
  fetchSensitiveWords()
}

// 文件上传相关
const fileInput = ref(null)

// 触发文件选择
const handleUploadExcel = () => {
  fileInput.value.click()
}

// 处理文件选择
const handleFileChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    submitting.value = true
    const { data } = await batchAddSensitiveWords(formData)
    
    ElMessage.success(`导入成功${data.success_count}条`)
    if (data.error_words?.length) {
      ElMessage.warning(`${data.error_words.length}条记录导入失败`)
    }
    
    fetchSensitiveWords()
  } catch (error) {
    ElMessage.error('导入失败')
  } finally {
    submitting.value = false
    event.target.value = '' // 清空文件选择
  }
}

// 处理导出
const handleExport = async () => {
  try {
    const params = {
      ...sensitiveSearchForm.value,
      keyword: sensitiveSearchForm.value.keyword.trim()
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })
    
    const response = await exportSensitiveWords(params)
    
    // 创建下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', '敏感词列表.xlsx')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 处理状态切换
const handleStatusChange = async (row) => {
  try {
    row.switching = true
    await enableSensitiveWord(row.id)
    ElMessage.success(row.is_active ? '已启用' : '已禁用')
  } catch (error) {
    row.is_active = !row.is_active // 恢复状态
    ElMessage.error('操作失败')
  } finally {
    row.switching = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCarLevels() // 获取赛车等级列表
  fetchComments()
  fetchReports()
  fetchSensitiveWords()
})
</script>

<style scoped>
.comment-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-table {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.car-name-cell,
.username-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table--border) {
  border-radius: 8px;
  overflow: hidden;
}

.header-actions {
  display: flex;
  gap: 12px;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 搜索表单样式优化 */
.search-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.search-form-item-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.search-form-item-group:last-child {
  margin-bottom: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
  display: flex;
  align-items: center;
}

.search-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
}

.search-form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
}

/* 表格和分页样式优化 */
.el-table {
  margin-top: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}

/* 日期选择器中文样式优化 */
:deep(.custom-date-picker) {
  .el-date-range-picker__header {
    text-align: center;
    font-weight: bold;
  }
  
  .el-date-range-picker__time-header {
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .el-picker-panel__icon-btn {
    margin-top: 3px;
  }
  
  .el-date-range-picker__content {
    .el-date-range-picker__header div {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
}

/* 优化日期选择器样式 */
:deep(.el-date-editor) {
  --el-datepicker-border-color: #dcdfe6;
  .el-range-separator {
    color: var(--el-text-color-primary);
  }
  .el-range-input {
    color: var(--el-text-color-regular);
  }
}

/* 日期选择器快捷选项样式 */
:deep(.el-picker-panel__shortcut) {
  color: var(--el-text-color-regular);
  &:hover {
    color: var(--el-color-primary);
  }
}

/* 敏感词库专属样式 */
.sensitive-words {
  margin-top: 0;
}

.sensitive-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 移除之前的特殊样式 */
.sensitive-search-form,
.sensitive-header {
  display: block;
}

/* 确保搜索表单样式一致 */
.search-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.search-form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
}

.search-form-item-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;
}

/* 移除不需要的样式 */
.sensitive-search-form,
.sensitive-header {
  width: auto;
  gap: 0;
}

/* 优化开关按钮样式 */
:deep(.el-switch) {
  --el-switch-on-color: var(--el-color-success);
}

/* 优化按钮图标间距 */
.sensitive-actions .el-button .el-icon {
  margin-right: 4px;
}
</style> 