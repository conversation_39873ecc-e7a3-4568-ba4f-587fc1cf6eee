/**
 * 赛车夺宝配置API服务
 */
import request from '@/utils/request'

// 修改BASE_URL，移除重复的api前缀
const BASE_URL = '/treasure/items'

/**
 * 获取奖品列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回API响应
 */
export function getTreasureItems(params) {
  return request({
    url: `${BASE_URL}/`,
    method: 'get',
    params
  })
}

/**
 * 获取奖品详情
 * @param {number|string} id - 奖品ID
 * @returns {Promise} - 返回API响应
 */
export function getTreasureItemDetail(id) {
  return request({
    url: `${BASE_URL}/${id}/`,
    method: 'get'
  })
}

/**
 * 创建奖品
 * @param {Object} data - 奖品数据
 * @returns {Promise} - 返回API响应
 */
export function createTreasureItem(data) {
  return request({
    url: `${BASE_URL}/`,
    method: 'post',
    data
  })
}

/**
 * 更新奖品
 * @param {Object} data - 奖品数据
 * @returns {Promise} - 返回API响应
 */
export function updateTreasureItem(data) {
  return request({
    url: `${BASE_URL}/${data.id}/`,
    method: 'put',
    data
  })
}

/**
 * 删除奖品
 * @param {number|string} id - 奖品ID
 * @returns {Promise} - 返回API响应
 */
export function deleteTreasureItem(id) {
  return request({
    url: `${BASE_URL}/${id}/`,
    method: 'delete'
  })
}

/**
 * 获取品质选项
 * @returns {Promise} - 返回API响应
 */
export function getQualityOptions() {
  return request({
    url: `${BASE_URL}/qualities/`,
    method: 'get'
  })
}

/**
 * 获取类型选项
 * @returns {Promise} - 返回API响应
 */
export function getItemTypeOptions() {
  return request({
    url: `${BASE_URL}/item_types/`,
    method: 'get'
  })
}

/**
 * 获取期限选项
 * @returns {Promise} - 返回API响应
 */
export function getExpiryOptions() {
  return request({
    url: `${BASE_URL}/expiry_options/`,
    method: 'get'
  })
}

/**
 * 切换奖品状态
 * @param {number|string} id - 奖品ID
 * @returns {Promise} - 返回API响应
 */
export function toggleTreasureItemStatus(id) {
  return request({
    url: `${BASE_URL}/${id}/toggle_status/`,
    method: 'patch'
  })
} 