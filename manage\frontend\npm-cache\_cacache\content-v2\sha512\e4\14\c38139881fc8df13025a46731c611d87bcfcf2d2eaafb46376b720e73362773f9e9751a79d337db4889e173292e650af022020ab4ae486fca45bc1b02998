{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:01.178Z", "name": "@parcel/watcher-linux-arm64-glibc", "versions": {"2.2.0-alpha.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.2.0-alpha.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-qmPdcQ7XGIueMirGp/J8O6mumW9fRB8MzskuVMGb8OrzUqEe5KtOhLSmTwmssGiHqGDUU0HwuUa328naFm6VJA==", "shasum": "c439f62e5140fe8b31a3c6656fb90c1709ad22a9", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.2.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 434891, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoKfEAq+TNMrBe83HJ0HrGMiFBLKoXJuTpXSYETmxXhgIhAPhx2tZZdSm70AeH8yacTD3OSPS8gQltKkqV0g8mdqRn"}], "size": 165925}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687727615015, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.2.0-alpha.1", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-5ANcF9+DQbbIPwd9/uGnxEVJjRb9rzD2BdvyVqSo/S/7douXbv1YoI0GAm+T9WYr9uTM4vFAB4eys+OV7kcaOw==", "shasum": "b65095232a6f5118a248f5212cb7704a12d2653d", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 434891, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG41KqeiPrB2XX693OqMvFVMLEqkuuDEgPGPR4DervloAiBu3YMLYqf4VkWYhf8fKB+ihCWNTRqXnNzBnu+0gFtXuw=="}], "size": 165925}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738107718, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.2.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-St5mlfp+2lS9AmgixUqfwJa/DwVmTCJxC1HcOubUTz6YFOKIlkHCeUa1Bxi4E/tR/HSez8+heXHL8HQkJ4Bd8g==", "shasum": "b82c1715a20e6725c89b2697a208d2ae488d22da", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.2.0.tgz", "fileCount": 4, "unpackedSize": 434883, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2+aUJYICQXnYYDo7MLr4AEXKe3Ce/BRuvmyo0MrzrcQIgUl0j3+fQ+8hlG62NNpWaSucKiSd0ZsRK6lgtDg0WTBQ="}], "size": 165919}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269821376, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.2.1-alpha.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-Z1aBGp3NQTpH0pT/p8sbFr4N+yHtHUxiJZSOPbihT7YijAY2zydF+efCrhpNVE6XDFiuvxtEUVs6FWUFQ8YpBQ==", "shasum": "d59c6be333a98bb4952c539231f2b79f226cfaff", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 434891, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBk6nu7wNky6Qq7RAa2uJK2Aootf6jRz7Y0Wt4LH6hvkAiEAieXLqF0vrgb20tfPYtsOMzMdr9xtgblozNQvgHQcqNA="}], "size": 165658}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571408913, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.3.0-alpha.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-mWpLIcYrxQAscPgfLcGZcYCMqD/GHX7ieioiBsPuqUt9zXVMmJVIS0/10cwLG1TkNBBgwjVRAr+G23A39CJ30g==", "shasum": "56fc5b71a7c82b653c980246530b76c7c908b2f6", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 455339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5/pQFh1apatid5sZ1OyBRtauzulu9LIzChbnKYkeWkgIhAPmrNdrWMuc2rNpNR5lCdmck1NQBA8JbRkQ2X7HXw0FO"}], "size": 175908}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750504869, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.3.0-alpha.1", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-1jAjPSOm32uPefCHoJ5dFJgFaCM1zp82qM/S3BZ/rkFvuvfKyq7rs8bq5XdHlfEpujOt2SN9oxryGyD0AJKvUQ==", "shasum": "7a85348339e3d4af8fbc6244a364ffca8b6c19dc", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 455339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtWsnsI9i/PzCffN6t95UqSme51Rrds0+xmE6y23EhLQIgC6Aa7LxFeyC7RhBlW02FyBJjOROO3YTTlU0k/1RBMNg="}], "size": 175908}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753402298, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.3.0-alpha.2", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-BLaVujKtPcLzWoAsIgfH3qogicFQkI9X7BdPSl1eIaVIlyVAzcfH3l0Dx8jXmUffF1jAZaQdqSfyLQnvn2G/Dg==", "shasum": "63385ba50d9abbb5432b367ce3cf056b0f20e423", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 455339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPqHGVUvDJMlwChUb0/JSzZn89PrOTeZsc0jGan4ag2AiA8Abc4fGNjZY4X8HRbLj2vZtMiHjHleV5o962AD+N7hA=="}], "size": 175908}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485203537, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.3.0-alpha.3", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-gw1h3OZYaNJYH46CQInrexgnHA27ppHcBnpnoQRQs6fww8xv4TgBU2VVQFayY0SGqoaHyNGPNFCQI+UPTSfEQQ==", "shasum": "98e2656693ca2233345465f01852a14225ade97f", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 455339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5GMEUiR3WthZAaOL8ZMl4TtP4dDu3o3zyf+ufOUx1PQIhAI6zFYP2wLWtxbc5Ku1KMlZC/gdMG1N+oquARb7QDrDr"}], "size": 175908}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557602500, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.3.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-mQ0gBSQEiq1k/MMkgcSB0Ic47UORZBmWoAWlMrTW6nbAGoLZP+h7AtUM7H3oDu34TBFFvjy4JCGP43JlylkTQA==", "shasum": "f7bbbf2497d85fd11e4c9e9c26ace8f10ea9bcbc", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.3.0.tgz", "fileCount": 4, "unpackedSize": 455331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGaajWUVPBFgRslw9RO0xu+K8+eBaxAFZEUy2Yj0ABcQIhAM2NBAbEfkZ2tq629F1ACw0fSh5dRKTmtX5BK5YBWi05"}], "size": 175905}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850811435, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.4.0", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-QuJTAQdsd7PFW9jNGaV9Pw+ZMWV9wKThEzzlY3Lhnnwy7iW23qtQFPql8iEaSFMCVI5StNNmONUopk+MFKpiKg==", "shasum": "56e09b86e9d8a4096f606be118b588da6e965080", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.4.0.tgz", "fileCount": 4, "unpackedSize": 455331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQUH+rOuo1REQVN/uZ8+MbxxuWzr6TPfxeuRCy5GL05AiEA2A9EeFkfa6X+G3VKpJLuxhd4do/1gCOiav6+uzQVoQo="}], "size": 175905}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360248927, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.4.1", "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "directories": {}, "dist": {"integrity": "sha512-BJ7mH985OADVLpbrzCLgrJ3TOpiZggE9FMblfO65PlOCdG++xJpKUJ0Aol74ZUIYfb8WsRlUdgrZxKkz3zXWYA==", "shasum": "6d7c00dde6d40608f9554e73998db11b2b1ff7c7", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.4.1.tgz", "fileCount": 4, "unpackedSize": 455331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCl+Vuo6r17gUVlr22kfgYo0nXhLxNBC1uyegQWKlCfcwIgbzQLORbEYhrwsvBAaifzDZl9Vlq+C6uLQCgZTyrBODA="}], "size": 175905}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702664689, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.4.2-alpha.0", "directories": {}, "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "dist": {"integrity": "sha512-vIIOcZf+fgsRReIK3Fw0WINvGo9UwiXfisnqYRzfpNByRZvkEPkGTIVe8iiDp72NhPTVmwIvBqM6yKDzIaw8GQ==", "shasum": "91dc10ccbcefd4534706eb78c9cc6f6aafbd5191", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 459507, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHFOoVd78cCQ/SdEEYRS7PiRVGnRwtnkP/UV3XMKyEP/AiEA1Kfo5GnA9PIYJ6dm+tV1PqZEAnUQZ4mbT2cvrb719+s="}], "size": 180344}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259255346, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-linux-arm64-glibc", "version": "2.5.0", "directories": {}, "os": ["linux"], "cpu": ["arm64"], "libc": ["glibc"], "dist": {"integrity": "sha512-BfNjXwZKxBy4WibDb/LDCriWSKLz+jJRL3cM/DllnHH5QUyoiUNEp3GmL80ZqxeumoADfCCP19+qiYiC8gUBjA==", "shasum": "7b81f6d5a442bb89fbabaf6c13573e94a46feb03", "tarball": "https://registry.npmmirror.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.0.tgz", "fileCount": 4, "unpackedSize": 459499, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCL9p0W0KFIZqP8C3nqMeRTyF2NwJnXAqngPts+1HnkzQIgfFxn19x8h0nSbbqdMVHsjJBnqPuinGlMSvIObTM8qLo="}], "size": 180333}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701341079, "_source_registry_name": "default"}}, "_source_registry_name": "default"}