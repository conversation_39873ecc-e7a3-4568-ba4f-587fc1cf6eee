{"dist-tags": {"latest": "5.0.1"}, "modified": "2023-07-27T21:29:25.170Z", "name": "to-regex-range", "versions": {"5.0.1": {"name": "to-regex-range", "version": "5.0.1", "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "1648c44aae7c8d988a326018ed72f5b4dd0392e4", "size": 7479, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="}, "engines": {"node": ">=8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "5.0.0": {"name": "to-regex-range", "version": "5.0.0", "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "d53e58b704377f6cb2abe26e0fd63cb1ee348dde", "size": 7478, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.0.tgz", "integrity": "sha512-LIBaWcdxFv9GhuMrXVdDJ0zLJK+3GjxCvJq/q68ZKEeHWGmSLl6m5ZRdflM96lO1QelulcqxU1SJ/IzucVmjWA=="}, "engines": {"node": ">=8.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.3": {"name": "to-regex-range", "version": "4.0.3", "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "64dcfb33638ec86160eb1a8fe6937d7160fcd49a", "size": 7526, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-4.0.3.tgz", "integrity": "sha512-AtJgwCeygrdcfleD1nolmv8TSJcsPvSsvnqQRzc1AkEa//+RRTseKZpaXOfZk2/U1B+bz0sRpkaF1oHX5YmHKg=="}, "engines": {"node": ">=4.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.2": {"name": "to-regex-range", "version": "4.0.2", "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "f4556608ab0e94adf697ec8aabc1a777cee80126", "size": 7546, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-4.0.2.tgz", "integrity": "sha512-sxk1o2k6PE/1pglSD23fhfgoTKNz3LT8FxfcBEqVDUjNzQV1VdAhipuiylH9kOm2qWsRTFO0mzRDXpLtm01XjQ=="}, "engines": {"node": ">=4.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}}, "4.0.1": {"name": "to-regex-range", "version": "4.0.1", "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "e0ffa0f6b924dab8ed8d6c87768c60f0fc056330", "size": 7509, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-4.0.1.tgz", "integrity": "sha512-Zz53svp5+xqvAZSIkLDdxu15Sv/qgem4HUbFWYPmSvRWWrfhOnXJKiEM36Zv9yibgvam8c+bNdtpbEKYI/wLnQ=="}, "engines": {"node": ">=4.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "4.0.0": {"name": "to-regex-range", "version": "4.0.0", "dependencies": {"is-number": "^6.0.0"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "130abc838f977433e27e013e150d8b5b4427f009", "size": 7507, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-4.0.0.tgz", "integrity": "sha512-EG/hemm1cqaGMaqEfwBVLdFILRh0NAtpg1TA59oNb7IlAFMhKtbeiDliT68poGxcIu+mF9NrL/MzYUqOK1Uu0Q=="}, "engines": {"node": ">=4.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.0": {"name": "to-regex-range", "version": "3.0.0", "dependencies": {"is-number": "^4.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^3.5.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "ef50e217a2fc2b97da74ad62ca9a578d929fb462", "size": 7440, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-3.0.0.tgz", "integrity": "sha512-6b3QvX2mf8yGBjYOWEDp3ZJBDn2RYx5QSoIKNho0zFxHcOEV5S9Ww9QdL6E3o2juJt9ouHu36yQo21ImlpXgKg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.1": {"name": "to-regex-range", "version": "2.1.1", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "7c80c17b9dfebe599e27367e0d4dd5590141db38", "size": 7306, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.0": {"name": "to-regex-range", "version": "2.1.0", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "ab2989a43c53bc367dd604adf22e330984840d74", "size": 7357, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-2.1.0.tgz", "integrity": "sha512-7P19jh/jrkD8/D5Yx2OL9JNYBmb81HocpDFtUpVc4vwTAiZosCMihjcRYcULwpTkq9zuyCN+mBny7RTIdDRMIg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0": {"name": "to-regex-range", "version": "2.0.0", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "directories": {}, "dist": {"shasum": "53bea64df461ac8f024f2b07d8e9ff27daec5985", "size": 6601, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-2.0.0.tgz", "integrity": "sha512-GPUromf0uvYuaPhTKe830QODQMJLBMnnjQRN0GbN82Z0S3DIVHCLGJw2rMOfeLV3qtmxnuWP30Ju3CEoLLx9Dw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.2": {"name": "to-regex-range", "version": "1.0.2", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "directories": {}, "dist": {"shasum": "48d5a984e7f701ba99f1e67b3bde1d1b11ecf74c", "size": 5093, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-1.0.2.tgz", "integrity": "sha512-KmuhH/HSx56UPweUyym97tlcqPemszSnU7hwQ8sSEEOnFitN8jGLZEQJY5mVPKHwst5XCOfQ90flxER+DM6a4g=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.1": {"name": "to-regex-range", "version": "1.0.1", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "directories": {}, "dist": {"shasum": "415a8c95a7bb3704aadff3f1efff0089590dd43a", "size": 5095, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-1.0.1.tgz", "integrity": "sha512-srZowjq9YqJNhXjKP9GPLpPuRWr93jVPZ687Pht4jAv00zWgk1ABh8w2fTrQdfRoxp7zA/d6moSnF5r4MnQt/Q=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0": {"name": "to-regex-range", "version": "1.0.0", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "directories": {}, "dist": {"shasum": "0f411a456c89a592465f54ed79d3c376ad285906", "size": 5085, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-1.0.0.tgz", "integrity": "sha512-zoWe4g+11aX4DAP0FkjqlY3/Dys0rwuwYgPANSN1I2vZW4leuUzDwD9vutm2tz5G9VvbXCaPazDLAqdABI6wYA=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.0": {"name": "to-regex-range", "version": "0.2.0", "dependencies": {"is-number": "^2.1.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "directories": {}, "dist": {"shasum": "12b35ace6ec656ea32d9303c2abeb07efa61c1c2", "size": 4296, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-0.2.0.tgz", "integrity": "sha512-niV/fY2U9bSm818O6ZfA44BcNXdL0fr7DhmYQb3BRpN7khvIbBIbyPzL8FuzHRBFZzGzYQwMw2tQ7NZGXvvMbw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.3": {"name": "to-regex-range", "version": "0.1.3", "dependencies": {"is-number": "^2.1.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5", "should": "^8.3.1"}, "directories": {}, "dist": {"shasum": "2dfef8fc4c46586a98739774c2e66e1fd5a24a58", "size": 4280, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-0.1.3.tgz", "integrity": "sha512-sExxxnGd/rnFfNtLzJJS1O64gLSoptQlZnDL582lfNpsJmnMgTsfMhlMPhZyA3Ce/McvDi90PrQUkS+NyXFKXw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.2": {"name": "to-regex-range", "version": "0.1.2", "dependencies": {"is-number": "^2.1.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5", "should": "^8.3.1"}, "directories": {}, "dist": {"shasum": "258539fa4984ba79847b1fb4294a53487e8d15e9", "size": 4118, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-0.1.2.tgz", "integrity": "sha512-80jFwVvXAwvUWIuqTwJ6EUl76t4rYXecAh2nr6t2S/sS8bUpUx4w530T9kU2SCjJlg+m964kXaO3FmcCmGKYrw=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.1": {"name": "to-regex-range", "version": "0.1.1", "dependencies": {"is-number": "^2.0.2", "repeat-string": "^1.5.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "c899f37ca02d1aa33611835000c45b5e46e5cfb0", "size": 3723, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-0.1.1.tgz", "integrity": "sha512-E+P16hr5OR/RWLmViRYgv2Vccn0Cr6jtZsmCB/pW4bUKUpJNKAklDaIAxJ5vEFj/RdAlI0st2Z/iOABMD1kpyg=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.0": {"name": "to-regex-range", "version": "0.1.0", "dependencies": {"is-number": "^2.0.2", "repeat-string": "^1.5.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "directories": {}, "dist": {"shasum": "1332e39727b43e2767c6ec26c7df880db3db11aa", "size": 3621, "noattachment": false, "tarball": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-0.1.0.tgz", "integrity": "sha512-2E0Man7t9dOh4fe8x5cPqonPBDNlhnnpeHlu+U5vWZ68j6fa6BewBv/l2MbrfL5FT2oTdIpf63TOwUVkkqxP5Q=="}, "engines": {"node": ">=0.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}}, "_source_registry_name": "default"}