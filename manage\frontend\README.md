# QQ Speed Car Wiki Admin

QQ Speed Car Wiki Admin 是一个用于管理 QQ 飞车赛车图鉴的系统。该项目采用前后端分离架构，前端使用 Vue 3 和 Element Plus，后端使用 Django。

## 项目结构 
├── frontend/
│ ├── src/
│ │ ├── api/ # API接口
│ │ ├── components/ # 组件
│ │ ├── config/ # 配置
│ │ ├── utils/ # 工具函数
│ │ ├── views/ # 页面
│ │ ├── App.vue # 根组件
│ │ └── main.js # 入口文件
│ └── package.json # 依赖配置
│
├── backend/
│ └── venv/ # Python虚拟环境

## 环境要求

- Node.js 18.x
- Python 3.x
- Nginx
- PM2 (可选)

## 环境配置

### 前端环境配置

1. 开发环境配置文件 `.env.development`：
   ```
   VITE_API_URL=http://localhost:8000
   ```

2. 生产环境配置文件 `.env.production`：
   ```
   VITE_API_URL=http://**************
   ```

### Python 环境配置

1. 检查 Python 是否安装：
   ```bash
   python3 --version
   # 或
   python --version
   ```

2. 如果未安装，根据操作系统安装 Python：
   
   Ubuntu/Debian:
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip python3-venv
   ```
   
   CentOS/RHEL:
   ```bash
   sudo yum install python3 python3-pip
   ```
   
   MacOS:
   ```bash
   brew install python3
   ```

3. 配置环境变量（如果需要）：
   ```bash
   # 添加到 ~/.bashrc 或 ~/.zshrc
   export PATH="/usr/local/bin:$PATH"
   export PATH="$HOME/.local/bin:$PATH"
   
   # 使配置生效
   source ~/.bashrc  # 或 source ~/.zshrc
   ```

## 安装与运行

### 前端

1. 安装依赖：

   ```bash
   cd frontend
   npm install
   ```

2. 开发环境运行：

   ```bash
   npm run dev
   ```

3. 生产环境构建：

   ```bash
   npm run build
   
   # 预览生产构建
   npm run preview
   ```

4. 生产环境部署：
   ```bash
   # 构建
   npm run build
   
   # 将构建后的文件复制到 Nginx 目录
   sudo cp -r dist/* /var/www/qq-speed-car/dist/
   
   # 重启 Nginx
   sudo systemctl restart nginx
   ```

### 后端

1. 创建并激活虚拟环境：

   ```bash
   cd backend
   python3 -m venv venv
   source venv/bin/activate  # Linux/Mac
   venv\Scripts\activate.bat # Windows
   ```

2. 安装依赖：

   ```bash
   python3 -m pip install --upgrade pip  # 更新 pip
   python3 -m pip install -r requirements.txt -i https://mirrors.tencent.com/pypi/simple
   ```

   如果没有 requirements.txt 文件，需要手动安装以下依赖：
   ```bash
   python3 -m pip install django djangorestframework django-cors-headers pillow -i https://mirrors.tencent.com/pypi/simple
   ```

3. 启动服务：

   ```bash
   nohup python3 manage.py runserver 0.0.0.0:8000 > django.log 2>&1 &
   nohup npm run dev > frontend.log 2>&1 &
   ```

### 使用 Nginx 部署

1. 配置 Nginx：

   将 `qq-speed-car.conf` 文件复制到 `/etc/nginx/sites-available/`，并创建软链接到 `/etc/nginx/sites-enabled/`。

   确保 Nginx 配置中包含正确的 API 代理：
   ```nginx
   # API 请求转发到后端
   location /api/ {
       proxy_pass http://**************:8000;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
   }
   ```

2. 测试并重启 Nginx：

   ```bash
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### 使用 PM2 管理前端服务

1. 安装 PM2：

   ```bash
   npm install -g pm2
   ```

2. 启动服务：

   ```bash
   pm2 start npm --name "qq-speed-car" -- run dev
   ```

3. 查看日志：

   ```bash
   pm2 logs qq-speed-car
   ```

## 使用说明

- 访问 `http://localhost` 查看前端页面。
- 访问 `http://localhost:8000/api` 查看后端 API。

## 常见问题

- **权限问题**：确保项目目录和 `node_modules` 目录的权限正确。
- **服务管理**：使用 `pm2` 或 `systemd` 管理服务，确保在系统重启后自动启动。

## 贡献

欢迎提交问题和请求合并。请确保在提交前运行所有测试。

## 许可证

MIT License