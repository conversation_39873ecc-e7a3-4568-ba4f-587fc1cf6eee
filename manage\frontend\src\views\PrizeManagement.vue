<template>
  <div class="prize-management">
    <!-- 标题区域 -->
    <div class="page-header">
      <div class="header-title">
        <h2>道具与奖品管理</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="backToHome">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <el-card class="content-card">
      <el-tabs v-model="activeTab">
        <!-- 道具管理标签页 -->
        <el-tab-pane label="道具管理" name="prizeSource">
          <!-- 搜索区域 -->
          <el-form :inline="true" :model="sourceSearchForm" class="search-form">
            <div class="search-form-item-group">
              <el-form-item label="关键词">
                <el-input v-model="sourceSearchForm.search" placeholder="搜索道具名称或描述" clearable style="width: 200px"></el-input>
              </el-form-item>
              <el-form-item label="道具类型">
                <el-select v-model="sourceSearchForm.source_type" placeholder="全部类型" clearable style="width: 150px">
                  <el-option v-for="item in sourceTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="sourceSearchForm.active_only" placeholder="全部状态" clearable style="width: 150px">
                  <el-option label="已上架" :value="true"></el-option>
                  <el-option label="全部" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="search-form-buttons">
              <el-form-item>
                <el-button type="primary" @click="searchSources">搜索</el-button>
                <el-button @click="resetSourceSearch">重置</el-button>
                <el-button type="success" @click="showAddSourceDialog">
                  <el-icon><Plus /></el-icon>添加道具
                </el-button>
                <el-upload
                  class="upload-inline"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeSourceExcelUpload"
                >
                  <el-button type="info">
                    <el-icon><Upload /></el-icon>导入Excel
                  </el-button>
                </el-upload>
              </el-form-item>
            </div>
          </el-form>

          <!-- 道具列表 -->
          <el-table
            :data="sourcesData"
            border
            stripe
            v-loading="sourceLoading"
            style="width: 100%; margin-top: 20px;">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="source_code" label="道具编号" width="120"></el-table-column>
            <el-table-column label="道具图片" width="100" align="center">
              <template #default="{ row }">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="row.image_url"
                  :preview-src-list="[row.image_url]"
                  fit="cover"
                  :initial-index="0"
                  preview-teleported
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="道具名称" min-width="120"></el-table-column>
            <el-table-column prop="source_type_display" label="类型" width="100"></el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'info'" size="small">
                  {{ row.is_active ? '已上架' : '未上架' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="viewSourceDetail(row)">查看</el-button>
                <el-button size="small" type="warning" @click="editSource(row)">编辑</el-button>
                <el-popconfirm
                  title="确定要删除这个道具吗？"
                  @confirm="deleteSource(row.id)"
                >
                  <template #reference>
                    <el-button size="small" type="danger">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSourceSizeChange"
              @current-change="handleSourceCurrentChange"
              :current-page="sourcePagination.currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="sourcePagination.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="sourcePagination.total"
              background>
            </el-pagination>
          </div>
        </el-tab-pane>

        <!-- 奖品管理标签页 -->
        <el-tab-pane label="奖品管理" name="prize">
          <!-- 搜索区域 -->
          <el-form :inline="true" :model="prizeSearchForm" class="search-form">
            <div class="search-form-item-group">
              <el-form-item label="关键词">
                <el-input v-model="prizeSearchForm.search" placeholder="搜索奖品名称或描述" clearable style="width: 200px"></el-input>
              </el-form-item>

              <el-form-item label="奖品类型">
                <el-select
                  v-model="prizeSearchForm.prize_type"
                  placeholder="全部类型"
                  clearable
                  filterable
                  remote
                  :remote-method="searchPrizeTypes"
                  :loading="prizeTypesLoading"
                  @visible-change="handlePrizeTypeVisibleChange"
                  style="width: 150px"
                >
                  <el-option v-for="item in prizeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属道具">
                <el-select v-model="prizeSearchForm.source_id" placeholder="全部道具" clearable style="width: 150px">
                  <el-option v-for="item in sourcesOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="prizeSearchForm.active_only" placeholder="全部状态" clearable style="width: 150px">
                  <el-option label="已启用" :value="true"></el-option>
                  <el-option label="全部" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="search-form-buttons">
              <el-form-item>
                <el-button type="primary" @click="searchPrizes">搜索</el-button>
                <el-button @click="resetPrizeSearch">重置</el-button>
                <el-button type="success" @click="showAddPrizeDialog">
                  <el-icon><Plus /></el-icon>添加奖品
                </el-button>
              </el-form-item>
            </div>
          </el-form>

          <!-- 奖品列表 -->
          <el-table
            :data="prizesData"
            border
            stripe
            v-loading="prizeLoading"
            style="width: 100%; margin-top: 20px;">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="prize_code" label="奖品编号" width="120"></el-table-column>
            <el-table-column label="奖品图片" width="100" align="center">
              <template #default="{ row }">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="row.image_url"
                  :preview-src-list="[row.image_url]"
                  fit="cover"
                  :initial-index="0"
                  preview-teleported
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="奖品名称" min-width="120"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>

            <el-table-column prop="prize_type" label="类型" width="100"></el-table-column>
            <el-table-column label="所属道具" min-width="120">
              <template #default="{ row }">
                <div v-if="row.sources_info && row.sources_info.length > 0">
                  <el-tooltip v-if="row.sources_info.length > 1" :content="getSourcesNames(row.sources_info)" placement="top">
                    <span>{{ row.sources_info[0].source_name }}等{{ row.sources_info.length }}个道具</span>
                  </el-tooltip>
                  <span v-else>{{ row.sources_info[0].source_name }}</span>
                </div>
                <span v-else-if="row.source_name">{{ row.source_name }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'info'" size="small">
                  {{ row.is_active ? '已启用' : '未启用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="viewPrizeDetail(row)">查看</el-button>
                <el-button size="small" type="warning" @click="editPrize(row)">编辑</el-button>
                <el-popconfirm
                  title="确定要删除这个奖品吗？"
                  @confirm="handleDeletePrize(row.id)"
                >
                  <template #reference>
                    <el-button size="small" type="danger">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              @size-change="handlePrizeSizeChange"
              @current-change="handlePrizeCurrentChange"
              :current-page="prizePagination.currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="prizePagination.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="prizePagination.total"
              background>
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 道具详情对话框 -->
    <el-dialog
      v-model="sourceDetailVisible"
      title="道具详情"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="currentSource" class="detail-container">
        <div class="detail-header">
          <div class="detail-image">
            <el-image
              style="width: 100px; height: 100px"
              :src="currentSource.image_url"
              fit="cover"
            ></el-image>
          </div>
          <div class="detail-info">
            <h3>{{ currentSource.name }}</h3>
            <p><strong>道具编号:</strong> {{ currentSource.source_code }}</p>
            <p><strong>类型:</strong> {{ currentSource.source_type_display }}</p>
            <p><strong>状态:</strong> {{ currentSource.is_active ? '已上架' : '未上架' }}</p>
            <p><strong>描述:</strong> {{ currentSource.description }}</p>
          </div>
        </div>

        <div class="detail-section">
          <h4>包含奖品</h4>
          <el-table
            :data="sourcePrizes"
            border
            stripe
            style="width: 100%; margin-top: 10px;">
            <el-table-column prop="prize_code" label="奖品编号" width="120"></el-table-column>
            <el-table-column label="奖品图片" width="80" align="center">
              <template #default="{ row }">
                <el-image
                  style="width: 40px; height: 40px"
                  :src="row.image_url"
                  fit="cover"
                ></el-image>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="奖品名称" min-width="120"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
            <el-table-column prop="prize_type" label="类型" width="100"></el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 奖品详情对话框 -->
    <el-dialog
      v-model="prizeDetailVisible"
      title="奖品详情"
      width="50%"
      :close-on-click-modal="false"
    >
      <div v-if="currentPrize" class="detail-container">
        <div class="detail-header">
          <div class="detail-image">
            <el-image
              style="width: 100px; height: 100px"
              :src="currentPrize.image_url"
              fit="cover"
            ></el-image>
          </div>
          <div class="detail-info">
            <h3>{{ currentPrize.name }}</h3>
            <p><strong>奖品编号:</strong> {{ currentPrize.prize_code }}</p>
            <p><strong>数量:</strong> {{ currentPrize.quantity }}</p>

            <p><strong>类型:</strong> {{ currentPrize.prize_type }}</p>
            <p><strong>所属道具:</strong>
              <span v-if="currentPrize.sources_info && currentPrize.sources_info.length > 0">
                {{ getSourcesNames(currentPrize.sources_info) }}
              </span>
              <span v-else-if="currentPrize.source_name">{{ currentPrize.source_name }}</span>
              <span v-else>-</span>
            </p>

            <p><strong>状态:</strong> {{ currentPrize.is_active ? '已启用' : '未启用' }}</p>
            <p><strong>描述:</strong> {{ currentPrize.description }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加/编辑道具对话框 -->
    <el-dialog
      v-model="sourceFormVisible"
      :title="isEdit ? '编辑道具' : '添加道具'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="sourceFormRef"
        :model="sourceForm"
        :rules="sourceRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="道具名称" prop="name">
          <el-input v-model="sourceForm.name" placeholder="请输入道具名称"></el-input>
        </el-form-item>
        <el-form-item label="道具编号" prop="source_code">
          <el-input v-model="sourceForm.source_code" placeholder="请输入道具编号，留空将自动生成"></el-input>
        </el-form-item>
        <el-form-item label="道具类型" prop="source_type">
          <el-select v-model="sourceForm.source_type" placeholder="请选择道具类型（可选）" style="width: 100%" clearable>
            <el-option v-for="item in sourceTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <div class="form-tip">道具类型为可选项，不填写将使用默认类型</div>
        </el-form-item>
        <el-form-item label="道具图片" prop="image_url">
          <el-input v-model="sourceForm.image_url" placeholder="请输入图片URL或上传图片">
            <template #append>
              <el-upload
                action="#"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
              >
                <el-button type="primary">上传图片</el-button>
              </el-upload>
            </template>
          </el-input>
          <div v-if="sourceForm.image_url" class="image-preview">
            <el-image
              style="width: 80px; height: 80px"
              :src="sourceForm.image_url"
              fit="cover"
            ></el-image>
          </div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="sourceForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入道具描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="sourceForm.is_active"
            active-text="已上架"
            inactive-text="未上架"
          ></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sourceFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSourceForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加/编辑奖品对话框 -->
    <el-dialog
      v-model="prizeFormVisible"
      :title="isEdit ? '编辑奖品' : '添加奖品'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="prizeFormRef"
        :model="prizeForm"
        :rules="prizeRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="奖品名称" prop="name">
          <el-input v-model="prizeForm.name" placeholder="请输入奖品名称"></el-input>
        </el-form-item>
        <el-form-item label="奖品编号" prop="prize_code">
          <el-input v-model="prizeForm.prize_code" placeholder="请输入奖品编号，留空将自动生成"></el-input>
        </el-form-item>
        <el-form-item label="所属道具" prop="sources">
          <el-select v-model="prizeForm.sources" multiple placeholder="请选择所属道具" style="width: 100%">
            <el-option v-for="item in sourcesOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <div class="form-tip">可以选择多个道具</div>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <div class="quantity-input">
            <el-input-number v-model="prizeForm.quantity" :min="1" :max="9999" style="width: calc(100% - 80px)"></el-input-number>
            <el-select v-model="quantityUnit" style="width: 80px; margin-left: 10px">
              <el-option label="个" value="个"></el-option>
              <el-option label="天" value="天"></el-option>
              <el-option label="小时" value="小时"></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="奖品类型" prop="prize_type">
          <el-select
            v-model="prizeForm.prize_type"
            placeholder="请选择奖品类型"
            style="width: 100%"
            filterable
            remote
            :remote-method="searchPrizeTypes"
            :loading="prizeTypesLoading"
            @visible-change="handlePrizeTypeVisibleChange"
            allow-create
            default-first-option
            clearable
          >
            <el-option v-for="item in prizeTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <div class="form-tip">可以输入自定义类型或搜索已有类型</div>
        </el-form-item>

        <el-form-item label="奖品图片" prop="image_url">
          <el-input v-model="prizeForm.image_url" placeholder="请输入图片URL或上传图片">
            <template #append>
              <el-upload
                action="#"
                :show-file-list="false"
                :before-upload="beforePrizeImageUpload"
              >
                <el-button type="primary">上传图片</el-button>
              </el-upload>
            </template>
          </el-input>
          <div v-if="prizeForm.image_url" class="image-preview">
            <el-image
              style="width: 80px; height: 80px"
              :src="prizeForm.image_url"
              fit="cover"
            ></el-image>
          </div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="prizeForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入奖品描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="prizeForm.is_active"
            active-text="已启用"
            inactive-text="未启用"
          ></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="prizeFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPrizeForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Plus, Upload, Picture } from '@element-plus/icons-vue'
import {
  getPrizeSources,
  getPrizeSourceDetail,
  createPrizeSource,
  updatePrizeSource,
  deletePrizeSource,
  getSourceTypes,
  getSourcePrizes,
  importExcel,
  getPrizes,
  getPrizeDetail,
  createPrize,
  updatePrize,
  deletePrize,
  getRarityTypes,
  getPrizeTypes,
  uploadImage
} from '@/api/prize'

const router = useRouter()

// 标签页状态
const activeTab = ref('prizeSource')

// 返回首页
const backToHome = () => {
  router.push('/')
}

// ==================== 道具管理相关 ====================
// 道具列表数据
const sourcesData = ref([])
const sourceLoading = ref(false)
const sourcePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 道具搜索表单
const sourceSearchForm = reactive({
  search: '',
  source_type: '',
  active_only: false,
  page: 1,
  page_size: 10
})

// 道具类型选项
const sourceTypes = ref([])

// 道具详情
const sourceDetailVisible = ref(false)
const currentSource = ref(null)
const sourcePrizes = ref([])

// 获取道具类型选项
const fetchSourceTypes = async () => {
  try {
    const res = await getSourceTypes()
    console.log('获取道具类型选项响应:', res)
    if (res) {
      sourceTypes.value = res
    }
  } catch (error) {
    console.error('获取道具类型选项失败:', error)
    ElMessage.error('获取道具类型选项失败')
  }
}

// 获取道具列表
const fetchSources = async () => {
  sourceLoading.value = true
  try {
    const params = {
      ...sourceSearchForm,
      page: sourcePagination.currentPage,
      page_size: sourcePagination.pageSize
    }
    console.log('获取道具列表参数:', params)
    const res = await getPrizeSources(params)
    console.log('获取道具列表响应:', res)

    console.log('道具列表数据:', res)
    if (res && res.results) {
      sourcesData.value = res.results
      sourcePagination.total = res.count

      // 检查数据是否正确赋值
      console.log('赋值后的道具数据:', sourcesData.value)
    }
  } catch (error) {
    console.error('获取道具列表失败:', error)
    ElMessage.error('获取道具列表失败')
  } finally {
    sourceLoading.value = false
  }
}

// 搜索道具
const searchSources = () => {
  sourcePagination.currentPage = 1
  fetchSources()
}

// 重置道具搜索
const resetSourceSearch = () => {
  Object.keys(sourceSearchForm).forEach(key => {
    if (key !== 'page' && key !== 'page_size') {
      sourceSearchForm[key] = ''
    }
  })
  sourceSearchForm.active_only = false
  sourcePagination.currentPage = 1
  fetchSources()
}

// 处理道具分页大小变化
const handleSourceSizeChange = (size) => {
  sourcePagination.pageSize = size
  fetchSources()
}

// 处理道具当前页变化
const handleSourceCurrentChange = (page) => {
  sourcePagination.currentPage = page
  fetchSources()
}

// 查看道具详情
const viewSourceDetail = async (row) => {
  try {
    const res = await getPrizeSourceDetail(row.id)
    console.log('获取道具详情响应:', res)
    if (res) {
      currentSource.value = res
      sourcePrizes.value = res.prizes || []
      sourceDetailVisible.value = true
    }
  } catch (error) {
    console.error('获取道具详情失败:', error)
    ElMessage.error('获取道具详情失败')
  }
}

// 编辑道具
const editSource = (row) => {
  isEdit.value = true
  editingSourceId.value = row.id

  // 填充表单数据
  sourceForm.name = row.name || ''
  sourceForm.source_code = row.source_code || ''
  sourceForm.source_type = row.source_type || '' // 如果没有类型，设置为空字符串
  sourceForm.image_url = row.image_url || ''
  sourceForm.description = row.description || ''
  sourceForm.is_active = row.is_active !== undefined ? row.is_active : true

  // 打印一下表单数据，方便调试
  console.log('编辑道具表单数据:', sourceForm)

  sourceFormVisible.value = true
}

// 删除道具
const deleteSource = async (id) => {
  try {
    const res = await deletePrizeSource(id)
    console.log('删除道具响应:', res)
    if (res) {
      ElMessage.success('删除道具成功')
      fetchSources()
    }
  } catch (error) {
    console.error('删除道具失败:', error)
    ElMessage.error('删除道具失败')
  }
}

// 道具表单相关
const sourceFormRef = ref(null)
const sourceFormVisible = ref(false)
const isEdit = ref(false)
const editingSourceId = ref(null)

// 道具表单数据
const sourceForm = reactive({
  name: '',
  source_code: '',
  source_type: '', // 默认为空，不再设置默认值
  image_url: '',
  description: '',
  is_active: true
})

// 道具表单验证规则
const sourceRules = {
  name: [
    { required: true, message: '请输入道具名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ]
  // 道具类型不再是必填项
}

// 显示添加道具对话框
const showAddSourceDialog = () => {
  isEdit.value = false
  editingSourceId.value = null

  // 重置表单数据
  sourceForm.name = ''
  sourceForm.source_code = ''
  sourceForm.source_type = '' // 设置为空字符串
  sourceForm.image_url = ''
  sourceForm.description = ''
  sourceForm.is_active = true

  // 延迟显示对话框，确保表单已重置
  setTimeout(() => {
    sourceFormVisible.value = true
    if (sourceFormRef.value) {
      sourceFormRef.value.clearValidate()
    }
  }, 0)
}

// 上传Excel前的处理
const beforeSourceExcelUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                 file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }

  // 上传Excel文件
  uploadSourceExcel(file)
  return false // 阻止默认上传行为
}

// 上传Excel文件
const uploadSourceExcel = async (file) => {
  try {
    const res = await importExcel(file)
    console.log('导入Excel响应:', res)
    if (res) {
      ElMessage.success('导入Excel成功')
      fetchSources()
    }
  } catch (error) {
    console.error('导入Excel失败:', error)
    ElMessage.error('导入Excel失败')
  }
}

// ==================== 奖品管理相关 ====================
// 奖品列表数据
const prizesData = ref([])
const prizeLoading = ref(false)
const prizePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 奖品搜索表单
const prizeSearchForm = reactive({
  search: '',
  prize_type: '',
  source_id: '',
  source_type: '',
  active_only: false,
  page: 1,
  page_size: 10
})

// 奖品类型选项
const rarityTypes = ref([])
const prizeTypes = ref([])
const prizeTypesLoading = ref(false)

// 道具选项（用于下拉选择）
const sourcesOptions = ref([])

// 奖品详情
const prizeDetailVisible = ref(false)
const currentPrize = ref(null)

// 获取奖品类型选项
const fetchPrizeOptions = async () => {
  try {
    const [rarityRes, typeRes] = await Promise.all([
      getRarityTypes(),
      getPrizeTypes()
    ])

    console.log('获取稀有度类型响应:', rarityRes)
    console.log('获取奖品类型响应:', typeRes)

    // 处理稀有度类型
    if (rarityRes) {
      if (Array.isArray(rarityRes)) {
        rarityTypes.value = rarityRes
      } else if (typeof rarityRes === 'object') {
        if (Array.isArray(rarityRes.data)) {
          rarityTypes.value = rarityRes.data
        } else {
          // 尝试转换对象
          const options = []
          for (const key in rarityRes) {
            if (Object.prototype.hasOwnProperty.call(rarityRes, key)) {
              options.push({
                value: key,
                label: rarityRes[key]
              })
            }
          }
          rarityTypes.value = options
        }
      }

      // 确保格式正确
      if (rarityTypes.value.length > 0 && (!rarityTypes.value[0].hasOwnProperty('value') || !rarityTypes.value[0].hasOwnProperty('label'))) {
        rarityTypes.value = rarityTypes.value.map(item => {
          if (typeof item === 'string') {
            return { value: item, label: item }
          } else if (typeof item === 'object') {
            return {
              value: item.value || item.id || item.name || item.code || item,
              label: item.label || item.name || item.display || item.text || item.value || item
            }
          }
          return item
        })
      }
    }

    // 处理奖品类型
    if (typeRes) {
      if (Array.isArray(typeRes)) {
        prizeTypes.value = typeRes
      } else if (typeof typeRes === 'object') {
        if (Array.isArray(typeRes.data)) {
          prizeTypes.value = typeRes.data
        } else if (Array.isArray(typeRes.results)) {
          prizeTypes.value = typeRes.results
        } else {
          // 尝试转换对象
          const options = []
          for (const key in typeRes) {
            if (Object.prototype.hasOwnProperty.call(typeRes, key)) {
              options.push({
                value: key,
                label: typeRes[key]
              })
            }
          }
          prizeTypes.value = options
        }
      }

      // 确保格式正确
      if (prizeTypes.value.length > 0 && (!prizeTypes.value[0].hasOwnProperty('value') || !prizeTypes.value[0].hasOwnProperty('label'))) {
        prizeTypes.value = prizeTypes.value.map(item => {
          if (typeof item === 'string') {
            return { value: item, label: item }
          } else if (typeof item === 'object') {
            return {
              value: item.value || item.id || item.name || item.code || item,
              label: item.label || item.name || item.display || item.text || item.value || item
            }
          }
          return item
        })
      }
    }

    console.log('处理后的稀有度类型:', rarityTypes.value)
    console.log('处理后的奖品类型:', prizeTypes.value)
  } catch (error) {
    console.error('获取奖品选项失败:', error)
    ElMessage.error('获取奖品选项失败')
  }
}

// 搜索奖品类型
const searchPrizeTypes = async (query) => {
  if (query.length < 1) return

  prizeTypesLoading.value = true
  try {
    // 调用奖品类型查询接口
    const res = await getPrizeTypes({ search: query })
    console.log('搜索奖品类型响应:', res)

    if (res) {
      // 确保数据格式正确
      if (Array.isArray(res)) {
        // 直接使用数组
        prizeTypes.value = res
      } else if (typeof res === 'object') {
        // 如果是对象，尝试提取数据
        if (Array.isArray(res.data)) {
          prizeTypes.value = res.data
        } else if (Array.isArray(res.results)) {
          prizeTypes.value = res.results
        } else {
          // 如果没有标准格式，尝试转换对象为数组
          const options = []
          for (const key in res) {
            if (Object.prototype.hasOwnProperty.call(res, key)) {
              options.push({
                value: key,
                label: res[key]
              })
            }
          }
          prizeTypes.value = options
        }
      }

      // 检查每个选项的格式
      if (prizeTypes.value.length > 0) {
        // 如果选项格式不是 {value, label}，尝试转换
        if (!prizeTypes.value[0].hasOwnProperty('value') || !prizeTypes.value[0].hasOwnProperty('label')) {
          prizeTypes.value = prizeTypes.value.map(item => {
            if (typeof item === 'string') {
              return { value: item, label: item }
            } else if (typeof item === 'object') {
              // 尝试从对象中提取 value 和 label
              return {
                value: item.value || item.id || item.name || item.code || item,
                label: item.label || item.name || item.display || item.text || item.value || item
              }
            }
            return item
          })
        }
      }
    }
  } catch (error) {
    console.error('搜索奖品类型失败:', error)
  } finally {
    prizeTypesLoading.value = false
  }
}

// 处理奖品类型下拉框可见性变化
const handlePrizeTypeVisibleChange = async (visible) => {
  if (visible) {
    // 下拉框打开时，加载所有奖品类型
    prizeTypesLoading.value = true
    try {
      const res = await getPrizeTypes()
      console.log('获取所有奖品类型响应:', res)
      console.log('获取所有奖品类型响应数据类型:', typeof res)
      console.log('获取所有奖品类型响应是否为数组:', Array.isArray(res))

      if (res) {
        console.log('更新前的prizeTypes:', prizeTypes.value)

        // 确保数据格式正确
        if (Array.isArray(res)) {
          // 直接使用数组
          prizeTypes.value = res
        } else if (typeof res === 'object') {
          // 如果是对象，尝试提取数据
          if (Array.isArray(res.data)) {
            prizeTypes.value = res.data
          } else if (Array.isArray(res.results)) {
            prizeTypes.value = res.results
          } else {
            // 如果没有标准格式，尝试转换对象为数组
            const options = []
            for (const key in res) {
              if (Object.prototype.hasOwnProperty.call(res, key)) {
                options.push({
                  value: key,
                  label: res[key]
                })
              }
            }
            prizeTypes.value = options
          }
        }

        console.log('更新后的prizeTypes:', prizeTypes.value)

        // 检查每个选项的格式
        if (prizeTypes.value.length > 0) {
          console.log('第一个选项示例:', prizeTypes.value[0])

          // 如果选项格式不是 {value, label}，尝试转换
          if (!prizeTypes.value[0].hasOwnProperty('value') || !prizeTypes.value[0].hasOwnProperty('label')) {
            console.log('选项格式不正确，尝试转换')
            prizeTypes.value = prizeTypes.value.map(item => {
              if (typeof item === 'string') {
                return { value: item, label: item }
              } else if (typeof item === 'object') {
                // 尝试从对象中提取 value 和 label
                return {
                  value: item.value || item.id || item.name || item.code || item,
                  label: item.label || item.name || item.display || item.text || item.value || item
                }
              }
              return item
            })
            console.log('转换后的选项:', prizeTypes.value)
          }
        }
      }
    } catch (error) {
      console.error('获取所有奖品类型失败:', error)
    } finally {
      prizeTypesLoading.value = false
    }
  }
}

// 获取道具选项（用于下拉选择）
const fetchSourceOptions = async () => {
  try {
    const res = await getPrizeSources({ page_size: 100 })
    console.log('获取道具选项响应:', res)
    if (res && res.results) {
      sourcesOptions.value = res.results.map(item => ({
        value: item.id,
        label: item.name
      }))
    }
  } catch (error) {
    console.error('获取道具选项失败:', error)
    ElMessage.error('获取道具选项失败')
  }
}

// 获取奖品列表
const fetchPrizes = async () => {
  prizeLoading.value = true
  try {
    const params = {
      ...prizeSearchForm,
      page: prizePagination.currentPage,
      page_size: prizePagination.pageSize
    }
    console.log('获取奖品列表参数:', params)
    const res = await getPrizes(params)
    console.log('获取奖品列表响应:', res)

    console.log('奖品列表数据:', res)
    if (res && res.results) {
      prizesData.value = res.results
      prizePagination.total = res.count

      // 检查数据是否正确赋值
      console.log('赋值后的奖品数据:', prizesData.value)
    }
  } catch (error) {
    console.error('获取奖品列表失败:', error)
    ElMessage.error('获取奖品列表失败')
  } finally {
    prizeLoading.value = false
  }
}

// 搜索奖品
const searchPrizes = () => {
  prizePagination.currentPage = 1
  fetchPrizes()
}

// 重置奖品搜索
const resetPrizeSearch = () => {
  Object.keys(prizeSearchForm).forEach(key => {
    if (key !== 'page' && key !== 'page_size') {
      prizeSearchForm[key] = ''
    }
  })
  prizeSearchForm.active_only = false
  prizePagination.currentPage = 1
  fetchPrizes()
}

// 处理奖品分页大小变化
const handlePrizeSizeChange = (size) => {
  prizePagination.pageSize = size
  fetchPrizes()
}

// 处理奖品当前页变化
const handlePrizeCurrentChange = (page) => {
  prizePagination.currentPage = page
  fetchPrizes()
}

// 查看奖品详情
const viewPrizeDetail = async (row) => {
  try {
    const res = await getPrizeDetail(row.id)
    console.log('获取奖品详情响应:', res)
    if (res) {
      currentPrize.value = res
      prizeDetailVisible.value = true
    }
  } catch (error) {
    console.error('获取奖品详情失败:', error)
    ElMessage.error('获取奖品详情失败')
  }
}

// 编辑奖品
const editPrize = (row) => {
  isEdit.value = true
  editingPrizeId.value = row.id

  // 打印原始数据，方便调试
  console.log('原始奖品数据:', row)

  // 处理quantity字段，从字符串中提取数字和单位
  let quantity = 1
  if (typeof row.quantity === 'string') {
    const match = row.quantity.match(/(\d+)([个天小时]*)/)
    if (match) {
      quantity = parseInt(match[1], 10)
      quantityUnit.value = match[2] || '个'
    }
  } else if (typeof row.quantity === 'number') {
    quantity = row.quantity
    quantityUnit.value = '个'
  }

  // 填充表单数据
  prizeForm.name = row.name || ''
  prizeForm.prize_code = row.prize_code || ''

  // 处理多个道具
  if (row.sources_info && Array.isArray(row.sources_info)) {
    prizeForm.sources = row.sources_info.map(item => item.source)
  } else if (row.source) {
    // 兼容旧数据
    prizeForm.sources = [row.source]
  } else {
    prizeForm.sources = []
  }

  prizeForm.quantity = quantity
  prizeForm.prize_type = row.prize_type || '其他'
  prizeForm.image_url = row.image_url || ''
  prizeForm.description = row.description || ''
  prizeForm.is_active = row.is_active !== undefined ? row.is_active : true

  // 打印一下表单数据，方便调试
  console.log('编辑奖品表单数据:', prizeForm)

  prizeFormVisible.value = true
}

// 删除奖品
const handleDeletePrize = async (id) => {
  try {
    const res = await deletePrize(id)
    console.log('删除奖品响应:', res)
    if (res) {
      ElMessage.success('删除奖品成功')
      fetchPrizes()
    }
  } catch (error) {
    console.error('删除奖品失败:', error)
    ElMessage.error('删除奖品失败')
  }
}

// 奖品表单相关
const prizeFormRef = ref(null)
const prizeFormVisible = ref(false)
const editingPrizeId = ref(null)
const quantityUnit = ref('个') // 数量单位：个、天、小时等

// 奖品表单数据
const prizeForm = reactive({
  name: '',
  prize_code: '',
  sources: [], // 改为数组，支持多个道具
  quantity: 1,
  prize_type: '其他', // 修改为中文类型
  image_url: '',
  description: '',
  is_active: true
})

// 奖品表单验证规则
const prizeRules = {
  name: [
    { required: true, message: '请输入奖品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ],
  sources: [
    { required: true, message: '请选择所属道具', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个道具', trigger: 'change' }
  ],

  prize_type: [
    { required: true, message: '请选择奖品类型', trigger: 'change' }
  ],

}

// 显示添加奖品对话框
const showAddPrizeDialog = () => {
  isEdit.value = false
  editingPrizeId.value = null

  // 重置表单数据
  prizeForm.name = ''
  prizeForm.prize_code = ''
  prizeForm.sources = []
  prizeForm.quantity = 1
  prizeForm.prize_type = '其他'
  prizeForm.image_url = ''
  prizeForm.description = ''
  prizeForm.is_active = true

  // 重置数量单位
  quantityUnit.value = '个'

  // 延迟显示对话框，确保表单已重置
  setTimeout(() => {
    prizeFormVisible.value = true
    if (prizeFormRef.value) {
      prizeFormRef.value.clearValidate()
    }
  }, 0)
}

// 获取稀有度标签类型
const getRarityTagType = (rarity) => {
  const typeMap = {
    common: 'info',
    rare: 'success',
    epic: 'warning',
    legendary: 'danger'
  }
  return typeMap[rarity] || 'info'
}

// 获取道具名称列表
const getSourcesNames = (sourcesInfo) => {
  if (!sourcesInfo || !Array.isArray(sourcesInfo)) return ''
  return sourcesInfo.map(item => item.source_name).join('、')
}

// 重置道具表单
const resetSourceForm = () => {
  if (!sourceFormRef.value) return
  sourceFormRef.value.resetFields()
  sourceForm.name = ''
  sourceForm.source_code = ''
  sourceForm.source_type = '' // 设置为空字符串
  sourceForm.image_url = ''
  sourceForm.description = ''
  sourceForm.is_active = true
}

// 提交道具表单
const submitSourceForm = async () => {
  if (!sourceFormRef.value) return

  await sourceFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const formData = { ...sourceForm }
        let res

        if (isEdit.value && editingSourceId.value) {
          // 更新道具
          res = await updatePrizeSource(editingSourceId.value, formData)
        } else {
          // 创建道具
          res = await createPrizeSource(formData)
        }

        console.log(isEdit.value ? '更新道具响应:' : '添加道具响应:', res)
        if (res) {
          ElMessage.success(isEdit.value ? '更新道具成功' : '添加道具成功')
          sourceFormVisible.value = false
          fetchSources()
          // 更新道具选项列表
          fetchSourceOptions()
        }
      } catch (error) {
        console.error(isEdit.value ? '更新道具失败:' : '添加道具失败:', error)
        ElMessage.error(isEdit.value ? '更新道具失败' : '添加道具失败')
      }
    }
  })
}

// 上传道具图片前的处理
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
    return false
  }

  // 上传图片
  uploadSourceImage(file)
  return false // 阻止默认上传行为
}

// 上传道具图片
const uploadSourceImage = async (file) => {
  try {
    const res = await uploadImage(file, 'property')
    console.log('上传道具图片响应:', res)
    if (res && res.url) {
      sourceForm.image_url = res.url
      ElMessage.success('上传图片成功')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    ElMessage.error('上传图片失败')
  }
}

// 重置奖品表单
const resetPrizeForm = () => {
  if (!prizeFormRef.value) return
  prizeFormRef.value.resetFields()
  prizeForm.name = ''
  prizeForm.prize_code = ''
  prizeForm.sources = []
  prizeForm.quantity = 1
  prizeForm.prize_type = '其他'
  prizeForm.image_url = ''
  prizeForm.description = ''
  prizeForm.is_active = true
}

// 提交奖品表单
const submitPrizeForm = async () => {
  if (!prizeFormRef.value) return

  await prizeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 创建一个新的表单数据对象，避免直接修改reactive对象
        const formData = { ...prizeForm }

        // 将quantity转换为字符串格式，如 "1个"、"7天"等
        if (typeof formData.quantity === 'number') {
          formData.quantity = `${formData.quantity}${quantityUnit.value}`
        }

        console.log('提交的奖品数据:', formData)

        let res

        if (isEdit.value && editingPrizeId.value) {
          // 更新奖品
          res = await updatePrize(editingPrizeId.value, formData)
        } else {
          // 创建奖品
          res = await createPrize(formData)
        }

        console.log(isEdit.value ? '更新奖品响应:' : '添加奖品响应:', res)
        if (res) {
          ElMessage.success(isEdit.value ? '更新奖品成功' : '添加奖品成功')
          prizeFormVisible.value = false
          fetchPrizes()
        }
      } catch (error) {
        console.error(isEdit.value ? '更新奖品失败:' : '添加奖品失败:', error)
        ElMessage.error(isEdit.value ? '更新奖品失败' : '添加奖品失败')
      }
    }
  })
}

// 上传奖品图片前的处理
const beforePrizeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
    return false
  }

  // 上传图片
  uploadPrizeImage(file)
  return false // 阻止默认上传行为
}

// 上传奖品图片
const uploadPrizeImage = async (file) => {
  try {
    const res = await uploadImage(file, 'prize')
    console.log('上传奖品图片响应:', res)
    if (res && res.url) {
      prizeForm.image_url = res.url
      ElMessage.success('上传图片成功')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    ElMessage.error('上传图片失败')
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    fetchSourceTypes(),
    fetchPrizeOptions(),
    fetchSourceOptions()
  ])

  fetchSources()
  fetchPrizes()
})
</script>

<style lang="scss" scoped>
.prize-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);

  h2 {
    margin: 0;
    font-size: 24px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.content-card {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.search-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.search-form-item-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.search-form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dcdfe6;
}

.upload-inline {
  display: inline-block;
  margin-left: 10px;
}

:deep(.el-table) {
  border-radius: 8px;
  margin-top: 16px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table--border) {
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.detail-container {
  padding: 10px;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-image {
  flex-shrink: 0;
}

.detail-info {
  flex-grow: 1;

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
  }
}

.detail-section {
  margin-top: 20px;

  h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #303133;
    font-size: 16px;
    border-left: 3px solid #409EFF;
    padding-left: 10px;
  }
}

.image-preview {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.quantity-input {
  display: flex;
  align-items: center;
}
</style>
