{"dist-tags": {"latest": "4.0.3"}, "modified": "2024-12-18T22:25:19.413Z", "name": "chokidar", "versions": {"3.5.2": {"name": "chokidar", "version": "3.5.2", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0", "fsevents": "~2.3.2"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@types/node": "^14", "chai": "^4.3", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "dba3976fcadb016f66fd365021d91600d01c1e75", "size": 26222, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.2.tgz", "integrity": "sha512-ekGhOnNVPgT77r4K/U3GDhu+FQ2S8TnK/s2KbIGXi0SZWuwkZ2QNyfWdZW+TVfn84DpEP7rLeCt2UI6bJ8GwbQ=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.5.1": {"name": "chokidar", "version": "3.5.1", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.5.0", "fsevents": "~2.3.1"}, "optionalDependencies": {"fsevents": "~2.3.1"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "ee9ce7bbebd2b79f49f304799d5468e31e14e68a", "size": 26207, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.1.tgz", "integrity": "sha512-9+s+Od+W0VJJzawDma/gvBNQqkTiqYTWLuZoyAsivsI4AaWTCzHG06/TMjsf1cYe9Cb97UCEhjz7HvnPk2p/tw=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.5.0": {"name": "chokidar", "version": "3.5.0", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.5.0", "fsevents": "~2.3.1"}, "optionalDependencies": {"fsevents": "~2.3.1"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "458a4816a415e9d3b3caa4faec2b96a6935a9e65", "size": 26183, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.0.tgz", "integrity": "sha512-JgQM9JS92ZbFR4P90EvmzNpSGhpPBGBSj10PILeDyYFwp4h2/D9OM03wsJ4zW1fEp4ka2DGrnUeD7FuvQ2aZ2Q=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.4.3": {"name": "chokidar", "version": "3.4.3", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.5.0", "fsevents": "~2.1.2"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "c1df38231448e45ca4ac588e6c79573ba6a57d5b", "size": 26229, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.4.3.tgz", "integrity": "sha512-DtM3g7juCXQxFVSNPNByEC2+NImtBuxQQvWlHunpJIS5Ocr0lG306cC7FCi7cEA0fzmybPUIl4txBIobk1gGOQ=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.4.2": {"name": "chokidar", "version": "3.4.2", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.4.0", "fsevents": "~2.1.2"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "38dc8e658dec3809741eb3ef7bb0a47fe424232d", "size": 26229, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.4.2.tgz", "integrity": "sha512-IZHaDeBeI+sZJRX7lGcXsdzgvZqKv6sECqsbErJA4mHWfpRrD8B97kSFN4cQz6nGBGiuFia1MKR4d6c1o8Cv7A=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.4.1": {"name": "chokidar", "version": "3.4.1", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.4.0", "fsevents": "~2.1.2"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "devDependencies": {"@types/node": "^14", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "e905bdecf10eaa0a0b1db0c664481cc4cbc22ba1", "size": 26089, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.4.1.tgz", "integrity": "sha512-TQTJyr2stihpC4Sya9hs2Xh+O2wf+igjL36Y75xx2WdHuiICcn/XJza46Jwt0eT5hVpQOzo3FpY3cj3RVYLX0g=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.4.0": {"name": "chokidar", "version": "3.4.0", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.4.0", "fsevents": "~2.1.2"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "devDependencies": {"@types/node": "^13", "chai": "^4.2", "dtslint": "^3.3.0", "eslint": "^6.6.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "b30611423ce376357c765b9b8f904b9fba3c0be8", "size": 25782, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.4.0.tgz", "integrity": "sha512-aXAaho2VJtisB/1fg1+3nlLJqGOuewTzQpd/Tz0yTg2R0e4IGtshYvtjowyEumcBv2z+y4+kc75Mz7j5xJskcQ=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.3.1": {"name": "chokidar", "version": "3.3.1", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.3.0", "fsevents": "~2.1.2"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "dtslint": "^2.0.0", "eslint": "^6.6.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "rimraf": "^3.0.0", "sinon": "^7.5.0", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "c84e5b3d18d9a4d77558fef466b1bf16bbeb3450", "size": 25608, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.3.1.tgz", "integrity": "sha512-4QYCEWOcK3OJrxwvyyAOxFuhpvOVCYkr33LPfFNBjAD/w3sEzWsp2BUOkI4l9bHvWioAd0rc6NlHUOEaWkTeqg=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.3.0": {"name": "chokidar", "version": "3.3.0", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.2.0", "fsevents": "~2.1.1"}, "optionalDependencies": {"fsevents": "~2.1.1"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "dtslint": "1.0.2", "eslint": "^6.6.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "rimraf": "^3.0.0", "sinon": "^7.5.0", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "12c0714668c55800f659e262d4962a97faf554a6", "size": 25567, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.3.0.tgz", "integrity": "sha512-dGmKLDdT3Gdl7fBUe8XK+gAtGmzy5Fn0XkkWQuYxGIgWVPPse2CxFA5mtrlD0TOHaHjEUqkWNyP1XdHoJES/4A=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.2.3": {"name": "chokidar", "version": "3.2.3", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.2.0", "fsevents": "~2.1.1"}, "optionalDependencies": {"fsevents": "~2.1.1"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "dtslint": "0.9.9", "eslint": "^6.6.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "rimraf": "^3.0.0", "sinon": "^7.5.0", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "directories": {}, "dist": {"shasum": "b9270a565d14f02f6bfdd537a6a2bbf5549b8c8c", "size": 25452, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.2.3.tgz", "integrity": "sha512-GtrxGuRf6bzHQmXWRepvsGnXpkQkVU+D2/9a7dAe4a7v1NhrfZOZ2oKf76M3nOs46fFYL8D+Q8JYA4GYeJ8Cjw=="}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.2.2": {"name": "chokidar", "version": "3.2.2", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.2.0", "fsevents": "~2.1.1"}, "optionalDependencies": {"fsevents": "~2.1.1"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "coveralls": "^3", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.6.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "a433973350021e09f2b853a2287781022c0dc935", "size": 25206, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.2.2.tgz", "integrity": "sha512-bw3pm7kZ2Wa6+jQWYP/c7bAZy3i4GwiIiMO2EeRjrE48l8vBqC/WvFhSF0xyM8fQiPEGvwMY/5bqDG7sSEOuhg=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.2.1": {"name": "chokidar", "version": "3.2.1", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.1.3", "fsevents": "~2.1.0"}, "optionalDependencies": {"fsevents": "~2.1.0"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "coveralls": "^3", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.6.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "4634772a1924512d990d4505957bf3a510611387", "size": 24590, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.2.1.tgz", "integrity": "sha512-/j5PPkb5Feyps9e+jo07jUZGvkB5Aj953NrI4s8xSVScrAo/RHeILrtdb4uzR7N6aaFFxxJ+gt8mA8HfNpw76w=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.2.0": {"name": "chokidar", "version": "3.2.0", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.1.3", "fsevents": "~2.1.0"}, "optionalDependencies": {"fsevents": "~2.1.0"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "coveralls": "^3", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.6.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "a0b08f9b770c082def0100e0fbcef322ad156a8a", "size": 24579, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.2.0.tgz", "integrity": "sha512-ViJAmAatEHwGCbBa8coPyRY6yiQ+yryuHT9XnkV6l0ct140Tf6JguJ0LtgoJrjxr9I9utL8IRuqbjhDJAeWIeg=="}, "engines": {"node": ">= 8.16"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.1": {"name": "chokidar", "version": "3.1.1", "dependencies": {"anymatch": "^3.1.0", "braces": "^3.0.2", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "is-glob": "^4.0.1", "normalize-path": "^3.0.0", "readdirp": "^3.1.1", "fsevents": "^2.0.6"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "coveralls": "^3", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.6.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "27e953f3950336efcc455fd03e240c7299062003", "size": 24057, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.1.1.tgz", "integrity": "sha512-df4o16uZmMHzVQwECZRHwfguOt5ixpuQVaZHjYMvYisgKhE+JXwcj/Tcr3+3bu/XeOJQ9ycYmzu7Mv8XrGxJDQ=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.1.0": {"name": "chokidar", "version": "3.1.0", "dependencies": {"anymatch": "^3.1.0", "braces": "^3.0.2", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "is-glob": "^4.0.1", "normalize-path": "^3.0.0", "readdirp": "^3.1.1", "fsevents": "^2.0.6"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "coveralls": "^3", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.6.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "ff23d077682a90eadd209bfa76eb10ed6d359668", "size": 24071, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.1.0.tgz", "integrity": "sha512-6vZfo+7W0EOlbSo0nhVKMz4yyssrwiPbBZ8wj1lq8/+l4ZhGZ2U4Md7PspvmijXp1a26D3B7AHEBmIB7aVtaOQ=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.8": {"name": "chokidar", "version": "2.1.8", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "804b3a7b6a99358c3c5c61e71d8728f041cff917", "size": 25329, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.8.tgz", "integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.2": {"name": "chokidar", "version": "3.0.2", "dependencies": {"anymatch": "^3.0.1", "braces": "^3.0.2", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "is-glob": "^4.0.1", "normalize-path": "^3.0.0", "readdirp": "^3.1.1", "fsevents": "^2.0.6"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "coveralls": "^3", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.6.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "0d1cd6d04eb2df0327446188cd13736a3367d681", "size": 24337, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.0.2.tgz", "integrity": "sha512-c4PR2egjNjI1um6bamCQ6bUNPDiyofNQruHvKgHQ4gDUP/ITSVSzNsiI5OWtHOsX323i5ha/kk4YmOZ1Ktg7KA=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.1": {"name": "chokidar", "version": "3.0.1", "dependencies": {"anymatch": "^3.0.1", "async-each": "^1.0.3", "braces": "^3.0.2", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "is-glob": "^4.0.1", "normalize-path": "^3.0.0", "readdirp": "^3.0.2", "fsevents": "^2.0.6"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "devDependencies": {"@types/node": "^11.13.4", "chai": "^4.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.4.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "98fe9aa476c55d9aea7841d6325ffdb30e95b40c", "size": 24211, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.0.1.tgz", "integrity": "sha512-2ww34sJWehnbpV0Q4k4V5Hh7juo7po6z7LUWkcIQnSGN1lHOL8GGtLtfwabKvLFQw/hbSUQ0u6V7OgGYgBzlkQ=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.6": {"name": "chokidar", "version": "2.1.6", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "b6cad653a929e244ce8a834244164d241fa954c5", "size": 26129, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.6.tgz", "integrity": "sha512-V2jUo67OKkc6ySiRpJrjlpJKl9kDuG+Xb8VgsGzb+aEouhgS1D0weyPU4lEzdAcsCAvrih2J2BqyXqHWvVLw5g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.0.0": {"name": "chokidar", "version": "3.0.0", "dependencies": {"anymatch": "^3.0.1", "async-each": "^1.0.3", "braces": "^3.0.2", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "is-glob": "^4.0.1", "normalize-path": "^3.0.0", "readdirp": "^3.0.1", "fsevents": "^2.0.6"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "devDependencies": {"@types/node": "^11.13.4", "chai": "^4.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "jshint": "^2.10.1", "mocha": "^6.1.3", "nyc": "^14.0.0", "rimraf": "^2.4.3", "sinon": "^7.3.1", "sinon-chai": "^3.3.0", "upath": "^1.1.2"}, "directories": {}, "dist": {"shasum": "6b538f0fd6d5d31d5dd2b59e05426bec0f49aa40", "size": 23768, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.0.0.tgz", "integrity": "sha512-ebzWopcacB2J19Jsb5RPtMrzmjUZ5VAQnsL0Ztrix3lswozHbiDp+1Lg3AWSKHdwsps/W2vtshA/x3I827F78g=="}, "engines": {"node": ">= 8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.5": {"name": "chokidar", "version": "2.1.5", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "0ae8434d962281a5f56c72869e79cb6d9d86ad4d", "size": 26091, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.5.tgz", "integrity": "sha512-i0TprVWp+Kj4WRPtInjexJ8Q+BqTE909VpH8xVhXrJkoc5QC8VO9TryGOqTr+2hljzc1sC62t22h5tZePodM/A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.4": {"name": "chokidar", "version": "2.1.4", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "244a65d68beedf1078c2f193cf2735c2cfdc7da2", "size": 25970, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.4.tgz", "integrity": "sha512-ZVuFiB9IGOHqu+Jh7B7fSTmzsfDmUtC6yqd/V72UPQeXbNHONbMV0chOXtLXjsP3NvdUsHTNMg02NtXPDaSmNQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.3": {"name": "chokidar", "version": "2.1.3", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "b2e65fc8cbdbb0c57a7d8147ad20077800ec60db", "size": 25787, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.3.tgz", "integrity": "sha512-Zkdgeb8pJCL4FG7fCIkD1ua1SENYGAqKQaqyMxkgVnU0IIWthjDVbSgcW5OY1IzgJ/Aatqjgl4O9iTZUeEJK5w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.2": {"name": "chokidar", "version": "2.1.2", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.0", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"@types/node": "^11.9.4", "chai": "^3.2.0", "coveralls": "^3.0.1", "dtslint": "0.4.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "9c23ea40b01638439e0513864d362aeacc5ad058", "size": 25878, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.2.tgz", "integrity": "sha512-IwXUx0FXc5ibYmPC2XeEj5mpXoV66sR+t3jqu2NS2GYwCktt3KF1/Qqjws/NkegajBA4RbZ5+DDwlOiJsxDHEg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.1": {"name": "chokidar", "version": "2.1.1", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.0", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^3.0.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "adc39ad55a2adf26548bd2afa048f611091f9184", "size": 23788, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.1.tgz", "integrity": "sha512-gfw3p2oQV2wEt+8VuMlNsPjCxDxvvgnm/kz+uATu805mWVF8IJN7uz9DN7iBz+RMJISmiVbCOBFs9qBGMjtPfQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.1.0": {"name": "chokidar", "version": "2.1.0", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.0", "fsevents": "^1.2.7"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^3.0.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "5fcb70d0b28ebe0867eb0f09d5f6a08f29a1efa0", "size": 23541, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.0.tgz", "integrity": "sha512-5t6G2SH8eO6lCvYOoUpaRnF5Qfd//gd7qJAkwRUw9qlGVkiQ13uwQngqbWWaurOsaAm9+kUGbITADxt6H0XFNQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.4": {"name": "chokidar", "version": "2.0.4", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "lodash.debounce": "^4.0.8", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "^1.0.5", "fsevents": "^1.2.2"}, "optionalDependencies": {"fsevents": "^1.2.2"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^3.0.1", "graceful-fs": "4.1.4", "mocha": "^5.2.0", "nyc": "^11.8.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "356ff4e2b0e8e43e322d18a372460bbcf3accd26", "size": 23609, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.0.4.tgz", "integrity": "sha512-z9n7yt9rOvIJrMhvDtDictKrkFHeihkNl6uWMmZlmL6tJtX9Cs+87oK+teBx+JIgzvbX3yZHT3eF8vpbDxHJXQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.3": {"name": "chokidar", "version": "2.0.3", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "^1.0.0", "fsevents": "^1.1.2"}, "optionalDependencies": {"fsevents": "^1.1.2"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "dcbd4f6cbb2a55b4799ba8a840ac527e5f4b1176", "size": 23422, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.0.3.tgz", "integrity": "sha512-zW8iXYZtXMx4kux/nuZVXjkLP+CyIK5Al5FHnj1OgTKGZfp4Oy6/ymtMSKFv3GD8DviEmUPmJg9eFdJ/JzudMg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.2": {"name": "chokidar", "version": "2.0.2", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "^1.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "4dc65139eeb2714977735b6a35d06e97b494dfd7", "size": 23468, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.0.2.tgz", "integrity": "sha512-l32Hw3wqB0L2kGVmSbK/a+xXLDrUEsc84pSgMkmwygHvD7ubRsP/vxxHa5BtB6oix1XLLVCHyYMsckRXxThmZw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "2.0.1": {"name": "chokidar", "version": "2.0.1", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "1.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "6e67e9998fe10e8f651e975ca62460456ff8e297", "size": 23424, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.0.1.tgz", "integrity": "sha512-rv5iP8ENhpqvDWr677rAXcB+SMoPQ1urd4ch79+PhM4lQwbATdJUQK69t0lJIKNB+VXpqxt5V1gvqs59XEPKnw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "2.0.0": {"name": "chokidar", "version": "2.0.0", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0", "upath": "1.0.0"}, "directories": {}, "dist": {"shasum": "6686313c541d3274b2a5c01233342037948c911b", "size": 23323, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-2.0.0.tgz", "integrity": "sha512-OgXCNv2U6TnG04D3tth0gsvdbV4zdbxFG3sYUqcoQMoEFVd1j1pZR6TZ8iknC45o9IJ6PeQI/J6wT/+cHcniAw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.7.0": {"name": "chokidar", "version": "1.7.0", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "798e689778151c8076b4b360e5edd28cda2bb468", "size": 22861, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.7.0.tgz", "integrity": "sha512-mk8fAWcRUOxY7btlLtitj3A45jOwSAxH4tOFOoEGbVsl6cL6pPMWUy7dwZ/canfj3QEdP6FHSnf/l1c6/WkzVg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.6.1": {"name": "chokidar", "version": "1.6.1", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "2f4447ab5e96e50fb3d789fd90d4c72e0e4c70c2", "size": 22730, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.6.1.tgz", "integrity": "sha512-/6SIsjBGK5mzf1i1L8ccsH0jZuzWvMump0iJ6LD3jYxhwiLjvJ+5GrpJNxay9MGRvTAoYmzLU/z19wyxEjfv1w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.6.0": {"name": "chokidar", "version": "1.6.0", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "90c32ad4802901d7713de532dc284e96a63ad058", "size": 22692, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.6.0.tgz", "integrity": "sha512-gX708zvyHflAXYD6Nnp4oa3crBOzM4vidTnkBslaJLYRpFKF/36MpPGWT7KkWfiGsq85wJ5AdhixiQgKOI/9Fw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.5.2": {"name": "chokidar", "version": "1.5.2", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "293e728640cc93dd8277424334b3c6d4ad3a348a", "size": 17392, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.5.2.tgz", "integrity": "sha512-Hc3T2qGwrnnZORa+Gmnoa5yGkr4cqYURAYBC/RXxPBAeO12C7/stcWXhjvxSnE3gTJMZvSOimFFarZUdd08NfA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.5.1": {"name": "chokidar", "version": "1.5.1", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "43115fcf2d8fb74f06b630aeeccd06715a146dd1", "size": 17174, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.5.1.tgz", "integrity": "sha512-4+UfpWJOkn2JZEY6UXL40BrFm8mNr834P/JLOgMk/DWGCGruL42TDmsBF5kFyZdPER2amGEGMhL7hEgIpWs4lA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.5.0": {"name": "chokidar", "version": "1.5.0", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "7a5f1a72e6ee3e1daffdae74832e8eb28ee2f19a", "size": 16875, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.5.0.tgz", "integrity": "sha512-pq65J0fZXsOA2WiZPTIsfliXtsfJHKQYUh6/9brkaV7CORzlHCVRXU8aTBrHJdiWDZGWsxjzK8Edbi1WQfDhyA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.3": {"name": "chokidar", "version": "1.4.3", "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "5fe733a4d9acaea51b26454b7e59559163d0dbb2", "size": 21744, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.4.3.tgz", "integrity": "sha512-Ex9kw8n2e5+rJUMkAAF2U4ZHn75kGltEgYPCG1jeMiSkec7XbPOtmBF1E66V+uzRcfN+qKTqbZ6RomuFIZrhCA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.2": {"name": "chokidar", "version": "1.4.2", "dependencies": {"anymatch": "^1.3.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "3eaea6c2898fa7208184a453d4889a9addf567d2", "size": 21661, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.4.2.tgz", "integrity": "sha512-OzygdyPQjlOargfc6HJvFqkUq1S5mOiuDrGSLPLeKWQiOG/IVP1uWwLuqN9iSeozSdMxcIoEVdABaSn+y9U2lA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.1": {"name": "chokidar", "version": "1.4.1", "dependencies": {"anymatch": "^1.3.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "df1d906769701a0f3df492c37dcc3cb35e6450e4", "size": 21604, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.4.1.tgz", "integrity": "sha512-5ltk/EAsPN3EJCMtaKsGC29j0mUUtCOjUajWsnPfQNTspEscbLCTVSVV/6AvE1F7kjjUO+SlfE9KP0XIxJkP2w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.4.0": {"name": "chokidar", "version": "1.4.0", "dependencies": {"anymatch": "^1.3.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "bb6fc855413c2e6ca5f54250103a692f2f64f51e", "size": 21550, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.4.0.tgz", "integrity": "sha512-ZFvBXIe00z6UHTyZTvtLb7UKbjWYqVC3ZWixSIHwJQkH5E6TenKoVklsnhR2TWBJVruyXN1ODgioRPRdTxlwhw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.3.0": {"name": "chokidar", "version": "1.3.0", "dependencies": {"anymatch": "^1.3.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "a7c2af0f4234b5d83e3491e403817a88d517e4ef", "size": 20866, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.3.0.tgz", "integrity": "sha512-6IzBBaG0l2O+z3AOfMzvAKLB6bEXW+K7oB9JXJ8De/ibiyqP/TCh8PbTuoUSL2agbdZ5/DeMoaEG+MEV3Euf0Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.2.0": {"name": "chokidar", "version": "1.2.0", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "lodash.flatten": "^3.0.2", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "d7cc02d05e94092ddfacad488ebebe588ff2ff30", "size": 20475, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.2.0.tgz", "integrity": "sha512-7O2ZUP1+4g8rJQre2LkG0hJCfY8aLuutetFoitwj0sDzzc6lL2D4nyah6JmIu+PmG4gcxUIhWxDb68mdIUoLFA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.1.0": {"name": "chokidar", "version": "1.1.0", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "fsevents": "^1.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "istanbul": "^0.3.20", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "15aa9169424828895c6c8aee4eee8d8156820b9f", "size": 18826, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.1.0.tgz", "integrity": "sha512-mZ5ef/iDew7YO4oaiN5jDwW1Ttwx2uTMXb528JF7Wl+Titpm/imo7wY+Mxt+0n527h5CUzXlNCJv1Hdax/T7fg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.6": {"name": "chokidar", "version": "1.0.6", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "path-is-absolute": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.8"}, "optionalDependencies": {"fsevents": "^0.3.8"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "0a1c0bce1e24993afc105a5b81ea26dda01e23af", "size": 18595, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.6.tgz", "integrity": "sha512-Fz5EFUY/h6X5ZUsUl2wtokjAax21dsj1RrmDidVWts8vvFaroEHSFmBK87Iqah56XDIinsh/n82S9r5kZmy4DA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.5": {"name": "chokidar", "version": "1.0.5", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "path-is-absolute": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "f29278a36e174365da56ccad488ecacce4893494", "size": 18737, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.5.tgz", "integrity": "sha512-bsPSV7fQoq/fdSas3Ld1nQAkezda4BvUcUM+tV10bw55dBYrfR2WMLSY/ZZqbUcykl6qvNbEmHGf7FTq4AWJ1g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.4": {"name": "chokidar", "version": "1.0.4", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "path-is-absolute": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "bd9fbb1ef0cccc60e2da10c9aeafad6f2c72043f", "size": 18686, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.4.tgz", "integrity": "sha512-45auOKIEL7StIyNKtQi/07E2HZo86LgpcOl30SNabmdbbYnZV34zIejMuhJhAIg2LKPOVM4gUlLMNmCfJT9Gfw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.3": {"name": "chokidar", "version": "1.0.3", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "path-is-absolute": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "ba63580caeb89bbdf869eab51bbca4f3ca441be8", "size": 18600, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.3.tgz", "integrity": "sha512-qb19c7VYGByaxL4ApVnbRle7g5bPvg8MrgKo0DhVO60NLgmYllQFP77HX/Fech74ygCDaIWR0BI9wEliCLrurQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.2": {"name": "chokidar", "version": "1.0.2", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "path-is-absolute": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "c5d4f226ccd0044a16278498eb009c0715ce48c1", "size": 18512, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.2.tgz", "integrity": "sha512-PYSwZWlqEPhNDwqvCfkj/O+te0FIIIgQEgHtwA9lT68EhjquOgMsrie/tygnlnD8QWFKaLf5JcgKeOKvxGgfTw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.1": {"name": "chokidar", "version": "1.0.1", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "b19e476a071ac0c7a01279cdc936e0d31c6ee06a", "size": 18284, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.1.tgz", "integrity": "sha512-3oejTYOC9bXBLTfepjySaTtENhSCLzSqz8e5zxzKPtiHOOxtNKwnmLY2fLHzzgsy70al1MYvJWXSv3qaQvM+rw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.0": {"name": "chokidar", "version": "1.0.0", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "4af904c033eda714142b8ac371498b939923f8c9", "size": 18244, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0.tgz", "integrity": "sha512-o4b924x32FEt3XGlA+tf89c2VrYqEot7AQQucAPGZq6GHkO/MBEpDAcOq4p4lmFPYP9V2fLFyP4nS+WUsp8eFg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.0.0-rc5": {"name": "chokidar", "version": "1.0.0-rc5", "dependencies": {"anymatch": "^1.1.0", "arrify": "^1.0.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "is-glob": "^1.1.3", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "45cb28ae7478b981d739b35f1e18d687aeb55802", "size": 18043, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0-rc5.tgz", "integrity": "sha512-JRTdNSzGWE/LTY/q8QPAZOg23Q7DFx+b0qCvn+xtVqKdKug+I9yY0GNgEj7J5ZN3rj21Ef3ou0AQKFl6/9iDHw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.0-rc4": {"name": "chokidar", "version": "1.0.0-rc4", "dependencies": {"anymatch": "^1.1.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "312378ae793e6320591f230f0f69c0de93d02b6e", "size": 16633, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0-rc4.tgz", "integrity": "sha512-pVJXqZyS5bNdRVyZqpgm8ZWQyrJHyVysnt8BGMxdTa7gA1bdx807wsjne30EmknsrEF/lvnnlHk8OinnC3E+ZA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.0-rc3": {"name": "chokidar", "version": "1.0.0-rc3", "dependencies": {"anymatch": "^1.1.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "readdirp": "^1.3.0", "fsevents": "^0.3.1"}, "optionalDependencies": {"fsevents": "^0.3.1"}, "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.0.0", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "directories": {}, "dist": {"shasum": "f95d5e60c7d66eb53136c8999c47e9d4f37118f5", "size": 18380, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0-rc3.tgz", "integrity": "sha512-oj+7cyPncU3XJzgVxWOG9cMJscy0i79yA/QCghVrMB+w+lK+6xdQDGOp85FuNidS6eOdea8KKO9NcVOBqGwLIA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.0-rc2": {"name": "chokidar", "version": "1.0.0-rc2", "dependencies": {"anymatch": "~1.1.0", "async-each": "~0.1.5", "glob-parent": "~1.0.0", "readdirp": "~1.3.0", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"chai": "~1.9.2", "coveralls": "~2.11.2", "istanbul": "~0.3.5", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "7adc3966f8b97492a95b7d8b98ba2c21cbdaa7a1", "size": 16531, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0-rc2.tgz", "integrity": "sha512-wa1GKFymb2ND+HnWsCK58/tpCiYXO0FLq6N9vYvi9DvafVn/++F38sdWmMDyHqh8gGUQS5weENvR9e/HS3bRnA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.0-rc1.1": {"name": "chokidar", "version": "1.0.0-rc1.1", "dependencies": {"anymatch": "~1.1.0", "async-each": "~0.1.5", "glob-parent": "~1.0.0", "readdirp": "~1.3.0", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"chai": "~1.9.2", "coveralls": "~2.11.2", "istanbul": "~0.3.5", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "e4ec078b2a10778963f94c00e5dca1696e6842cc", "size": 16528, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0-rc1.1.tgz", "integrity": "sha512-TFt92e2iauhdo/gDRqAR6gqyuHhGQe2MruYo/s6kW7GOOoA0MQ0oc+xg6pEWgM+1H6xobcm0j3uESwoQbML19w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "1.0.0-rc1": {"name": "chokidar", "version": "1.0.0-rc1", "dependencies": {"anymatch": "~1.1.0", "async-each": "~0.1.5", "glob-parent": "~1.0.0", "readdirp": "~1.3.0", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"chai": "~1.9.2", "coveralls": "~2.11.2", "istanbul": "~0.3.5", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "b1b447f2b388375820d8e766dd918bd109ddeb87", "size": 4369, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-1.0.0-rc1.tgz", "integrity": "sha512-osvJYJ4Eh1994GDDZAahcrrid0x6w2AW/tWNjV6yRwGCCyoLGsRt9PZlspG4CARy79yDNcCbKBe/hiGADaQ/bg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.6": {"name": "chokidar", "version": "0.12.6", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.3.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "be204f5b9634e009311256e5d6e8e0e508284d2f", "size": 12689, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.6.tgz", "integrity": "sha512-nMQqT43NJ46Yc75wx9pQ7p/yhzg/5qXHPcgHKUcTLCdcUr1xMUM92SSY4sfGNpJ2zRHQqUsk1hnlP4GW8mm0Aw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.5": {"name": "chokidar", "version": "0.12.5", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.3.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "69ce0c49752dc4d32b5a45023b4b83661f858107", "size": 12697, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.5.tgz", "integrity": "sha512-T8nlJ080ztlzS+7j5+3L1G9pFi79I4wL4mKbFdTVMNFA6ywM2yKjpEPPiMUw4T+bXGJiTI46hXJVzCDbEpCBTw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.4": {"name": "chokidar", "version": "0.12.4", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.3.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "572e9299c5f820ebb02d2ba1e5d06a52bea75706", "size": 12700, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.4.tgz", "integrity": "sha512-b2CvF9QRrjjeg4rYU60jNJ04jWWBln9msii1QSKYjNFoBV6mazIIL9vW2hJKmo4eVhXz0oasm7ew0gcHc6fD6Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.3": {"name": "chokidar", "version": "0.12.3", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.3.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "02987da6cae9ebc012370a225a1184ad69940683", "size": 12694, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.3.tgz", "integrity": "sha512-ZNqGPHSIsk1pHA3EUGftftoUg9C2DpZBOEkHxJp7lUCIhXqDht/8hm4fmcl2JAI0Se+tlRR18vZ53Ld4iLwqig=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.2": {"name": "chokidar", "version": "0.12.2", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.3.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "9ea1719bf2cc41136ea6e8a3dc7d08ecf36c8c12", "size": 12694, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.2.tgz", "integrity": "sha512-WpS8oMsv8evIbg+mJQztuaAHL3Jlf9Y94apqZ0wj062p4lWkYdOfOQfXMXq0qvcZYfUmFNxrY/CI3rqW43PtOA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.1": {"name": "chokidar", "version": "0.12.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.2.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "b988914fc44d2869334fbc3b7e72256c37ab6d54", "size": 12621, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.1.tgz", "integrity": "sha512-JOFOHWHUgU7XLoht6Z0/4VsMEKxNyZNCj7GJpRkgEahacWxcF4sBTN/h2P3idgO1SFkvoF59gRmwJUg801Rfaw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.12.0": {"name": "chokidar", "version": "0.12.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.2.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "71dbee9c5c6867f6b3cab081ce9f05ef9b8a0532", "size": 12663, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.12.0.tgz", "integrity": "sha512-Y9iTb7/Hzv/y874NLyeKp8r4lEKoGFbGrG3jQAOKZiIlI3uW12aXfa/H0YSsja9uDMipVDjRZQFJHBPHTzhUgQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.11.1": {"name": "chokidar", "version": "0.11.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "b00e01717de445783782ef5c48a803e05fed0fc4", "size": 10390, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.11.1.tgz", "integrity": "sha512-MSSZgpYMN/MJalJ19zAmSxlxYkfpOTnt7xutjQTuQ3YCf5/PypW3KA6PwpXmi4G3RqB9bnrNJaP59iOjZuet8Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.11.0": {"name": "chokidar", "version": "0.11.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "9a556c68e426ff4d13841d4bc826819fa2f2f61d", "size": 10392, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.11.0.tgz", "integrity": "sha512-G5rfDJPTOMb0TdOLOATBf3+EbXCRA52Ictqm2C6cJ7tZY3+ZmsdZ9q4XsRy9cK5s098FoqFx9t3AZXJlIzYulw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.9": {"name": "chokidar", "version": "0.10.9", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "7e53db756aee61a731365c00753a35d2513e4fee", "size": 10091, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.9.tgz", "integrity": "sha512-q68F/4UvB3CNA2PXfSMXE1jrzSx79Y2ggJmz/3pv39i8Luit8j1P753aIE/I16mSDg+oJbDrWbFhsyDwq1u8rg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.8": {"name": "chokidar", "version": "0.10.8", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "f19abef62746ec4e252195ff1754df4351ad2ef8", "size": 10146, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.8.tgz", "integrity": "sha512-JnP5wCPWR3Xdn78n+LHrwipWhVnaE5g38S5EFEG66pBOatLAodco8wLgupioU39yBNRTytebqyJ/NmHDGrK6iA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.7": {"name": "chokidar", "version": "0.10.7", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "50a097e8bc1dd0297bc6b7d8c21aa4b9fdaa068d", "size": 10071, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.7.tgz", "integrity": "sha512-rD/ZvcgNZXsiTQhp+ofnmqvJbN1ik/eNu0g4a7vIGDD+H9OF1HYkr7EG6AEXOH1BR0V4sLnllMnHjviKILgy3A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.6": {"name": "chokidar", "version": "0.10.6", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "e911c29d27bb422e61c3d2eaf621faae23c3741d", "size": 9511, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.6.tgz", "integrity": "sha512-8DsXKR6XJaf1IQ8+u3YH1jV2+qHp5r+J5vUrRO7fefHsPTC/MBzI/mWJwVS0IZWgfdvikoassHImCn2VnpyWyA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.5": {"name": "chokidar", "version": "0.10.5", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"readdirp": "~1.1.0", "async-each": "~0.1.5", "fsevents": "~0.3.1"}, "optionalDependencies": {"fsevents": "~0.3.1"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "4a5f343c4562cd2c48fa9e6531c3feebe21eaf79", "size": 9140, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.5.tgz", "integrity": "sha512-NuUuBpAabKdzrl9ee9eQ9mjSlWHUGvx7UIZcc6wFyOd0Nz4d0DGDsZwm4bt7Tu2/69ds33Jlx/zrwhpT88RgEg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.4": {"name": "chokidar", "version": "0.10.4", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"async-each": "~0.1.5", "fsevents": "~0.3.1", "readdirp": "~1.1.0"}, "optionalDependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "f223829f5511b79ac0f888e981a93b241d01c89d", "size": 9013, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.4.tgz", "integrity": "sha512-lOAU4qAlVco4hRTKinevYW6400zTV4EAp4qrI2vnBZVWaG+H3F73ZrNpUR9q+6Rm/0puZ0T5lniDjTtC/ciiAQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.3": {"name": "chokidar", "version": "0.10.3", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"async-each": "~0.1.5", "fsevents": "0.3.0", "readdirp": "~1.1.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "5f228db51820140d0f9f712bd2c29f4fbc1bba49", "size": 9014, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.3.tgz", "integrity": "sha512-b<PERSON><PERSON>bhuRBaXwKI2b9ngLGtyNDa25KxWk2vk81ERdvHUUWMwgPC+TqyBhSRAcT+Cgjt6J3W1UNnqB5AmuE2DuZWQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.2": {"name": "chokidar", "version": "0.10.2", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"async-each": "~0.1.5", "fsevents": "0.3.0", "readdirp": "~1.1.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "devDependencies": {"mocha": "~2.0.0", "chai": "~1.9.2", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "directories": {}, "dist": {"shasum": "54b4221d6c8e0c57f2d2e432f8212d64d205b240", "size": 8762, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.2.tgz", "integrity": "sha512-YoYTjmdG1xhbUuEGCAI/we1IlehPMymu5PbEJhWPBkI7S17Ajn0dzlvfGAYbCxieoD1oKdpCHs5y4KLQ4A2uyA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.1": {"name": "chokidar", "version": "0.10.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "ec2b4e9910c75a2b2e09ff5fdf283029b73af199", "size": 8427, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.1.tgz", "integrity": "sha512-hoY1bxXuSbHKoj7Usj+Me2rPUaCXQSik/d967IS//7QkVjrp7BiGKZw0h6slr3Q9iCr1LrV1IYtpuuJxfDyiiA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.10.0": {"name": "chokidar", "version": "0.10.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "0079e9cd2c92f65f527893117ae63cc6fd03292e", "size": 8430, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.10.0.tgz", "integrity": "sha512-0DIxXwg5kDmEr5PFfQ7LStYE9HxDc2h3zqd++7qrtXTmXPZADRlUDhGSUuhpVbM0/Uskeb8rV9lq5ghsKywkXg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "es128", "email": "<EMAIL>"}}, "0.9.0": {"name": "chokidar", "version": "0.9.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"fsevents": "0.3.0", "recursive-readdir": "0.0.2"}, "optionalDependencies": {"fsevents": "0.3.0", "recursive-readdir": "0.0.2"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "c1ae41561dbdb89dd5fac615453d20b48a946c2f", "size": 10155, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.9.0.tgz", "integrity": "sha512-lo5j8BRespGYddDgKnzjblnrh9wzCacnegaIopE2RvoAR0+W20bF1jeRVlhOUHdG/y4RbOcEkoWytfiVbUpk6g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.4": {"name": "chokidar", "version": "0.8.4", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"fsevents": "pipobscure/fsevents#7dcdf9fa3f8956610fd6f69f72c67bace2de7138", "recursive-readdir": "0.0.2"}, "optionalDependencies": {"fsevents": "pipobscure/fsevents#7dcdf9fa3f8956610fd6f69f72c67bace2de7138", "recursive-readdir": "0.0.2"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "3b2b5066817086534ba81a092bdcf4be25b8bee0", "size": 9988, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.8.4.tgz", "integrity": "sha512-auWFkYQV6w4821+YQATR3ueTMRPMAK9qVzl9cyE1O7dM7l2/NI6IIDcvPrER+RzdgoCi5b9aCy3btoZFsLVJbw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.3": {"name": "chokidar", "version": "0.8.3", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}, "optionalDependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "2d8da709a8fe3f6072c429737d7d0175fb81bef9", "size": 9840, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.8.3.tgz", "integrity": "sha512-2jbJx3mfXmW4QxzEirX2bmNYU0CmzODrZ78gdgFukTGxKYb+MmdGCd7v2W3Ir6Up2Q3longaZb4MCtTUNuHpBQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.2": {"name": "chokidar", "version": "0.8.2", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}, "optionalDependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "767e2509aaa040fd8a23cc46225a783dc1bfc899", "size": 9378, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.8.2.tgz", "integrity": "sha512-wJaDLi7IiYOeFZhP2zPjwMqD69E8zpPOyq/rXTrB79mWZtzIwh9JFEE7hXOcWG5qEJp+px8ADCn53Iyz5ulT/g=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.1": {"name": "chokidar", "version": "0.8.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"shasum": "8ee0c99ef48420902fded73b862eac2dd75da4a6", "size": 9549, "noattachment": false, "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.8.1.tgz", "integrity": "sha512-Bl/CteHnM5g3eKGPMkraZQW7Yzk2Gu87eE3jdaMfCNJP79sVa54M5KHStr3WtJl4vVoVSndVDws6IFLTmMa3Lw=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.8.0": {"name": "chokidar", "version": "0.8.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {"recursive-readdir": "0.0.2", "fsevents": "0.1.6"}, "optionalDependencies": {"recursive-readdir": "0.0.2", "fsevents": "0.1.6"}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0", "rimraf": "~2.2.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.8.0.tgz", "shasum": "13af7baabeeb7aef71bb14132f9eacfd5abaed66", "size": 12074, "noattachment": false, "integrity": "sha512-7pCMVc2Sacy7+Lxx3iaoWP6glKsrY96nTHVGH9XOGlpbyb9kN1KIWQztPrkwhZgTUSLyWMzP9WW7jQtR5a4+YA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.7.1": {"name": "chokidar", "version": "0.7.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.7.1.tgz", "shasum": "a5a5b2d5df265f96d90b9888f45a9e604254112c", "size": 10675, "noattachment": false, "integrity": "sha512-zF2jwdBLDRVey6RtGDU1zn6C3hG1XMtA6u2qFSzRraMOBfk0TDmra0jZsOZGfXyiOS1n/FlNgIxMXdKLxRUj0w=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.7.0": {"name": "chokidar", "version": "0.7.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.7.0.tgz", "shasum": "bfaa9bdf30c0921dbe0a98bd93a3e06a5d5814e5", "size": 10612, "noattachment": false, "integrity": "sha512-DAHbgvcg59rkY/OmglFyLrlb47w96W3u3Epma2wgTS4Mcua9dSQRmMSdYGYlN3ESU+7qGXMeJucPB6261IyapQ=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.6.3": {"name": "chokidar", "version": "0.6.3", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.6.3.tgz", "shasum": "e85968fa235f21773d388c617af085bf2104425a", "size": 10062, "noattachment": false, "integrity": "sha512-IV+veAC39dHzegDTA7fib37BRGGw3ywg9HAtpJnpaYgZRnUvSlgdgFK3yKYdkHCUnsOvoZeOD/KRMgQkY+FyUQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.6.2": {"name": "chokidar", "version": "0.6.2", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.6.2.tgz", "shasum": "827ca4aba5f88198037d8e369d206b44b38e17ca", "size": 9247, "noattachment": false, "integrity": "sha512-NZlEhCCJU3BUGhbxyir1oP30KZE8DGXRfOyxJZDZKkc3pKXNrpcx3wAqkVsKMwUi3/YxRWAoQCa5YQ6EQy3u6w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.6.1": {"name": "chokidar", "version": "0.6.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.6.1.tgz", "shasum": "a2042d38e08b2b9b85bcab62b51eb59aa31f8364", "size": 9034, "noattachment": false, "integrity": "sha512-bh3ReqqhhnsJdP5JkDMcJ1QKa+IZfSOFUah54v7la4OPiIBBHd//FeQH8xSRw2fxaloMXZk4oAZ5B/HperOA4Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.6.0": {"name": "chokidar", "version": "0.6.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.6.0.tgz", "shasum": "74df31f575676bf2053576e523b0d52bbca84cad", "size": 9045, "noattachment": false, "integrity": "sha512-IBQcJqANIrauuyWwEdVktBUpX6VNQFsPWqDMZnESoz2UECyTa5lrBOCpQh9Cl/7ucfsDQ81TUnEV7TPtxV2rsg=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8 || 0.9"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.5.3": {"name": "chokidar", "version": "0.5.3", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.5.3.tgz", "shasum": "3c5290279c4ec552466671988b76e2bc10c39191", "size": 9306, "noattachment": false, "integrity": "sha512-2mV8OxfkhQDPUiaNaLgcKKjMRTe5NIKFg2rnzIJvKWe839BON0Ore/ZmjEUjy5+6AbYUhSLMury3UCm2CLZ0tQ=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8 || 0.9"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.5.2": {"name": "chokidar", "version": "0.5.2", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.5.2.tgz", "shasum": "867e9e2ecece696897ac7bf4a656731d4e79b860", "size": 9302, "noattachment": false, "integrity": "sha512-9RE<PERSON>yyebUDNBDluraxezEOWfkpBrBn+2fhUI1Btf16hxNDql7gRcy2qQYfclpbPc+xJh/QCC9HPgTpClRX+2Og=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8 || 0.9"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.5.1": {"name": "chokidar", "version": "0.5.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.5.1.tgz", "shasum": "097e679d96a31cb1d227c14c97aa7650b9913de2", "size": 9008, "noattachment": false, "integrity": "sha512-G4jWBIhBMHdxMljQxb++O9iseG9P+75/Gn2X9oAUr7tfHY+eIE4fQ5jnfmXgnMR1lRjOB4Z5DkIjzBLSH+CMXw=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.5.0": {"name": "chokidar", "version": "0.5.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "~1.7.3", "chai": "~1.4.0", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.5.0.tgz", "shasum": "0093837680c05f8268f868cb96ac87b0bc78c872", "size": 8758, "noattachment": false, "integrity": "sha512-WqHdExtgTA4GA2FDKpNmSJJMoGtW9uce6Q3UFZFEqhpgZkDXFG/jbOuSRrXVruw0nQfRUA0VxqZN7lqNQu0EUg=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.4.0": {"name": "chokidar", "version": "0.4.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "1.3.0", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.4.0.tgz", "shasum": "4d8164c8288b930b62e7f1515a37e146329d26d3", "size": 6379, "noattachment": false, "integrity": "sha512-g9+cz4Ze3hjSEILSLKPUWeBoKkjRe1UagzguM3hIC96g/0SToL+BSA8EtAQWZLgtUXiHnEW4ztzZZ6+KjykZOg=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "hasInstallScript": true}, "0.3.0": {"name": "chokidar", "version": "0.3.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.3.0.tgz", "shasum": "153d3aa76231b47c7981ae8163aa141cf9c27009", "size": 6053, "noattachment": false, "integrity": "sha512-nW4UtH0vm0HIL8+abag7QTh0fBcffGK5H0MdBvCzbATO9R2exY2AOMDFG39U3FrGHH5Qi1+5xGLLQKbNytBKZg=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "hasInstallScript": true}, "0.2.6": {"name": "chokidar", "version": "0.2.6", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.6.tgz", "shasum": "852022031f1fc1feb54af2fa0b5efe527370d454", "size": 4620, "noattachment": false, "integrity": "sha512-vhvUE6i1ua6DfjKdp/jDp0fCJFWEuwlkn1sLtv5szJn38WxBYEeO6OJFjk1OmN8ofd7Qj364FXRUW63DU2mpig=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.5": {"name": "chokidar", "version": "0.2.5", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.5.tgz", "shasum": "4228827b67e28e01cbacb34925effd8768f437cc", "size": 4572, "noattachment": false, "integrity": "sha512-5Ihb9UiEfSekfq76yDBWuA8RAtk2LillUltR0vBSEjVvozr6x2qFGgQlH9OHmFCArxQ+Z8b86Tnn2klu3b50OQ=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.4": {"name": "chokidar", "version": "0.2.4", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.4.tgz", "shasum": "aa6df18b362a4254990dcd28014630a1553155e1", "size": 4220, "noattachment": false, "integrity": "sha512-ZBD9QcTixNgpsrr28FR0XjntsfboEXqYIvJTd5ZjHH+/R3gtqq8ReelTklIzvH7lqiEglPaKoMuJYhOzomdnIg=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.3": {"name": "chokidar", "version": "0.2.3", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.3.tgz", "shasum": "1f322a97518fe051e07fc6fb2ac576bcdb208be6", "size": 4133, "noattachment": false, "integrity": "sha512-qNFu+UewuesXifi88LJ6fvQoH6kW8RlCN26x8vb8aGcsS/bU14mMHcX2pptS/FeWH19K4Fgi8xfBmiBx/nhXuw=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.2": {"name": "chokidar", "version": "0.2.2", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.2.tgz", "shasum": "3dfa716e7a5a55e503a77004f64e758f86124526", "size": 4110, "noattachment": false, "integrity": "sha512-5Y47XyG1a8vs4a4tCE6l/OINik7fBsvunNC2bhy+S86G2kFUgSs4P6L9mhGRAB7gzRpqaf4wbqWqCIjZjFYvxQ=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.1": {"name": "chokidar", "version": "0.2.1", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.1.tgz", "shasum": "ea10091cdd6c413253d8f3a83db6e775d96eddbc", "size": 4094, "noattachment": false, "integrity": "sha512-HUCF3BavfQAC771111yzh8HVIOpNAxbauEZOiUaHH5s2ODF7qiAngO2fXnIXrv/C5MZ7tkamHbV+GkHH66nOOg=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.2.0": {"name": "chokidar", "version": "0.2.0", "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "dependencies": {}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.2.0.tgz", "shasum": "7c585f4d9dfb772669db81677d86530b87efff05", "size": 3990, "noattachment": false, "integrity": "sha512-xDR8wjY422lXievx5wQOu3f4hjan09ChofXz4DOfv6KDDCkDq0pgHxKsF+mGbFve4szrEF1Cb8cROZhOqBZn6w=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.1.1": {"name": "chokidar", "version": "0.1.1", "dependencies": {"beare": "0.1.1"}, "optionalDependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "directories": {}, "dist": {"tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-0.1.1.tgz", "shasum": "874eb8ccf2d3e17db63224ffde23f9134f80f7d5", "size": 3943, "noattachment": false, "integrity": "sha512-rbn5wcfS9YSfR6XjSB4A2l0pgzx+gYEcKYdwPPO1Je8B0/59l/UWj5+ZJskHqgC9U1zZ3rOiSsPutJdvSqtGsw=="}, "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "3.5.3": {"name": "chokidar", "version": "3.5.3", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0", "fsevents": "~2.3.2"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@types/node": "^14", "chai": "^4.3", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "typescript": "~4.4.3", "upath": "^1.2.0"}, "directories": {}, "dist": {"integrity": "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==", "shasum": "1cf37c8707b932bd1af1ae22c0432e2acd1903bd", "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz", "fileCount": 8, "unpackedSize": 90082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5m9bCRA9TVsSAnZWagAAztcQAIDHyIl6hzpIDyV0qrhG\n+z/l/zKpe8R73nh8apUd41z29D7aDUj1piRXt+CaCfLfrRCrmflHN52v2JIM\nv4BV8gh4dMmAvXbCIHpJdjdOzfsqt2Qc9Aabg+HaFBcF8Psc9LlWKHFeaRPU\n2YnZeDjSxHaP2mUCAFkww67wSU60yhjD6zTm61MCt7zezjJSTV3d6HFLE2CN\n8SzY80PoKFjPxyXyYNcP/4XTsWNq2h6jmc1jMqgWqkbYzq9Au7luWmxGnYvq\n1qsIPbDeByu6+UqshsIsQLs2poJIjrdJt2QJK+WxOrtFg1m1ka4Ie4T6jAEc\nxnzWQLe4Nv3On6EXAT2oVRuCLZEGuT2AQKUOMYjLdQudJK6RStuqcgWY8qEm\n2M0kiaa877nEZkehibI0Z0PE0kLVpxrVB82QeHFV1eVOURHAl/SpFw69LBpL\n65zkrMTXY+Ox99JQtXjxAFIhE7Hl5WaVwhw6xCzSsDsQ7SQtFAqsSsNZ2ZQp\nzNifKP7J9Dbm+GWIdtAq6+LOfuWr/D6jX/I2JY2bcRCOHSPgCDYxE8jaZ8Qc\nw6DCFOCkXXgFFNtO3IaeGIHEV7NPEqqEKLU2ZCd5NZZBpVXrnbISBGIOrAW5\nG+fdggxnFb1Xst3+f8/6ZpP6Se6yFgyEoXGo8GvBBZiarAYYLYvY2WJeA8pu\nn2iN\r\n=BB/X\r\n-----END PGP SIGNATURE-----\r\n", "size": 26290}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}, "3.6.0": {"name": "chokidar", "version": "3.6.0", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@types/node": "^14", "chai": "^4.3", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.0.0", "rimraf": "^3.0.0", "sinon": "^9.0.1", "sinon-chai": "^3.3.0", "typescript": "^4.4.3", "upath": "^1.2.0"}, "directories": {}, "dist": {"integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "shasum": "197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b", "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz", "fileCount": 8, "unpackedSize": 90200, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/chokidar@3.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrLz+QQsWUqY9288Mi4YOnMuwPeEYbxzyCC5yi3TMZRwIgDyU1ifjk7JLypXSonu2jzdODCLTzZX7mFb9zTmJvubI="}], "size": 26452}, "engines": {"node": ">= 8.10.0"}, "_hasShrinkwrap": false, "publish_time": 1707260254159, "_source_registry_name": "default", "funding": "https://paulmillr.com/funding/"}, "4.0.0": {"name": "chokidar", "version": "4.0.0", "dependencies": {"readdirp": "^4.0.1"}, "devDependencies": {"@paulmillr/jsbt": "0.2.1", "@types/node": "20.14.8", "chai": "4.3.4", "prettier": "3.1.1", "rimraf": "5.0.5", "sinon": "12.0.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "upath": "2.0.1"}, "directories": {}, "dist": {"integrity": "sha512-mxIojEAQcuEvT/lyXq+jf/3cO/KoA6z4CeNDGGevTybECPOMFCnQy3OPahluUkbqgPNGw5Bi78UC7Po6Lhy+NA==", "shasum": "4d603963e5dd762dc5c7bb1cb5664e53a3002225", "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-4.0.0.tgz", "fileCount": 20, "unpackedSize": 256334, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/chokidar@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHgw5wCXd8S8e8ncsAUkqsgCIiuCr43nmsNdy0/GGt4AAiBOA6dLmfZH14xm+owunXTU2r4qcfSzMIXJjE6WncjOkw=="}], "size": 37124}, "engines": {"node": ">= 14.16.0"}, "_hasShrinkwrap": false, "funding": "https://paulmillr.com/funding/", "publish_time": 1726187416656, "_source_registry_name": "default"}, "4.0.1": {"name": "chokidar", "version": "4.0.1", "dependencies": {"readdirp": "^4.0.1"}, "devDependencies": {"@paulmillr/jsbt": "0.2.1", "@types/node": "20.14.8", "chai": "4.3.4", "prettier": "3.1.1", "rimraf": "5.0.5", "sinon": "12.0.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "upath": "2.0.1"}, "directories": {}, "dist": {"integrity": "sha512-n8enUVCED/KVRQlab1hr3MVpcVMvxtZjmEa956u+4YijlmQED223XMSYj2tLuKvr4jcCTzNNMpQDUer72MMmzA==", "shasum": "4a6dff66798fb0f72a94f616abbd7e1a19f31d41", "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-4.0.1.tgz", "fileCount": 20, "unpackedSize": 257054, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/chokidar@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqB4H3gAgsxWdlgdaRKerqdxCaqZAntR/+tt5BfS7KWAIgE0XEBrp94RGwD5FuFJIl8itdHUCUK6VfMLlfeDdxj1k="}], "size": 37246}, "engines": {"node": ">= 14.16.0"}, "_hasShrinkwrap": false, "funding": "https://paulmillr.com/funding/", "publish_time": 1727010617466, "_source_registry_name": "default"}, "4.0.2": {"name": "chokidar", "version": "4.0.2", "dependencies": {"readdirp": "^4.0.1"}, "devDependencies": {"@paulmillr/jsbt": "0.2.1", "@types/node": "20.14.8", "chai": "4.3.4", "prettier": "3.1.1", "rimraf": "5.0.5", "sinon": "12.0.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "upath": "2.0.1"}, "directories": {}, "dist": {"integrity": "sha512-/b57FK+bblSU+dfewfFe0rT1YjVDfOmeLQwCAuC+vwvgLkXboATqqmy+Ipux6JrF6L5joe5CBnFOw+gLWH6yKg==", "shasum": "97b9562c9f59de559177f069eadf5dcc67d24798", "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-4.0.2.tgz", "fileCount": 12, "unpackedSize": 148603, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXb2E4SC1KywYUnfmcZMh7ll5ZKuv/pkhIs4iZ3iXC8gIhAOUBw9GMVnKkHPe6n5M9Fi4aFxur6Fx4jQPICRy4Xkg+"}], "size": 25127}, "engines": {"node": ">= 14.16.0"}, "_hasShrinkwrap": false, "funding": "https://paulmillr.com/funding/", "publish_time": 1734376005997, "_source_registry_name": "default"}, "4.0.3": {"name": "chokidar", "version": "4.0.3", "dependencies": {"readdirp": "^4.0.1"}, "devDependencies": {"@paulmillr/jsbt": "0.2.1", "@types/node": "20.14.8", "chai": "4.3.4", "prettier": "3.1.1", "rimraf": "5.0.5", "sinon": "12.0.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "upath": "2.0.1"}, "directories": {}, "dist": {"integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "shasum": "7be37a4c03c9aee1ecfe862a4a23b2c70c205d30", "tarball": "https://registry.npmmirror.com/chokidar/-/chokidar-4.0.3.tgz", "fileCount": 12, "unpackedSize": 148759, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdNPZNFgCQB0Av+H+o5aEev/7DL+txX2or2RCLYBpsbAiEA/qduexHQP8lKlFX2+jqYF/YFTtKsLT8TIUTLXcsa3Uk="}], "size": 25133}, "engines": {"node": ">= 14.16.0"}, "_hasShrinkwrap": false, "funding": "https://paulmillr.com/funding/", "publish_time": 1734560494451, "_source_registry_name": "default"}}, "_source_registry_name": "default"}