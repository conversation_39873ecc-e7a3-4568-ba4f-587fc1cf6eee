{"name": "@vitejs/plugin-vue", "dist-tags": {"alpha": "5.0.0-alpha.0", "beta": "5.0.0-beta.1", "previous": "4.6.2", "latest": "5.2.3"}, "versions": {"1.0.0": {"name": "@vitejs/plugin-vue", "version": "1.0.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "b61a7b1da8b5f73ac79f5086886da13f6a674ab7", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-NG2pXzQdhKynTXp/m/dcz7NdG7o7E2X2bp71SyYLcL68jyYhaNSzf6lB5vFbjnO4WVq2Kr1nvbtxEKDMlJkuLA==", "signatures": [{"sig": "MEUCIQDtstCV5Bw4n/425JLO3kDxNvZTj5NPE3pLz4/3wXtZTQIgAaPEV0CG5zDPWR79Cy6fL5/VUHCL7ftxMGtL7tJWCsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6sRsCRA9TVsSAnZWagAAyQUP/10Im5dOaACAmFe21nUr\nYUuC9ExzeVj66B1/4iCct2TP81K/2TkfSdOBd81j0AtNZD2FXfzA92HK0ezF\nxbGVxTqDyQxeSP1KrIxKhgZvzeg9FPru2u05Vx8Wm/3tvIqxBdQgGByiP8jW\n2WRSvvEZ24ZPnUzh79kUUaVHbGCMm0NRZRuw3S0fKZlSAllU/jlFonyGreGl\ndE+SKgeXeMR3tOyky3ddPa4c/jlPGXT6X+tWe2RToyFpuVXIP2/Of9d9aQvy\nAV1mnUORLgvPszrI6+rRKgKWrEXC0vtr92FQXGSMUiRLcOdHbq+bpt5Hqihy\n/IiQBMgUeZCvQZ8zEZearp24t808Asi5mYUP/dQLLk2Ouj2bHqliDp+ffYpa\ng6/FZlNKZUe2S0bbCFFFdlRdghiGCllfYQy2ImhDaIPiFM5IfIeh2n/AVJo9\niskv23v1YZLP4f55nxnpamgvYpNXpBY7AanZwg3kDDTHLMey6mIieY2Yd1B0\n5OD12TNCuXxJPPXW6M/DLpsupkr8jPe/paus6iq34mm3W5TZtL1hst04P6hJ\nGU6lXADUs+8eAP5uPrA6zxocF9/EawFCN6vaZTKCRTvlyyWTV8mMF2vWBNOJ\nIaA9tlDCjiLjPdquWJjFq/+FKtkCUr/mPq02deC4yR5IsH72P9jJsSl6kD9E\noBY1\r\n=biYS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.1": {"name": "@vitejs/plugin-vue", "version": "1.0.1", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "48d5519e82e36ade50170b63364af3f220a6b20c", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-SVrvB7qk5Qx0djO4BS6V1AtMXGKYzPUk7yg8UhLiHw6cAkWI/hQMRGuipQ0E8TdwHruJtynqUw1Rf4fxfQdYSQ==", "signatures": [{"sig": "MEUCIQCCIWxcHUXaXuNLXKuPxDwreR04t769+/Hc1djgEjrXKQIgLD4cQD3YhswjRkeKPucxLGLXlpEFsn9LIpuh/pcoUtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6scUCRA9TVsSAnZWagAAu+IP/iE9qnPOqhP2I7DZRuJs\ngvgZMAnHm9fKF0BYIqNLDifJ2l5dmmzJrKwx3DxVe4Qp743Y1uCD5HS5DeZx\ntBHCiKAOnKq5qjsnJ2jvR2P2/HH3j7wJlEWivRwnLMaf3j/Di1TmSOwlb5oV\nF9n9bVGT3fvZ1MWGvJYSX2kCxiaMuePC1n7vIOPKuLrYeHF8b4RWPdvUnRD6\n4+QNl3EqYironPJCNDePg5tvEaIBeP/Tlt/986ukPjTHq4KV4vGiwhbDOoTL\ni0ndeaWDCUbJlKsCBYvvVF7mIenv37pyEDtCAomLkGgNlUVuRgfWF1NMZQXy\n9WjnSiHdwLt7SzwyWXfy4pxR0O2Mk/A8fAr0BoUXCKsnQ0kGn3BnjwQT6Ld/\nP9oJqyLf6eLFrU7Pc6/kk03ypWJ8YskvrpssiSdeGqaXVq3NkL8Eo3WDf38r\nuO7XldktI5i1XOUbYelRmFfFS1zx83sh33yu/kj2LdVvfD+QKceXrZP9oBbi\n1V0J7V3ioJNhPbmH2rcKLUSiWym/0hsZuGPGwMJXP/A6TtaZipTGlAfb2dNP\nmxNWkoB8SXFH3KyEiIHE4NVu09o2xWyirWYQwAdG9qqdHQ1d0oO27aSbrHYJ\n+SXtYXDpgnk/j9VOnyGXsm26OmAXap2Ytn5J+n0y5cuJ34lh+sUPD6JGEuvQ\nWofq\r\n=szLt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.2": {"name": "@vitejs/plugin-vue", "version": "1.0.2", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "46da003df81c5ef74cec6d59c16fa13c34bd8108", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-qtmVCJ3MfGebpbRIK/UFydlEuvDrz5Z4caARXDWWy23bM6nFq7wIV6/tW4LRdmSHxFdyvogctQ1+zl4brJg1og==", "signatures": [{"sig": "MEUCIGUgam7m1obye6oj5wvMrpdIuc1A0EXb3WPa7hxvr/45AiEA48/lubCB3w09hgRSfJu0CzWZcR74QxWs6eVfBcUmVGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7KAVCRA9TVsSAnZWagAAUisP+QCrqwjAx5eJOxgrwUAP\nFjmxlmI37Ikb8lsiwzYc1lhvmOoAsLx+EVCLkORYDQ+ecSexVdcgKrt1ooJ7\nHZaMMwrJYZgxhHWK40HpXFvGdd+bmlZLQsOFgZKjngpj12xYwiyr4GpaUYsC\nh9jZxGaJbUuPpA0zE9BJDA+On4eTmp/ro/xilYler9AiEohuCOO83x3KokiZ\nsVzd1jWsqiuU8jWm9YNPK8tvV/E1OpgLibed2BA1Ve2c7ShAiYq0iUaJ0+ws\nKdpAqqWUE44UoUJF5iQvSAQ1XjjITkVRerNSouYre/AfkXE10DEx8EGmey6n\nvCLzvlUYMmztP3AWIDOkrKVcKicwWkpFZjS/lK6L/rMwxfgPI8ye8VWRIVpK\nUi0NB21O3es/w+EcL5trtE/B/DgoVa4D3NiuJSyTEpTcENmUMpugq4b/UIqC\nAgLvJfY7Efd+NAdfAJkZXSInXHtK9K/MHYBROHMZcuw2fHjhr4Glpw1dJXN+\nNhyxeAe8/IiCmt26im0O9yKo6BONEZppv9eTaf0/Au2A3P7QFRSu0h0jb1zk\nh2zujKo8j88g9zrqDuWZIuPTyDKJTumw9WuHu+CDmYImPHTVfyCKUGOjoZXZ\nX9jNlqvzkexnp4x+tgHdc30iAJIcpeK37d24ZA06R4bmblBOwBz/pBPv6k7L\n+J/s\r\n=VAlt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.3": {"name": "@vitejs/plugin-vue", "version": "1.0.3", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "2df3c22802f2d2142ae3bd8d3e93623df790446d", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-sOVHFS97zxuRLAMj10C9Vaiv3WeEwnhtee9V+yv/G/xoJTXPJIRct4Nj2unPtp5zAUoCL+iTVbIC6LnNmNE4Hw==", "signatures": [{"sig": "MEUCIQDqaG1PpmMbu8pNvHg5gVzE05oT1ZoGACm1ohAd4+2jFgIgWb/xKDuWl6YrilxGPA4YD9UXC8g206Q+xKgOU1As4tU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8Lj0CRA9TVsSAnZWagAAr0wP/3fCqBVNAPc9ptrJEdSU\nLycsl5MUkXAqTvD/t9AL3hR6Y69b/GuHPttjhNSb81YB9b/Du+utU/Ek5UO7\ntDlbgKLuKPje5SUEhMtHMxEV9DHX3UntUL6Sv5TgWdCraC1JdK+14/jC2HLu\nWeBvXBuREoXYcBdyJMXl23bo+n5s0Opm29FZEOXNE01sM6wESyM+8eoOQER7\ndDc9M7EfWAepqJGrSLENFBWyTV2kWYKkM/6h2d4oBJipvAtYdCnJfTs71fgj\nIfqv2JoxE8chXygk4l76hYg472tH6IuC9/lIvLNwgcxsMInhYyN4Rq605IPf\nm7b+RzDn1XN1jN1cbWsyHBcwkaYhGKzEk1dC5DvopoiMXn28EG28L2wiEt9S\n+DTIzTo+srmTi7PnfU9I5/HKap+x7bH0k7xgp28SV0aOpa1NHerPaYCIhXr2\nXKl48jmlTegtPuZ3rmb3+6sNFDvrjkjvPg/lYt7BVA1Dp0v+JfUeEffYYvX7\n9+h+DHZ4qslv3cwXEMYIoSxOciS3fVtKNWth/nv5+nbuXNbhNRgZQQyscYBt\ndaUwI3ikWYjvmdh7q3KC169lnlFyjA3dPw3WZ22YjCLtkUnmIotSw/kUz+ad\nR8dLYA1cOyEMc7P21dcxzu0CUHhtaGF8U0uJ5reldneeEttTL/Rl8X5mlZXQ\n7fb/\r\n=11GB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.4": {"name": "@vitejs/plugin-vue", "version": "1.0.4", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "124b06b185a8b536210bdb00948c17e52ab589fe", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.4.tgz", "fileCount": 8, "integrity": "sha512-VaXpbmriAOeCdstHKjw3L8/8GZ33fcshBbWlN6IocKQW+GOGV4Xm8Vp0Oa/VpPVCSTHMcqvKyQ4gGEBpDHdWHg==", "signatures": [{"sig": "MEUCIEClbKZMAwcVxHB2Ka/yHc47yyd9XgJzU3GKrUQSjPFdAiEAuCoqVbUVDtfMLsDHiAbLGekmaOihXXdZ5qWtq7oQM0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8o3eCRA9TVsSAnZWagAAe9IQAIO3aTCAjf12xvlGTMns\nZgzpRv1WNagI39tSq7XdJf69m6h2cv5k7bu9BQzgUwyqjPd27QUWvt+MkeEn\nG8rfqWVboecNpsCsBvxVjVd5WjtMTX2QiE46bW3gJ4OKfeUQdWAIbcFQURuw\nhMP5z8EZqGP7cZM6VPx+BRXgDOfdgIP2dBUroG9Sm9I1FSt/XpC+KV08g9al\nGj93ijGRDK/+SeAfG8GSUvc98oqRmokknxY0A5KGwne52hnPyXyzZQpsE0U2\nunggRZQaJsqpkdmB18+UyeJbOtdWj1xdba0s+aq9VjUgzAbUtnUQaOnsxm/H\nzRR8L+t5Klsf3NUS19SxQtwuu6OfIy8tGpzii+dq2kEKszK5g+JBPw/VHwrR\nXC3ij5ckkQKqT5GXAqVUyDfIgyktRZSph7Y/h8oAJN324fuy0/CM8NK0opMP\nZV/MnBUkpxJEHFzvJvPoymXV/g+cVE37qfqblHnwixuM5raEK7u8Tj8W22wE\nkFF9toK7J7oEqL7lU8ho5DTRgr2mn8SauQ02DOmhq7MbavIxPQdWxps8e8zP\nqYqzADQqvZoBqE8faJ5aHrnqvMucIZebt4EVKnBq6feavMgQoQQWX/DmdYtR\ncn6E1lresofH6MMBHUP+cv9tS5omhgqE8twyXjJ4/uEsinKR2LByHBr+K2ZQ\nfWtF\r\n=8ldU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.5": {"name": "@vitejs/plugin-vue", "version": "1.0.5", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "2639178e975bebc505e9be1c88d25faf9bc4dd06", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.5.tgz", "fileCount": 8, "integrity": "sha512-Fq/Z1rTs7j3QhvmIjeIHqInw2YneXa8Td3z7cYQhyAZXF/WmGMegbapeBqGAoAcGSOfWpOO7Tr0c/T+Qke0O6Q==", "signatures": [{"sig": "MEUCIQCEeDpLZTHzH9xQNF4WwqUME8jruFauuzQ/HHWWL+zK6gIgP3KK7VddWUuUfD8xNuN4OuZAh97k2D1Evuk+LN16he0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+hWKCRA9TVsSAnZWagAABscP/3JOwdf8/D1mi9gWtOuW\nNc3MY0PxGDUfX/OrpapbTxj5TcCdPhUxZM5e5KoFSxK8eZmT6tsOH+A1yXPC\niwKlQwBHiRcXX7V3Te8+GNZhF9xs49bLRtzg1uXRhnuzmKh14k+DwiF17Cq7\nFAYn1JPuvd/BqU/UlI9eKnb2EVRoMuzUemGHVP1rbSWlok05HXOkFfij5GUA\nYv1UVjtLgnmG9XXsKS3or2JqftRr4wUmu7CV0e1spfmuQyMfehC9lrE0EFRI\nZBEOzebdKa5rsrCQwW7jl1kJGskOQ+gfa2gXla1ULDx9WyI7yMNbcPQzW6v4\np+xeP7SIimTBwsxTWvpi8J/GyjRYMbsG3hSvxQTdMH331IrXbehimN0bNR+B\nIokhQU4LNNAlrWweMei9YJYWMy6wslURulwY7Uir2OqVNivB6g53k8ywaRxY\nNKwj5NtOWdTdLkLnQpAstca0KH/RIChvLyT4azZZLh+j3k2XfylDoIccq+/H\nDdGs48OaN/oUz3zQpymx92GaKnT8ZMcSDy7izUpRmStg1Vc8x4tKUnQDZS2m\nH2VjCPMmOtZHdEcVi0LvfXPeNFHPHKsl9+HfijtQlhZOzKhSeEVyCZlTrn/I\nL83Gl7vz4HdDeAMnDBfgNNNqmdoTChIGmXUdpUejgpsxIY0GfhPCo2kCbv9B\ntKw+\r\n=Dd+r\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.0.6": {"name": "@vitejs/plugin-vue", "version": "1.0.6", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "698afa5a77a6dcd22cf7757801f46a6f01cdbb53", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.0.6.tgz", "fileCount": 8, "integrity": "sha512-c<PERSON><PERSON>ewtxnVVpjlhq6DoZ7VP7sF1jTZYVg66ehslZ0tJANWk1uRiCXdqD8yQ4npZ4XewDICQzK+c+9i3Xsubx59w==", "signatures": [{"sig": "MEQCIFI7gxmymMARPEBhYr7qRJi/8GVxIQhvyrUdr7RPiHrqAiBEpn36gIgvoo2w9+xNlMsWB9lSJuqCHkUht98WB/I+Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAa7pCRA9TVsSAnZWagAADJ4P/iy8UYP37mlEEMWumoQN\nla9Rc0jqyBc0ArBrLhlvGOmq81nwwic+L38uT3mcuzn5BTvlcxMLgVSDn9mk\nqiffDt19508f+FJAZ33o89fY7iN9EMug65ikAPiUr/5zurFPUMREpB2w1U9Z\neYrIZEqo5CZ1bR60sRKIbQBfA2ALeyYq69cNyurGoXk5oNl3miGeo8ZlCT1i\nhLhBqvLMgW/xHyOe22JbZl9DSdgag69G9FlAZ5r5zFz6ZoiLy2SwT3mUCSMp\nZJBC9YE9id53ncTXDHq+aYIH4QjxIRq90cvDvfeuAdFish7T6e+y72/dBGAA\nf3aJpB65TPs34TR8eGQ3Zv5klFzZtPOzSvVMBGbAoPeF0GPJjHoZjd1nlJjp\nxKhwUKiwB2DXGURIWaGH34GS+LUX55BEWAWuKaKywkSatZXy/aLJ4E/BjuRC\niSxSQQLQtYWJ/SOKh6xILfx7keNZezsVEIxNb9T5f+24Tm+UV3tHeY84oWEY\njiBcLgwcvLcAxN7cdE0uEuKz3DHh6d+ZOZUQPf02TTu744JkSGTpJ+tgt717\n0GUedtYDizoO9+fBWb7b+uyZTKWJET0PQ7ADcerYJmCkq/XrKfbVSqKwFp69\nahHXiOEkwIx8U4rAe29bXaic00/8Ypoc3eekEENr2+MJI62GQbSeTJAsssHf\nDVmv\r\n=dKDW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.0": {"name": "@vitejs/plugin-vue", "version": "1.1.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "8ae0b11388897b07259c9e5198c0e3fb5e4b37d9", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-ExlAt3nb3PB31jV9AgRZSMoGd+aQRU53fc/seghV8/l0JCzaX2mqlgpG8iytWkRxbBPgtAx4TpCPdiVKnTFT/A==", "signatures": [{"sig": "MEQCIHQ/TQPG4G9dR8XFyeWifPr84dlfYdaZ6IXoy6MaEppuAiAVbO/RsCkJsYAFp32JDQ0m2HC3vdS4OJkj5RwpnQBsnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBvkvCRA9TVsSAnZWagAABt0QAJRTrEBo+dO2/nPNRqOv\nK3HvWCXFgUnq1psMflbjZl4kYK0NHJ4uoVN3NI58s2lk6PZfzXBlItWpsYb+\nV4lzKwMjS3Bh5Z24DJIgq7lRT/aYDhKEI7+it687K81X2YCNe9cU62MOqjsj\no+6YYrL2q/rhrXFnh04vW1JFAjw1ugQX4mE6arODfiiHQ03zpMGwLbbKp+Ws\nKMdT0ClRZE9eiMeXGm9fAjqJaV78uEWN+osNRvm+YqARIDxPojH/w1kY3beh\npAVLlHe4aWBgEzB4G3CudBAQj6Nyxzn0JBd46PUE4BAXB6X5NPfl1+IYyTN7\n9ziDstewsVx082XXRsK+UpExEpkFQkkCnOgUrFjVFDNDlejA4uPwHHsEqQQR\n1WcQzJqnDBRmTbzIH1DDdikBzP8bNcWc59FGNNhJ0+2abDZBcIVJ90s9WyEF\nhAKnupsmkBBwS/gXWghcP3+9bUF50pew316gJSFasSkZcgQIsIfe/5meV5Vi\njt7hJddMrlMwgeHyP1YNVpw8qHOcf3t/ZB6YX2t9KBrSAC9UikzEBwzOSuqe\n6aH2MwTkRDrpBl9pB+wRME3vmdXkkFVrRO0Op+7zgxyXawDIa7NaD6MB2JXT\nIdEOO4IecYx7NPDGrryfqf/gMpI5IFYhwblH4woLBJEOCp37hp+c7OvLJ/uf\nqlA/\r\n=NXQt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.1": {"name": "@vitejs/plugin-vue", "version": "1.1.1", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "6eccd9aa0a2c3365048bf82b9fee437e995ae15d", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-Oia369l4L4Wd6hW0paQ1OUsPbGHd85KQllismua65C45xMu40og5lzGreSuGEtW+6yB0I5FyLrC7tk5yCucHwA==", "signatures": [{"sig": "MEUCIHZXNlXrPa25xw6qiyjKg2Xtqdd0p/0WfD0y0MVX3/mhAiEAq9TgVE4+NIL5Vh/4xvWb6UaPwDVcN+YcqPbZMLnmEa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgC+sDCRA9TVsSAnZWagAAKlkP+wcwxsQejgee9tUol0iW\n9eNkp/pZlxUZyLbPgBLjpsnuPTUfVBVH/U0/tM5Tvkn4W5pZ5MMluCCe3tLN\n2boUkdH/EMCgciQwlZwiRxpz5tIkybgQCd1W8AL1xMY2sp0VSHvMl4f3vLob\nh/nNgXIoM9sxHc5O5MB8DXz8qO0qJbIZJgKmUGSh79klKntY4G5KI/j3uoWW\nZcpYdmQbJdGXp0Ms/LbeWBXYsjnVkq1B0kn+IaXpVjyGoYgixzVtCLXIUjDy\nEVJSPUanqFN8Cy4hwRRSN1yDMq4MXT1cTfarrsGnPvICCFsv4HYSrIpFNdDO\nPKY0S8i8PlOjv25Aa1j0fY6wecYPmvlAlKNotlvPN9u3lUxwwcp9oq3vhPux\n6GJGn6ng6U40eKtR8ySmDumXW+c1DmJ2hop4OA60KRfMWWIrM/mAnQLCBZp7\nkzGIULfF9yNRfOj8LGrcbvGTcnFqflp44DSZamUX6pVHPXi/L1q5OqoPlndP\n5L8V6v4TUR1BLjNUrfgJLig3X+yWjfFPhxrLHGGFqvyZPKNyoxvSQNDNljE6\nBOrqsMh6PvC3keTG7yMue+xl0ifsML32UlWKeSGOeECkYC//7rRiizDcTiUg\nApFIoKLeas6tCMA+FyfurV4avl5Yj24Xtcjk8GkZt0MW5pcqh3dWoPt/5Q+e\nVsEV\r\n=7c6b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.2": {"name": "@vitejs/plugin-vue", "version": "1.1.2", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "64d1f0e0739675f5717015ffb4d861c53af8fe60", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-a5ORYuPsiAO4Kb2blA/x63mDiBQBxEJkbjhVtiv5IP/I7fGfpwXPPGHx9LHD4MedpXp8icngJYMKO0hOwahtmQ==", "signatures": [{"sig": "MEQCICgvDhZkRDAyb7iSOhj++TedU56MrA2dIZ7xYV7Lr6aDAiA7MaQG1gLVLeZtIBxwzwoMpvDqN2mDG76mnrTOWz0WYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDRvBCRA9TVsSAnZWagAA0HAQAJZ/ys3YaOKqoVhujBvK\npQFoZ98d058xAz8ikmliPr4qZ/1+Q1dD0KffEPWlJUmMHp1u/Fb8LZBQ2ZVv\njsIzJvR6cNH4CubJ9Rw4wCij/kS8SFXbj6RjoukbsTEbrFYf4aj1qyiAgNTQ\nTdQ5f3K10cluft5PidpOKeWTa/Bp02IJRhtl+7A8RhMJZKSzp6xKKQoyMvw5\nrGDy3lvdO8+Wm9a7mfSyW+in2zF8rsoYsaCYmJDwxVpIETyYaz3dPdqCxnpz\nProUrlpk1mRWKISLwNXjOZfG8oX86z6syH2ZpwlNUi4hXoFcW+P9ujg0cIOd\nieHjp+YbnSbObQ2YYJ2/iR9jeRCqNIL1sugQCqD21GsuEpKyCFBS2onvm5WV\nUrkr3XhWjzXkMgmfShe/orBvOFSpLSc/OzKnOpK/1gbWPvRHdtxXDcHtz1nB\nyOjdmK9Ck1/hUjlbIY0Qxij+wHp8e+yFPxfHJn8jDFPhov/m/UW0QJzV1pNE\n18jc/yB6jvY3ATNB2MRXElGB/F1S0qzhS+YFU2my9mCLIzZQuqJAf+sUO6YE\nGhtzUH5F/NuGrBoE4lQYzyTyVp90Zr2ZHvXo0uEhZswqhwPHxG0jelprlHh3\n4mnLaj722+6+KRZ8zKumFCwTkvs5VmC+r68FDXF5/FWiaviIkkXKMy8NH+jF\nogMb\r\n=rgQG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.3": {"name": "@vitejs/plugin-vue", "version": "1.1.3", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "94c3822d3c2425ca240b86437b8a8c92f29e22b1", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-RjqnMVIGoo+4dyjm8/5sAHkcbPNhFuFyQWO8OeaJMq6HBGqULHphf6J2UvnEi8TOmjfSFHJl1mzls3DtBvvz9w==", "signatures": [{"sig": "MEYCIQDSlOn8CUHm/byebw9Jpyg66GnLOcIk5FM2k5sIi0VWcwIhALqIGxiW5lentME06Em30Ohww8ZJFZsWQLic/0Thjkgs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFHK0CRA9TVsSAnZWagAAH9cP/1bPa1RnO0AfINlt55FT\nvkyTosMwR6LVieL8QPP5wqmUojp6e4KCN85/aC3FmEt4TlRt2pC3VZifS4rn\nReeXamKb9rkucGUAdvHHMeqt3M58jsjtWBMeOga5s+wps5CBJgVRR54Im1Uo\ngSRT60oDP42r1EV3mZ9ZxBsIphDBNXK+QQ5YTS+1YxOy9s0akT0YDujC5/Sk\nDo1+o81YloYE91PaVXwsKGUddr8VV3po1gg9bB+NGyjzZF6k8zELWtBKgQDk\n1PaU0JoaiUGpSgEYZWoDx/XC2odW4pN0+s6CUiqmmrBJFytoByxkwtK5kxcw\nv3LgfSC888XTp5QxOkMgFD0TDJ69s/dc2VXY6BNueuY5rYKDRtvP/kaZgkBc\nN8h7MUocJOev4OotTVnixuSwuzvmY/iUPIMk89YnNAi3R3KI0KRputyrR4n7\nU7wCgdRC8pw06Q3iDQX1oeUD5Otv1BQUCTRJXhQh3KoKTiQcUWSQ6uynycDU\nIojJ0USQ4zxm8s7GfZYzSn96xWwnVqiTowqOnfSoi6gLoyChAuFJ6j3WOWeP\nMQkCRl0FNOP1ePEtFNqTisNGM+91VuQsZUP4mbDiMrdOYn6QTUwUrpJ0Stg9\n+KImFPxEY+zLBQ2WvyBiGhY87eDec6DAoe/4VW2k8p7NC+Mhl29uPOLmTpKW\nks4S\r\n=xobw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.4": {"name": "@vitejs/plugin-vue", "version": "1.1.4", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "1dd388519b75439b7733601b55238ca691864796", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-cUDILd++9jdhdj<PERSON>hgJofQqOabOKe+kTWTE2HQY2PBHEUO2fgwTurLE0cJg9UcIo1x4lHfsp+59S9TBCHgTZkw==", "signatures": [{"sig": "MEUCIHKS9mvo3XWH4PpLkh3UTXOEPTor6DHQtklJjN91ajoBAiEA4L1KtvdW6dVaQGWVpor8P1X2zRevmZHBkrT3ixyYLAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFbQgCRA9TVsSAnZWagAAEG0P/RNnX2zEcQo72XuEAG1h\nRSOx33PNrACh3gE3aV3S1Zu8YFrqO6gqpDUqpq/bX/V1/voR+BywD4G8uJd3\n/UKH16KNmb9YkBHOFIpiK4YKwIQ1D6Gn7SUbAbkx7VhbX9R4/Gom/73CS8Zr\n9AoDWAhIpuLgD5hEYKREFjxy6fYlmA9011n1XHhjMrrOrtbpFrCLi9XHXYsP\nCS1ZmDulP71zEfnp+t6h6wHmMxc52ELqeCuKD3p3D5AulFwj8h2wiVOXEUGS\n3OOjyTci9I9bqAHx+Ke9WARKU+f5sYlj8XGwffTSpHdwSrqimsN9UYiCfTs2\nxcgM51x7WRxUoS+lOsWWaCt19vUITXKitFlkZ4xoiXCVLYe6sxuSpmxvgbDr\nFIP4OPJonzybFHn7Va+498gCsJVCGsMq+1O8DUdy3dM+o4L7XneXhbdifPFq\nWK4HypYVHvAFnPekr/9GjHcv2ML7v/LpFkWpHx8AQZ13BBLfgGKi4y8vUZRU\nJktgr979VXaKnY5RWr0w2Ghy/G373QAHF4/7Xhh+o9uWb98NtZUl293CkNbI\ny/ILWRNzX9P+Uqfm4LYI0moY5ViZYOhBY0jlS+2wEtYCvVRh8yyUnvhU1c9N\nrWaR3i/nqSWbDkZc+vSU2xsadb2RPCk074vngn70hZWonnK72PfiYdsI9sWB\npTiW\r\n=oI61\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.35.1", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.4", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.4"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.1.5": {"name": "@vitejs/plugin-vue", "version": "1.1.5", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "fa1e8e5e049c35e213672e33f73fe81706ad5dbe", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.1.5.tgz", "fileCount": 8, "integrity": "sha512-4DV8VPYo8/OR1YsnK39QN16xhKENt2XvcmJxqfRtyz75kvbjBYh1zTSHLp7XsXqv4R2I+fOZlbEBvxosMYLcPA==", "signatures": [{"sig": "MEYCIQCMiIEHH2/eRQC/HZOqsJW9JLxCG1vLppDDzRJry8vMygIhAP32sHNAkVffiiCjuDyo3wEEhJ0nrEEfKUlofpA7iSm3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOSJECRA9TVsSAnZWagAASHMQAIjypbxxbihDFn4pu+vs\nT5Ot3ZgcRELGJ6wLJC6cgUyHxfR7xFoGiga52/9yqj7F16lLdxq5CtaIv9aW\n8g5wlaNkD/1ePCNHAd0X7mVe9n6ogksnFnjnZS44V8Y8uOwoBIDYacl4dviR\nZGFNVNL+CSGKy69C6EzjCykUBWeqZlcJZ7l4zsTT8lBN0ZLp4d7H7KEkVxH9\nU8dEDFBLFB40i1W7y9bhBq3mJomLKQoqGWaQE1kFIuCwfgAkTVfD/jprECLj\nqGDAa3us982uLpP1gmv5nzB96lXwJiZQpPOu4soaQ8KYelpyGewXKjQBdfhe\nUCxmKDRBYdXucY7BSZrjcspXODmKJWOk4Kq36nQrb5ZIvr7uTIcG61wQCuWa\nKCnQZ7xYC8ZWuFUSvPcOSAEEgUPyyb0dUNyeJwRXpgg1topBIEahorIkadYi\ndLhsXJ7pJk37Jrm11anjaC05zA7iZM3he2RyXqs1y5B94eqjm/ScgUzGwEjf\nMyMFDxulezD3Uae/pgIc/B/hMo3M2ODWtqG5eOIcMxeZ74KxH4Az0D5aNCCW\nlT/zbI7wu+lbvsvBPx9uT1JltFTYN9x+XIPXWmzWdykAO9OpwYHaq/VfpYGy\na7wjdJKvXatDdk0Dt52cdhTfZYXk29IXZRumzRKUCrRENwq/NIR03f8qnwDS\nK7PF\r\n=g5Z4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.6", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.0": {"name": "@vitejs/plugin-vue", "version": "1.2.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "f0a92470b74761f90afc8cda204fa3bec9df09f4", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-IhSJfJH6IDNEAnhr91+2vhLLe/1SqkA/2BP19jwtn54DGI+cNbZIxiPhHIdKUpdRo0QwErOh6Jy1Maxk2uVo7A==", "signatures": [{"sig": "MEUCIGrP/CWL4YmJhWHyGzNM17hgPnsHZbPmtvN6LmvvMyuVAiEAuIwqglJ4f20cnIrylQhLIP4K0cNmaxECMePOMuz8beA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXlrDCRA9TVsSAnZWagAAduAP/0E4q4rgxf3hUgiWvK7R\nRuzOeghptugzCpyHosJrRu8RAP19FF0B/rQ/ofHXV9tTjNIDbUbDM4g+fZmV\ngiWhj2B+kDAMCzRhhQU/NrShQD/fhWw2FYjMa6ZNFJsH1xZQO4gz31EdkwPV\nyofswBKIIzMDcfYmcEje+DlLG3JMbEzT4GuSYotu9cEfMkty7ULWJWvBjKnm\nGwY75sRtx7zZs9iOpMHKdez28P6WpS1J0UzEoziyu5+G6VrQPVR3vI79P6qD\nd4RexiC8zoftLasPPOIQ7W/haVdyB2dBlFGJztoU7BIaZ/6DHZz2JDyFunU3\nrWmBFH0nhBLQgQJ+S9WbePht9FGLH5o1VaHJJRrighK1qOvq9OyCdqfqoV43\n9uYlncMrPnhsaamPqncAAG1whx/bX75an9mn0QbdH9UHSSrtogXrrMfI3LXb\n74kUsZOlxxdt0Hf3umcI1jQaGehc8ergnE2o1Z1HV9KvEa8ICsMuosfKns95\n27QD2a1Ri9i15V0Q39oVKyuD8c/2Ge5eBUvp8ZQ2tpCk7I7nTW323Jg4mESP\nRVQ2YL2wfZ9IjETgWlPjNzpHuYeVrlFRnY3oK26ydaysmNhSadFV4X5wFQU2\nvJOQe5Mngp2+jNTKRmcCbQ+uva1hJqO5dzHIhdKSDZJspvh6Z38Xriaspvxl\nmpKg\r\n=gZQe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.1": {"name": "@vitejs/plugin-vue", "version": "1.2.1", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "6de49436fc346f829a56676066428e3f011522ac", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-TG+LbEUNwfFrx1VyN+iq+PsiGd9MT16hUdJY+BnMXj3MrLAF8m3VYUspTDM3aXoh48YDmAkMjG4gWFRg3lbG5A==", "signatures": [{"sig": "MEYCIQCr83EmYAmMuvHBz9P98Wh8NmLg9uZo9IMxh3/BN3+RkwIhALdYbFwl1Jvi3hm9U0D6DL/Hnf9jzN8IojIVfgJ26YPt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY8gsCRA9TVsSAnZWagAA4WkP/0B+dU67kDqZ3f9V++UE\nWOmBvSqNBtF7NEuQDVMLhzix3ADnpt+kT2v20s9oH06QNWaITl6ybnV+QGx9\nNqp8/AhoTd/aoIc3LalROAGF73AusZKsaKQ/X8O/AQY2OmBre46C8hMhY1Gr\nYvH4B1OY+hF2gRGAgUgHnW8QT3AmRlCCpy+ZkPE/KzLRQ+jesDHCXREQyOT+\neGySMMYDryWNSr4vNpvdn97QXjWPpkz2gAmehIAd76sWRPKBGIM0EnUDKUR0\ncwj0b1YLAKYgmaJMGF27QuCAyQqt8x/wvEOgg0lCts8Dxad9Jyco/n/+AM+l\nDNgJXZwz6F/wHM0U605HcgpK5ZMQdph800E6rAWT5PL5zHqWZ4wr3JwVnyPS\ng2s5/mwa6B5KxrbMOSWbVD9tVSPWYoTDrowyokcwImDHpkapuOQCogafZzHb\nXevfgr4SdAHTDrDzB9OYonGzrDfKqvmdcyMWAcb3sZBpWT5ZYXpFphj7l0fQ\n5xob+xF3MnDRZV9VGTWsyKAVdB0MZSscjXmn8gl+9xsniUD/ejPqqqOUP1sJ\nLOW+XNfUV+XSZK1PAalyZmWe3aSPF66/QkafOvgkCWv7LKRBpbnqyQcX6hyU\nzOzsluydZiwixeZQovFETLbPuoKbJzvuT3rUd5BI4zeVUpuG4IDwULSHjFRf\nCRAL\r\n=2+6k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.2": {"name": "@vitejs/plugin-vue", "version": "1.2.2", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "b0038fc11b9099f4cd01fcbf0ee419adda417b52", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-5BI2WFfs/Z0pAV4S/IQf1oH3bmFYlL5ATMBHgTt1Lf7hAnfpNd5oUAAs6hZPfk3QhvyUQgtk0rJBlabwNFcBJQ==", "signatures": [{"sig": "MEUCIDKH1yKTF2XYUfPewADDyS40vjmob5G9SftUF9BTHwPJAiEAyAlxttip3GwvrlTIoUDW+5VUAjNE8pyXhUtXq8fHmsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghFnyCRA9TVsSAnZWagAAWdoP/R76u3V4nDfHlvE2D8uJ\nAcoqbz8GSXM9aNDHI6gwHWuTMYTN/mwwo4I5H+I9Osq9jIQ4Xk4rzAGcoAkS\nTag+OdRmQbaIjkINRA0oXAcFJmRck32KhMemo5Km7/qWkvWiQ3LsKO3cdGtG\nmBoKbbU62uzEpqwOwIWiepDwgRlEqr9Ul4Taeq+PkGmsE/V8GDI8fOGJ7Pv/\nOrBdr6Unt9ajDljSxbfiQa7ohzBHZ5xBx2tIPbwq2qNpPRYxU3wOvwmKvxTY\nxaqw2F+RNwBmFl+t+r0US3wVHo7gF3Q3flEZwNvCcfUPqLOQoyZAgUqhvRah\nCkjiCJlYTtaAc6OSbDW8Mbp/F33pfh3/CDqIk52uPsswGTLBZM6OVPIbyHwq\nS52LZag5O99XNT+ErxvXTtOPhq8sOEylX1yXby7uYEHNYOiF36UAcjVATLIm\nIwnKR323Ck1MWebARlv2yQI7u5rWJ/euZydPZoHxD/YNuNBTopQM2nMbak76\njZQXV8RvUlvrtJaRtg6PKbhZnPqmnUxhnoYj2OICl2LS3hKT1TB0gvjlHynl\nXsKTPKN37sff60VgI8W+hEuRwMDdes8ebAASIiOlVKyt9My01NAECoSsA2Vc\nvtqCTz0DCq2yVE2NX3ZWjeHtW3mza01LomWbmG6Xt/d1fNpScVFxChkCQrmB\n/aSj\r\n=KC5P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.3": {"name": "@vitejs/plugin-vue", "version": "1.2.3", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "2e8e008b1cc3a6ad1dfbec75743c7ffd9b4872a6", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-LlnLpObkGKZ+b7dcpL4T24l13nPSHLjo+6Oc7MbZiKz5PMAUzADfNJ3EKfYIQ0l0969nxf2jp/9vsfnuJ7h6fw==", "signatures": [{"sig": "MEYCIQCKvxemQLwZcaTRKbCFffsj2uoyVhZjeu9aUD4OuytwdgIhANQFj0e0n4zry84F0kGIFskv3z4zjImqQKo891WWgXZB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtXyzCRA9TVsSAnZWagAA5TkP/2qPZwOSRSqLf6t8y/yP\npfK/F8apSXwsuFMr7G/fRe1Ie4ZyAktDA6yzbaQmYKnvYJfLUah6RmnMYTB/\n+epVm/zlKMqcNCJraSkeNDoHaJeEjoZK9DwHW6JbnIFGtVqhKCWZ25RaAPE1\nsCZeiglMCeCKLCRmsO2mx5/YRDa9259R/81Y+jTgHFM5pfBPFoS79C07Eshl\nEZrOty23BOhSyaR3Px0yGaHQMKR4ZNn+XAaubsRjkFIvINnsPBG/w8JNhNGQ\nr7GxiAyUhhSywbZuTp1uooZytyMzDZNTyAB/mYfGJYR0wQH1cWT77BpIQPMh\nDIa2U538ulCvDV9Fw4H3d8EZaZHujwIE2O4/4PaPbBisrNRoeWE+BK0orsVz\nj28xy4GOWzTreeeZIY3ELlagDLSO/Rm9dpsYUc5/+NBfMhjJuB/DZ1Cu9fBu\njLkMCR+UQ1W+6q3N3S5t1nL5FoWmjbu0CoZfA9A3eEDp58WoxklC6mpPtyA4\nk4JWn4+u6Hva9HIbiFEJVL17rk0dhKBh1cLmqsJldc11winvsJELHRhJ9pF6\nAunUewP8DqxvQYoOU339mYjN55pC/SxTAkyDwsKOWkqF0BbHc9dYPaNDsSyc\nt0RlqTqfS2XDEC3lIoMwtXq2hvIdJZ8IhWHW2DlQswATEx1dY5nt9smC8/zT\n5m+I\r\n=IDHt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.4": {"name": "@vitejs/plugin-vue", "version": "1.2.4", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "a7aa6e6a31c556a8b781de730316deeecf7f56f2", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-D/3H9plevPQGgQGwmV6eecvOnooLTecPR63HPffVVWPEhbfvmtYLWgznzs456NBb2DItiRTCIa1yWxvGqC+I8A==", "signatures": [{"sig": "MEUCIQDp1d4L3O1dbLmzn2RSEHbLK6Ra6CImsOH15H6Lo122TwIgY3tL95EcgiJywKbCRu0Dpwy1IHlCLL2y5tBXlmtwO9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2PHOCRA9TVsSAnZWagAAWPkP/3JhYrgKGkns59cd7OuG\nUaBbxLOg/LlvGNb9m98HJghxVMUw+Ns8Rq+k44eiP/EVqSNZeWzSL8q1g2v8\nQ10t2NuvEloidehu1fcboSvRQnmMebk7zzIEBrcJ9JNejF56r4akwJjv45ii\ntnVxlwe7Vi4dMocrQUKPlBKQewAvRR8gVQoxrCaYBqZbg8eGdAjU/S0D4jk+\nl/eQwfkN+MQwpVTbi+UhFs+bhTpHwui/dZQgCDNmffU3RINs+93bnNRiTNvn\nSwrre6+j8Yw+RHiIAawKU7i/cl3kFumPgWoQTEuk0j9rj6VzM57zWv2qhUdn\n8ZjWcVk6z367q7ruQw+/tSWxAzcFqduqHsdpReyx3Wy5bx8jnMng6klfQmcU\njLwkA8XoIzNwiPsmd8d+/Bp8i4kzM6lhJjJ/9pM/kuoOfqowlNAHi+1WPsIT\np+vFKS8M9dqqUmNoeE7CILvUr6mDuOEAWP3TySEz4y+0a2d/su+PFSgDZja9\nNlgCZ4bllZ096nJcyX58r/xbygi9Ei62nBQqqR+kf4F9VFZABR6GWtFnOGuE\nhYSwAq49d680wYH9qHblHvvwn7vwtiXACdeVNsN04y+ZLq9jQhRxCLB2zlwV\nCT3XtflmXdIK/DwLpItJ1YIIQFXjBwNypvLDN/RouS7mRWvWxWhdKDphKvgF\nHugp\r\n=9/Jh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.1", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.2.5": {"name": "@vitejs/plugin-vue", "version": "1.2.5", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "ef7dc4a92e53fe866b54bcc1266788513262ac09", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.2.5.tgz", "fileCount": 8, "integrity": "sha512-GIR31mdXTEfvElmBUaRhDc5v7lfdkEdawWQqJRiaRL/5qKsH+xusukglkvJz5y7+c6dEpxgmvcATv2BbB7+fzQ==", "signatures": [{"sig": "MEUCIFk9BRmrHjETj9YF5d6xUFjevwcVsPClUdf6Qpdj4aFFAiEA54NjVP1YQ6rAHWMkcW1CIAq+VSpuW0DJ7szzW7ggM/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7DSeCRA9TVsSAnZWagAACtcP/AySXLACnC/IiheXTpO/\nx8S1HgUnqqgWDfJPstYtc3c7bVwm+FJGWtXTRacoZzjjxq3USIMKOc7Id3p9\nRNFVegkt6FwZ8CBqmgut4KmEQ7A2BL6qe2Iqyt0bidmcr/L7zjqgYkRmU132\ngbMdygBrlj98INq4Gj281cRiCFVz5svWjTbJok0GsfVeE5ilVqdgm4tFSjtV\npeS6c/QJsTPBvohm3UrNJUR05Hgg4U3Z/T0knXUbtYnW+39o9q3WYwZZ5BNH\nL3UbVN6i+3suI71V8ZWvBOhMHGUIHd9P8+tplp+V15C9JiL0Y1qo5nSRmzPE\nZAp9l/l8QSaLnTm6UJWh6ZGvlzv4keHCWAuSicKAn8Fjvd/2o8V28dQpQ01Z\nrXl8sDvF4qgkmCHdcSlGAcJgQw17f0nx1K6o7PiIocJP76mNykkI20aIyPZE\nr1Gt+cNDpDq7Pam+mP7yM1xXca75eba1gFKHS15G9t0LQMpPKAWCSMAIeXNb\npQnYlt5DUKwE6epLwM8gMg7+yV3pHkz/r3OQySC82AWgzrVpDQT1MvJvJvkO\nht1EqOfgAU4zocUY9t+tT9ceSQkYhrraCGUJC9/vYo3a3WXTnjnAU+fKijqF\nuBvQDZb1CpfPbqQVFJWNmUGXiAAr24VhXE/mv+yGRLjii30eapZ71VT3iRLL\nMaOq\r\n=M8bN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.0"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.3.0": {"name": "@vitejs/plugin-vue", "version": "1.3.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "fc89a6c9b5a5157916afece4edc8fb55bf1bbc42", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.3.0.tgz", "fileCount": 8, "integrity": "sha512-wJvuJdTBjvucUX0vK4fuy60t+A9bJSZxc59vp1Y+8kiOd0NU5kFt4lay72gMWPeR+lSUjrTmGUq8Uzb99Jbw3A==", "signatures": [{"sig": "MEUCIGhuN1HdZ88Twfc+I3Bxu8tgSK/2qPLQcMqtgTrN3Hg2AiEA1aKnd8VxQ4Ab2LTohd6eiwAQE3aXP/9PwcTAR8ScTDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/9j4CRA9TVsSAnZWagAAESAP+gJOwC2UZpD+ZnFFqeAP\nZXBU+zbSQA6j1iBOCaBq8GaEWJLsPI80zhtuXcrptSMe5hdDILqY0rvw+jzp\n1IYBvngtgTlyKQBxgA0gzbfmqY3vZG+yjsCHL2U3VdeMqNLPc6M6xcZXFQgz\nCV4CX8JrN/gkdfkILnbunNH9uyqMqg0XftnpygWAQQwmZb/k2GLIw3AiCSKV\nLHVDxVtHA+l3i59+/uCJDWRPlet5tMt9p+3cmLkGpnbOlqWq3FJr9SFs7as9\npFSyGpO1OrwzbBMEF0PbiQU0Duubhj/NzSKWCd8Unzz8IxM9dQuouAq5bBOD\n3/X+SSt+Yi8U135ryjUggvkLFOz5Ja4Nk3X/21NxsVr12Yx2LbObbXWGLixL\noHhrtZpK0wnRbDcRJ3w9HWRzY7BX0JcSi5jcbWgxWKD8ZfXYXSgI8k6UM0AL\nLODnGlIEZkYe4ddIgUa0gdvLwJDh1bU7DL/MX44iWPKAdqil02B3ge177Ayy\nKMd3Ztb/k5zUgOcdZQCgShdKG+ATr0/SRJlAnyUokemW+bEAmmG0S0lEFtK6\nhu40bZXse+VDHJuvNskCiVxtjB5Ewrnim1HB7d1n6WZ1+sHw5zYUGFqfMKgi\nc0iSZ7b5p2wEAlab/+Xe6B2QVHxpekLpqVQOWwT/t/hwXMKUfPl5mbOjcRL5\nH+nj\r\n=7ads\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.4.0": {"name": "@vitejs/plugin-vue", "version": "1.4.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "962ae01b7fd16ad4007898c64ed639136e12215b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.4.0.tgz", "fileCount": 8, "integrity": "sha512-RkqfJHz9wdLKBp5Yi+kQL8BAljdrvPoccQm2PTZc/UcL4EjD11xsv2PPCduYx2oV1a/bpSKA3sD5sxOHFhz+LA==", "signatures": [{"sig": "MEYCIQCufRKVHJrDEIgFDAcY6+jmHbjqekITgnnIrDF+EZUwpwIhAIQfjIv6ZDfjAx7NORA1gJ4hIlUwRSJMVilSk8BxvdjC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDdnWCRA9TVsSAnZWagAAD3sP/AxoTcYfLPHAFT7LdLvK\nLJLWhbbKn9jP3uOMbvWNKpjmoeqHUfoKkgnaYv4f/QPBWg5uVlUlo2a6jBwe\nKAOk+RBFvtu/b0/zn/N+CfBccd852R5lY3mywmgK8ogp0MK66xCn5ptoR9Cq\nOSQG1fmYQoRvdhKcdaUhtagk4ihUi84XT6BTlchA4gdWt5nz+qPAFkAOnaqj\n7/qZBxv9jn6jGij/ody3QPorsOAdpaNnPXKdO4vS1tSvanVG/U7KyEF7zS1+\nmvcfqQRoqr7HlP+s4TNkh0/X4SeDl+I8eINjnkIee+5eWGOsXeBhlb8TWve6\nwdJLBg6tEtHr5iitLpvxulOMvGtQa+8Q4hJoVI/eQPP1KHHZCSeqCR6v+1iW\nm3lqltIp3yqMI2bQPZWD76KAWzNRa3L1V0iQy+FF4GOEYw9JTLNPjPWtmnoM\nOOYI13y86+NtWocwHbIb3iQr58jwspecpk0L3ED7/leMhwr56PKgmGnsVqlG\ni4qZBjomYFoevXbVksx6ONcUTnpelMtbLQpjoLYd6h8froIHcqrytyRt/VvM\nifZipBGfO21gYnzi26k84Ryq9aHXQTELEeOrgW6FGf0TKePp6BkA81Kq2yKA\n4JDhgBleUCYUFKJXYWdBl44DCH8Vm//9FhwBSG5ftvfwNsJ4th22Beq/ZSFX\nMBKy\r\n=JJmS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.0.8", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.0.8"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.5.0": {"name": "@vitejs/plugin-vue", "version": "1.5.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "8b05b84e2e81d64423800541d9cd34245226a60b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.5.0.tgz", "fileCount": 8, "integrity": "sha512-DwTvr9J/SSYHsrTiYTWAaUn0OnqgdINlfuhtxpWHcJVUtxGt07zZylV/ITjfeYgdcUCi0oLD+n9lwLnxlYSGtA==", "signatures": [{"sig": "MEYCIQCtHKcKjFRH5BGFQMwwAqrm5ccEfaUir4HdBWyNe+9+HAIhAMgJGQp2sGSOMBR1tZHOryuFUFkth0JOxuGPn1eyego/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJPwzCRA9TVsSAnZWagAAkT0P/09zJRvWW01COOaplzMq\nFhJAtl10Km4xEFgpmSr1eQFpb2w1/eQh18ZNsqwXPnmO28BBUPYRpfVm2YuJ\nBKd7L3JmeXNnx7IMBTQxkSZ5yImBD9Y3qDVwwh/E+nq1viVcwE1hJDkaFGxh\n5E6PV83cFMIUJTNJ/1iClzYQjO2iMXiXOv/cPn2IuHujC79goHiHOF3pWZk4\nx71/gLEU0yw3li3lrmZVd3GMeNnFcLokj9jtcRLqPG07kiQUMwndU09HRSNI\nnmMsupTy3lscFxNE/QHkBQetX2GIUwpN6OrddoeSgETE3r/Og/y21L45P0RI\nukDZzta0h8OTvQZVlZqv/uJQPWcJYqzskBC1N6t53uHvc3r1vsVzJg5XGbjU\nBiu9RzuYLvqZ1u6IQWMXMDoZKlMfHtzuBTPBRgYKiuIcK3af03cyTdiZd5hc\nVq7Y5IYkDRQuT36u5gLtl4wUkE4Wxvc0C2d2AN8du6hG40LBtDfclt2HU6ac\n9sKPNYQXMp+oi7qnthzmAIdw+TVOKK9dTyqfc8lx1BCrnuk1KMDiqGXIxYc5\n1cO1c9XuiDJdc7HwCCtuhX9ffBLRZ7EHIEFmZ9QspswJ3+rJJVzDJRkG5+nf\nEuSi/p0wBucneIp3HveqDA9yvEs92MseRlpCyPSWGPKqnQVaDi5nKUBphr0U\nzrfi\r\n=39ws\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.0", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.2.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.0": {"name": "@vitejs/plugin-vue", "version": "1.6.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "e5558e20c20e9098cd5bd65b9901fdcd2c354983", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.6.0.tgz", "fileCount": 8, "integrity": "sha512-n3i8htn8pTg9M+kM3cnEfsPZx/6ngInlTroth6fA1LQTJq5aTVQ8ggaE5pPoAy9vCgHPtcaXMzwpldhqRAkebQ==", "signatures": [{"sig": "MEQCIC4bERGMtiU3PE1XU02El/BY6q4Irr2Q1y/qi6AJEABcAiA8SQMggnxILE+9UrhDwqs43bU4NvaF7mnEoWMZQhJcrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJSe8CRA9TVsSAnZWagAA1vIQAItWsqtQ9/eSaNduBm7u\nsAiKeIgDPr5wh6sv/rvWNpNqoWkFaGxPB21s54OcB989f/pNwizMlMXxYgKY\npkSMZtEFyIlk6tDRg4qljHg8x3/2+U6OUQ3QigWRYHVLZq57l8VP9w8OYExv\nBFnZegb33Wd26ulhHZZ2mjN9fdo4rQMzqDV23dxy0RFXjaBqNtORa8k1Dp+9\nT0Wrx7DpBe8NFqS4gEvsfekkIZxZ+2S6P5E//1BxLPPG5NyscVcl4qZb71KR\nqELw4Lonai82Awqw2XpL92rgfPaB/UmF091JgiE2V613A9B/zzoFCyIfSYqm\nwDrCe3dIohYzpG4iL46E3mYgAW9HarMI+oeUP5yMke1GxYVy+XDTZlEUzaUH\n1wC6gY4Eg5DaZCIj49dzVxnr4XX0lZAU0GFh90bhb6PQ83B8UScmzFVq0GqH\ngW6Nj8O6Q5KcKqZuevW/kGCvorTxb1FWzg0rCVtCZNlMQn6NxeQlbwD/6vmq\n2lNHkG/xYty51uqAJZ46x0vWlEb+amShrIHvaEJ5z3bG96P8FuBsyHBajNB7\n9nm2LgVYUyHJ4v/NBDFDHXbZ0R8VmAJQ5NP66+vWSdAKJvcjb7VhJrFw1fc5\nPn8jeHFq4ZBgLsyDpOlD7rNHAGaEEjV1TiqrdGzP8arqCwx4H9wsknb9c8Sl\nRmoH\r\n=3ViV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.6", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.1": {"name": "@vitejs/plugin-vue", "version": "1.6.1", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "a40a06661af61fdc1bebdfb811b28f50ad39dfb3", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.6.1.tgz", "fileCount": 8, "integrity": "sha512-tX2Ju8fOyEtfstfmwjfSJcstTFXwzdFAUbYJ5bWWifvKIgbe6B0FbM8l3Wb7wOaaxKn4FYkii7WQnAPcsTqaIA==", "signatures": [{"sig": "MEUCIQCbA2KE1+iOlZ1wpr5ZxFljNNtT6w9+foxYGqE8F+TXuwIgR8zNl4wkQ/rk9UdY7cACzIUsRlIG72d11hbm61l8K88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNiLWCRA9TVsSAnZWagAAAg4P/0RjyYa6ag/EKbU0huWs\nldjp0zI6YM9+j1fWdbyIm1UCCUPHxTmcS+gVqDVL9sbG3zHeu9zNMILS5tKc\n5ht1d1CzxtdnQOmsoK6GCtokqqMNVMsbIcqq2kzu2u8C/USH7oZGmhtRcEYy\nPPqhZp/HrguCDtRhcwvRGVhW9qARxd+SzSjBpVsxMvP3EQWM/bHdnJpcSmqE\nyD6n/cOcrcB1aD0Zxc5kGACRR4i8EwUbsnQK4tt0ZTRVXXc3LgIjXUZZhK5+\ncgQkLhzm26y5ub/I/QTbLoCIw4Qm9NXoW9zlMdXlLoS28ohS8SN5f67/XoPz\njjeQrF5F7zkGT0HL+rf28znA0bRYrTpQ9qRJrnHK4lHiF46c6+d277LdmdAv\nhvaD9scrzHQmknrydF+3SQ+L1oQqzj7Y82ye3WtAGDw3m8xu79iwQu3m2Mvf\n0YltQ6bQSRjmqNf4Agg2QQ0U2Aic1vcgJVqcILcGt4AYErw33b4kpICLBo2K\nLTsp2nec/d1E2UqmY0RhupsGys+/Yuq4/mNJ2iITcqUu6uPsOhU+Y5bMxzGW\nMxj3wEAmqWRfhoHGPVq9S8TlcDCcps9TWquciuVc6MhhGARuv2hISpeGDxIb\nBJKdKtpg42S4ATqFuc3M8kh/tyQnudj2iQ1uT/ibtzahXKBxVRgPf5tvvAzR\nzRwW\r\n=b2TM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.6", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.6.2": {"name": "@vitejs/plugin-vue", "version": "1.6.2", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "40dfe314cff610d4dd027a0b4ea2a93a257f3fc9", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.6.2.tgz", "fileCount": 8, "integrity": "sha512-Pf+dqkT4pWPfziPm51VtDXsPwE74CEGRiK6Vgm5EDBewHw1EgcxG7V2ZI/Yqj5gcDy5nVtjgx0AbsTL+F3gddg==", "signatures": [{"sig": "MEQCIGLCS2/j96c+r7pdQw2HiBs1BB0M7IWfJiJnX1O0UvvUAiAlAY+m+TYiBiVkmT6hL81kpxul02Vs4gK7G4Y5BfGwfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOMsGCRA9TVsSAnZWagAAxJAP/i7OZCT4MPoeq2DwZDRE\nmVhDBRUMvTy2o2qkABmKOIvOCjpIsYw2dCP2gU4E1+X14HfNSkZyxP7CupK/\nkv8SnDSV/urxSQdeQOCLIqrpTFK6pHEVqyPGWv2aCA3xrz7tE/hden+Q8adv\nch9KwKRZr7wihBXXUZyoUZNlVDyPZSQJS974wu4cbz6e/7pn+mUlEfOVJWMx\nW89IJVdpCw/PrP3KBP2mtiwJ4o96Ra1JQqclGD4eAFKeEQUbUvVtvf1Xct2e\nokN5nuv4CcWZoOyZIZrQ+NMj2zx0Heql1l0h+jm4P/8gqVzyhZGfqv4SI+u9\nvNFW4cI/Dpxw1SwXraKEB5E9hKhf7wsM4M9Et5eZHsTuQ9fHNb0e4OnsoeiB\nfjW5ETW0QTMIBADH74IptatL5yPdvvYY0xYu0LaVwYcZOIBETTpw+G89UDXc\ne9Tu1iwm28grt/vgZDtV0bfOg3VB3W0a9qbGx6rVW4XA/CqHyhg1Bq1FERyb\nU5pKNlNFpxyjAelwDZnaQI2C+d4pY8b3HPkDDFATvaPk2nn75xGAyo0h8rbq\nyBMJRTocdL0Gn1xA8paneydHOLuJlKU6rOeKmEmpcRDAhkNssx8iuBdRbDrs\n3jC6nPAOsUSgvKU+5ND2bf8PWYjBkM2DBRmERnfdCrw7RztCcRgadZZN3cmm\nlEyt\r\n=WHg+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.6", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.0": {"name": "@vitejs/plugin-vue", "version": "1.7.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "88b95295a2d15da66157735a1432af595c18031c", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.7.0.tgz", "fileCount": 8, "integrity": "sha512-aNa36roLdq58Ay7tQ70IEXnSGwobC1xaWk77/ZgLL7x+YeBkVsd1UkbFaKeIs6a5KkHbBGNxz6Kto+rW4wHs0g==", "signatures": [{"sig": "MEYCIQD5m0GzVZ1ssP5hDJ9cc4jl1vshUOi2a6furir5DtOZNwIhAMK6fWOaP2qSNLBS8cloLYOwRfgmvJED4ogQgA4WWWhq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186836}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.6", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.7.1": {"name": "@vitejs/plugin-vue", "version": "1.7.1", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "8b0fc87628425a8ab39798a023c652fcfc0619c9", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.7.1.tgz", "fileCount": 8, "integrity": "sha512-ldKF153gyXMXT5ZNkzk05OUXO2rri9fEZE6YOs7WxmTPAGQkwhKtzayke+wXhP5AfRY/Svn71nUVHgJzLMrasg==", "signatures": [{"sig": "MEUCIFgHHi9fAJaYLJtrIS1RgXc1x2M/WYI5uzPo3impUeMbAiEAyXIQHAWHohBxYxsensD3cyChwVQIaP68mled3eDGSyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187472}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.6", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.0": {"name": "@vitejs/plugin-vue", "version": "1.8.0", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "412b0f280eedad4fad982a0b3f1ae7b4bd128aee", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.8.0.tgz", "fileCount": 8, "integrity": "sha512-vwR5QkJ9wQiOGTHtYHJuwCaWtcyBp1P/pSxGg5j5+OeWawQso7yLktllEPl+zHFSSDE5Xibe41LjVG4lIHizmA==", "signatures": [{"sig": "MEQCIC0re/huuovH366mCyc4KdO/WwTTzASBT4maC2WuUvkJAiBo4cTb7+zqWPjwik3cR5i4euujiGojqmqOj3sZn4Soig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188075}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.12", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10", "@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.8.1": {"name": "@vitejs/plugin-vue", "version": "1.8.1", "description": "Note: requires `@vue/compiler-sfc` as peer dependency. This is largely a port of `rollup-plugin-vue` with some vite-specific tweaks.", "dist": {"shasum": "6cc2a8dfd04201e3c868c239091aaa89aa75f880", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.8.1.tgz", "fileCount": 8, "integrity": "sha512-gktQGZ7qfaDdVJhT86fWSkyhP+bdoA81f5S2TQOL5Sbe5q7B36XfLGq8Q0BpHoqhPSflAMe6WwM1IecP1sChRw==", "signatures": [{"sig": "MEQCIBIrlck5MM0B16iebgUbiOEBJ6pepJ8IX73q5L7C5j33AiBpp6ptTVuSqrNismVGI/ddSL+fpB3g4uklMGYyWwEhdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189082}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.12", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10", "@vue/compiler-sfc": "^3.2.6"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.9.0": {"name": "@vitejs/plugin-vue", "version": "1.9.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "ae458a2eaac1d06efac8d71a4427d5d742466d72", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.9.0.tgz", "fileCount": 8, "integrity": "sha512-TQB87f8baZsTZO/g7Az/dTCwV8eDxwkrP8hMl8HpwLJz/NKvmXtBqa7bYg8GKWNwqjy+pBRLdgiML+BI3HphOw==", "signatures": [{"sig": "MEUCIQDWifOtEXrwCn59DpBUOkxfiPF2jxgcbqdTUxLsMJJUFAIgObehKE3AbFC1RqyZnzBoaYMYVT9FZjq+NPzg0hlYafU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189140}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.13", "debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.38.5", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.13", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.9.1": {"name": "@vitejs/plugin-vue", "version": "1.9.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "69a92d066f7fabde656c5a9ed983bf8c066bcd8b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.9.1.tgz", "fileCount": 8, "integrity": "sha512-9YuxaU2nLoSS/S1Ep4QTG/pEIh96LlauNM1g7LN/EOJ14Nj8HBeSy1OL26ydxb+MPhKn5XKGARh5wQF0UjHbLw==", "signatures": [{"sig": "MEUCIGbOeKfRfmA/dPtj+pHB5W6k7Eo+yX6tfL9FypjH6wzsAiEAq8QZZXA1+zWGY/rtXhuOGBL6PRwofylg8ripyhNAl6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189339}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.14", "debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.57.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.14", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.9.2": {"name": "@vitejs/plugin-vue", "version": "1.9.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "7234efb8c3c3d60c7eac350a935074ab1820ae0e", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.9.2.tgz", "fileCount": 8, "integrity": "sha512-QnUQJvGmY+YT9xTidLcjr6NAjKWNdSuul1M+BZ6uwTQaO5vpAY9USBncXESATk742dYMZGJenegJgeJhG/HMNQ==", "signatures": [{"sig": "MEQCIENKx+1Yv5x//4i16yb/j8mC0QFbeMlLdb1W0XAaqN7MAiByhmYeQXdzw4MST+GnjLEPEVYN+BUoeFQiA/cnBxm9uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191600}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.16", "debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.57.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.16", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.9.3": {"name": "@vitejs/plugin-vue", "version": "1.9.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "93d61893ce6c723d0209af0483ec8b91a2cd811f", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.9.3.tgz", "fileCount": 8, "integrity": "sha512-yW6H/q+4Mc2PcVjSOelcsMrg/k15DnMUz8jyCFsI04emc3aLwo4AoofUfGnjHUkgirrDxSJLVqQVGhonQ3yykA==", "signatures": [{"sig": "MEUCIGJGR3KPs22kJcOnkxk4rZNhTJEPc5wrw2vj68GHKHpxAiEAxSD8c4/+x8xh+eyEDbweznrmCuzx8WZwuxE1ihATlPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191896}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.19", "debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.57.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.19", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.9.4": {"name": "@vitejs/plugin-vue", "version": "1.9.4", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "4f48485432cbb986a9fb9d254dc33ce30ddccbfa", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.9.4.tgz", "fileCount": 8, "integrity": "sha512-0CZqaCoChriPTTtGkERy1LGPcYjGFpi2uYRhBPIkqJqUGV5JnJFhQAgh6oH9j5XZHfrRaisX8W0xSpO4T7S78A==", "signatures": [{"sig": "MEQCICyB9zmnfMrkm5vUPGieqrxHLmC1DHuaQLkIxI49AaRXAiAMj/K95dphgSk09Jj55rw3qxy0EDEzVdMgK1HpnOnOVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192295}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.19", "debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.57.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.19", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.10.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "1.10.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "6c5619482c51e2748b19e0e0d8c855e1aa01b893", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.10.0-beta.0.tgz", "fileCount": 8, "integrity": "sha512-HVdqMh9Bi9Wdk9YN5ho+VlBTxmZFWO2LCM/856pSlRK2qN2i7O2mFHFVAjTCRLtZjBpOwwB6W9RkKem4Q7oiwg==", "signatures": [{"sig": "MEQCIDT71fBaKPRZgskR2cqWZM5SNw/O12T2EIoUl8OCdsyYAiBEQq3bL8eHxUMWTe9y2QzQtUtcSshaaYPy9SEiKWTWIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192583}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.19", "debug": "^4.3.2", "slash": "^3.0.0", "rollup": "^2.57.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.19", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.10.0-beta.1": {"name": "@vitejs/plugin-vue", "version": "1.10.0-beta.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "338cf2a73aaf69fdc3a80cf1e2d748aff69af834", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.10.0-beta.1.tgz", "fileCount": 8, "integrity": "sha512-Qejy8iaiiRxLz3qaqxuOFsyGwVhu0uNK7REzPAbR7PaNAMBO1jexqlsKFh4+WITY4a/vvT4WxLCBo6rGpsoQfg==", "signatures": [{"sig": "MEUCIQCwuHCLk08VjcWcPdIWlBbdGZ73bFwfis/1r5PAPhew7gIgKAeYE7XdbKHgj9ppvcpnQk2nlxdpOUkzyZclywmfUDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhl/pcCRA9TVsSAnZWagAA4wIP/ijrc0XSaNBQ0KgSdv8I\ny1qJotDI8ixiNq8aGCYD1g3FIgjNzMfyHTaE7YgBx1YdLngMP00aJ7SpS5VC\nyt3tA/w4scgsfZVJbv+7//LtKKwwmMU1afdghOO3guXFx9xzoslSWYzdV2+t\noS2NhgU2Ad9cEJdYTS3V0jNPVPJ0nUQwBHJHA9kYFIHgtWxYlKbsPHXdLfU6\nBoZb4KuEztmNTJGS4EFq+pjPKMJ8PuUBZMlP6KRe8kQ497cV0K01A2QsP0KU\nZ8fv553YzeRS+Kc1Men7WywK6AZO1l6TsdBA800g/8dz1COCM3XwUDBtDmF/\nYC38fE558ntMQnF5bLMP+y7v0aHfqLx9Na6GGrGaSj6Zdeejwkn87ipMbJ0q\ngwBalReZzfyp5rGBBA8i8tNsDXNa0ImkGlfVI83FAInukA1bsl41bP/Mz6Am\nPf+mW2fKthi5UjOGTaTiEe2qUK5scV3bFhAgR8GtPrJvf9JON3gYuMjECHC9\nt6wfq6BsUkOKOAOrwsJQsZpaAOg+Xktq3XurwUbvZ/0a5Qe4EQKeNBpQzaD9\nKnFcyy3XoqC3lUgh/W3B6sbhU2uzw9Ie4yEmn1XqRGg+Yc4qzP6bBpS6HnU1\nnTY45YzK7omJOeiTdplAlyTIp9i5IYPNl0xXnO3EugnYo2xV7fRNfDupQNRN\nZRbE\r\n=VDDm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.22", "debug": "^4.3.2", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.22", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.10.0": {"name": "@vitejs/plugin-vue", "version": "1.10.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d015c12d905bb7f76274c2bb272c3662151bf3c0", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.10.0.tgz", "fileCount": 8, "integrity": "sha512-XkSN7lduhQ3z/WMXv2spqt9TCS0znCjnvIGmbud2bxViWWWR4JWXE+x/cQZ/klR0Ug4Ts9eubL7LXAysON5Uvg==", "signatures": [{"sig": "MEUCIQDyyBKPsf3jRTJjQGxJ7j+rndWJa3WiH6IPAjnycKx7IwIgDO6kgmReaO9rN47qg8u/HP37hB5aDHvKlk6sI2JWwyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm3iWCRA9TVsSAnZWagAAxsAP/R+wrIvG7HTKPJCQPBdK\nB6qeIEzI/kbjjMGGk8pcTqSLEIHopyRDG6DKkrtWArRZQ6kDlADJUHqgbyiN\ntwdkSsDRoUgk5o3krcx2D48jU1AprvJfxI4f3PTvQ6F/pW6q+H12pHHWulQ5\nzvJ2cDbh/EJOg4Dleg/w5m1mp9XdtB3iWdecFutKX2g+QOWxxbd9JUvh6K89\nM2yzgtqM2drNOxGWCMGu9iO0Qdp6MCQHz1C2SfCwcYTuh56EzLVKg0Mw8eRa\nj5sWSBmBioF2VBEKIWWCvOIs4B+VLnSXwe87F9pbc/Ad0vOThn6Zy+3lJMrL\n+9xd2yGn509oHQXuyD0+6rhPRKSMjcgo4e+YL5yiNXsXjj24n0OriAAlPmiH\n3CzY4cdXyiEWbAwBsMmnlY/ob8/fM0Tcy1xI6PPA6Ov5toPLniWJl7LOm3xa\nyWA3qEGxsQe4jIXMSjqV4GkYah0eG+5+KxRewdtqQpoAMFtP5V+mG3j3uk9E\ncT9tykb5jJqo6xyad6a0/eolBt1l5cPjOJgOHL+8yKFnL+eO+c2bq1ldUTFQ\ngbk6Qiv+5twS/2ShXOeJTHGZjHFHf1YEAcQwZki90A6mMH1hID7oGn9S6ioV\noOo8iNF19MOIi4/FxawIJFEi3WmiHpPLzqILlaFVZ9TvwHUC3u4g2A6lRRq1\nCTYW\r\n=J0tb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.22", "debug": "^4.3.2", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.22", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.10.1": {"name": "@vitejs/plugin-vue", "version": "1.10.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d140e93d574c5eac247a79f5b15df665dcb7635b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.10.1.tgz", "fileCount": 8, "integrity": "sha512-oL76QETMSpVE9jIScirGB2bYJEVU/+r+g+K7oG+sXPs9TZljqveoVRsmLyXlMZTjpQkLL8gz527cW80NMGVKJg==", "signatures": [{"sig": "MEQCIDGsUI1te9qv7f4+o+zBBxDt1aEN+lio6/Ba/FtY7mwiAiA9gjFCW3nQoBtDOtLczngtptVtEqCjndSHvnkca2Ztvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoIutCRA9TVsSAnZWagAAw0QP/1gyFe9JjQnA/sRzoOGB\n3q/uXQuSjVNTQ/AQSBK+fvLeKeqlYiiH+a+O7A92t08nvRRc/y2nrOarKngP\nDziVHidFq2BTEXmhRfQ+fyHOQA9Io875EIm1ilH4N3OlJWpS9QJLmKKEdIGB\nwarXT64QTcsLhC5+prkaGocfqlKt6TYvNuL/Ot7bZ7iT9FJ5jge2xCPcyv8b\nXuosKYnRmbmwnlJcwl5CSR69z/ifGEVFTQh5k4yveZBrf+yVtLp08dfdTfxu\naiQ9URd+0yMT1YxDpdnzeSSmg/2lpqafmXZaONleKDfmF2X1fTqJfzOm9HgA\nDYSwHgTa9Z1305vjSTYRqpsVxCVVy8jWUrsktGHi5EHQdgpD+VdRAxP6KDCV\n4+Hq9W7MODgqd7vJXhYTqjU5+9GcKRHxFkXSrx+AN19Qzw5x4g6L9+jovGkr\nOiH+n8MVcHm9UWhMDHBwEExH3gB56/BkuozX8bXKJ2bA/n6L8j5a8Cw2cW0C\n5t7wtvEXAQfFUlesR7wk9Ox77RPamd5aIMRWoTJU/W6FDxKFzgoVsEMelzN7\nDcpLqeFwAebRFqz+PoBFAWqRCpKg+weeuf5xn9DjnxATc1dY8IwM6hMGhfui\nWNBfnHVvHos73Z470CM4ZoFklBvH9ZrNDP6rwgylts+tW3Aadp7mzrAKMyzv\nSqbH\r\n=Gpyv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.23", "debug": "^4.3.2", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.23", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "1.10.2": {"name": "@vitejs/plugin-vue", "version": "1.10.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d718479e2789d8a94b63e00f23f1898ba239253a", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-1.10.2.tgz", "fileCount": 8, "integrity": "sha512-/QJ0Z9qfhAFtKRY+r57ziY4BSbGUTGsPRMpB/Ron3QPwBZM4OZAZHdTa4a8PafCwU5DTatXG8TMDoP8z+oDqJw==", "signatures": [{"sig": "MEUCIFd7XMX372/fZ8tI9XOMUXV/wjy8QcIkAsfcGG0l0I5kAiEA5gE8Hbc7rDsawbArRfPSb4C3uB8Dk2Q74Vk/QVFMU5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryK3CRA9TVsSAnZWagAA1oIQAJZ/I9q8zDkdxqDJmGuJ\nAOeP583dnqBGYfP+ApUwf+9aB9kNMGANw2XznAn5aA3HBgvV/H+hXvRA+k7r\n+7G43dCbD9fOZ7hEliJX57JuRnKzl7HFg/ZJwex9zSCoJ9btaHby8BJRiCE+\nTdcBXCEgDjuuJcJkGKyKmb/xXUfY4CvKAoZqzRLi9ClneAOEV6GZLDY3g05C\ndbmRCro8GMSht5O4IwSJIAMMJ5spwhdOcen08MMMoC2kAjO5zkstka+qMNtz\nPNFj7DXQuQ/b1wz5DVzzhWftw8X1hi46ZnpeMCPovPz3IirTYxNepxkYENZN\nuf22DkkadHqV1BtJxFs86gjLmz1Yt6xXHWscGzdSJxIl4fy8zKVKg+VgvbGK\nQscbb1Gv83BJiqgLRNayFbTdbVpAUHvPXl4AzOAQS+mht7RNHQbgSfmEtxsB\nE3a9/sX56XU2nV4YcvexwZrx1f2XjxrIieQmlX9cAKhd6SWyE/APd/Fcz1dN\njReqQwwSA+MJL9h/9Th2+LdphlnXAJbgDNq+6XQejUorKWpX9DUt/uOV5tOG\nlMS6hu/MDhdzeigqg8J0bpYroxwnY5elBPBvTaSTngV5RY9SWPm5S1y1BSc8\nYbwhZf4SDcDHqTY7wctBXO4o3uFX7gZg3Z19E/RVOgPVCTb8AxgdXOaxHMgc\nry9U\r\n=ywcU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.23", "debug": "^4.3.2", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.23", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.0.0": {"name": "@vitejs/plugin-vue", "version": "2.0.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "5b5879cae44f48874196d018213bf1dbaca8f4ea", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-4Xn1h9OcaAf7KYrvz2oEi52fCCCLcCzyr3pDOrzYTWrs0DrzNOXt9fT5IiGb1f/uoNTdX3aAkXVGNXrGkzF/zw==", "signatures": [{"sig": "MEUCIEFePYb5mJvaE/jN7GWXh2eNo9Xw5TaQpj1sXu0BNMuvAiEA/RYvh1+wReYNbMCjtuM0Ny/Xo57PNdwAcpWX0M8aDlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtav4CRA9TVsSAnZWagAA2XAP/2LeMIWONoUMPU5ZhiSU\nwIr8qGQ6qPiRtN9Cx1/P9cKwgRF6ibZZ0H9t2Qv2hJH1VCo64luUzKgoPym3\nGARWBKyY77hH9rBiNacalnhdLAbX7+CLns90aoTGQ2fRbAh/zqhdEoHmi2Zf\n+Q3L2tGd04DiK1QO2tL4kvMNCDOgkBjSwTRDiKXAXCtW8nHGdEUcvhothMnF\nCghFBiuCK4snkhrFQmYtP2CqWuBVbi3oCSVmoDiIG8fqHa4GGysqxJK/SL40\nkMuPFXLzvd5afVdaXZZLrKULZAX9zNTkSvhLIAOHYOu3t17k9c45m6t0UH6R\nqnbuTb6wFzFJCg4ZuL+SdOUKHLjDlJe2hZdE05o0ZGU/Q8e5WOcPCO/C1qQU\nGPFtV/PXGqJN0J7NPoEVY2qj0GlQfoFdhirn6GQgjShNJdqVUlAsGYZIsS7u\nn+sNcw+rvQAHwBGjNW9dKsz5Umfmn7LGuo3dgX6/Xn96yrLWdrf1Ez7c7CKm\nmfwaKucQbmekd/6EysVWCUZRr+diwQ4D0fpQOziiCD6dYzLYjPthNdYWVsrI\ndKri9Sd7PXJY7bNN21zQFhaqrZxTiAT9ZtWCViOgL6QfXviwPsrbUOn3Qln3\nGulLYPPwqWESuSv7kP7RyYqrPV0tZ+pNRBGCl3GB4Zfbm6W8829aqJFvG8xn\nKadt\r\n=luRc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.25", "debug": "^4.3.2", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.0.1": {"name": "@vitejs/plugin-vue", "version": "2.0.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "db0e5eacf96358e04cc501c9008079b25a70a4ac", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-wtdMnGVvys9K8tg+DxowU1ytTrdVveXr3LzdhaKakysgGXyrsfaeds2cDywtvujEASjWOwWL/OgWM+qoeM8Plg==", "signatures": [{"sig": "MEQCIBfqFty6bPZWn/PjT8RNufA+WTWDcfZT6f5eN4aZPo6cAiBLe+Q2aEOnCMak/A+Kx1fCqq5F4fBFN54iQVU+SIGtaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuJO2CRA9TVsSAnZWagAAWB0P/iiC/twiGy7/XG2XnJ2Q\nxHu702tPmJO1UE2l3g/iok6IoBwV0zOif3IUfmT3HLFw95YL4BH6HZ4JgzOU\nwFV87kWc/0pWTFYEHBs/uWt4t39CxHooiFoLHYedCzsGgQ2Hp8cJKmQ/5vox\nmwllbpnFYPNklbRM+RhrpkyDUv8TcnJWVEGguGKOp1I1OPvAsS3ImQwprTKj\nDelvW037bcAH71kdqRT6zdzix8eBmZYQaJ1514x3jQbBbqK+2ROAuCJUPpnD\nroJ/WieWnXi9c73lqxESsVyjb3QRL6+JvNmovhPkUyr5FTwqLl0KMwF3Q1pY\nP5EsNXJSwKveUI57xFgi9cfPsHxNfxzZ9ocR1nEFYlxV8J4kNKeP93w4yELH\nHB516CbKpG7iG80dJxYsurUIM/W5BkqbPYnQo8mkU7wm78kUxMXeBUs81rls\nDWhOBQDWAsEGCL1PoAaGEAu1Qm4R+MweUDaswjONY7be3MrsBcxk/kDjsiUR\nKvUDTZ1zoQR4LYlWPlPgFyqVQCZQAw7z390Q41q3OomgLh41iwp4b8KEaEC1\nOP10YDwVtdKFG9gXdywCIZghCypYYqco0Kl76Zyvk2VwkG0AVp4cFV/Azqxg\nfV7x+YJTtNCOgQTNrDu8tI4YVvcnKFX3h5i2O2GvKs22ElSIb5RdhjtQFjZf\nysjj\r\n=5onA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.25", "debug": "^4.3.2", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.1.1"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.1.0": {"name": "@vitejs/plugin-vue", "version": "2.1.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "ddf5e0059f84f2ff649afc25ce5a59211e670542", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-AZ78WxvFMYd8JmM/GBV6a6SGGTU0GgN/0/4T+FnMMsLzFEzTeAUwuraapy50ifHZsC+G5SvWs86bvaCPTneFlA==", "signatures": [{"sig": "MEUCIChPnxHqG3Ci0sxvJr/7nDgDV37mTYgEP6tFdiZHBIRsAiEAnYNONWPnkK5g0sOT30b2iv9SDFSJ3BIb5G9WZwuC7mQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6qqXCRA9TVsSAnZWagAAk88P+wYyf+U9morz1mDWypUZ\nbJ/Md1X2Iq96LUKlyoimgtAtBfz8A03CI/dL+SaqNxe2cDHTNq0FF68CmroE\nHGFJD1RYo5EN4LtDlNX0r7tGY91yPv7PTDsRiRVfBmUs32yY8di21mTtQKhc\nw+3IjAiYOmw3Tsc2HYCRHjRd09a9e1uxKC7OkwGuT/UQjiBJvtELI58Mgl6p\nvZSfcaLYPS03mN+4fT+FWNmHQ6fZOa+uQvumzb8aKOO5wq1MMwV2LXyxRr3T\nKvm1G1KfZSA6a8HN//U133cLpjalVC73iBl+pJf/BGr6dvwhVMC7l4iWWm0f\nzDf3u+HIUCvNDEQ4c0EX4W/RhiCx1psp2x+pu8kVgLcnWCeC1GUazcxDjfJK\njatcBZzrPrqhlgYfurJMg/4ib2RbS9fu2si86QENj1vcXcZpo+aUDCXEhL99\nRe2ei/Xmf5edSLYxtE4SAs6yPnxqX9Wx1LLtm/ym0UVfhhltu28aDsKJGmaY\ntRDsmXr4hRt91WtcOr5P+BsA+XryefhtFjCQDLf0MLstPQtr+WZMCrBvXKUS\n1CdweaJS2ycealtL4UU8ZKqVK/reJwT/BRzqXwM4eNoyAHrdpg3Rp1nW0ocX\nt+3wj4iaOULHiBIr4S/i3gri6sElMjDTPUtNZVgcgJxnTgIYKyzD85Edblxn\nt0gs\r\n=K0l4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.26", "debug": "^4.3.3", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.1.2"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.2.0": {"name": "@vitejs/plugin-vue", "version": "2.2.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "a0affe3ee09f70a9a1415bd39c0f8a58fa78b419", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-wXigM1EwN2G7rZcwG6kLk9ivvIMhx2363tCEvMBiXcTu5nePM/12hUPVzPb83Uugt6U+zom1gTpJopi/Ow/jwg==", "signatures": [{"sig": "MEUCIQDe2ibtUNtZNeG4cgz7jxWX6Lz+OZ5SE9E3WmT9LYubWAIgZvQpbscGuHG5gkQr2Uv0pGv3UV4L77K6VNtFJB/EGU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA1jiCRA9TVsSAnZWagAAFHAQAIFdSeTWVc2sESk1VMc6\nzISt8aWpWV36JSufpO8xIVPSPMgnErsT8u+oxvKoQGzOW67zZye8TU60Q0d1\ngbpYA3q2e8h66IUf/2+5mEpmXWxhnOaSGsqChyU5CUZTWqjz7rBaLzrp+Jt2\nwsXXQjPZObDPQrZISyb8sRzuJJQI2eDttbMd1LyEaLv19pP7aqGlxs6cD6ag\nzW2ixLrrBs77b69vnVL4PGy9Min7lv+uSmeOiq98ADfZJKNa+YL0OEwZcDja\nVOCzo8HkbszOKWNUdxZw76cRMYhUMsSbtxWk5eH0dUI6T+sN/Oc2i38B8S4E\nQF6tjiJxTXrFp2RFJWuiRnj8e6qsgdgRnss1c5nJMMoO/aI0JPENonnS0nLE\nYDWwe79fbnSBTzcvlN+L5Ns3Zc2VXTfhjppIflfz1nS+eTU3LZLvVjlo+ZkR\nwBi1LfiFK/x060NE7HkXZ0k55zAHOavy16XWaiGm/QEp//zQRwgtFD4/tgor\nmJxfaT3fQCZIHF/qo6avKop/7cOzl8Od4SIBOHCOHeTJoxE90YR1Pws8XwtO\nqq467cDMo5ZKxDx59VvT5jFA7+edLN9DFUhhJOUeUz4U27rFZJOIlViEJfw0\n2HjOREjjQcetFrUUEH8r0GF9y8NcpWORcRuKIqFHDWNCzKwxX32K+fABRZzd\nQBCf\r\n=tAYt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.29", "debug": "^4.3.3", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.1.2"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.2.2": {"name": "@vitejs/plugin-vue", "version": "2.2.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "df5d4464ad8cb97c9fb7407a1e5a3a34f716febb", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.2.2.tgz", "fileCount": 5, "integrity": "sha512-3C0s45VOwIFEDU+2ownJOpb0zD5fnjXWaHVOLID2R1mYOlAx3doNBFnNbVjaZvpke/L7IdPJXjpyYpXZToDKig==", "signatures": [{"sig": "MEUCIQDoXmd9y0VL+FrTq5zox+5FCiFkFJYfjYxUb95Z4/DqpAIgUbedOPtPR0imkuXZ4RIt0AjfwW0WAZHjdaYdXnfYfUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD8tVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMPg//aQwzpC84NlyUdjU1pnRxDuEx3IVUH10nKTfqPcwkzi8aWKjH\r\nu4cPzZyOfjqwDsP8C7K3WL9cqygm9XBfB6WuAkL2UtBnMAAr5+V4zMycaF+a\r\nfg6olkQ3L4VhrbUwUUE52DguRFZh4REBq7prqvFk4oRBozw622HxJ04PnWTN\r\nR+Cw585I2x6S0yDRgvTWleqN2yPZJMGEhNtB0lmd/bPRaYBOxlIjxCO9UN79\r\nn2xCV5nzAxzU+Gw9sp5tOud7yMykx4zYHnE3rJGcwr16wdBoRnSoCAi70Eqv\r\nRkZwJJ81xIre12c51FnS37Y8gKxpqUM1mtB5jv50Q0ZsGZ/4Dbm4yyj0NQBo\r\nrCxBHFFP6pg1iD3Kik4dxLTVluu6SO1Vw4ZPr0yE7Djos8pzWI3WWZToKA/s\r\nNat050E9rTsPzn1TAyVv6OXz4bnY1yg3J7J/zGhadQwQlWI2unobZT7YKyQ2\r\nIiEHsX2fGFf9MNt6TASlHPLAOAUzgfe0nc7Q9gyB56Qi9bq/cXPYL1ibQ5jl\r\nWSxG4NpKatTwq01R9bjytDtUhUhh05pAahWY1BdlwFQpectE54VPvE2U4yo0\r\nBxrZTcuE9Hi03E8gCGej0Nql6dnyvFyWUfDvLtHx61/8GyMv/a/ylZfRkX9t\r\nSn/AffaVJFvBoGEqSILczGqb8IKcxf0IEHs=\r\n=QHkL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.30", "debug": "^4.3.3", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.1.2"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.2.4": {"name": "@vitejs/plugin-vue", "version": "2.2.4", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "ab8b199ca82496b05d2654c5f34ffcf9b947243d", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.2.4.tgz", "fileCount": 5, "integrity": "sha512-ev9AOlp0ljCaDkFZF3JwC/pD2N4Hh+r5srl5JHM6BKg5+99jiiK0rE/XaRs3pVm1wzyKkjUy/StBSoXX5fFzcw==", "signatures": [{"sig": "MEUCIQDpYwHk1xOU9H1W8A8ucxlxbvooEXwKL1kzb988LdtkWgIgNGS6v7fhKljkgr3iF5cj+rnCohif/+zoK48MRG3lKSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHQQqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/lg//VbSAGauYviJOvtyVGK86N64u52f1Kj6OxZ/xxLaGnU6u33Nh\r\n9uJGoYzGdZr6eva9EIaNSCumWn4qmjzPbB3YxwKzIa9uE5VPJXdkMoNmGkvv\r\nXEzahUiV4xnqw2k9u8ILBAPE6FItLLnVmEd07WfETkQyNbvtHsULb7xjJqWQ\r\n99uMqLlUvnE79c7QH26df1rrLEq+ZT9Irx0AbEK7jV5N/lLU5QJTBxRZsZAG\r\nAs9cXqMJq2EskC7WJETAQCnhZAhJNFEv42xcPhUaD+Ttodr1+THI4pffDVN4\r\n/ckYjJk/ama1RwkxnJefWvcfzophGor2vxbtFAhWdBefnqLqNSoKlRE63fvq\r\nYyHBQjH5b94Jzo1feuhRpFB2vBHpoDdJazq/LmzLumQA+8u+GW6PRng67gRz\r\nMlnAF3K5+lTqJzJDW1ARhN4z8UE2AfqWywvFsNY7sQ5IHZgzqaEpBDavTP3k\r\nFYY18WiAMnCa80VQkxEvpPpB1Rz8EQ8X9z8duhNvUDC/LNZdotRUyjvHNbyi\r\nKP3IFvz58zJHUDTEWITJw7ACZ3WhPwa3NnOoDaxLwHdouDes+Zi5kuQ3Hhly\r\nR75lz+HQql/4wJ8uhAgAQF32PdU5WnO1lDcDEty5IV84E+apWA6BZG0cvGql\r\nWn76owWGjDkuDCmRIggdMAMkM1qQqxMK2so=\r\n=fQw+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.30", "debug": "^4.3.3", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.1.2"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "2.3.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d639465b7aaaae0bb5060a217cf1394087a2c5eb", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.3.0-beta.0.tgz", "fileCount": 5, "integrity": "sha512-YQrg4B5Gv2HZtP731lB2MBmQ3YqwKIMIp7eO9h5B/yhAi3Q7mKHa8tfPTPCaGvpotJuipQidvk494qIQYID3Jw==", "signatures": [{"sig": "MEUCIQDWkBdFcaFTLf85N9p1XQaapker/4Otgi5FuVLzeP2XvQIgEwhlK6gv5KkRZD0CIx3m5xyyFDDnjZCJ/YCbEI6WgD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOc1JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokFQ/+M5X1KkYQ1aPb1+3tDrBHZn86M1TMtl+OaLnWEtj+YvCQgdj3\r\nBkdc9jIKxyncvC1aSwzT+d9bTRvjnRyyDwwqQgU/9/bN1xhHo7cMJeQYKDN4\r\nGdoLP/8lDK86JH1sPv1KUBZgL6ATAHPRs1EObsR8qtgAzXvTMQ6NZvBF1gOi\r\nEqtMOai/HapFXdZnksgMfwKUxH2p2DKGOiq6lFDR0AAmBHhzO72JnwANg0fm\r\nbshNnvIBavzfvqjHb62YPPsJMUg2JbhWogPLg+lDk52YEt8ggcwjmZQ4MWmh\r\n4XnO7gct5dv1K1/qX9WuVxbMczyFYqaGV6jW5nCq89ROzmLHIYbsVGUViEfr\r\nHSsqezcpArjalYQkPg3fwn4esYJUJmWQzWAFhtxgGKX5XCfmsmP1E2bD/uEm\r\nU1neMGommbMSevNUcVmIvoaqP/WpFLCQMvAvjnGI4/kym1/8d9EkXJFFPF+Z\r\nBwDN0vgmq9MUKeNUjEJUa9Jo39F8ELTyuY/r7HiJYmeGZvDacHmMV+AGG89X\r\nra87t/63JhhENQM0e8/IH6UpgY2+3s8DrstHSOXClq2HL+EM6sG5YL3soM/e\r\nDYw1XWzjAvFVwByTRt5Mx0xBSRV0Kq+EWt9LmXk9fVdB/FV4jPKch2E5zVcV\r\nN5Ld6kWEJp5PnEcyoYzVne/Vy6SZEvIMq4s=\r\n=xkKC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.31", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.2.0"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.0": {"name": "@vitejs/plugin-vue", "version": "2.3.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "217958134e9c5fcb93a6d02b8b459cd744963b27", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.3.0.tgz", "fileCount": 5, "integrity": "sha512-3j4lppvOMAYyX5mpmwm8GN1ZuqGzmkOCDGDzwwQKU5aJ3pTodaWhprZatuDB2cAtAcNe+IiGBCllBkJSLUDFYw==", "signatures": [{"sig": "MEUCIQCYZhf8Oarqo8kL+BNMxX0JNXe1i+aVqw7mFnqNWl1VvwIgH814CYKB7P8rBlckxIgccuxjyrmv+q/nTWfJRqBvBeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRE5xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJPQ//TkPlot+QfJ3Ogekw+NUqStaRP8sC9k6qL5At6+6xahpd7Xsb\r\n/a5pZClctMUVfHxsXuZgkrrfBxk/4FWv0248ajrqc/wqPpJgZM6w2ZaxTnjD\r\nQ8hxNBASg5H10JhIKHhP8lwiPkeM+md+GZ320Qsz8r5vm4d/cuC6OTbfhgB+\r\noQyoCdOJ489aY4+YtgKfnwTCSXyA0pPH9LcbmReC2/O8ZYlx5g4Q3o4bow6j\r\n2Yb3W/pVlEr9fmBJz53ghxECI16r1wRa8v66MLI/8GVqdZeBV8oeo/d8gcnr\r\n7LLVUfFBZgqKL5xRBorubyaNf7XRGiDl4P7v7saK/sQjPyOPsZM1RDKOeLrK\r\n907TslYC7lP/PWrZB9M8kspIIbPZ8NqxIUGOGbqqlbfal2roDZ1c3pbg1JOt\r\nnz86l3TcSbr0LhmIVylUCElA7bx+PCuO4liU78X6dqiXWZ3ykLCB1RGRogNo\r\nnajl6x1J273v5hb80thqHIdTCkm/POWL4iI8UYr7cWdeSBNjIrJVkO5KAcqs\r\nEeI2SIJyQYqEZpnjM7cmJqVBoUAuc8kumMnrUgYhf66NWxxDOKwWHeTC36Ih\r\nh9DMOaqKCj5pssbNVG8A67+elML6vg7E022nrCZvs4mHiiVovLNsd0JKofwD\r\n+P9vKvx9HN3pmOIYcf/TZ2d4BUQ+gKXfJ3w=\r\n=sxTV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.31", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.2.0"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.9.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.1": {"name": "@vitejs/plugin-vue", "version": "2.3.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "5f286b8d3515381c6d5c8fa8eee5e6335f727e14", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.3.1.tgz", "fileCount": 5, "integrity": "sha512-YNzBt8+jt6bSwpt7LP890U1UcTOIZZxfpE5WOJ638PNxSEKOqAi0+FSKS0nVeukfdZ0Ai/H7AFd6k3hayfGZqQ==", "signatures": [{"sig": "MEUCIQCmFgWy+uCcc5Od5wgVhTbJ4ULKrNKACHLpDGbILdU6uAIgb+2H8uvLjYLknH/QsqWKWJqNtgrLEjkEvAaMHUnqRx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRJqfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXkA//XyC7U3/TFClwuu7hmjk/w94pxuX5/TzUm+9xMe36WYQfX4Tc\r\nde/pxZ2sataftoWd2eNVmbCUiWiMTPffU5ZNM5hwDyiiI0Ur49roa5Xvu5ED\r\ndyYXTS3toDf1e0O/ugiJOc24I0ikKVgvoDwaCeJx/bIExze3tnhBWoemEqMS\r\n+4VLlqWxbHW55ncDK12zidIc02NhkSMJU1MLhtr6eY3vTdE9rH9AzDnEO4XS\r\nSm2fW7tUB7AeeWYMyNjvmDRHEiPtG3sJ35R/I5B5p5MLBSTTEQ3mv0XEOPpD\r\nobcx90hxvSajSxFF9Y4mkYQW0W2/gVm1wmNFNwPNRMUFs8ssVu8nTKK+Pdfk\r\n4AZm5Th72I/o0g2d30cCdSwQVeKd+v/Jk36w84T3IuhsB3n1wLPqW55JBoWX\r\nodE5jIWqThqKa5u0JIzZbAqGq5KbiD47Sd85ptq8p6Th9RDjHqzwNrxMhof1\r\n6N8M5qfGQ521dAaR7enqudNWDKJcLKZfPbNZZjDhsVEwLHu/pRvWOjaW1hE2\r\nWwjyzEUmcj3ni4cLLyIB8JXpT0Gfl84HQwEsVu6X/umbjLv2h+mjF9pOqCzO\r\nkiRUC7yr89yF/+RJgN1OP5mKFzUBpi0/PhCV5WpdiLnAEfvBZYgdtPIyflad\r\nx7ndGQt/lIKzqxfHjYXwZwpL0nSVfWyrCKo=\r\n=gHbq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.31", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.2.0"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.2": {"name": "@vitejs/plugin-vue", "version": "2.3.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "12ea7a42e6c83d5ea48e14dc41fd2bbb5806ee37", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.3.2.tgz", "fileCount": 5, "integrity": "sha512-umyypfSHS4kQLdYAnJHhaASq7FRzNCdvcRoQ3uYGNk1/M4a+hXUd7ysN7BLhCrWH6uBokyCkFeUAaFDzSaaSrQ==", "signatures": [{"sig": "MEUCIQCHFq0RG5HyZFYQiNtDJFNh2SoK97owKop2D34RwZbuYwIgCjB15uYGtilWeRD7uUGmu9t21GA4STD/E19yx9kpKUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicrkKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrx3Q/+N2qkVgS5X4eDR4CCTuNW5OvGVgPCGTTuanthydjEPbqmi+OH\r\np2k6ePlf7Nt3lqq6lGAO/4QvPVqH7To4Iai+8q7d+M1O+4EQEi4ioHfijXUt\r\nRnRUYLgE524mYeJUeWSF0na14P4uHbHkkCckyma0nA4/wiDwwZqsIJ3e46Pl\r\n/Ax7uOzkEISf4gfERp4SK0g49HjD4pPiiU+SfOUUQQrp6YnjQ/FdGOkm96kd\r\ntEMpOsHPAEGBlvHXsDnETCEfurRKHH5SGFJKNc0H1wDGUEO6jgLUvO2qvi58\r\n89+5x84Fb6+tS5xIUatVbklQ2xnY7JliINE3CcToi3e/KEzPeblVVYmE5G+b\r\nEoKHxT3Y9NgpMuGtqPgNH2eqZeIHRLDo3yEEPCfFup7op800eV3yvYubYTXG\r\nvRDCDxhhD1fC/SI9IrFzcZ4xTEq0Vq0m9Xk/RQVi8xD+d+lEuZtTTMpA91dR\r\nHFXD2/2KHRtiGIlgtCH7Phw8GBIMEt8O4kNPK+C7ZbTG2CvE+mv2+P1oqsbN\r\nHt4Y+mmJ6u5brFEldY1pjOGSgHF7O8+d0LurCXS2/0aqBKz4TxfuL/9rRT3X\r\n86AbegaRPPli3PvEyiGj2WA+6sPhN2/JvBIe/BojLN/RcFLGwQagPHYT1qMZ\r\n8LCpKszYuFelgGtoer7bYrGTJiCzP78Cb4A=\r\n=Edfm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.33", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.2.1"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.3": {"name": "@vitejs/plugin-vue", "version": "2.3.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "fbf80cc039b82ac21a1acb0f0478de8f61fbf600", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.3.3.tgz", "fileCount": 5, "integrity": "sha512-SmQLDyhz+6lGJhPELsBdzXGc+AcaT8stgkbiTFGpXPe8Tl1tJaBw1A6pxDqDuRsVkD8uscrkx3hA7QDOoKYtyw==", "signatures": [{"sig": "MEYCIQC167pocnAWe0acYKE0/7NSvLk/CdbolicyItKz3YsXZAIhAOsEl+LtnwAMrjQJHAf9UOmNc7UBMI5Glb5S8LyULLkI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie34wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVYxAAgnOVFgeMYdemQtixn56R1a48/saUB29vDvBGu4cKdKIR1MUJ\r\n9B86dF2vbzsJhM9nJCaxFr9vwHws1EWFscuG8kGClWtGv6cAUH+BCdk0T05Y\r\n16AsmxwRejtJ5x160kEvkpdEXgQ5ZzxEsyEc8YyFamY9RnfInYp0SPw+x6yW\r\nACTC1JEhRoiPNgxmelwpg71SNJwRT4bMixuU16bXOueqE3VNJpBB8FNGbj4h\r\nir5huuHDhDk10VSdc21dBk+lAjDACf80UZv2wsaOv1fgD0fABcBemv/zh2Og\r\npbLpcaXXsKYpcBGvxxK/D2plN6yNOwga3vxg7aEhGf0iCRxuafiIRjk8FvQU\r\ntAMJ1EvPKcVizaQvTE0RXoN9DPMq9K9GtFMj0IOzNhvXRQdCFlJRryMdmsQt\r\nFnA1+fi5xJRmNgcXVNNa7SzkB7N01fVWWQdF1XYNh6Ty69fL2u4gYxV7c4Yg\r\nY9EEaP0BakOg6wpy+D2XNJ1sNoVo3Gf5eF6UKwcYYece6bNzIqJrHWz8vdDR\r\nzQyrL/X1wCJrcvW8VyZSKmvhKjvbnO/SQzmeMg0I5kJ/5+bhLta/ZXV/C1/A\r\numGsydYBVN8AyuZ3Nwgm/a7szTeOeyEd/8nvYYCf9SbMPswFliGgl+9jINtk\r\n3cySO3QvQNixHJnsJqNM0fn+0OYEbHGFXNU=\r\n=pXJ+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.33", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.59.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.2.1"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-alpha.0": {"name": "@vitejs/plugin-vue", "version": "3.0.0-alpha.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "6976c4d5bcf066b44d5faea87e0a0f9f52dbe66e", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-sWj6B7FjcxF/nFDAdwBE+OrqBgiooi9RWxjr+ab+MmO03IBe2viZTjiPEJV1uwsQ3wHNANn/r12vyzGFFWKlwQ==", "signatures": [{"sig": "MEUCIQChK8Bc2WJXO3COTbySHDhL2X8dPdQhTvSZQSTjMLLwkAIgKKndaZJkY7dH/jpOG6KdAYjEdCbgO3q33jE9j4NbzSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiflxWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojERAAnsVu1jxnDyZyzj4b57Vbfnmyj6gAY8PeXB0H8F+5P+PWwEa9\r\n/uUBrfUDKIVlteR72alSu20AqToh7oon9Xmnr8NZJ8X+63ZiBbfGWMdVSvZm\r\nNuxYVe1uRG5HGe35op288Xn9tyF0GFiX7sitHAlVAF/XcLR81dedp5zOeh22\r\ntNiXWbEjDA5EW1wKC7hW2RQpMZbtyc7fikBuJ+lF8UYuOJ5AnwfdoH09PbX2\r\nWzzoRNmn/JjyYmzU60eLjmSexW4jMSLXQ2STm4fftUe1Nx394RORSyff4rS7\r\nAYXxgf+kIdQmrxodIVWls+Slw36Cor4+PRkCwImP0ViCaOSTpkk7CqJAuW/i\r\nGSvRIE0yKmnKoIbrlp/3s/iVJ/nZFc6wU8fOJMrTyYQi2h75IwxEKNOFErdX\r\nJKschD2yygr8hiphVicpCj961Ot6ux/oCcL07RMJGDJwVsKt3JHmYboCKPGz\r\nXxby+di6GK/Zq1cLWNOjDRe0+a/ocqnL0FohoLOkLXMMQX4S+FWSsv8KqT2w\r\n85qeDAG1McPyqDvx8s1mkYNdzodv7Eny5uxPAtvBcsQdks0REnB27wnaHOa7\r\nQtZ1snaXVRjB2sCQ7I2xeKi1Uav+lfGstnzLItvgWXNpGKWcmsq1PwbY/R9k\r\nH+STAi10ngoTP0vB5I663exYwKBSzQH3i5k=\r\n=N/C7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}, "directories": {}, "dependencies": {"@rollup/pluginutils": "^4.2.1"}, "devDependencies": {"vue": "^3.2.33", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.72.1", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.1", "@jridgewell/trace-mapping": "^0.3.10"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0-alpha"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-alpha.1": {"name": "@vitejs/plugin-vue", "version": "3.0.0-alpha.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "94c770281b59ea85c3364601e448ab901928907d", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-wGiGwB36BgvxLVFLdWrR1/Aqj2HOMmn6KzSdrb/jfRelEj4/uquxw6kl3p7OW68IGvR/gh7uPkZ6HPrQoUxXOg==", "signatures": [{"sig": "MEUCIA9kf1qm+4kffAK362J+Y2BuVLsMpJQXs7WJoGs0I3cMAiEA3pBC37eO9TPsMOT69/yEpqXWrsNBNLT5rwMeoJ3YAWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihZylACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqljA//XTfHcrKRQMCrxFNzNTDjbkLP7yEhR3a9gxPi4AjYYmln5Wll\r\nCB1CmBrQsTc0AdSuAcpLqKALgUY5IKQ0KKhx8hT6RuIFwSHFRhKS2rReStSg\r\ndvMgkhbUJgz/VNSYIPND4IAtiQZ6xLSM06sj7p8MomtDkeIS47jg5iMdfIpH\r\nngAXBjL+vs7+07AwoTRAEyGE8W1HuA6wa9B4S64dAYsrVFd/hM6W1Qc8rk+o\r\ndrNe/4xbGTHsh7xlo1+/ZlMXy28C/ALAzvGGOmh36OEHeP6B30EYJ9gpCWIW\r\ntqYm9vFg0dUHJDibbA2bevbPAhC+xoYDpujBlfKmWqCXIO8mI5vYoJv4BeMb\r\nXS0ipb9g5Djvoge4B/78V5Fsii3C8prSJszxWZRQV6ERJ/Wh2fa9/T0wf78v\r\nfTtAO5FU2nU7QSOw5PHzhWdwa9voSavI15N0XxDWxFM4XBQjFgcUpXKvq82T\r\n8slSWVBpQndTnb8CBHP9/LfnubdN2ii7hBIBXIdhctVCzisa1pYmhmEksocz\r\nLRFVpBHWSA2lz0VuOHZluYkgIIqUO8ZQdD9jzciGSp6GGkDMt1ePhjMbA3/N\r\nujTBWA6fQzPqY0P8LakJHx3+2jrf24eIMGeNyIoi8+DrQcCugyTctrh4njXY\r\nS2vWOG78BTgSd/HbpEdWAeQJfVXSEMiqwCM=\r\n=r5JX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}, "directories": {}, "dependencies": {"@rollup/pluginutils": "^4.2.1"}, "devDependencies": {"vue": "^3.2.33", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.72.1", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.1", "@jridgewell/trace-mapping": "^0.3.10"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0-alpha"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-alpha.2": {"name": "@vitejs/plugin-vue", "version": "3.0.0-alpha.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "0ad0bd6bc1329adbcb2d4eea3b22f696523ade95", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-UgP+lomMX4kFqTFBoMFOjyy6/TdKIqm3h9kFZSSkxvdOkENrUR+PXHnNKAbAUcjrUQR96Rd2HXry18nxlwBuKg==", "signatures": [{"sig": "MEQCIFCZyyxiacrm3kkpU49S7gx3iD6yGbsqZVc2Lq+C8ALpAiBMkrlslCUsxHVP1+neSumoSINt6iiKs/CzqdXmydIEZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirsuKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRJw/+JVlOHxBQ0it41h2aPZI5JZ8LLXUmlg8eOkPN4l30BLqOqGgw\r\n43DVfuJ7ZAYar42BCsHc+XwR9I+YBtyMIja9Nr8p/32nipLhF1L3mZNo1N/i\r\nMwxFjQZ5kBoFlyeFW/zHe6mDX5xtUiArdWBPTHV1W4ESrua2u9NAHrOuS8lf\r\nxSG+KJfB69LoQ4sPq75JEPiNruvTgZlRM/dBg3seNq2KDkJavsnr3Looy7j6\r\nE6nwQGg6wdpLl7F/jvuzT+UO5ttxGy60b3yJLl/CpkMGzGjAhSHtJqneCC4i\r\n4J4gaVhP/updg1bgvAVedCjAFw+1AVNctvZ+EHOpXFFS+ETwd7mKg/0WF7DQ\r\nQ59OT8HirzvrdE+y767sTlCgk7xtka+w06nUMegWp86dQQnQ/AK9KVTCo8jM\r\ndVPayfRR0846j94H92wSuXIXdyeDWOkadNFNGKnD3jvP9Y4GzVDpraNhoY7L\r\nsO9OiPJKnM1P+dyCioNWxVlJw+700RgHfmkWOEBLXMivDUnSYbonpblqKJHE\r\npt5V0YZ6k/pJO2cn4pnKe/57bWta3gwllZczW+0rWaRjQVD2g47FbQX5sqN9\r\ngvs/YhrKl7qIKiyy9bmlB3czWaDJa3FtgAJaV+gM6XX4O4H73yvzpbGVVddf\r\nHudNeyleWbmjHuRU2qYOO+Wg8O4szbEE6os=\r\n=dkF3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.6.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.75.6", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.1", "@jridgewell/trace-mapping": "^0.3.13"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0-alpha"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "3.0.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "092f4f50ee183818e252331833541dbdcae1b91d", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-t8os1QK1qpovpgYAJSOWYEu+Doy/DZRW1cNwMvUl0qo+Yv7D9a3cxo24oL01lbojcc9ABQhyvUP3BsvFNtriqg==", "signatures": [{"sig": "MEUCIQCF4kJctkeXsCa6+nLI7Je9fkk04bBo/Kn4cFvzddwMGAIgZVTIveiibacPoe4r3kiUfRoanUXLdmh9+PVXfMchFOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiscxWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEbw//ROz3cE0rqZncmAqDff648jKfX1n3QbxFg24OC71kDNy4K5fH\r\nJmlCdTSg+0BMX94UML8GrBq4GQAsYTINfYgqIUdLjC81r3Dd6ALGvKhksg8F\r\nP2sHlF1r+ue7QSt+LbRSV6eZWtMop4qJF4bXSq5OIqH4XK9SVaKJfvdH5Yrp\r\nGJRKp5kyx8n+vrSfUg1I2oTAA7/DCtPISdXtfKvPRIfywj+4274qe9guZHPu\r\nlwaIJxfAatrz4f/lgeoWrtdCX3XqmvyMtD+ZaG1X0zpn+JmMW/ynhB7Rz4I2\r\nlL8Kgb7+FbQG476d7SaKqQ/bRjNNM7UcSo2Oc/xADNYpvEQjDrM74FSFIYNg\r\nvOoBwInMvF315DEyFoG88OxhRbYSk2mABraMMFd7Hgg8g7dkQJd/i1x6p1Zp\r\nl1xGgUj1+h7udRL12XzNhNrAxmv9uNzWcLU2RZ7xJnExzKUYim5h1I3Gi61x\r\nrmT5T2W4D/SzVsF+G8LWlNDdX7FB4189fXmuDH9p5hogPkYIwp017HFAk6jc\r\nRGkbVGGrDg1Li84ewYG8JFDQKILH6A/VcKvJxSEOp9P0J7biMM7tRV6SShwy\r\nnnb/RzM5lMQJHHbzB5wBjnzAVmIoxowsNi06yMOyx/1mY5EENSwC/9D1gr5I\r\nqlS0r+IZLsWmYmwGq6dowKIN99Q9cAVYp0c=\r\n=4kop\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.75.6", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.1", "@jridgewell/trace-mapping": "^0.3.13"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0-alpha"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0-beta.1": {"name": "@vitejs/plugin-vue", "version": "3.0.0-beta.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "65a6be6ed619955a5edea6115dedcfc5da4ed3f6", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-cPVQHIKZkVEQ8qW7+BlbTrGJXNpP2aMKzVhQdTnWK9u6cSDmVdZOXHmKPO2KVvrNpFXXS8R7hHBXMsSApA+XOA==", "signatures": [{"sig": "MEQCIG0+HPnMRMpLLBzA7qOpona0TyfZwgq/AoJ5lDjUrp2WAiA1lgTq0UahE7Tvs+JuKZVxq2orwlyRWZme+KXTIurTvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixUANACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPVQ//QsAWa4fajwPzsOV7D0W7ZIorpq3Eda+LM98aiEUVUpTvnKsA\r\n0OJqhYZyoHQrGQDNnS8g5jd/J3HcRIelJy+GVpnirNUcLAVE1OzHnJIiJlnQ\r\nVlxWrRok444bxIs701fdbYhkj9WPSNUIPkywSp904Y9TgjONtjaM+pKxwS9d\r\nyfZiSvYGJxI2QsCG9ALmWGLHOAb2Fmu/ky6vSpRIOt8tdONtuB3haZVIp37A\r\n/A4FzTc2ggeb4ynnEXCjwiVtf9dEmA9rJ3GAE43CNbEI5rgZa7yuTN+vkiWu\r\ncf7f7Pt9l+81cuEJx8k8hjp2sTrr9EvKbkKXesmcVrPzmrPhY7aYnCHE7G4E\r\nxBzaCxXtn8RVlDrJRCgZnVKrZuzw0FHccWTCbt/8xTZlThVm5QXaATTvdEsu\r\nM1HHr5u/+bgy8ew+0zaB946+1PMXjgVRo3tYxaKzlneBbjhUmldxRBTCRgXv\r\njd9RgTIEEQNoS9eFIpCF1iKRYFn5OIVoiZpQK34VuW6kMc/O1mLbwpZNfXCp\r\nPBhhsFs0YIkGIC7bE+6sRpzeS1a+vIhmzR5Y1+dEO3by9TdQlT/MubXh1ZJd\r\nmpknxLvBI61A1K7Se0PE6OVRskelZ+JldRm+NousHrB6QoOGN/4Wk3TX2Ub7\r\nxmpI9LPglVR/jKjgv7l4dINUln/cKhWkkmU=\r\n=8vlk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.75.6", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.14"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0-alpha"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.0": {"name": "@vitejs/plugin-vue", "version": "3.0.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "7081e2b3fbe04e291bb85107b9fb57a1fa5e6aeb", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-yWP34ArFh/jAeNUDkkLz/kVRLjf5ppJiq4L36f64Cp6dIrMQeYZGDP9xxdemlXfZR9ylN9JgHUl3GzfqOtgYDg==", "signatures": [{"sig": "MEUCIGNZU64UsTQtDUrZFa9bsBfeZugA34DdePTE9As7NfBgAiEArS4/+mFMNQxEAzqK20b0hznXrgfRMDzfPSlRA4cElYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizroIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOJxAAn6jVZ774rHhYAMBnFINxkusEllprMUZOT1ONXtMi3540237r\r\ndYhxrLEK5gF5koxBugtoazBoMRYWwZt28NnBBIbGUOzF//JMCkJvStw7ye8J\r\nKCMgYkrMN7zRkAnA8+EhmQpfuG+2KxCGwaLZeCih8iBH83F2AjgWPaTi/osa\r\nejBbS5N45CTZXFNfG6IpJ0SoAigbAwOYen54R9F4LAv2B1FAo6gakoWx1JgU\r\nlVCuABArg/pd68QgL+0o8zeJs2XdBrNB26xI+/CdCvGHQUtLVRu3u6POgRQE\r\ngMOVXZ/8Iz7KPpeLqEPW1Kt/cY5gTAErg0aWfsADi7ocrXnsnwl5WK5e10i1\r\nrDL2jKh3eDUcacMv4UWz2O/w3r19JEXagVIAcdW3AcMqZ5LwKcr5iTN3MreL\r\n8fP9oH1rvNoHCaBBs+S+8LOZ3YR8IIxlrrrmLLK/MHwokGj7QGZ8LBhdWaex\r\nEDhLYglIFw86gW+RFOSjF/cP8Xfav8raVux3UOemW+Dq8NGLtoePUs0GAtlm\r\nojnz6K6nJMYYv3iOm+WFnN9NiXXZSRGbJfhm4UBAq2729sHlEGa9vJBSH/hl\r\nc+8BnB4vdSi1YMcV0hPnW9lEITWESmilCTkJfIQ9i92J6Sh3w4/qVo6DqFCt\r\nNVR+ibKcogfNPpAgbm+C5REe3m8itbmMFcc=\r\n=hOTb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.75.6", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.14"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.1": {"name": "@vitejs/plugin-vue", "version": "3.0.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "b6af8f782485374bbb5fe09edf067a845bf4caae", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-Ll9JgxG7ONIz/XZv3dssfoMUDu9qAnlJ+km+pBA0teYSXzwPCIzS/e1bmwNYl5dcQGs677D21amgfYAnzMl17A==", "signatures": [{"sig": "MEQCICkC3l995iMqYwjK9w6q65La23fAUWxOOAS4WfekOo3cAiBLeSesBESY4eoKDQndYdISamcFfHIitbsgWORTrwlseA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1PHMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuCA/+I3wZKdZtZDbcBEYqJ+C0tWI/JioGLBSK77adj31cM9HuWM2w\r\n7flpXMgijaj5Xtpifx7OfwEKfr8tYcG7k7Hgu/wETZBrafZzUpXAILQyDqC2\r\nVJ0Im+Y4SGeaLemN3n4YactAKshw5RVhdNqNj/B336AYEaKReNdoAEsfNd5F\r\npaSJtNgDG1iwZhszDmG4r5kbE9plLyQto0x5nTv+R44XP15XeDaX5EH7FFm0\r\nJwza1FLvAlsCMsNP7zJGhm0t7ILmJZsMSG00D3FkboybNcAUlRuAesUbMNG1\r\nbX/pxDL7du/Uerds/iH4nqvV7MeLKPZ5PpmRKZONtkX1xF1rIvodODxDCzI/\r\nU0I0Vq8euDkujIGUdDHjgHqV7PedkWbf70tF/9h5XqijwWE8ZAl05xeL93PL\r\nIzRb5tNxyOBLdgaS7H+AJEAU8x8vgtGd8rsUkVdughdGLFNusPQMb1psvGzK\r\ntkddxVeSHf16qjn7pe+aOuothK1gqbP6wTOqP1mD7AwEgMS5sU3q8bIELSpf\r\ny3CET/6X8ax6euptrYlKbsI6vE/pqgk0n/YvQsbVfilwErCsvi6aoLlUN+L8\r\nNx55OC1d4FYdhAyBiY71krCEVO2EHutDGXPxmQdgYHPovBW65Mxs5Hdshf0F\r\nRFFPMZT4+NQWdK8hTpS/tfPbSUIFigHlyH8=\r\n=KCNf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "^2.75.6", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.14"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.2": {"name": "@vitejs/plugin-vue", "version": "3.0.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "a4ba2d3b8a6a104fe90f1357020339b9a3304883", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.2.tgz", "fileCount": 6, "integrity": "sha512-LAqb+tibmsKmSbxBzsMqWVJe6gDE4BDmFLjIzk4YdHgeiRCTokPbK8yRB3dRxDRwNk4IFwCFVE6+WP+YM0mbJg==", "signatures": [{"sig": "MEYCIQD4Vf8YgZEiMLM7Pw8kP1KBqwCZ1Pj3j/HyXiVlCoZzkwIhAJE6wCUmSvmS3ECg6BfNwzSe2mPgjYR1lkyNBxE7GNSq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9WK8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpREQ//ftsKVhmF9jpnqKS+3/3U3rVItVsZx1fqRbkKfYyQ226iJFNb\r\ntWspU4WCpNJ6vpawPC4JdRFBAY+gAWEos2h9hy1BREnQ1FRCxECXhpZzrd2D\r\nbKSIO5UT4kJSA4EMjpZrS/eNe3flVRPv4tYaUk2pFmOWTFDetmUbLTFXJ5nZ\r\n/7i/TUpBpl3dOoyhhZvn2U70s7Zm3MVLGwykFSmLiyunzdp/ZJj8vYF2RzT1\r\nKSR6cQolOl0FappF3BkI6pVEdUtFSvHmkI0y4FkTiE+BL2Mcxvk14gCkwnEx\r\nv2D8qSeiI7vKOk/mLQBD5u1EZDKdi2XUdL1wRrp3pIrAgyeoDY0L4k5CK2LF\r\nMkwfAO2PycSU5dUASKhtQwOw3aZiPi9F5UNbzT6mX35VnE0Wiu7FT/CN7gvC\r\nGRMGPttA5gPwAuQLX/o8dhfWDVfD7C/B/N1MXChfWsfSyRZWkE6EcCD2KD8r\r\nwc12ZRARLCQ6Bq7hSGs0fsYJX94dNHgAUbxcv9cQ0tw8wCGGtTAAt3zMiV7v\r\nMiiceAHZ3qdEnxVIkpb0YwmV3kLRUyohhthqzwfb9l+UUzArTYlkDp3XQqbN\r\nlEJFGCGMjAHrTTWRs2mto+3DRLxwoJfm5nIQVb++lngacgFAcaYonpGwQYeq\r\n5VS+2FDzUy+VVuAPiOOLLUhvu4o/baolJE0=\r\n=pWa5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": ">=2.75.6 <2.77.0 || ~2.77.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.14"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "2.3.4": {"name": "@vitejs/plugin-vue", "version": "2.3.4", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "966a6279060eb2d9d1a02ea1a331af071afdcf9e", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-2.3.4.tgz", "fileCount": 5, "integrity": "sha512-IfFNbtkbIm36O9KB8QodlwwYvTEsJb4Lll4c2IwB3VHc2gie2mSPtSzL0eYay7X2jd/2WX02FjSGTWR6OPr/zg==", "signatures": [{"sig": "MEUCIQDlZ7IMHk71m/UGR4IwTH1AZWV+8P7o01gNU6QLLFV5LwIgNDYt/Q/vbh8Wlw8H0d2ZuLjOzfKYJ53KzqurLWDofOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9k9JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSyRAAkArLFm+VL1Ds7eGcgKXpq2sWoajFEjRmp6bF7ucN6/AvM82a\r\ncRZegEJh1h6cZ41aprPi6N3aSpgyihMhjuXBc8T3wPY9Z9dLQChbxVUb2lLx\r\nZYP9vcVa7td7XaVhuuWKE3K38aZIHn3MzpkS3JTkWOYGJfljUSdvR/EM+SMp\r\niE8/DSHKX5kzThLvPiVq1kwE10/YueOMLc4UdsB67Y8UNABdpX61JhSww6VP\r\nbXJohnpfdsqvqspUnjgTUBjcRvRFPXemGmwAI0k09PEK84G2aZcJPA1D1O3y\r\nKbYUx5PKXHz+5wq76Biya8RlZdNXNREpLAtnkMntM1sNBxHcC2h3gLZX+Miz\r\nqtIQ1oi+4qSi3kebl79E8zE33yA8h7Cc/lZjmqho5k/pFUScWZVMjXRRv0Fr\r\nwA6Pz+LATPBMtMqwlw4wG33sy1t2wgvAq06VOdJJiEaafQvtsMlVgkFqmQga\r\nWelCGj7vtcA9BJYsIDr0pCsvU11NN1R3xDDgG4BCZ9oI6BJ6rf0CVZmUm6Bp\r\nh55c78HAcbARwGBQeNddL2f9xJiOQ0oUa2ErN0J7QDoyTvWenv0cKTu7eCVS\r\ngrGF7v1FrACK3AizZnA+wyDHLKCtYj5b+PzC1s8jCgyhZFhK5mudPft93dZx\r\nkQ/zzs5yp/JVwsV7Dkk8qNRFX3LSXEOi5QE=\r\n=j+D1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.33", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": ">=2.59.0 <2.78.0", "hash-sum": "^2.0.0", "source-map": "^0.6.1", "@types/hash-sum": "^1.0.0", "@rollup/pluginutils": "^4.2.1"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^2.5.10"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.0.3": {"name": "@vitejs/plugin-vue", "version": "3.0.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "7e3e401ccb30b4380d2279d9849281413f1791ef", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.0.3.tgz", "fileCount": 6, "integrity": "sha512-U4zNBlz9mg+TA+i+5QPc3N5lQvdUXENZLO2h0Wdzp56gI1MWhqJOv+6R+d4kOzoaSSq6TnGPBdZAXKOe4lXy6g==", "signatures": [{"sig": "MEUCIQCV/7Wv0628+dxPg04shKVZ/zjlmEf7MV1go+RQE1v6GQIgHqyBmQo74bQXKcGqbzyA9YvX37wZChC/W6TyZ8EU++4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9mlmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHBw/9HcZPK19B0Akh4cKwRiMY9PsQIKyTDKSNzPYzmuOupHxA2veN\r\nwiUZRTdxL71AohkoqBGXons0Rh/EwKomZ4y23XcqAKP+X9zrx9tc0e9k1Gu/\r\nqRwtgZxnYZ/+NCioDD/d4JUy4Csk7+wz2HtunhgbiKhGC3AQPe+MKKr2PX0w\r\n0HLBf29FUc0b8Ozq77kZLiM5iYA27vq9341aCTsKYcmDBzrTBXs+xpkSz7uW\r\nDN+mwfK56VAiIZgGEOgl0yVfCtzoyiBepF/BIuGxIcB1pEeURNYyX7MqAOu3\r\nDIM7FKLJuvuuk4kHSKprN966wSr7WErEmLnhnPI6epkpsS999YwkkUIEyzpC\r\n7QRuRnzNl6W30Z7fe/AszimZWBnXkQ+1YcidpZ877HYASH4kCK4F5rawJJny\r\nJ7Dmq1Q5jfg6iTGLjSdEI2iTrPMZ9ZRjo9civz9B1BfuRe9B2q6fyZGlJkCI\r\nHZyDGmwNezWpM40bhlhxmJYUhoFmqH4HgiasAPobceZOyC52Lbqjjo4UfRr1\r\n3QdteglnJJ9B4S+DAoY4cPUo8ZA/1/Rfhh16qR956frE9p9MpPclqIPnd4Ij\r\nOrzKlewx4cJnueqqO2PTW7ToSi8e1aZfV8YRNapO7GrUmUU1mcVOmfYdYnJR\r\n96L/v1Sj/KILS809Uk0NOMq4Vde7qYG0K20=\r\n=d45v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": ">=2.75.6 <2.77.0 || ~2.77.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.14"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "3.1.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "b42e285d16f8fcaa4a991cb118a8356c99318e53", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.1.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-cY4oG9isCkk1Ro7PAUc7cSDklbA9Ev6Yy9tqLXtzWeTlN+duQuLFW8EOWAEOe5h0+cztIISw8L+xW/w06fzt0A==", "signatures": [{"sig": "MEUCIQCaaH1fPLYVs51FocYnxXG/82bKpbziuyyAcUhNREKepAIgRTcenPVrri1j+5W6uKe0ds5Nf6itmyWQOoz8NelN0zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDMarACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCdw//X34EA8FfmewhPwLE0DFJ1mQoO58MVXeegde/1V4/OQDIe8Z8\r\nDvEiM3Q1qO8HXsgUE1WUgZZ8tga5IBqokJ76bzfjOxuwwRhhT3mkhtwkQaCe\r\nZfoplSoLlkFXl4+5m/cgSl2q3u46WTFrP3kNRh0eeHDuAYS1IXO9Hp0Dqr8H\r\nwyXLDvra2IVNy6/yycqdCTFO+B/BQuXDhVJ3JzobQmo4kbQKDQaxGQTZqynF\r\nki8nsu7Hu5BdQYLFZ0twj5DRj6gMmG5zrCQFLTXeW3CUAIzZ9vmhW+YyxiNa\r\nmXdhcYDNgEIq+znqwYXI0NQO40DkS8dh9JlyarPJs3DubwprOzvbW4H1Nt9P\r\nKe/9caKudlUaVvc1QuZEXivEOMiKAO3RYmMw8GTqWhcGOOgif6FeBqjyoWzm\r\nPVRFkJq+hY2waM3oaln7aB9Kfoy5Gy2cbLRrswB/gLp8+4QPH+0kwPpwqEND\r\nxDgW+okMx7JQ14aZda71uDrSAxSMS0XZfX4UTOawFhvogeobqCnB5xPoc6lM\r\nvoe7N7Ft3t2Rbf5DMvyi96twiP9y8LAhha8wbbfu4hEijaP84xcFSWcwp+E+\r\nYVYSyNksoGx1Ac3KbJxmhna6JmkpUuF54fnR/ZzoExKqn53Ar1TDPhtX/1Q/\r\nxUU+4iJiwzpewsqG6DzDC2CieZ+vQJLJfxI=\r\n=XF7m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "~2.78.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.15"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.0": {"name": "@vitejs/plugin-vue", "version": "3.1.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "3a423ea6943a450e806da412a911150e928598ed", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.1.0.tgz", "fileCount": 6, "integrity": "sha512-fmxtHPjSOEIRg6vHYDaem+97iwCUg/uSIaTzp98lhELt2ISOQuDo2hbkBdXod0g15IhfPMQmAxh4heUks2zvDA==", "signatures": [{"sig": "MEQCIBIZmoxMiK/Osa/FaWZEeH8b8CS0RfYjpbtzq9LD4xMvAiANYgEFZTwPsxsQqxlhLwFzy2dK1RJIHq8YthBkDHR2xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFcYIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHahAAoZfYBDkH5Zj6q0bkoufkfSxFaR3kySg6nz22V9ak6aMrx4vJ\r\n0fxBzawGHQPH1s+O5SB+mu2vhVVctyZ+mnvSnhfERP70qbh4M2D8msSkXtGV\r\n4xRWh8tP3+rE55MwQ4ZDezreR6YOFJ1FHM4fwVDDToRdWtF4TxvRPyx5E8Rt\r\n2SuMH2Ewagcgl7EglBZ2q6WmbvMypiumFeUgqNh6C+mI5HEe9ksHiicsOdlw\r\nY6ojZwiIIuhxIM3CMIXSsAMdOxLgwycg/mlTqwTXa8MLdXnrvgcLkG6Zd8ob\r\nrqmZNwXQvrjnq4bdLzDhDBcV2a2MPvu34tcfV8UGgcbzJGujNmX07w/3tvfr\r\nylPLz1Pl6QHtTKSH1Dj/XInK2zlqDc2HNPtxmc8S1AgmcGptkcROg2AomwFS\r\nWLXlOcQcNIXj5jg0rKSY2qXBXol8V8O67M0j5JT1I2QY6QGvZjY7iAdP/MZn\r\nQjT2xP4+Iw2be3qpIEJ7xmek8/7Gq3Idzlmw49+a0KDMjQ7FFFHb13966gdM\r\nwRUjwMF+InxRt7ZegskZjqzfivEBcgIqh612Ai0lgO5aN/0MEu9K6StXXZ8Z\r\nn11wXCw0hUvry7Qv8E4u97ryISE6vNzjnDn3rjZO7IswsNExfIWJjzfEkQiF\r\nu4sb2rDb9Qt8T4y5bDkccfUVzbrC+WaAPUE=\r\n=6quE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.37", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "~2.78.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.15"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.1": {"name": "@vitejs/plugin-vue", "version": "3.1.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d8175f2f86d2009f9e0b25e68d3caebfa95e2b4b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.1.1.tgz", "fileCount": 6, "integrity": "sha512-fr2F2eRQVVvbnBqzXotQ99y42QUSjAFrSe3Z8T+R8KhWcme+W46eqldZUhT1kafvw7eV/hlwDb1HUvOdprJNxw==", "signatures": [{"sig": "MEUCIC6m1e4mZZ0NSSeLQg7NhcNAET+TJnwr588KXr51+GVnAiEA09MOvvg0gZukfoTzdmIQ5ZpIKXJQqwWIHyRDE8bbd3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOZwbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdQQ/9FWU9k3Sm1FLkq/GekVI+hsvCY6ZmC2i+2ZpMl8NenrM0QiPy\r\nBq1lL7HRJgTTsHxpfkT3fm0TWCDQzvNY1Gvb9+7acJ5LNeLnqMaIcq1orc65\r\n5sUnBaX+77iWxC+UpRDOaQ/qHFjgBKEDSUs/OqtB4QF3I5ux80iwZOy0Lz1s\r\ni7lMpfHQ8lCGNGbfFnTAUN3Ro4polFb46yIzhjtT/nX8oV3nBqXZMbg007ID\r\nojv3O7UiO12ley0DJ/unQ6lvC2ywyGqXVDUaMCKV9Y3UzuZS4pp+3zT8zkxG\r\ncBJKtnk9UnWyoltbDZqZkuzsswhPEUWzR+hXJidPcPDztibVa3jWFuQn+LKE\r\ncoTJPPHRV/gvR9kJjOyq1hHRwUHVj7K0CQogzymbfRv9MiZ4xnx4ejH2NYSw\r\n22Y2UHjemQv+46KAmtfIEumk3RmPTHrYR8tDksMuRHe3b1rW2I1zjmFvtweT\r\nOLNGfS5z5irxAcjCmqd3tIhi9lr4zj5QcMvksUU/j8MzZAsgebPk68JJ41eX\r\n0ypbBIV4Jnz/KFYuysbkSbP989VU/1WEF/nXHivi3Kjk5Kz+TZC9CcKLfh15\r\ntv4stmhpYIw/EELeW9PXDNoAaDidyccwC7LO6FqZjUbDSdCsdGKKDgYBuRSv\r\ndmlO4u8SYG1Hknbp0IRJrq1ghpyI1Y28FJ8=\r\n=Vc3l\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.39", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "~2.78.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.15"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.1.2": {"name": "@vitejs/plugin-vue", "version": "3.1.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "3cd52114e8871a0b5e7bd7d837469c032e503036", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.1.2.tgz", "fileCount": 6, "integrity": "sha512-3zxKNlvA3oNaKDYX0NBclgxTQ1xaFdL7PzwF6zj9tGFziKwmBa3Q/6XcJQxudlT81WxDjEhHmevvIC4Orc1LhQ==", "signatures": [{"sig": "MEYCIQDl/s8Bpu9LfXD7q7dHa+EnJs/clcc9LQThrRvwKbv/KwIhAK/yKF9F1BQDXNOnhDyIyHO8O7tV64CcVLEVnQaJB/cn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOZxNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1oA/9HpWHz4YbAfQkskxFbh1QuIDy/xxprFzHmIhAGdTKfih/cVpb\r\nGJON2dkP+Y/DmzTUSdYG3skN9NqyehKHEea7hNSU0BvNNCMuVxPFk1ZOdExw\r\ns84nniHZcYgfSVLLIkDdr46foWiU9itjFqAF2V5bhG6wNujykfbnPiQRGrZy\r\ntWXCtE8pBENnLeqSIGoyUn6eL0wFfE0ocN7kN2NjBkc6SgicTE4FEzYXNUKy\r\nHmBw0ULE8qBjRFDc5tcFVKOBu0mhrQ8MmBo4TSj0JMjCH+x/OdqnDxqEUHcF\r\nF5L6ZsQrPCLtOv0ex6cZN2pKAsdJCjsQ+vMBFQuLE5gGaW4vavs41ahO2KHc\r\nKchtmJnQM4qlsHQie+XdA/51OQJD2IuJ3Q+QVh2uuDp0fpFYpMwhC0LHAapW\r\nVPHEVXZo1Uya/sKefwQK6+eAcPe56QCfPMSF68qwiAtLXVIUQ6TAFNG0Mly0\r\nvbQgNgAnErmRzqEnfcNq9vcQTlO7JCbQp8aOhxeisAVh2SS+43+RrK3w2XA9\r\nFryAogclstrzNMUvqAhcuUGw7yaD3cz2ERlBMDlxFdDTR46UFKR9y+2Iaq3L\r\nl2+xAzo7vVNImAgdVhVmo6XOm7+64I7SxlpZUmlJMgyQfyqD7xeJ1ACbEc8l\r\n08cX7n/EQsfuJdZIgh6sNngR/bHcmmXuRfY=\r\n=hpHC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.39", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^4.0.0", "rollup": "~2.78.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.15"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "3.2.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d4537de22ef9b45dd224a4b6fa119607bb4b8bb5", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.2.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-C68mNe9aw86y93jLMFJrcRKfn9OGxAMojN5FCXhTK5HNsxW5ou91HXlRqR1n4faHF0vubveOBH9r43dgAjC2IA==", "signatures": [{"sig": "MEYCIQCuAwnVTo/i0xTxdFTBFeYRlPlk7gsHHxIOnJdtpl9JSQIhAOatbTBRz3lnNLcJTG9moiL4hcHVQGlgnSjSj/8Ylo3u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPVXtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNYw/9FQDon9pPA8dG7wDustYAIGZLxXG2ZIx4v/3FuSof/ZW0krY7\r\nI0WeeTNM6QT4M1kic+D4XFQQmb6S5niRyj+erZCN7ktGfUi7ERra6LV1MIED\r\nP1jomhVvRMKtH3es8HGd41UDuxVL7o2ejz8ImCaQIodonyGIUbfVQ+3jdMM5\r\nq1bdmnPT/UTmfXwsCSPI7fTNxK2o+Lv6/fvykTLhiV7iqnC+ks9V87c2qqVW\r\n2yCM5F85ZxQ1j4IOepYU5PlsqB1S3aAmpSZJvGLrRpMRvX9Ll85BzyjePIKp\r\n6rdVZpLtFBZLxQX58oyOn1TYuCTYwGdvilmyPkksPov9SaLvVgmoOhrTPK1/\r\nMu+9ei/ISaRcX+XfonvYhPhto2gbedcB6oqr3TlAYJfj3UJCLJki20okeP5P\r\nRin9lARzoiGRPeN8wXVCH//Fkybjx/cJBGevu9kyTyn0JQtjF+uWhmvMi73d\r\nEgYGQMlxVQOowH+WM0CBuAO00OYwBvLuPi4G5dX4RKemJrELPyr65tixyprz\r\nwbUhJiCGS4bau4+DQZBXjgLnOO2jv9UkwJj7HmEYrZeo28N3TM8RdaGp1hFn\r\nKTFHTiDHVhHJ3FRqfxAxKHrxFjXLZI1C5+u0T+c5JJdoLYIMuFxOreDEkVFZ\r\nDUusT/qXcy8d3ijcmeADCkGAOLxdAO/S2Pw=\r\n=2+2N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.40", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^2.79.1", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.15"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "3.2.0": {"name": "@vitejs/plugin-vue", "version": "3.2.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "a1484089dd85d6528f435743f84cdd0d215bbb54", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-3.2.0.tgz", "fileCount": 6, "integrity": "sha512-E0tnaL4fr+qkdCNxJ+Xd0yM31UwMkQje76fsDVBBUCoGOUPexu2VDUYHL8P4CwV+zMvWw6nlRw19OnRKmYAJpw==", "signatures": [{"sig": "MEUCICcV/5CG2cvzjD1c+cdJDpcyOXziKDm2Bovl3XidNgeAAiEAyfHrwZHpywQBMOwLns/j7snRy/44DPE11yDoMj0DNyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWTIhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrbhg/8DrtGVIdmJ7MCB5W9bpECmRfUjnV3r0YzJLJA8FExQAmpsKCU\r\niZgKSXc3zOaGNUGkhBAwBSYUoF4JvQ1J5qddmsEowApsi2fv8DfsKUgVU7M0\r\nrnOfuR4+cEiJ69fV0+H36FnNqP+XxYNUn6F0Mr3ZTZ88iaJjPgYfkYkhqPiD\r\na77Bu7NKUUUeOKUW6Zt4QCUMOtiD8RDrFqZicNQ7y7aq+/uvZLHLnIB+lGVp\r\nA1zlLxZf52Hug7HYqPkrTXrg+5XorUF0HRA7e0Nnf06aKmt719U2vDmT6eY/\r\nCx+s+TwGWS41sU0fHjrzAItgiOGixJQufiJ1i5rk2yVlxUUnHpa4bRKKSQYT\r\n6GSWWm00QaNsHGzbI9yoHpkOsAJ06beSepsAO3/vUOJf7xJVDrGa7wzIc6VD\r\nnweHGmLPtwAl8wiB8AlekplDGOGh9hLrnfyxd4G4afJn8lCeM/tEqWwDqMXb\r\ndvF2vuqaE2zJ31/k9M/5OId3Tzhr2y5355Ch8brigXkOHNpUu2kjpQOFM7h+\r\npbJ3jWs/pxLiWjQtlSdNKK2LdwQVbCcqsA/QYhkaIf2Y9lxCi8JVMWWZhFuE\r\niFCfjF/qtP16nZbRxombZGZSahQWoNMlXXll5Bjgok+1CkySc2MAORZLkqty\r\nZFsFxY6XYyXYoSIVGUbHhmGKcZ5uBEQiTvQ=\r\n=Tbn3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.41", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^2.79.1", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0-alpha.0": {"name": "@vitejs/plugin-vue", "version": "4.0.0-alpha.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "72efb43e96d545f6fbcaf1763d1deae63373abeb", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-uQu8RpCsJ5yBPgPlWWI67npNIKeT7iQc41fdnX4BvqWviRuKdLsh2Ydwa/HgnkmDQMrcaLryY32tq4YOqBV9tQ==", "signatures": [{"sig": "MEYCIQC55v0iEgjtkTqS+QZlfElm5/HzGyVNa1Rzu24RMQyuvwIhAJswa3KTDh5QvV9pS35phJPjMiSRmuDYyX1seYk8xPkP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc63RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqicA/9EDZv+LB4F4U2EEKWVjny/FIUpVh1NFvwB0peRhXMHRhdngNw\r\nm/TXJUjMXPJL86X721hmzNhFb4QGJjoY364ik7OPZ4Wo9EhWzmCpSvFjoDc8\r\nzRQPMRFwhluN/LDBHpFDBfqJ6gcXWs1SxxOiqWquuGnvwvusgFx2285YcD4w\r\nytk9WFi2Gbqhvl7qDVvbQtSG55VAGdCi1bB+F+SNh2UmnwJa0QCAbStpzJ5z\r\nuBfv/bwVUKS0x2eF/8CmjGrN7oBejlg4D+KbptZBSglKJPk9/iGBdtglmW1h\r\neFiK9QDb1VAStOWFFMVJd24f/0gQAwxh1esnl54PJhN4mfSXzsYM0cNi+Cue\r\n0EIygWjSNhKTdS+4OmQWUpdhSOG3ovYawX/xKU/p7YYyOvbQq4jQBcARKYoe\r\nF0jdaaNCCIGNGR7fIq5kL/uWbshJBdX4aHcCVV25nbavlTPnrHUTgChAaoZ8\r\n2sdGxuh0pqNujk5bhrL8LYez7ismM7e1nqwFjShV6ozclxLKqR0rWfLvgPC5\r\nhCmJ+v18HNQHFkzpKkxazko5gZ3I9qWDgewrKsQx8HFtjLNB0NAyutaScmAU\r\nYigcm/hOfCdRuv1wxEzgIHpmiQnxrIO/8QLJ9FAUAZTfZyBT48GyNB8mejx+\r\nnefxW5mpj3H9AUM5iWnt1AMtO2BIKD3yh4I=\r\n=XBbR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "~3.3.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0-alpha.1": {"name": "@vitejs/plugin-vue", "version": "4.0.0-alpha.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "3f781a56ca78079be583543a025b5579909021b6", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-Mx+Y2sXTtLgsPiCOd/LN3Ky8QckVRSrgaG3zkm6UkVjzHphcIgz9B9Sl+QxETMYPCgmRXFFBTtULXInDI65sNQ==", "signatures": [{"sig": "MEYCIQCuG/ELvzPUFumzb0+R1FesJ65Or1yjDbA6yZwACsyjoAIhAP+hJWBU9r/ilXY/j3xcDuxHSW++rPQ9gbQBbPWbG1I1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfKxGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqScg//fTjbWmUY/RGPoCrWm++nzZ0MtsIzLb0p71AAj/Vg147MbaA5\r\nX3Wn9xmWok0qcakujrMgf4M2JW4npK3+TzvF5lWyKUN5nqMQBBg46vNOvTVR\r\nXTwllWSOmPV1JVwgqJigXtcmG7CuAQ8Nm5A5xoFhVEvtwyf1V2usiy1SKG2x\r\nX3KmE3eV2NJnkoVOxyust16kIxH33pXnIOA351L+VRshzHehOfyyHTm+pWBX\r\nWR4Aj6SWaWFrFgsUeN78FQc06TYNFTSot/X7CkLO052I0wJJ50heIzxRN+9Z\r\nInKSqYZjK66Z56n6Ur/8ssKuzgst53sDcWf0rsiDku9g5RHYGAcGusSeMHUC\r\nDJnRssFmzLre7AmaAp5R6jM5zdpbcE3PUaOAlUGVwpHb6Z9JsRfmDqcKEtxL\r\n5DRAunmG6zgUy+7rIkcmZQ44JCjiKnWqz4Bxr/I2B2lofDhPTSS2QOprnWgj\r\nxQwG3AubMXBETzc7N+H0iN2TvouuXjMivv3UYTaig/3TLvWsYMW6LD+oXtPb\r\nmNDFHJmJbvn6lSPdQME/QWkDwNO++uTTazU7p7k+wf+eXkwcE3s37AT+6Dpt\r\nyNHKdGdIVULM6TPpdi1Qwx/e9VDpe8Ix8ec6BDvR48uTgYcYw/G9iDDb/rFJ\r\nORQmfltMTNIDxCsb+Q0CmVEkk3XVvVKOdVg=\r\n=aa0s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "~3.3.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0-alpha.2": {"name": "@vitejs/plugin-vue", "version": "4.0.0-alpha.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "cd517ac8dc4971564f33c1077125eb96a5f95b5a", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-9HFfWQUIgthEardZqF/p1dK9bF5vvt5r9syNA0BpUPC00UFZHEcJ9CvgXHrzH2oOHcA2MErD4N++n0Ngufat+w==", "signatures": [{"sig": "MEQCICYE12UkAvl/1BqlgptWwV1eqn3EWgrch3pcalDQ8frqAiB7pBBPbKLDnqI28zLFbky6jUInOOt4ujRMOlLEYrcKVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh4w/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqehw/9F81Vq8ua9+/gq5nXHfUIgUgAT6o/Fvd2Az03S9hQQ83UoWBx\r\ncC8gXzrUuwqUGgRHGvSqikSATRgETTJbS7jvEhuPYMNFIJC/YmPpcmQA783+\r\noK2TFh6YLZxpZUY0OJUp6H8PoGdKgWszAn2K1pAB5RKBKioBNdjEYZ+ufsxl\r\n6LPgq7HcfbUDo+f8RuF/rTG8AFsPYtT9Ou5CWn5hYS/3Qg0vWt8UsLIH+yQk\r\n2XBvoQxN9/Y24ORIkJOKP1+RIOUmXl0cpRwM4n9RrBwCydPDHu9RSjYJwjur\r\nBfVcksA9BfrN6wb77H4foJokzK7HB4X82GVnR7K1hVByd6Hzp0SLCU+RGqbD\r\nyInyh+y+nvrk0heblUYjDwe4M5V9mbyIoHUz06YGd30mBtBVd5/fkJD+/Rcb\r\n0lxOH5GOxI2miXkoyc1SNVMzeCys1RpHzNvFvFDmaT5aizTLaE/mkGS9Z58N\r\n8qim/KJb99RpOVGcJxiqtd9TNoUxVI/HI0ijITz3uy1Ke52uhmmqazbWyfhr\r\ny86/PujOCUtDqoE1E6KOcOx7wXvfwVCPFKTkWkkSAFxX1Vgls9El5pjFWRUQ\r\nC8UtS7in8bwyrkCUKmdTRI4uyVkCHLXRxh44cH1jqQM4P9NF7fe8eIt655EH\r\nPLVXOqE07vfhKcwaaZ/uvQX+ZLQy1Kbb4bc=\r\n=0pCk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "workspace:*", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "~3.3.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^3.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "4.0.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "f45b0ca9da1300d8ae90e04b3f1669eaa4311193", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-ip9aE0C3lyjwVCdM8Wa2oVhXOCRJPauNUrfftkipeud3xVHXUYI4akCIamMIMgXorqpd16ms7zLckfO1rU9i6g==", "signatures": [{"sig": "MEUCIQCs2t0wKfxOfOscjdxZgku1XlL2qYhSrD+lE5gW2BgUNAIgExSZsGEvZkw9JfgaTauWKfRZVgabBjI1FcejkQij/Tc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjja/SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7ihAAhdnAe5E5gMMfnnl6jKqMh+Clxyk8pVECcuoeVRKSFWOk3pUO\r\nZu0YrzI4lWjj2tpcf5PpD3titrB7/BDOvysVesPFVu0/u7vN7xSp83HTIzyL\r\nPyAKSEuxPbIwXI2CddTVf71zYFU/adNp7Ac+/CTKvMoHiWQdWSeKt/nKQNkE\r\n7YLZimFSaV7W13daE1TvEOM++3H9meYN/YbDM661j8Wi1qYMhJaBSMQDjB50\r\nPSe2p+3uwDjt7ZszZcyAtf0V3hb5AnzzGt6uOWILewoHSss4tYVOLZp68+4S\r\nwBkiWTx+m5wXyZlG9RcAzC4Y5newWe531TFOWezYPwDOZQMS9jHZUAwRYcv2\r\nfZDuYUirhA16vHblFrZdvkNCNuSLnfMmxDBg9s9THIgGyYyMv7x6FDR3va/Z\r\nLRLksQGf+1eS8Dh7NH1/dxDOzmxxz94gS3C9g9wpvbJ3bYbtdxnz5NXDU6/L\r\n9ASnJ/8aDPj69Eb5YDxN2QpZZDXcnXVby78fCfMhKs6PIsaAUIbmNoEJ9Azi\r\nWMijvi3fr73nBis9wYoC/LeRxp1YHGzKwUkTuvHszFlCu7ej0BaiFfK/G9Vt\r\nqmFbJ5fCexvuJBPy22U6Q/eAEUYskcirjK4FJNdvVi2IgMidK9AstYCex2jO\r\nKt+8sHbJbHzSuw1O5cEgd47yXKVE9dal5GU=\r\n=+Z0R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "^4.0.0-alpha.0", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "~3.3.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0-alpha.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.0.0": {"name": "@vitejs/plugin-vue", "version": "4.0.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "93815beffd23db46288c787352a8ea31a0c03e5e", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-e0X4jErIxAB5oLtDqbHvHpJe/uWNkdpYV83AOG2xo2tEVSzCzewgJMtREZM30wXnM5ls90hxiOtAuVU6H5JgbA==", "signatures": [{"sig": "MEYCIQDbPO8WQc2GL/Oe7Y7KKzy2iv1XzENM1OwW5XY30vb4NwIhAINo6couH3h0rbn14bt/1Wk4Y/ggN8blcERKUr3uLGEP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkv0rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfuw//cbNhnXkAyDUoTuNabpHaHLJXNtfZ4L+9mwGGUzVPf5go1h7m\r\nVGljkKD1mBWcW6putdHA7e1JCIkcHpSURqsDgcpe54iPPsLsksKZN9u57ify\r\nzbRgPRpQj/AdwgtlMczo4whfyUgCQFRMhDRCUTAtqp/nal7I8gq9SStenk21\r\n77xslzw+qjqJRLKKeKOrXmPvXf9KGYJkWhDi7Fkc4GhHQBtaIdXNpLfJb8/k\r\nX8698LoQHljSJxon+4egei8LxMSc78YqfkseCB6aEp7vXFkrSLbTxf9cU6wZ\r\neOMA0JGBiSz0ChHepPKc8TKG3zT9+F4sLhg7k0TGZrJXbn5RXgSqPP5Vfo6m\r\nGPjQDZTBylMqWnKatKkelcUgEcqsYOmeaD4HtpwCQqx3rP4tU0A5amEBropU\r\ncMj7Wq2BV2xAjCWwyBzySa24MMFRWxYG80QcfbR693GVOvac4ASF73U1UzYO\r\n+SdN6IUSiqZbkTF/1UfCFYOLgqDxNTrN9INeWcwi6jEhXbsBxVBtEVniJafy\r\nUQlUqjk0Y4GIOWAX7eZxOweo6wzzunsz0o9iMtJP5JBC+V7fulrGXKto2nSh\r\n64brhuRTHdadVk66yy8AY1ThvvMlBgITDdGzgijzZ2vJOvtXai95Lo77Yuss\r\nX++aEQf9SX5l2asinEjx20WxFvaV5WbBjAU=\r\n=nfQJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "^4.0.0", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.7.0", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "4.1.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "a8470e13b4e5f271c62fb2348cecaab74f25125c", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.1.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-9FgeBvXAEkMb1fncxN4kXN66dMJK/tEBWJLt9evWB+aaBdcEbUbhiKVAp+oFG3Q62Jsn4/0ZzHZZhNNsIRn2wg==", "signatures": [{"sig": "MEQCICSoUYmiIFiGbyKqSQTgo7zkwvcrogiSWXTgC9ZSFziaAiBo4Zp4VqOKoP2sFBTbA32lAwZmFMLdiZtdH30NE/zQOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBz4wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG8w//XD1V4FSEdqoV1p0Cs2A5HsiLWfnoQTwG0RtYVw7Xpz4ZrvvJ\r\nWMURXsnH1j7rKdoXxAGnbMtfWGRHqZZf5jfqOzwLz/5HQ3h7JcUw8q6awbo+\r\nZ2HbrwpHYw1XjGe2WOR5cDTL2E6bbBh2cvMKiYz06xYZCLlQ++aZ7DENEmVK\r\nMaKFa4nSWWblZM2/N/4NmW5CY/Vzr7+9SBoAuvcGMZhfB2/o4k9P0ht8x9TW\r\nL9KSsTInGdxkEofLhKbiY9cKousuLc8sQdkaW9HJVRuIZuy3wgXQ85iHhuvf\r\nC4cy8l52zw99jT1yOYGDagWexNP0Bcq8iJILNo1sIdFHts6aKrZ6Lq6miqZ9\r\nahoJ26nq94ZG8lvK8alxbgr6szy8ZxpUrclw8iC8zTnoT+lGzNF6Ine9QCp0\r\nRzfP/7Ja/HBMiV8T7XnkV6TVNBMgFGh9EjwBnqPwOBDyfvNcsAZmvTp6dwpl\r\nNCXbbYO/paP2fK59XXxCsVDme4sQ3+w9RvbvQAtsW9N6Z48Zcptt87e2RiMd\r\ntGf6OCpxEUhZgFfuL/m0iVu0CaTaybqybLD4LbdtTtb+UZomR/+0+wQEQnk/\r\nlqUnaLLp1Ki/prMuJyvMFRXJ+r1vlpGZTLgwUSBTOHL7M0NZ+0HNJr9Kj3Vt\r\nDvfHoius5X2WhwH98reGX/kT09Z91HiNVo8=\r\n=njMk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "^4.0.3", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.1.0": {"name": "@vitejs/plugin-vue", "version": "4.1.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "b6a9d83cd91575f7ee15593f6444397f68751073", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.1.0.tgz", "fileCount": 6, "integrity": "sha512-++9<PERSON><PERSON><PERSON>dcXI3lyer9UKUV4rfoQ3T1RN8yDqoCLar86s0xQct5yblxAE+yWgRnU5/0FOlVCpTZpYSBV/bGWrSrQ==", "signatures": [{"sig": "MEUCIQCECrD7tMSBVQ+qG196aaFWWSh/KR7Wn9U9pYh3E59NcgIgKQT04U0yu1DprPJ4A+nKkPImrS574LEzZvAhPOxZ6+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEt11ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomAA//TBraQYquN9sX6eh7z7mc994u2JjYEBohqevJlgJ7p29Rmneg\r\nA+oWr6b9iEC+9o6E8FykIZ+It76E6Fzo5TXSzsqPzVXPhGH0L9M56bh+0e3u\r\nV8QtBi5ykkUdMN03Ghfq5guYXHDgZ+EIZ2Nl+STreO4CyTx+DZufIZDKsalD\r\nrjGBMNxmz31mf1W1cZ1RV6k2ny2BlSnXvA1L/2TCb+xwjSuraCx4hP6WDHI4\r\noGHhCQlGp14pE1p2475YSgUxriG/Vq6CbGT41EsOBNQGmzuht70YL6Jkuv7R\r\n+p1shtdD3A2ahVKO9QyDFr2niexeVbb5AwLVra5zNrgk0rtnEtG7+gavcT6z\r\nBMts2W646Dy/mNdmLf10nCpKX2rRWzdSgtu7mCv+ifGAcl9gyIIW4DmbB2XP\r\n2WHRYUivJYOofO0L4VuTklc5qqDQHKRwSyl66SagiiqIqlp7x4O14PGtLFXo\r\nZ7ryXP/OoLMZX6jmo430N+mSre479lqSRjhqpWmF1KMM6SqSRVCPX3b9IabD\r\ntYkXNCrdgxJA5COPm9FO7OX6WhtJmIjAUCdlb27IAakh6wxuedUxkSAnCFzj\r\nDDAaBlP9/vDRfDwAJeNiAMnnr5ltKJOMTTv0ddX6xYbjUISQ5Bkyo1CJ7lOx\r\n/IgfBPTIhJV3rsrCEp8MPEQxTiP0KcHwxAk=\r\n=YmqH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.45", "vite": "^4.0.3", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "4.2.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "cb23b5be3c0cd12dac8c8e59e77b68544887f816", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-U1SWAtpExyvFTZMY+3LawRjCCY0b47YyRqt9TJoolg4360VhYuPiqWCUBlB+Magy+B87eKDg9jmoVqG2X9Pm/w==", "signatures": [{"sig": "MEQCIFjuWnTm9fiLdFKWDMcBY4c6hyhZdecrCRHi5Im5CHDtAiBjM5kIZwAYciDepIKhFQ7VXrnNbOoPjnfNqBc7kupFDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPRkRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPXBAAgwMvAwxEy8Zf7A8wFwRShyHQ2/UD0pbENFrLA2/Qld4hX4GP\r\nXpJPXvjzQe7DsRQb9xMo/awPqy2isYR8kMtEHi6vb2y/2sGDuaIBb/S3dQ7k\r\nZE2n+JiDxgJ+ivyLF9K+n3aEVAnOEJ4BZpef29TPvNZ+5TzPNdeCOgd4t/x3\r\nRZzi7dIJQg1GpvJ7i6Y0Z7Qnhz9kLJsYW/tCLYRbS0x4n17prhpExprFSGOK\r\n9jaTbgaCetTvPVCS+rFSCOruMxtLWM8YH1lnRkk8uq2nTZAo2Hs+Qcq3eR6s\r\nnJXQ5SnURzy7HYpZt7VQjy+3Q5C2ni5wFNFnanU7dkV7LyrPDmmN/CjWTBJO\r\naRPyzHOBxdEYQMo4uCAYsPLVEVeTeP7vxFrhYlI2jEPa+oKg/vyRRcbRHoQs\r\ne9Vh3o6dRjBb3AujLMYN9bHWUmzjpgOoHQ62YX8IDCoA3Ax41TL2M2eJxZad\r\nZFcwl515V7rr5SRdIDTFegkuLfCKJvGbe2BBSb6wWcXUAOEsfnH9sGZvNvtl\r\n0xHlI9qSV59CM4duWlOgrZO0jXh1q9gZ+qA80lHaAds+zCKZCLwjoBN0Gg1/\r\n9gdw3v3ztr5d16jzKJFKGpA5EcJGILW5iL94tGg8AYsEbBPYr6sxsaxS5ocv\r\noxK5pANoDCln2gWOFKfF93yU43AU/0LcDvo=\r\n=Ry8R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.2.47", "vite": "^4.2.1", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0-beta.1": {"name": "@vitejs/plugin-vue", "version": "4.2.0-beta.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "b690079e8ed6ef1482b00a8b0afd894a4345ec41", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-1Qi+iYnHseLgCqi4iLlN5lqhgY3UBY5GLqeRhmrQsNZMGj9vPfKzMIgp4Gqh8cOgOop9K5m5nTL8NwFqHK9sIw==", "signatures": [{"sig": "MEMCHwm11KcgPqYGetUiy3RAWzAiFtDx6zZz5lLRvsanDpECIGpHPdGTSVJ4T6O8VXNqseHqI1ntSwu4Ys7ooCgfWg3s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPqIHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHbA/+JqcJJm/nP5xb4QmMktrPpPxeGcXnPB+AGJJmZ4MkTcQ/ffdG\r\nEX6TvlW1AWDXxK820+PDZpKrimAZ0ErTed5OVNXqb37SeiL2AbbUnQd6GHds\r\nzr17ROgYfTg+kzPZj7N6CIFpxxIEl8zTrZspW5dh5+1CtJf0FlkZi46UJjLi\r\nuh7CqUT6WjljdLqYIX058yF5X/QLhTSac3N7cZArZXNZkUN5QikHIPPhv+TF\r\nUr885JPmGV7/LwUg/Jqtos6VK9x5osom8oSr2t+BJmBgrHqS/RFolMU06Cxt\r\noIh1xE135YW4YqCCW4dhnQ3yKYh8Syg2h8TqSqs3QEqN3yd642p8g4szz0Gd\r\nlG+fAZM8F3akmfmcxkgpHh4jtKu+YnoEe5SqnXcpvyY6r3bcxDUdFpJaEaLT\r\npDxLTqV/kH9GVM7wQeiFjtYiAsZFPJkEOWj4ImZ7es2s/B0HT/1s3z1uUVK1\r\nYrgcP7S6h7GkKyE56QPo1wbGNVeBPyDrbZXO/5uf7xLXH6iumScSDObGumiQ\r\nNChIFSX5UNgpoqAUrGCwFYTtXd2G63ul9p7uO4+NOF+4hA4ITNBgt8KZq0og\r\nt1uoS+gBRru2sYNQOVdrCeNRsqIdt2zJDzaqgNxeCpD3VNt81GdQiZyEp3Iz\r\nwtty/zQGVEY90mcDPjaXsrUbH5FVUowJzAo=\r\n=4wps\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.0-alpha.11", "vite": "^4.2.1", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0-beta.3": {"name": "@vitejs/plugin-vue", "version": "4.2.0-beta.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "c10e21bfe1c1f4a3dc6df2414c5955aab52e2fac", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-0BNAfEEH2Bom0gvDPpGU0BZPc9D0NBu/GHa/bkKTSspcW6z+vsXjqL9E+jUQDyDDZMOH5aZ6dkLQ7X4PbqvSmQ==", "signatures": [{"sig": "MEUCIQCM2VJD0ui3TLKTG62nVmwvvIhaI+JUsNeTFbfd6VEGswIgEGL3azX43bQzdVuehune2lvo/Lu+GS37tkfbYzrTMdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkP1SmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOOQ//XIdYkUMznhjOf4yFtzqn/MLuu4N7MiWT2KjVeNwHG7+TNqg5\r\njZQCKOWybgiO8Mlw+IO3J/7STzucfr6WYD75hhTpUPm7FDfIHP05mc9nt4a8\r\nJ19CPBu6Tb84It3nd1rHWUHASDHU/5PwShhlSGSUcB0MxzvndiNs3RjXXqOQ\r\nnZwSUIdsOy/GOXBOh0egtW7W+d9J/5wvKD6evajvZmJ9AOb7quFm1vPoLD1F\r\n+Wht/V/2EVa7/jh0mpWNIBgXN8tEUv5TWmrD0i0i0XYQKRLxJvPnwuUqVEG+\r\nbxs0Y4jbQRAiP1snsYDMD3juyOuIrdh31F2hqxzMWHvsbZA46TidSfD1YeDq\r\noT9xAXF/XlufU8MPf1ZUxOpu3FeLrY/mRpMcARmu4ESXjdQTAvLBDOMx4Foe\r\nwtEXhg8awoams9oWSXz2j2U6RSWByWsY5WIIEHzjxgoohqKrqwbv0FmfZHqZ\r\nkMaZLDsDhOrFT9IQp5ST8Zq69cvSURXJ0x/TIe2XMdzak+Ls6GsCkN6pIgVq\r\n0BMsBk+NzS06dcM02B0C1qurzjAy4vMTkiawypRdJoUqqly9XiP0VmbzSJnw\r\nTBukWd3/2RBeNTmSichHsTBSwSxeOlbq7UoteA/TjZBMFKPiZLRNWTnQ49Oz\r\nx+3nTNKZRUo0fsmyRUiiowBO/G0YYiWqj6Q=\r\n=v8q/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.0-alpha.11", "vite": "^4.2.1", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.0": {"name": "@vitejs/plugin-vue", "version": "4.2.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "23d0f58d1b006c3ce1b612a5f7671210fbc543f0", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.0.tgz", "fileCount": 6, "integrity": "sha512-hYaXFvEKEwyTmwHq2ft7GGeLBvyYLwTM3E5R1jpvzxg9gO4m5PQcTVvj1wEPKoPL8PAt+KAlxo3gyJWnmwzaWQ==", "signatures": [{"sig": "MEYCIQCMG0Zq4h/5GCG+yph3U0nn669K8ygaBlNSc7/UkE0WOgIhANK0pahPYIzGMCfotCkMIVHEy7i2CIkFHwAJwd7GXwIs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR6M9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofnBAAidzlAJieNIqhrKYuXtvEPAuAuRBMQklimASMwuS53xem3f6M\r\nfbp9aWPQZoolnEbzEfxin1U+KRlybYaV2sOkRpypVk0ae/K4dcQHL58+rtEG\r\nOH3rgEE+zyO0ay1njk3PsXoDd8JlKDxXj4WpqllnlS/iDVlH3mA2Gu6kNAv8\r\n0h0CZik+Q1aIDEbbecW+6ZXoCIraDiQVPKubsth6Rb5OHzh1mLCHehM7pON+\r\nnBucw2S1yDMwGJLXSqtyUsFpPuuwVVU9H+Q7DiQklhtkKcgItBm/vhRc3AHq\r\nIb8hsqXtotVdX0QcIoVTxDirk0qYIzBca/UZiT2SE1sEv5VAqTIwMYY5U0wW\r\nhoqLYbaEFalu9h+iU4O08aocdewIlB5O1v7b1xxnJXjFDefB278WN7FhF1SR\r\nVNYuTyIHtgQDCTg5J63bIOYZUh1F1IT2XtMwWjelDZrSjEn0z5zm12rDUJCp\r\nXIpxhUGM/hAbwuQp1dV+HZ/Y8QdTIXdU49wyyO84QPc+y9tGBrJpEBhHSSKL\r\nS0vz2Wk9FIZSn9/KNdSJrpp8Z4IkUIQzrfXS7FfPelAlJBm+JlT3WP5EN4rT\r\nNhCkPfiTNkaAYTRXQprX5HmT90ZXZfrhGTLciujEK01MRElh2aLIFAhx/KFz\r\nwBe0vEtUVdhCeU24PRD+S8mqeVHzirHVYmA=\r\n=ihZ5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.0-alpha.11", "vite": "^4.2.1", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.1": {"name": "@vitejs/plugin-vue", "version": "4.2.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "c3ccce9956e8cdca946f465188777e4e3e488f6a", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.1.tgz", "fileCount": 6, "integrity": "sha512-ZTZjzo7bmxTRTkb8GSTwkPOYDIP7pwuyV+RV53c9PYUouwcbkIZIvWvNWlX2b1dYZqtOv7D6iUAnJLVNGcLrSw==", "signatures": [{"sig": "MEUCIQC+eQGM/WDgTc05N28kyFvz+t+ESyzWTIGXyfSQiYr0WgIgIolwMa39WcCRfz+QalERWstygLtBWRE1pVAjWD24PN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSMkTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4YA//TOW6VK/2xnTPUS1icq8T6vW3g8a6qpF7qMAFDCg49Bm4RFaf\r\nSPWonJZ3Ln2oLBL3bRlDJGHQfhra7R4kpwB8BUy6AdU5WOJ3zNOAyIWYxYOy\r\nZLHapuLkdPRmyFMuO0M3GVZcjPO4LWGth/X9wG6DsRrXtPelainuiVHS7qng\r\nYkOyaxlrBjWNQEunGXUcSMMxR4kG8AQGfKR/EWG1wiNSS8PdiMR3A2AWH8gn\r\n2+OGvE2AxUUH99DAy6Rwn63O086Koo/zGFwiQmvNJldthZ7N+4cpyQftdWgZ\r\n/wKjvQVoJ7t7iIJkf04SZsFBgg33kYnOZcGjGPhrQB6ufByxnJNEnLGf0h48\r\nZcecSijM12JNMRJSSlV4iB8pQcdcVF2g5YZLK/4IjxGjuVw+CNmkmzTFgFIz\r\n3nhsIXLnwynQIo4yMwupDdaGcj4FhdvYwXQOGltr409PbZgsYIjV1RB7h5ah\r\nbkU9/0EnzjVg7T5cvhpXMJ15bdfh9jsYSESmEAeUQZ6X4x9N7s28yHz488YV\r\nmWfTS1YY6YaEdMh1sUKg1coYMvGVjt1rwHsf9Kk5yXX6dqp5MBNL3Za3bzJG\r\nv261ySyyKrQJxSybN7iDtUJGPcDuKGVuxG3ibmybkBh5KaldegS4F84PoOJb\r\ngfGbEi9ji6kcAZ9/fd9H+RoRwzDEozn45Z0=\r\n=hJWB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.0-alpha.11", "vite": "^4.2.1", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.2": {"name": "@vitejs/plugin-vue", "version": "4.2.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "eb145aa86e673e5e3620132851200dcd604af8cf", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.2.tgz", "fileCount": 6, "integrity": "sha512-kNH4wMAqs13UiZe/2If1ioO0Mjz71rr2oALTl2c5ajBIox9Vz/UGW/wGkr7GA3SC6Eb29c1HtzAtxdGfbXAkfQ==", "signatures": [{"sig": "MEUCIHCFSZ+hKqdT6S54Ujg3zRNhw3+1gK65R94Nj70HESKHAiEA9WjZRSd0Dpt0RvcP5LmKN/DxgSYQBeSpe8GEk2haaaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172263}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.0-beta.5", "vite": "^4.3.5", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.2.3": {"name": "@vitejs/plugin-vue", "version": "4.2.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "ee0b6dfcc62fe65364e6395bf38fa2ba10bb44b6", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.2.3.tgz", "fileCount": 6, "integrity": "sha512-R6<PERSON>DUfiZbJA9cMiguQ7jxALsgiprjBeHL5ikpXfJCH62pPHtI+JdJ5xWj6Ev73yXSlYl86+blXn1kZHQ7uElxw==", "signatures": [{"sig": "MEYCIQCmtYS650FoXo3BKZ4rVWTqB7iydB2vKEnfQlxHkLtaCQIhAOKfr+300zLkArHD8iqlaSUJid8tvXNny0U7+pJWLgwE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172331}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.0-beta.5", "vite": "^4.3.5", "debug": "^4.3.4", "slash": "^5.0.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.0": {"name": "@vitejs/plugin-vue", "version": "4.3.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "1f2cbcef1c29651021dc15fd3ed5e538a188e0f6", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.3.0.tgz", "fileCount": 8, "integrity": "sha512-EvZ4KZGzf9G9Ir593f2hmQFJNPrLeC6XSzqvLJXWr6uTDAA1IyhB3ZVDhmqQ7jYx186ABddHLYoVqN9Eb9GGgg==", "signatures": [{"sig": "MEUCIQC5ZgrRNpc4sGZ09hBFP6S6NOkyAeiVME9Y29/jztgmlwIgbXlCEntgk/NBo/iD9hNj3hqWviN5SRKr7lC5tOpuPt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178811}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.1": {"name": "@vitejs/plugin-vue", "version": "4.3.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "635cf512dd1d5c78adf9e24615696e4918cd9d88", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.3.1.tgz", "fileCount": 8, "integrity": "sha512-tUBEtWcF7wFtII7ayNiLNDTCE1X1afySEo+XNVMNkFXaThENyCowIEX095QqbJZGTgoOcSVDJGlnde2NG4jtbQ==", "signatures": [{"sig": "MEUCIEQ03Bf1nVSUtsWhU7FszBC2S8H0N8MTDoKecoVAZCLCAiEAwJ8xFnSw77NvNLXbmwCxXYmwuUhhYeB5lY+maRboTaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178161}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.2": {"name": "@vitejs/plugin-vue", "version": "4.3.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "9f1b693d56370c5c01c3a2a09858bb5afd9d154d", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.3.2.tgz", "fileCount": 8, "integrity": "sha512-iDDhGruwhKkwNwT5qgtGaeTxF4ULs52xpQbsC27F01kf3aQBHtrDP738pmHw4oclVAUA3m+Vk8gxhDV5KbfM+A==", "signatures": [{"sig": "MEUCIEuT4d3tCZlAJdgrpT4pt5xYJhzwwH83xl9s2xi9YRKMAiEAmCU+/FNIzs1k7Kbo9lrNNvdJ5XeJywOp5DlElV9H/+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178827}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.3": {"name": "@vitejs/plugin-vue", "version": "4.3.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "3b2337f64495f95cfea5b1497d2d3f4a0b3382b2", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.3.3.tgz", "fileCount": 8, "integrity": "sha512-ssxyhIAZqB0TrpUg6R0cBpCuMk9jTIlO1GNSKKQD6S8VjnXi6JXKfUXjSsxey9IwQiaRGsO1WnW9Rkl1L6AJVw==", "signatures": [{"sig": "MEUCIQDkZJ9yh9VBcRZYyOgkmVWOJ4QjOoH/tl95z038Y+PM/AIgSKPiigoJBXMzYITis6w5SJELoJBFmUsAkgIKof0heGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178902}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.3.4": {"name": "@vitejs/plugin-vue", "version": "4.3.4", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "a289dff38e01949fe7be581d5542cabaeb961dec", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.3.4.tgz", "fileCount": 8, "integrity": "sha512-ciXNIHKPriERBisHFBvnTbfKa6r9SAesOYXeGDzgegcvy9Q4xdScSHAmKbNT0M3O0S9LKhIf5/G+UYG4NnnzYw==", "signatures": [{"sig": "MEQCICU2IvJ+5Qcq7kpr5zSOcesM09wBgo12dzGs4Qz3Z3x7AiBXkL1gIdhRLGrQFakxgX++sP6gfExixX1WNQNS0zid8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.3.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 179250}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map": "^0.6.1", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.4.0": {"name": "@vitejs/plugin-vue", "version": "4.4.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "8ae96573236cdb12de6850a6d929b5537ec85390", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.4.0.tgz", "fileCount": 9, "integrity": "sha512-xdguqb+VUwiRpSg+nsc2HtbAUSGak25DXYvpQQi4RVU1Xq1uworyoH/md9Rfd8zMmPR/pSghr309QNcftUVseg==", "signatures": [{"sig": "MEUCICrH04weO9RgofJrB4BmbxPz2KQ417nOfPH3ed62G/41AiEAj8eL36RN3wghemMsPl6XBKN/sb2iVGi0+q0iP9jBHRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264920}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.4.1": {"name": "@vitejs/plugin-vue", "version": "4.4.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "5050895fb8b2258d2f228110849df4a8a94b9038", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.4.1.tgz", "fileCount": 8, "integrity": "sha512-HCQG8VDFDM7YDAdcj5QI5DvUi+r6xvo9LgvYdk7LSkUNwdpempdB5horkMSZsbdey9Ywsf5aaU8kEPw9M5kREA==", "signatures": [{"sig": "MEUCIQDaHAyE43EMfDFbmhq8tD4Z/MsMgoHNRZ9aCOMx15bIZQIgN/+6QR5RZkfPkN+MnXb09M+jscnUKpX2jO153aWxP0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182920}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.4", "vite": "^4.4.9", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.17.2", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.19"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.5.0": {"name": "@vitejs/plugin-vue", "version": "4.5.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "b4569fcb1faac054eba4f5efc1aaf4d39f4379e5", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.5.0.tgz", "fileCount": 8, "integrity": "sha512-a2WSpP8X8HTEww/U00bU4mX1QpLINNuz/2KMNpLsdu3BzOpak3AGI1CJYBTXcc4SPhaD0eNRUp7IyQK405L5dQ==", "signatures": [{"sig": "MEUCIDn6Ow6R7kLreEkNVuI8HKHhGyz2irx7B8k3JQMhyW6dAiEAguciPy2ZjJk/TX4kvb1G6j3mPWJjsZUKr52Qo9fL/48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 182942}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^4.5.0", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.29.4", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0 || ^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.0-alpha.0": {"name": "@vitejs/plugin-vue", "version": "5.0.0-alpha.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "6e6f7e351d28d21acf002ea535cad5af4f28ff70", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-2t6mxT4hYljhupF5DcZZ07rZz5mm5LwJs/hp3VZqI/bpY5CganQKro9RAz8psWJRI8FZA9bDTQlzXIUNyqZC1Q==", "signatures": [{"sig": "MEYCIQCromHhFRPiIcZEv5QkQYnIAaTWRDYS1Z5QNAWgSeDbbgIhALxTe5GwJd700uZc/JY6ztSzaEVD0wCR6V5ofJX3lQtx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.0-alpha.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 181656}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^5.0.2", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.6.0", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.5.1": {"name": "@vitejs/plugin-vue", "version": "4.5.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "84815bfeb46928c03a9ed765e4a8425c22345e15", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.5.1.tgz", "fileCount": 8, "integrity": "sha512-DaUzYFr+2UGDG7VSSdShKa9sIWYBa1LL8KC0MNOf2H5LjcTPjob0x8LbkqXWmAtbANJCkpiQTj66UVcQkN2s3g==", "signatures": [{"sig": "MEYCIQC9C48iU2DyOnKMU5S8sPn5IEoYGODLQVLu9M503/NpkAIhAOAQiyRb7fW7yJRt8Lx8QDvoKtvHpkVw7kd1UQpEkwpZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 183014}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^4.5.0", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.29.4", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0 || ^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.0-beta.0": {"name": "@vitejs/plugin-vue", "version": "5.0.0-beta.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "af37962d38095b3841c66a883cf3f25fb4b8a256", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.0-beta.0.tgz", "fileCount": 8, "integrity": "sha512-dVdeuSPH8c9RhNUQoakngexnUGBi+0FTUomgr+EuENdbOdBcSnCcAZLVBR1RQlyDAR+UldSQ3aTJof+K2n3c3Q==", "signatures": [{"sig": "MEUCIQCWilqlyqyNblGjWm7YwZgFV6i3A6n9y34S4gt24lJpTgIgM9G3QhZueM313MMhAQ3ke7mKNb2i7eG4ALrA36dMhLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.0-beta.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182368}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.10", "vite": "^5.0.6", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.6.1", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.5.2": {"name": "@vitejs/plugin-vue", "version": "4.5.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "1212d81bc83680e14448fefe55abd9fe1ed49ed1", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.5.2.tgz", "fileCount": 8, "integrity": "sha512-UGR3DlzLi/SaVBPX0cnSyE37vqxU3O6chn8l0HJNzQzDia6/Au2A4xKv+iIJW8w2daf80G7TYHhi1pAUjdZ0bQ==", "signatures": [{"sig": "MEUCIDL2yb5yGim2tjbj8WGgGqjzBgkmv07vOPJWhHPwc2EEAiEA8aYv0Lb1rhPsPpwaxYvivD9XU9agoeHDmt5B2I3AuXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 183656}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^4.5.0", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.29.4", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0 || ^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.0-beta.1": {"name": "@vitejs/plugin-vue", "version": "5.0.0-beta.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "87960b5bce75f7bb64421156127d0ad4986adf0b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.0-beta.1.tgz", "fileCount": 8, "integrity": "sha512-zFAHH6RJH2w/LQlFyqrml96yjYmT8n8e3O4esRxHzCn250uOlkuc0IAqFJWqdxLmQquEM4q5/ECnQJRGsKjoIw==", "signatures": [{"sig": "MEUCIGlSVkxagGVc/W6gqlWoyROExhtTcXuvJpFMyEJFCqObAiEAyp5D084OpDv0ij3TyzDd9P1Y5LPRaGMZkxBUhnHP1WQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 183095}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.10", "vite": "^5.0.6", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.6.1", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.0": {"name": "@vitejs/plugin-vue", "version": "5.0.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "8e8946548e6dfd9eb0391bbc282ec8e52103e0dd", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-7x5e8X4J1Wi4NxudGjJBd2OFerAi/0nzF80ojCzvfj347WVr0YSn82C8BSsgwSHzlk9Kw5xnZfj0/7RLnNwP5w==", "signatures": [{"sig": "MEYCIQDxGJgKsBvEIg1AJW3S31+OHastMfa5foo/Pw2RrZ/NrwIhAImne7nh53MXz0HGadQanJzDiaGMhMgCko7pV0RkcMfI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 183074}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.10", "vite": "^5.0.6", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.6.1", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.6.0": {"name": "@vitejs/plugin-vue", "version": "4.6.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "6e1011303f8224e04d339756f1fa398ba79fc0b3", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.6.0.tgz", "fileCount": 8, "integrity": "sha512-XHuyFdAikWRmHuAd89FOyUGIjrBU5KlxJtyi2hVeR9ySGFxQwE0bl5xAQju/ArMq5azdBivY4d+D2yPKwoYWUg==", "signatures": [{"sig": "MEUCIHjsDphu2fxsalWkxNMrpDAa/wzWC3pgzdMKdaK+D/pWAiEArOn0SFFz/WaJ5iO1ihF2ZuG6mdY1e344dnGQDDzOSUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 184086}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^4.5.0", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.29.4", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0 || ^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.6.1": {"name": "@vitejs/plugin-vue", "version": "4.6.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "ab4ba6a451d07eb7427d687dbb08fc6a864ad871", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.6.1.tgz", "fileCount": 8, "integrity": "sha512-4JG1b1SPQpviIXkp4cwUaHluU0KCgjLprdyYaw4cq6OkJzqFXuao5CefsOaftcRpw8rlMQVwmHEurK+1zIzTlA==", "signatures": [{"sig": "MEQCIBKVBpsyAxZBmbwDByCI7zuxelLJ77jADbg+IzwioIGcAiAzuIPD+822jseYL4JKiKfGiytlAku8wKr/L41oHbOSHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 183888}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^4.5.0", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.29.4", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0 || ^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.1": {"name": "@vitejs/plugin-vue", "version": "5.0.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "8ee6311f9401666291fc8a9106a17431f1416b2a", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-lwvZX5tDhJpRJEKsjoUSWgaD26Lk9X4aDYGAPpr/Q6cLTT3PC8LPu2dsnYEweAZiNgHsbyKL2Svc/CDrFOsbtw==", "signatures": [{"sig": "MEYCIQDS1x4nd7cQULQc+uVuKYJLiSHUCikaZJ2ISdB1ybfZgwIhAOqKI8gy8DsR7M8FeDOpCxfUGCaVasFWg/YwcPqc7MaV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 182876}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.10", "vite": "^5.0.6", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.6.1", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.2": {"name": "@vitejs/plugin-vue", "version": "5.0.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "8428ec3f446b9c2f7a7ec950f34e3d6f3c665444", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.2.tgz", "fileCount": 8, "integrity": "sha512-kEjJHrLb5ePBvjD0SPZwJlw1QTRcjjCA9sB5VyfonoXVBxTS7TMnqL6EkLt1Eu61RDeiuZ/WN9Hf6PxXhPI2uA==", "signatures": [{"sig": "MEUCIFBXRyWMX6nwIAwMKXh/PU8xllG+ueTjaL3n52aD0PjXAiEAkTV+dmGTk816V3EsnNP7DORRHCvsG+WknFFWkP+JC/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 183557}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.1", "vite": "^5.0.6", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.6.1", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "4.6.2": {"name": "@vitejs/plugin-vue", "version": "4.6.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "057d2ded94c4e71b94e9814f92dcd9306317aa46", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-4.6.2.tgz", "fileCount": 8, "integrity": "sha512-kqf7SGFoG+80aZG6Pf+gsZIVvGSCKE98JbiWqcCV9cThtg91Jav0yvYFC9Zb+jKetNGF6ZKeoaxgZfND21fWKw==", "signatures": [{"sig": "MEUCIQDl/E5ljnSkSlNGPdjcHqEkQDogb3F4SIS/3wI6y23G9wIgdtSe9XT7huKN9lVDsAh/cPK1y2phcB0aHnJOJvcL0hU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@4.6.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 184750}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.3.8", "vite": "^4.5.0", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^3.29.4", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^4.0.0 || ^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.3": {"name": "@vitejs/plugin-vue", "version": "5.0.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "164b36653910d27c130cf6c945b4bd9bde5bcbee", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.3.tgz", "fileCount": 8, "integrity": "sha512-b8S5dVS40rgHdDrw+DQi/xOM9ed+kSRZzfm1T74bMmBDCd8XO87NKlFYInzCtwvtWwXZvo1QxE2OSspTATWrbA==", "signatures": [{"sig": "MEQCIDMIElPBaefe3suMPs8P9gXt9FQTq125Paiu2mUDwXxjAiBCQoL6sKOpwcdJOXkh8BYVqpa3kcoCGhW57JVjQzfLng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 183988}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.5", "vite": "^5.0.10", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.9.2", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.4": {"name": "@vitejs/plugin-vue", "version": "5.0.4", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "508d6a0f2440f86945835d903fcc0d95d1bb8a37", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.4.tgz", "fileCount": 8, "integrity": "sha512-WS3hevEszI6CEVEx28F8RjTX97k3KsrcY6kvTg7+Whm5y3oYvcqzVeGCU3hxSAn4uY2CLCkeokkGKpoctccilQ==", "signatures": [{"sig": "MEQCIBDWNDOj8d6SZiq9mgHUBHgE4ro+l3Dc64uB6NFzVq0PAiB0jgj98m/Adk3ybBJYs6hKGeBB1z3nranlscU63rCmHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 184702}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.15", "vite": "^5.0.12", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.9.6", "source-map-js": "^1.0.2", "@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.22"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.0.5": {"name": "@vitejs/plugin-vue", "version": "5.0.5", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "e3dc11e427d4b818b7e3202766ad156e3d5e2eaa", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.0.5.tgz", "fileCount": 8, "integrity": "sha512-LOjm7XeIimLBZyzinBQ6OSm3UBCNVCpLkxGC0oWmm2YPzVZoxMsdvNVimLTBzpAnR9hl/yn1SHGuRfe6/Td9rQ==", "signatures": [{"sig": "MEUCIQDFJdRF3c2TdyZ/U9hdmal8/TUeR3BuOfG3tV6gb5pb9QIgU0hPXg7XUwbGDWrn0ZHPSJPX20pbCJVRCPuPCvMdDN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 185456}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.27", "vite": "^5.2.12", "debug": "^4.3.4", "slash": "^5.1.0", "rollup": "^4.18.0", "source-map-js": "^1.2.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.1.0": {"name": "@vitejs/plugin-vue", "version": "5.1.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "d29f2aad9127c73b578e7a463e76249e89256e0b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.1.0.tgz", "fileCount": 8, "integrity": "sha512-QMRxARyrdiwi1mj3AW4fLByoHTavreXq0itdEW696EihXglf1MB3D4C2gBvE0jMPH29ZjC3iK8aIaUMLf4EOGA==", "signatures": [{"sig": "MEYCIQCWSARLSbvEj8E0npE69feBl4o3BiafVZVWudeGNIZqhgIhAIBpXHeIEoBQ01Br8r/0uYkX7Mgli6Y7uDg6RKvjW35p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 191601}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.29", "vite": "^5.3.4", "debug": "^4.3.5", "slash": "^5.1.0", "rollup": "^4.19.0", "source-map-js": "^1.2.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.1.1": {"name": "@vitejs/plugin-vue", "version": "5.1.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "bb9ddb0af012450edef4f5d65d5b3a3c7630864f", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.1.1.tgz", "fileCount": 8, "integrity": "sha512-sDckXxlHpMsjRQbAH9WanangrfrblsOd3pNifePs+FOHjJg1jfWq5L/P0PsBRndEt3nmdUnmvieP8ULDeX5AvA==", "signatures": [{"sig": "MEYCIQDokPSu7SUnw2BUg/2+UBErbFHm++JnJG5WVkvJpMm/KAIhANwTI8RWkyJMY+AgBUWrQMaeUwFTj1WkU0ED1pAo039+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 191545}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.29", "vite": "^5.3.4", "debug": "^4.3.5", "slash": "^5.1.0", "rollup": "^4.19.0", "source-map-js": "^1.2.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.1.2": {"name": "@vitejs/plugin-vue", "version": "5.1.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "f11091e0130eca6c1ca8cfb85ee71ea53b255d31", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.1.2.tgz", "fileCount": 8, "integrity": "sha512-nY9IwH12qeiJqumTCLJLE7IiNx7HZ39cbHaysEUd+Myvbz9KAqd2yq+U01Kab1R/H1BmiyM2ShTYlNH32Fzo3A==", "signatures": [{"sig": "MEUCIFSuMbFnJV/1BzbJbsrnJ0EnXRSNfJYTNOgS3nzn9tzRAiEAuMXTPW3Mf6Hd24ZQeabC5h9rUd0tyxG6qK0TAO1tu/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 192002}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.4.29", "vite": "^5.3.5", "debug": "^4.3.5", "slash": "^5.1.0", "rollup": "^4.19.2", "source-map-js": "^1.2.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.1.3": {"name": "@vitejs/plugin-vue", "version": "5.1.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "42b55c1a4d6f329aea25c5a24db51a31db31c693", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.1.3.tgz", "fileCount": 8, "integrity": "sha512-3xbWsKEKXYlmX82aOHufFQVnkbMC/v8fLpWwh6hWOUrK5fbbtBh9Q/WWse27BFgSy2/e2c0fz5Scgya9h2GLhw==", "signatures": [{"sig": "MEUCIQD1BZV5jUExAEPS7BD11mM9mtuB6GhU1Lw11q66tQBsLwIgd08OQF4g3Ask6RFD+hHAaUo3NNW9pzn85UD0i3IvMoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 191752}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.5.0-rc.1", "vite": "^5.4.2", "debug": "^4.3.6", "slash": "^5.1.0", "rollup": "^4.21.1", "source-map-js": "^1.2.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.1.4": {"name": "@vitejs/plugin-vue", "version": "5.1.4", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "72b8b705cfce36b00b59af196195146e356500c4", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.1.4.tgz", "fileCount": 8, "integrity": "sha512-N2XSI2n3sQqp5w7Y/AN/L2XDjBIRGqXko+eDp42sydYSBeJuSm5a1sLf8zakmo8u7tA8NmBgoDLA1HeOESjp9A==", "signatures": [{"sig": "MEYCIQD5CzX+hG71TgWyvCNJJ5SHq0iqlqlGnlQaea5zBinK7wIhANty9R3aFnSJh9EuxRPPAa60QMVB1YfF5Z+yilWLuuhx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 192861}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.5.3", "vite": "^5.4.3", "debug": "^4.3.6", "slash": "^5.1.0", "rollup": "^4.21.3", "source-map-js": "^1.2.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.1.5": {"name": "@vitejs/plugin-vue", "version": "5.1.5", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "ef1d7e19f8cecb0d1301c987dc0bdf8b778e3c82", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.1.5.tgz", "fileCount": 8, "integrity": "sha512-dlnib73G05CDBAUR/YpuZcQQ47fpjihnnNouAAqN62z+oqSsWJ+kh52GRzIxpkgFG3q11eXK7Di7RMmoCwISZA==", "signatures": [{"sig": "MEQCIGOLI6mY7M/OBtsERhcFr98Nw18pglwpewITmpXzulGPAiAJuNXwtUZI7R7wPY8gxeQEjQdDfECFECXwP75XC8RjvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 194404}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.5.12", "vite": "^6.0.0-beta.9", "debug": "^4.3.7", "slash": "^5.1.0", "rollup": "^4.25.0", "source-map-js": "^1.2.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.2.0": {"name": "@vitejs/plugin-vue", "version": "5.2.0", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"shasum": "994f3b4f12d3590c5a6895df4cbd270d9a6d5e17", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.2.0.tgz", "fileCount": 8, "integrity": "sha512-7n7KdUEtx/7Yl7I/WVAMZ1bEb0eVvXF3ummWTeLcs/9gvo9pJhuLdouSXGjdZ/MKD1acf1I272+X0RMua4/R3g==", "signatures": [{"sig": "MEUCIALX33MYfl4GtryiPM/Q5C+ri5c1rXoADW8Rjz7afV21AiEAsm1iuYh3irDfvEHvDFs2BBX8hQaCGvn9AmDzR/I/iJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 198076}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"vue": "^3.5.12", "vite": "^6.0.0-beta.9", "debug": "^4.3.7", "slash": "^5.1.0", "rollup": "^4.25.0", "source-map-js": "^1.2.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "peerDependencies": {"vue": "^3.2.25", "vite": "^5.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.2.1": {"name": "@vitejs/plugin-vue", "version": "5.2.1", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"integrity": "sha512-cxh314tzaWwOLqVes2gnnCtvBDcM1UMdn+iFR+UjAn411dPT3tOmqrJjbMd7koZpMAmBM/GqeV4n9ge7JSiJJQ==", "shasum": "d1491f678ee3af899f7ae57d9c21dc52a65c7133", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.2.1.tgz", "fileCount": 8, "unpackedSize": 198079, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiGaNNmlMIdVXQSbdjRnNnBPrKPVSD3XrYCHWf9woJ9QIgGsKtQzXR1AkBpTT8MERAf416Zk+pORoocwPEKwO91io="}]}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "debug": "^4.3.7", "rollup": "^4.27.2", "slash": "^5.1.0", "source-map-js": "^1.2.1", "vite": "^6.0.0", "vue": "^3.5.12"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.2.2": {"name": "@vitejs/plugin-vue", "version": "5.2.2", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"integrity": "sha512-IY0aPonWZI2huxrWjoSBUQX14GThitmr1sc2OUJymcgnY5RlUI7HoXGAnFEoVNRsck/kS6inGvxCN6CoHu86yQ==", "shasum": "001dda4e6229ea17972f4a1fb04090674523c6ad", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.2.2.tgz", "fileCount": 8, "unpackedSize": 199511, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGfQO3IGqBoMB9z35qUVm31o94hqPGrE/3z92w61bFbJAiA2+NzX20ihFH4QhYNvl+Wn3mFRbHrwc4Osxw9CAP3s0Q=="}]}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"@jridgewell/gen-mapping": "^0.3.8", "@jridgewell/trace-mapping": "^0.3.25", "debug": "^4.4.0", "rollup": "^4.34.9", "slash": "^5.1.0", "source-map-js": "^1.2.1", "vite": "^6.2.0", "vue": "^3.5.13"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}, "_hasShrinkwrap": false, "hasInstallScript": false}, "5.2.3": {"name": "@vitejs/plugin-vue", "version": "5.2.3", "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "dist": {"integrity": "sha512-IYSLEQj4LgZZuoVpdSUCw3dIynTWQgPlaRP6iAvMle4My0HdYwr5g5wQAfwOeHQBmYwEkqF70nRpSilr6PoUDg==", "shasum": "71a8fc82d4d2e425af304c35bf389506f674d89b", "tarball": "http://mirrors.cloud.tencent.com/npm/@vitejs/plugin-vue/-/plugin-vue-5.2.3.tgz", "fileCount": 8, "unpackedSize": 199486, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitejs%2fplugin-vue@5.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCGF7uMV4Qk36zouGVWTlfkmDEIXPC7Pq2ABR7q1yr8FAIhAJ0j4t+Ei59JbN3YC9VPP/6LRxADyafQAI0z0iCor7vg"}]}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "directories": {}, "devDependencies": {"@jridgewell/gen-mapping": "^0.3.8", "@jridgewell/trace-mapping": "^0.3.25", "debug": "^4.4.0", "rollup": "^4.34.9", "slash": "^5.1.0", "source-map-js": "^1.2.1", "vite": "^6.2.0", "vue": "^3.5.13"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}, "_hasShrinkwrap": false, "hasInstallScript": false}}, "modified": "2025-03-26T06:38:21.601Z", "time": {"created": "2020-12-29T05:53:47.974Z", "modified": "2025-03-26T06:38:21.601Z", "1.0.0": "2020-12-29T05:53:48.381Z", "1.0.1": "2020-12-29T06:05:07.723Z", "1.0.2": "2020-12-30T15:43:16.906Z", "1.0.3": "2021-01-02T18:18:27.686Z", "1.0.4": "2021-01-04T03:39:10.478Z", "1.0.5": "2021-01-09T20:43:54.236Z", "1.0.6": "2021-01-15T15:04:09.667Z", "1.1.0": "2021-01-19T15:22:22.712Z", "1.1.1": "2021-01-23T09:23:15.032Z", "1.1.2": "2021-01-24T07:03:29.369Z", "1.1.3": "2021-01-29T20:40:20.300Z", "1.1.4": "2021-01-30T19:31:44.163Z", "1.1.5": "2021-02-26T16:31:00.602Z", "1.2.0": "2021-03-26T22:05:54.874Z", "1.2.1": "2021-03-31T00:54:04.217Z", "1.2.2": "2021-04-24T17:48:33.669Z", "1.2.3": "2021-06-01T00:17:55.021Z", "1.2.4": "2021-06-27T21:46:54.711Z", "1.2.5": "2021-07-12T12:25:01.894Z", "1.3.0": "2021-07-27T09:59:20.103Z", "1.4.0": "2021-08-07T00:54:46.494Z", "1.5.0": "2021-08-24T14:03:31.453Z", "1.6.0": "2021-08-24T17:09:16.456Z", "1.6.1": "2021-09-06T14:16:54.166Z", "1.6.2": "2021-09-08T14:39:02.546Z", "1.7.0": "2021-09-18T16:19:41.200Z", "1.7.1": "2021-09-18T18:59:26.451Z", "1.8.0": "2021-09-18T22:09:58.357Z", "1.8.1": "2021-09-19T20:45:19.729Z", "1.9.0": "2021-09-21T19:21:23.842Z", "1.9.1": "2021-09-23T17:01:09.245Z", "1.9.2": "2021-09-24T21:05:01.952Z", "1.9.3": "2021-10-05T11:24:05.777Z", "1.9.4": "2021-10-27T05:39:46.651Z", "1.10.0-beta.0": "2021-10-28T13:01:16.184Z", "1.10.0-beta.1": "2021-11-19T19:26:20.383Z", "1.10.0": "2021-11-22T11:01:42.892Z", "1.10.1": "2021-11-26T07:24:29.468Z", "1.10.2": "2021-12-07T09:00:39.708Z", "2.0.0": "2021-12-12T07:59:52.277Z", "2.0.1": "2021-12-14T12:53:10.882Z", "2.1.0": "2022-01-21T12:44:07.093Z", "2.2.0": "2022-02-09T06:02:10.436Z", "2.2.2": "2022-02-18T16:37:41.979Z", "2.2.4": "2022-02-28T17:19:38.987Z", "2.3.0-beta.0": "2022-03-22T13:21:13.754Z", "2.3.0": "2022-03-30T12:34:57.505Z", "2.3.1": "2022-03-30T17:59:59.950Z", "2.3.2": "2022-05-04T17:34:02.497Z", "2.3.3": "2022-05-11T09:13:20.067Z", "3.0.0-alpha.0": "2022-05-13T13:25:42.869Z", "3.0.0-alpha.1": "2022-05-19T01:25:57.881Z", "3.0.0-alpha.2": "2022-06-19T07:08:58.709Z", "3.0.0-beta.0": "2022-06-21T13:49:10.528Z", "3.0.0-beta.1": "2022-07-06T07:55:57.431Z", "3.0.0": "2022-07-13T12:26:48.704Z", "3.0.1": "2022-07-18T05:38:20.571Z", "3.0.2": "2022-08-11T20:12:44.751Z", "2.3.4": "2022-08-12T13:02:01.164Z", "3.0.3": "2022-08-12T14:53:26.649Z", "3.1.0-beta.0": "2022-08-29T14:01:15.129Z", "3.1.0": "2022-09-05T09:48:56.666Z", "3.1.1": "2022-10-02T14:11:39.866Z", "3.1.2": "2022-10-02T14:12:29.901Z", "3.2.0-beta.0": "2022-10-05T10:01:17.554Z", "3.2.0": "2022-10-26T13:12:01.353Z", "4.0.0-alpha.0": "2022-11-15T15:18:41.101Z", "4.0.0-alpha.1": "2022-11-22T11:02:30.294Z", "4.0.0-alpha.2": "2022-11-30T17:00:47.091Z", "4.0.0-beta.0": "2022-12-05T08:46:10.025Z", "4.0.0": "2022-12-09T09:17:31.778Z", "4.1.0-beta.0": "2023-03-07T13:37:52.087Z", "4.1.0": "2023-03-16T09:12:21.441Z", "4.2.0-beta.0": "2023-04-17T10:01:53.553Z", "4.2.0-beta.1": "2023-04-18T13:58:31.958Z", "4.2.0-beta.3": "2023-04-19T02:40:37.991Z", "4.2.0": "2023-04-25T09:54:05.890Z", "4.2.1": "2023-04-26T06:47:47.447Z", "4.2.2": "2023-05-11T07:32:59.657Z", "4.2.3": "2023-05-12T10:41:07.035Z", "4.3.0": "2023-08-17T06:19:39.248Z", "4.3.1": "2023-08-17T08:25:19.724Z", "4.3.2": "2023-08-21T08:15:56.867Z", "4.3.3": "2023-08-22T03:22:54.380Z", "4.3.4": "2023-08-29T08:04:59.201Z", "4.4.0": "2023-10-02T14:02:01.447Z", "4.4.1": "2023-11-08T16:44:52.869Z", "4.5.0": "2023-11-16T13:28:31.957Z", "5.0.0-alpha.0": "2023-11-28T18:22:28.385Z", "4.5.1": "2023-12-01T06:56:53.214Z", "5.0.0-beta.0": "2023-12-07T05:37:54.954Z", "4.5.2": "2023-12-07T12:49:26.255Z", "5.0.0-beta.1": "2023-12-14T01:34:35.050Z", "5.0.0": "2023-12-25T09:35:26.237Z", "4.6.0": "2023-12-25T09:42:24.985Z", "4.6.1": "2023-12-29T16:51:03.340Z", "5.0.1": "2023-12-29T16:52:35.949Z", "5.0.2": "2023-12-30T13:59:01.190Z", "4.6.2": "2023-12-31T02:06:29.937Z", "5.0.3": "2024-01-10T07:30:26.414Z", "5.0.4": "2024-02-09T18:05:12.225Z", "5.0.5": "2024-05-31T07:41:43.266Z", "5.1.0": "2024-07-23T16:13:53.454Z", "5.1.1": "2024-07-27T09:23:27.538Z", "5.1.2": "2024-08-01T14:19:43.346Z", "5.1.3": "2024-08-29T18:07:57.359Z", "5.1.4": "2024-09-18T01:12:56.746Z", "5.1.5": "2024-11-11T01:49:04.837Z", "5.2.0": "2024-11-13T10:39:59.452Z", "5.2.1": "2024-11-26T12:47:45.443Z", "5.2.2": "2025-03-17T01:16:25.116Z", "5.2.3": "2025-03-17T11:41:29.290Z"}}