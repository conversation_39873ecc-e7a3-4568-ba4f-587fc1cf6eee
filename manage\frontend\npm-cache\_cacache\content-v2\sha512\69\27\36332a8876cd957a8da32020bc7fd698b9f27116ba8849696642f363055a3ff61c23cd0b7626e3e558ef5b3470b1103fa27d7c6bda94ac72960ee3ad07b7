{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:23:44.439Z", "name": "@parcel/watcher", "versions": {"2.0.5": {"name": "@parcel/watcher", "version": "2.0.5", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-x0hUbjv891omnkcHD7ZOhiyyUqUUR6MNjq89JhEI3BxppeKWAm6NPQsqqRrAkCJBogdT/o/My21sXtTI9rJIsw==", "shasum": "f913a54e1601b0aac972803829b0eece48de215b", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.5.tgz", "fileCount": 40, "unpackedSize": 1965654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuq4xCRA9TVsSAnZWagAAqJoP/j0mYPvmsel7BrI+7TIY\ndA8FEkv4ACUJtgX+NYS+Hxf8WDpANn7zvKusX5ZfWolrMYdtp/bbLNP4MOwc\n9DpxV4tQFQRd2CBJQsOWeb82w0p6HZOaOOEupGNNH52eCM3o0FunORQWPZGF\n5F0Wc9AVSyX9M3+GnvHFgTiZpFrousCB5QYInvyuKtRuJPeYhK+QnKNRPyeD\npPMKbk1gHp91hNxqujGkA5KlzafNd+4pFMRrr9FApAG8RCdrjG0Zn57mMbT9\nPKby9aNQzJW+mN+QLNib/CAInYWZlhRekEh2vYeCXKI+qBfMPGS8KmHw/L5f\nYtdwfwY6jif52hnpjCLG1T7m6sF75GVOrE8BePzzY8hlIFV27E3F1caobQQX\nvtT4UoUM/zvdkGUQXAgd4cBe6WgR4mzTT/g2fs2n2SGqCKFC5MfKuvEsOayW\n6lEelhmLo5w5b62QSqFeRMQ18ppMoxneSOk3DWirLfyF9C8g07MZt/1p9HHQ\noCkPPsSJ5CRckOZmXI2t8NSdls/ltUfckyHcS/7ZZbxbEi8wzme0SzYCdmIs\nwLh8b5PDIbHoMpVwaL9knibTltrG4OIaqZ4VYaF/6r8dc4ZoUsTMRnmLLLHe\nmpsZAFmCIGUfYa+NEcomwkqILYex//Wo/pDxnx2qUYJplNxiVHsB2sWgvc6B\nug1W\r\n=sFNx\r\n-----END PGP SIGNATURE-----\r\n", "size": 777211, "noattachment": false}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.4": {"name": "@parcel/watcher", "version": "2.0.4", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-cTDi+FUDBIUOBKEtj+nhiJ71AZVlkAsQFuGQTun5tV9mwQBQgZvhCzG+URPQc8myeN32yRVZEfVAPCs1RW+Jvg==", "shasum": "f300fef4cc38008ff4b8c29d92588eced3ce014b", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.4.tgz", "fileCount": 40, "unpackedSize": 1965472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpQ7XCRA9TVsSAnZWagAAwBIP/jDcgGvpHSSbnjpRfHB8\nmrdFxfdTnjLKdYzJ8o+Jx8rPdFaPEtXR1uzH1kzcWTUviM9/0UEi/GDgwqmB\no9sbsZEPYVYdWpSdSvkejcggfbV/ajYrKnlUij8jm9WCmVvZbOAhcKM3QDe/\nAOzGwBQadKG8Zt1+0B6xC3vZJB3t+5UQiYZB6sroEQZqdflNXw1QpeMSQclD\nuFuUaBN+H+/8Ekbb/L9BeK0L61vw28VOtAUKGRY2JSNIhZ6ZjRGBf3O6boQF\nEiKn0MsdSp03KjnBORKA1PckCSHML+HqVIlEAnsO2P5H+jTea2CaG6DcgM3R\nQHUwC47KBiULjYnHPkU0QUbvJjhClmlenL2WSrvHhOZt9XhOKDubf93LWLc0\nEpVWbbdwV0AS259WEFKDyvlY/j/9+otRHBpvWY6SLiNcgRJ63ZL0bFl+/r9p\n6t1LtfdK1cE2eOCZjdjXeaAbmycFGak2WFsj4bMXqI/7yzl/MaGrJ+Eg/Hwg\nJZTbtnEtFj/Yz2dbRnh1d0AYT4Tpjc4G2mssmM/ZykzP3mQj0QPCsYLcVPnZ\nKyODaN3TbHp6gfR87sCYs5FaTY0zoU5n/DFNE95UISPXOwpYJHnAXwn+Vv9o\nK+2gvohZYgBdj2fGrFD2/R3+4OIiFnCF9iy8wIoxrpgA57AcL/KxC64Mntzm\n0485\r\n=6oTx\r\n-----END PGP SIGNATURE-----\r\n", "size": 780447, "noattachment": false}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.3": {"name": "@parcel/watcher", "version": "2.0.3", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-PHh5PArr3nYGYVj9z/NSfDmmKEBNrg2bzoFgxzjTRBBxPUKx039x3HF6VGLFIfrghjJxcYn/IeSpdVwfob7KFA==", "shasum": "2bae7720f2b9c21ea0b89bab55479c7e8937231e", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.3.tgz", "fileCount": 40, "unpackedSize": 1965213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm+YVCRA9TVsSAnZWagAAczMP/i/HboHhdH1lIG7AZy2o\nKxao2pm2NcnGDGw8Ar8DJDdFd75abmG8DJ0meRtyImNZ/QXStWu5YwZPOtXr\nrWy0jyctrxkvxVW385pGbMwVBtnm8Hkldg2L0RXlbZw3XaMZzcZMp9g3VwWF\nGzeMa5ppsPr6P6lnhflWWrT0H9iEvbc7GgQxBa/fB3ax+jSx92d32CLHIdjf\ny7us3KPFyBMc/ts9WkQQBMWjF81B5ZVWV496TNa1NtmNmoGjbF2BuFnuH9lg\nQM0klkEaS4GBA3+yvA9GvBOmXcRK/oRIvx5yLpH6p5LMezVTsfxlQTdhjCx4\nZbCDSrsybEP47/9+QAolTnCA0FbBNULkFePm93wnL4wTQvL8vR7o1i5KucsX\ntrq6LuN58VzvgywOgA4MSGV9Te/zg9hk0DhUORI8KnbDmWdBCiUOASMs85LN\ntcfjIvSii6cXTc/s4wkVA74NAv8Tf8xMTFRtCsPAbAoziPxnjJRq4k5PM4ou\n5R34dAgnxritzmWVNrwP8FI3XYTocLyAyMhzCHz9g025k3Chzb5LE6FOIzWP\nQzbDO7iNJiGZYmmWTcH3nXTeinZ4haKBFFUcPoUxSwOiiyp8DyxwROO6xPw3\njrY/2pUQwJBRvgXntH/G4GaZ+BUQSqFmQIQJsz4HTaPSf+j8CZH9xstNVnPj\nhQAd\r\n=fYY4\r\n-----END PGP SIGNATURE-----\r\n", "size": 779735, "noattachment": false}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.2": {"name": "@parcel/watcher", "version": "2.0.2", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-WGJY55/mTAGse2C9VVi2oo+p05oJ0kiSHmOjV33+ywgKgUkUh6B/qFQ5kBO/9mH686qqtV3k2zH1QNm+XX4+lw==", "shasum": "46bef14584497147bad5247cfb41f80b24d24dfb", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.2.tgz", "fileCount": 40, "unpackedSize": 1970009, "size": 783332, "noattachment": false}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.1": {"name": "@parcel/watcher", "version": "2.0.1", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"shasum": "ec4bb6c43d9588a1ffd3d2abe6df5b501463c62d", "size": 783210, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.1.tgz", "integrity": "sha512-XegFF4L8sFn1RzU5KKOZxXUuzgOSwd6+X2ez3Cy6MVhYMbiLZ1moceMTqDhuT3N8DNbdumK3zP1wojsIsnX40w=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0": {"name": "@parcel/watcher", "version": "2.0.0", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"shasum": "ebe992a4838b35c3da9a568eb95a71cb26ddf551", "size": 782838, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0.tgz", "integrity": "sha512-ByalKmRRXNNAhwZ0X1r0XeIhh1jG8zgdlvjgHk9ZV3YxiersEGNQkwew+RfqJbIL4gOJfvC2ey6lg5kaeRainw=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.11": {"name": "@parcel/watcher", "version": "2.0.0-alpha.11", "dependencies": {"node-addon-api": "^3.0.2", "node-gyp-build": "^4.2.3"}, "devDependencies": {"fs-extra": "^9.0.1", "husky": "^4.3.0", "mocha": "^8.2.1", "prebuildify": "^4.1.0", "lint-staged": "^10.5.1", "prettier": "^2.1.2"}, "directories": {}, "dist": {"shasum": "8d6233d4416880810438cd2628e6a35273241ab3", "size": 709921, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.11.tgz", "integrity": "sha512-zMIAsFLcnB82kkk0kSOZ/zgyihb8sty0zVrsz+3ruoYXkchymWsCDsxiX4v+X2s8Jppk3JE8vlnD4DKs3QTOEQ=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.10": {"name": "@parcel/watcher", "version": "2.0.0-alpha.10", "dependencies": {"node-addon-api": "^3.0.2", "node-gyp-build": "^4.2.3"}, "devDependencies": {"fs-extra": "^9.0.1", "husky": "^4.3.0", "mocha": "^8.2.1", "prebuildify": "^4.1.0", "lint-staged": "^10.5.1", "prettier": "^2.1.2"}, "directories": {}, "dist": {"shasum": "99266189f5193512dbdf6b0faca20400c519a16e", "size": 710436, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.10.tgz", "integrity": "sha512-8uA7Tmx/1XvmUdGzksg0+oN7uj24pXFFnKJqZr3L3mgYjdrL7CMs3PRIHv1k3LUz/hNRsb/p3qxztSkWz1IGZA=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.9": {"name": "@parcel/watcher", "version": "2.0.0-alpha.9", "dependencies": {"node-addon-api": "^3.0.2", "node-gyp-build": "^4.2.3"}, "devDependencies": {"fs-extra": "^9.0.1", "husky": "^4.3.0", "mocha": "^8.2.1", "prebuildify": "^4.1.0", "lint-staged": "^10.5.1", "prettier": "^2.1.2"}, "directories": {}, "dist": {"shasum": "58010b069a8e64f0c3d0059263f6a525522398d2", "size": 709468, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.9.tgz", "integrity": "sha512-k3luHa2W4W42uAe85v61HFMJKBf8txJmEJBB4BlHuNoFomA7lLX35nsg1APFYS/HKvf0oIEqEL1HvPRx+Dt+QA=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.8": {"name": "@parcel/watcher", "version": "2.0.0-alpha.8", "dependencies": {"lint-staged": "^10.0.8", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1"}, "devDependencies": {"fs-extra": "^7.0.1", "husky": "^4.2.3", "mocha": "^6.0.2", "prebuildify": "^4.0.0", "prettier": "^1.19.1"}, "directories": {}, "dist": {"shasum": "7aee9504b87eebc73c794c611a6673e58b81e0b1", "size": 706006, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.8.tgz", "integrity": "sha512-9aQu1SFkR6t1UYo3Mj1Vg39/Scaa9i4xGZnZ5Ug/qLyVzHmdjyKDyAbsbUDAd1O2e+MUhr5GI1w1FzBI6J31Jw=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.7": {"name": "@parcel/watcher", "version": "2.0.0-alpha.7", "dependencies": {"lint-staged": "^10.0.8", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1"}, "devDependencies": {"fs-extra": "^7.0.1", "husky": "^4.2.3", "mocha": "^6.0.2", "prettier": "^1.19.1", "prebuildify": "^4.0.0"}, "directories": {}, "dist": {"shasum": "377f273adde0655c29b46d6249a08c0fa06dcfc5", "size": 694126, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.7.tgz", "integrity": "sha512-ry+kXaOe22YV1UV5Wnv99Hb+gyjjljOYOaSN1Nc5qHL83SxKhawivI8pKSqoaCSlE8aRnIz8NQ0x7pABUyIpng=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.6": {"name": "@parcel/watcher", "version": "2.0.0-alpha.6", "dependencies": {"lint-staged": "^10.0.8", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1"}, "devDependencies": {"fs-extra": "^7.0.1", "husky": "^4.2.3", "mocha": "^6.0.2", "prettier": "^1.19.1", "prebuildify": "^4.0.0"}, "directories": {}, "dist": {"shasum": "239430bcd88e02ef39c97ba30935cadce82c457d", "size": 694292, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.6.tgz", "integrity": "sha512-vlLVEel0fLIvPI+iPwttPff3wj/OPmg4AJA6UvgQKNwX8prhu+qW3C2KOqapkh7sturtJaA5o3RwuC5O0gqZEw=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.0-alpha.5": {"name": "@parcel/watcher", "version": "2.0.0-alpha.5", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"fs-extra": "^7.0.1", "mocha": "^6.0.2", "prebuild": "^8.2.1"}, "directories": {}, "dist": {"shasum": "b16e8af9e6cccf249e094075071701d01f73a1be", "size": 1778442, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.5.tgz", "integrity": "sha512-QE5S2KgyM04pCECr+ZbwKZuzNrrV5eUadPYDhSRdrVc9uOIXfxUPGHgRsp99g+No9Zo+FI9EWL9jEiHmmAb77A=="}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0-alpha.4": {"name": "@parcel/watcher", "version": "2.0.0-alpha.4", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"fs-extra": "^7.0.1", "mocha": "^6.0.2", "prebuild": "^8.2.1"}, "directories": {}, "dist": {"shasum": "d2c88ed7945c00373ab984bfb39b2394f882b794", "size": 27046, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.4.tgz", "integrity": "sha512-J8<PERSON>etm6Yeh27lNVr02/bTxqQ5dAs040C9OB1Zs7C4kaDCq/Dx7FVph0aRKDhAiZ0BiSBwgZEh7xmD4ZU844kxA=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.12.1": {"name": "@parcel/watcher", "version": "1.12.1", "dependencies": {"@parcel/utils": "^1.11.0", "chokidar": "^2.1.5"}, "devDependencies": {"@parcel/babel-register": "^1.11.1", "@parcel/fs": "^1.11.0", "@parcel/test-utils": "^1.12.0", "mocha": "^5.2.0"}, "directories": {}, "dist": {"shasum": "b98b3df309fcab93451b5583fc38e40826696dad", "size": 6784, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-1.12.1.tgz", "integrity": "sha512-od+uCtCxC/KoNQAIE1vWx1YTyKYY+7CTrxBJPRh3cDWw/C0tCtlBMVlrbplscGoEpt6B27KhJDCv82PBxOERNA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0-alpha.3": {"name": "@parcel/watcher", "version": "2.0.0-alpha.3", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"fs-extra": "^7.0.1", "mocha": "^6.0.2", "prebuild": "^8.2.1"}, "directories": {}, "dist": {"shasum": "af81241b734f9a96fb6771d5900266c00fa62d6d", "size": 26811, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.3.tgz", "integrity": "sha512-kR30TWdVrApH2R/UZ6uIWp72SdINaCC5Tk63TPMthDbSMbjwb1kXJvOrNbIDTOoxyitHH6xZICeyBCTf78oWag=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0-alpha.2": {"name": "@parcel/watcher", "version": "2.0.0-alpha.2", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"fs-extra": "^7.0.1", "mocha": "^6.0.2", "prebuild": "^8.2.1"}, "directories": {}, "dist": {"shasum": "f7124c34d4386105d8ec43fa81864cc2790b1434", "size": 69251, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.2.tgz", "integrity": "sha512-MEslD2btlI7bmT3I72JKt3Fh11apn2YgaWBJ4wNMRzM5RBADAxuKQvGkqIOL7qR4PMWqbibWETYN9v7rb+Eb+w=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.0-alpha.1": {"name": "@parcel/watcher", "version": "2.0.0-alpha.1", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"fs-extra": "^7.0.1", "mocha": "^6.0.2", "prebuild": "^8.2.1"}, "directories": {}, "dist": {"shasum": "3954d1fe8343a17886a2290d8304aa126ca8bf46", "size": 69251, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.0-alpha.1.tgz", "integrity": "sha512-FK3ojn8ngW1Hl8veOc53nEPWLwXYNQ+S8qpkJOQph7ggg1OWa7838tLdI61nrL0iGX5gDYtZoNq9RWD8PsRTyw=="}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.12.0": {"name": "@parcel/watcher", "version": "1.12.0", "dependencies": {"@parcel/utils": "^1.11.0", "chokidar": "^2.0.3"}, "devDependencies": {"@parcel/babel-register": "^1.11.0", "@parcel/fs": "^1.11.0", "@parcel/test-utils": "^1.12.0", "mocha": "^5.2.0"}, "directories": {}, "dist": {"shasum": "769024b2a810b0c3b38c310f297d104c77df3660", "size": 6832, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-1.12.0.tgz", "integrity": "sha512-yijGiAqG7Tjf5WnFwOkiNWwerfZQDNABldiiqRDtr7vDWLO+F/DIncyB7tTcaD5Loevrr5mzzGo8Ntf3d2GIPg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "1.11.0": {"name": "@parcel/watcher", "version": "1.11.0", "dependencies": {"@parcel/utils": "^1.11.0", "chokidar": "^2.0.3"}, "devDependencies": {"@parcel/babel-register": "^1.11.0", "@parcel/fs": "^1.11.0", "@parcel/test-utils": "^1.11.0", "mocha": "^5.2.0"}, "directories": {}, "dist": {"shasum": "a05ee752d47bf3627b77a64a6089404683edc255", "size": 6723, "noattachment": false, "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-1.11.0.tgz", "integrity": "sha512-1ySF0sH06jyhpaErW1UWC7BNgkAl6PJyBjuu2cLTW1o71J2iQqgGt95cbuqmfmjI3l0xYN+nauDFqHERaj7Z8A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "2.0.6": {"name": "@parcel/watcher", "version": "2.0.6", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-6CaHXp6BNrFY5375OGQLSeaxfO8csgGWbO1U2nUqufDtUks7ZIG5wAyj/wR1zkOxRrhN0EaZWvlgSKYqo7a9lg==", "shasum": "9087e44ea93b2d7e901d479bb9b0578f25b2173a", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.6.tgz", "fileCount": 40, "unpackedSize": 1781917, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSha+F5ayBpk92Nczl9AAenvh6AkqrSyTExj8+kHIrXAIgPSXtCHCrw4mxosujcUc3QcXgSrJVqe12QCAEU3HP8iU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYd0PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmGw/+Mu3wHjVuvrLW8OY7YH8b69wt5VmASjGYmfkqAxXABPpNHeSs\r\nicdZ4L0MmRxoulOjttk3yQ/0QUbdwb/OyYVTMrHJNUbq8qFgwcIBVCY0rGvP\r\nGZWLS8ge8k9LBaGZXpshhv2UGU5r8NvCFqXBGcMDMUlDPQDrbdbH8qPvqU6J\r\niyhJmcYMXiu4/m7qb6t/qN649K7cP4fdw+hLT9G7352Il45IsKW8g0bhpITL\r\nl09BCAUXAJ3aZIL/09WQed6OB4gELEanDOCXUUVMPTpd7oCCy6eE+yqxKWax\r\nGpZX4i7SmKPDOT4mGeoNM1SLYfVV29m1wY3WDWCKhF318575aD0wnJtjHMHG\r\nG2L7uvto4vqSXXlQ/QKPxjRQjN8d5vydx76qgeT8cvSfWs1CTD1MWP3Yy3wl\r\ne6Tbycd0RIWqiMDq7WXLQTKdAvRzAOikWeGaC9mHhyvbDBcXKK1EmIVRQZLa\r\n/nMkfO9cgEpVIZ8lu/DUBaV6hoeJci/Kd60n9Ej9z2YNK62e8830yYMvbOt6\r\nVBOvQubmIJfSFBVMDOQEjQ9BbYzU0FMosxMV4lKrTAyWK91todpzUKmG1e0V\r\nybRUW4aqtWj1zJDx4NSRALtySLrDsn6Z6oVV/NpYrdS3NHqzJjp0opvHUudj\r\nNUBRKw/2XVzm0JpzIFBp7nXxgPaojXWcTtw=\r\n=pKk9\r\n-----END PGP SIGNATURE-----\r\n", "size": 700522}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.0.7": {"name": "@parcel/watcher", "version": "2.0.7", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "lint-staged": "^11.1.2", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-gc3hoS6e+2XdIQ4HHljDB1l0Yx2EWh/sBBtCEFNKGSMlwASWeAQsOY/fPbxOBcZ/pg0jBh4Ga+4xHlZc4faAEQ==", "shasum": "c95fe1370e8c6237cb9729c9c075264acc7e21a5", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.0.7.tgz", "fileCount": 40, "unpackedSize": 1782783, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUM64oPIKTGkFMmpMySqjzNXUSQTAomcuV/LH6RBmxlAIhALIn7bjys9t7BLS4cMrHtmDcJ+Z3sHMNa6bfpjnuRMKY"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZJ9kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppnBAAmYZhHaC14gREbFGDnVvsVVfhABflk4ZOtM3TMceSeKja/NsV\r\nv2fHARrMSh5ZgF6y4jjioAT2FPunpSnOdsrMyPR13G6j3Ok2An4oSra50bfw\r\n0VFnkefUMBN5HalgizzLYckDMMNIBfdyhwtd8C2PIGTuOgumUw6xouLFGF7D\r\ne4Pn7Nbdkm2XFu+65zBj1MZhowxfEMj2MLVX8QbA2afDQi62CjXEb7PoQ5me\r\nOfJdZoSxtDH5oqLjnuIa7CGtUaWzgKnwnUlozJn8Gp2mbtxW8R6sDJU+mjL9\r\ncKzCtQ1+Np2LHNUStTcQmkvhX+bOue3EW0ZGkS1IgqTTVNBFcS4QjCKv6q3X\r\no7GXosf2FGBr+GC7p8p0W+eaABqR59IRkBF5+JbJN1XAP5X+XxF5wHCm8jZ0\r\nSFYEdKu6cSPzcn8Nhkl7WUnYAEGdRfORtZsAwqXr6KRjmMrHT2P3gREVO+KB\r\nhTYC0oUBC6+duTHwOMNgVMXaPh7DiwOorxrYq3m5ByGhE0DHu3+u7WZbbKyw\r\ngi2GClTUCrJs20bLCg3wqjE1Szt6Sjn+PzxZ5P5J8LKMT4hVYo9mPhJFDd2m\r\nW/8tClajJCjoaWQZmJUu2Bd4Vn8gegOifmTVkwGISzjTj7StngDkfwbVPacU\r\nnVbWdIJqAJFxJGgrB/Vn/PK1X9D7TEcgt0s=\r\n=R1VJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 700731}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.1.0": {"name": "@parcel/watcher", "version": "2.1.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "prebuildify": "^4.2.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-8s8yYjd19pDSsBpbkOHnT6Z2+UJSuLQx61pCFM0s5wSRvKCEMDjd/cHY3/GI1szHIWbpXpsJdg3V6ISGGx9xDw==", "shasum": "5f32969362db4893922c526a842d8af7a8538545", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.1.0.tgz", "fileCount": 42, "unpackedSize": 2194421, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6SpthHYVDLdyPTmcachax0TzR/63zrvuSaI1FYFAnEgIhANbDqioHUDjtzhCRKH1Nc5ZkVUW1emCUbtOBir5qo/B1"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtmRnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDzA/+M0wNuNFmM8woZjv1qG+HLNAxC5rioUTIN5Oxr1/mSecfRfg4\r\n6nF9erBvxoWIl8BpSG+gdV3K00/dRFxaCb8rSd/7qWjOQ9b+KzWjOD6xPWOy\r\nMfTAEVOC4LqfXzt2amjpm2cBR5LaDyh5aR3k7uRfvGftNVAhUE4EwrtFJjLh\r\n7Dji25ipR04DK6yAqmj5cpp+PEz7zS6VSQNIAAiTpFHZUb5SLIJx1oPOaUds\r\npKkvug/RMQUUG5FvRQmf2lReoe4EfagxLtuzgISSGpYE+zPcZNfHDi475nY/\r\nZN5CtLRSjMWobx+/8vouUYKpqE0Bxex2ss+90o9LqmxWWgTSUmUYe4MqrvLW\r\n5RAfoR4jo1ZWmbmLdqv6Dj7t4lSsoxjDcKcZhduhCSjlmYQFeK5nRwI5150g\r\ntPTwLXMTwFc6SEGNlGOBKj9/R0hcB4c4psiTjeHAO729Mg3yg82507jTO6MJ\r\nwnlOvwlAO8xz3bhnLBSJEbFmrLbS+xh5HbIM0OSYjZeQ+2Nm3ployx9Wjzuv\r\nN208oIKESWoPq4JYqiuCfkT/Ff8Dm+OYGYESgWyhr9NOUIKee8PZYpPcRiSp\r\nyWEH4BF9VpwjWKeODWnZWT9b1DGPGtV/ROP6IK0IjVMGCsL0TwsHmQU/U68g\r\nannFT08YUZ8sOYbv+Ri7zyAzcN4ySpbDHcY=\r\n=iX6d\r\n-----END PGP SIGNATURE-----\r\n", "size": 872938}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.0-alpha.0": {"name": "@parcel/watcher", "version": "2.2.0-alpha.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.2.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.0", "@parcel/watcher-win32-x64": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.2.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.0", "@parcel/watcher-win32-x64": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-Tg46S6pUZ/VFVQi+01JUwWkMfRV5b9y+q09BGhqzqmqFr+FqOKTcf5YSFupBOBELL6/TIzlPAdyhdQ3iasGhhg==", "shasum": "da10a3dd8dfb4d3e79aa0109dbedc3dc4eff3115", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.2.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 14357, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBr2wbmgLPK12Lx8lCgcCXd/Ouubvx0GLl2iF4pVZzfUAiEAksXGzSUA1HQ4E+CnRSWXOWFOFeqQ/z7WzX3HmonfDnQ="}], "size": 5205}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1687727623354, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher", "version": "2.2.0-alpha.1", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.2.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.1", "@parcel/watcher-win32-x64": "2.2.0-alpha.1", "@parcel/watcher-win32-arm64": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.1", "@parcel/watcher-android-arm64": "2.2.0-alpha.1"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.2.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.1", "@parcel/watcher-win32-x64": "2.2.0-alpha.1", "@parcel/watcher-win32-arm64": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.1", "@parcel/watcher-android-arm64": "2.2.0-alpha.1"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-68z4Tio3DeolrQL9Gre3kLkfMkPPE5RTsRAnnIPYKwUeEKKawrFtZgLvtMsD91x3ysdqwDVpVfU77PJ0cLsmOg==", "shasum": "cba95844fcb8fca27f759bd5f9a212992d14c704", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.2.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 14818, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDenIYAKMS7EKBYfddvM6mTTE7YpifIIQ+6LtHp0/oDkAiEAnYIbrbt5Fd3VrMjL+9mtH2w767mGaV0OmaFM6xppxBg="}], "size": 5354}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1687738120835, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.0": {"name": "@parcel/watcher", "version": "2.2.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.2.0", "@parcel/watcher-darwin-arm64": "2.2.0", "@parcel/watcher-win32-x64": "2.2.0", "@parcel/watcher-win32-arm64": "2.2.0", "@parcel/watcher-linux-x64-glibc": "2.2.0", "@parcel/watcher-linux-x64-musl": "2.2.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0", "@parcel/watcher-linux-arm64-musl": "2.2.0", "@parcel/watcher-linux-arm-glibc": "2.2.0", "@parcel/watcher-android-arm64": "2.2.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.2.0", "@parcel/watcher-darwin-arm64": "2.2.0", "@parcel/watcher-win32-x64": "2.2.0", "@parcel/watcher-win32-arm64": "2.2.0", "@parcel/watcher-linux-x64-glibc": "2.2.0", "@parcel/watcher-linux-x64-musl": "2.2.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0", "@parcel/watcher-linux-arm64-musl": "2.2.0", "@parcel/watcher-linux-arm-glibc": "2.2.0", "@parcel/watcher-android-arm64": "2.2.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-71S4TF+IMyAn24PK4KSkdKtqJDR3zRzb0HE3yXpacItqTM7XfF2f5q9NEGLEVl0dAaBAGfNwDCjH120y25F6Tg==", "shasum": "92067954e591d239c3ecfa08add205f88f476068", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.2.0.tgz", "fileCount": 6, "unpackedSize": 14710, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKsmnXdjbtSJYo/NIlATCQkfgwTXacYVNA37//PjsySQIhAO0QwvFRO+KbKleL3O9s9iUcRW1eRiGI8J39LpqLvjl6"}], "size": 5346}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1688269831733, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher", "version": "2.2.1-alpha.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.2.1-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.1-alpha.0", "@parcel/watcher-win32-x64": "2.2.1-alpha.0", "@parcel/watcher-win32-arm64": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.1-alpha.0", "@parcel/watcher-android-arm64": "2.2.1-alpha.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.2.1-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.1-alpha.0", "@parcel/watcher-win32-x64": "2.2.1-alpha.0", "@parcel/watcher-win32-arm64": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.1-alpha.0", "@parcel/watcher-android-arm64": "2.2.1-alpha.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-/pOCqIpA0xswCULEKndY7bVKzgt7MtKERE6IKfBLpr/e/thb9po6ES+aEcfol3YDEMYY13pbGdAK81A+LjICUA==", "shasum": "117e4ff05c3fc9c72060fe0761706b61d90fc503", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.2.1-alpha.0.tgz", "fileCount": 6, "unpackedSize": 14798, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhXQW0tMeUpWqsz+sIk5pzID04VXiQU2HwGAe4J3hxbgIgBaGxPFPPfiWaPx+W1lqf2S5TYO7GUid97VgyCuzboKY="}], "size": 5353}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1688571418472, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher", "version": "2.3.0-alpha.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.3.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.0", "@parcel/watcher-win32-x64": "2.3.0-alpha.0", "@parcel/watcher-win32-arm64": "2.3.0-alpha.0", "@parcel/watcher-win32-ia32": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.0", "@parcel/watcher-android-arm64": "2.3.0-alpha.0", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.3.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.0", "@parcel/watcher-win32-x64": "2.3.0-alpha.0", "@parcel/watcher-win32-arm64": "2.3.0-alpha.0", "@parcel/watcher-win32-ia32": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.0", "@parcel/watcher-android-arm64": "2.3.0-alpha.0", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-Z/zX2KVnfnOkqG8x2iphmbLTeVWNFHpkwVHF8KfVqsFtL9mwimJnFpsmhWh8UnpTEW15saEEZPr98ftcW/FY6Q==", "shasum": "622489b36f3ce841484f1b572d615f3d299ed394", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.3.0-alpha.0.tgz", "fileCount": 6, "unpackedSize": 14335, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG31a0VCQgMKsjYxYofYaqrKoN8+YVMvj/L4sxqftXy5AiEA0EGZgwCRlwPjPvKqYGyFeARl5bx/jKg4rQeJjnWcTss="}], "size": 5287}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1690750518742, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher", "version": "2.3.0-alpha.1", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.3.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.1", "@parcel/watcher-win32-x64": "2.3.0-alpha.1", "@parcel/watcher-win32-arm64": "2.3.0-alpha.1", "@parcel/watcher-win32-ia32": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.1", "@parcel/watcher-android-arm64": "2.3.0-alpha.1", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.1"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.3.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.1", "@parcel/watcher-win32-x64": "2.3.0-alpha.1", "@parcel/watcher-win32-arm64": "2.3.0-alpha.1", "@parcel/watcher-win32-ia32": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.1", "@parcel/watcher-android-arm64": "2.3.0-alpha.1", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.1"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-mPsG3KZMyOTAEYnyQDOsFyE0ulOISVIQXZoUqkZUOLEbEA9n2exi97ZPPggr7HHZUfoAjJzFoyxYii2BXXS8pw==", "shasum": "2829b574f120cb2222943fd459c87a1001f41a6d", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.3.0-alpha.1.tgz", "fileCount": 6, "unpackedSize": 15199, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDjYaAvm3MQq0ghOQ81Jl1rHSbDYn2lX8VirnG2+9XbAiEAnuF0og7HdAacj2OJc0aPsrChsNNRUMKn+BgBMQzGWP8="}], "size": 5653}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1690753414310, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher", "version": "2.3.0-alpha.2", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.3.0-alpha.2", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.2", "@parcel/watcher-win32-x64": "2.3.0-alpha.2", "@parcel/watcher-win32-arm64": "2.3.0-alpha.2", "@parcel/watcher-win32-ia32": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.2", "@parcel/watcher-android-arm64": "2.3.0-alpha.2", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.2"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.3.0-alpha.2", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.2", "@parcel/watcher-win32-x64": "2.3.0-alpha.2", "@parcel/watcher-win32-arm64": "2.3.0-alpha.2", "@parcel/watcher-win32-ia32": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.2", "@parcel/watcher-android-arm64": "2.3.0-alpha.2", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.2"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-Sr8xPFkWRwiNgJ2Gf4zAtZWXtvzRYDPF87n4GvFvj7hSLErJPuKynymmkcgj/76NHzUSkdjCvwj51Gq5jaA/4A==", "shasum": "3942838b502f5573b80dfc75ab3882cf7828e5ed", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.3.0-alpha.2.tgz", "fileCount": 6, "unpackedSize": 15157, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDa2TrN4WVhm4WwAJrN8colf9olOn48VEoPT0F/zWk4uwIgcD02TmKJPlCUfPhPOqzBiIaPMBRT0uhuPSlSWQ0bWJo="}], "size": 5615}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1692485218588, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher", "version": "2.3.0-alpha.3", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.3.0-alpha.3", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.3", "@parcel/watcher-win32-x64": "2.3.0-alpha.3", "@parcel/watcher-win32-arm64": "2.3.0-alpha.3", "@parcel/watcher-win32-ia32": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.3", "@parcel/watcher-android-arm64": "2.3.0-alpha.3", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.3"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.3.0-alpha.3", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.3", "@parcel/watcher-win32-x64": "2.3.0-alpha.3", "@parcel/watcher-win32-arm64": "2.3.0-alpha.3", "@parcel/watcher-win32-ia32": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.3", "@parcel/watcher-android-arm64": "2.3.0-alpha.3", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.3"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-M5s4PkCeKck9PNOEVsn/FhBWWH9n51eInraP6vi46DCIXAEbfWLyIj21vXAIL8s9HlghO8fY0RtZqu8bCHBrFg==", "shasum": "51cb6dcad9a6058e97065ae69c5fb7968a275fd1", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.3.0-alpha.3.tgz", "fileCount": 7, "unpackedSize": 17182, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEEwatoSsS83XkwBAnh5EDiO6L5aebulBXq4SXoOMQMvAiB2A+9Y5ahjyHq50RBWUlM9KPijhOjSXMLU4DyjMkbZMA=="}], "size": 5843}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1692557616752, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.3.0": {"name": "@parcel/watcher", "version": "2.3.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.3.0", "@parcel/watcher-darwin-arm64": "2.3.0", "@parcel/watcher-win32-x64": "2.3.0", "@parcel/watcher-win32-arm64": "2.3.0", "@parcel/watcher-win32-ia32": "2.3.0", "@parcel/watcher-linux-x64-glibc": "2.3.0", "@parcel/watcher-linux-x64-musl": "2.3.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0", "@parcel/watcher-linux-arm64-musl": "2.3.0", "@parcel/watcher-linux-arm-glibc": "2.3.0", "@parcel/watcher-android-arm64": "2.3.0", "@parcel/watcher-freebsd-x64": "2.3.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.3.0", "@parcel/watcher-darwin-arm64": "2.3.0", "@parcel/watcher-win32-x64": "2.3.0", "@parcel/watcher-win32-arm64": "2.3.0", "@parcel/watcher-win32-ia32": "2.3.0", "@parcel/watcher-linux-x64-glibc": "2.3.0", "@parcel/watcher-linux-x64-musl": "2.3.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0", "@parcel/watcher-linux-arm64-musl": "2.3.0", "@parcel/watcher-linux-arm-glibc": "2.3.0", "@parcel/watcher-android-arm64": "2.3.0", "@parcel/watcher-freebsd-x64": "2.3.0"}, "devDependencies": {"fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-pW7QaFiL11O0BphO+bq3MgqeX/INAk9jgBldVDYjlQPO4VddoZnF22TcF9onMhnLVHuNqBJeRf+Fj7eezi/+rQ==", "shasum": "803517abbc3981a1a1221791d9f59dc0590d50f9", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.3.0.tgz", "fileCount": 7, "unpackedSize": 17078, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5DraXfeTltan2QOTVaJTJJnKsLbbSwD63Jnb6z8KIDAIhAN2cOYbiYiahVjcTUHl40DpvS2eNRobFq3uNLSzTfUj2"}], "size": 5825}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1692850825586, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.4.0": {"name": "@parcel/watcher", "version": "2.4.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.4.0", "@parcel/watcher-darwin-arm64": "2.4.0", "@parcel/watcher-win32-x64": "2.4.0", "@parcel/watcher-win32-arm64": "2.4.0", "@parcel/watcher-win32-ia32": "2.4.0", "@parcel/watcher-linux-x64-glibc": "2.4.0", "@parcel/watcher-linux-x64-musl": "2.4.0", "@parcel/watcher-linux-arm64-glibc": "2.4.0", "@parcel/watcher-linux-arm64-musl": "2.4.0", "@parcel/watcher-linux-arm-glibc": "2.4.0", "@parcel/watcher-android-arm64": "2.4.0", "@parcel/watcher-freebsd-x64": "2.4.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.4.0", "@parcel/watcher-darwin-arm64": "2.4.0", "@parcel/watcher-win32-x64": "2.4.0", "@parcel/watcher-win32-arm64": "2.4.0", "@parcel/watcher-win32-ia32": "2.4.0", "@parcel/watcher-linux-x64-glibc": "2.4.0", "@parcel/watcher-linux-x64-musl": "2.4.0", "@parcel/watcher-linux-arm64-glibc": "2.4.0", "@parcel/watcher-linux-arm64-musl": "2.4.0", "@parcel/watcher-linux-arm-glibc": "2.4.0", "@parcel/watcher-android-arm64": "2.4.0", "@parcel/watcher-freebsd-x64": "2.4.0"}, "devDependencies": {"esbuild": "^0.19.8", "fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-XJLGVL0DEclX5pcWa2N9SX1jCGTDd8l972biNooLFtjneuGqodupPQh6XseXIBBeVIMaaJ7bTcs3qGvXwsp4vg==", "shasum": "2d3c4ef8832a5cdfdbb76b914f022489933e664f", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.4.0.tgz", "fileCount": 7, "unpackedSize": 17033, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSgwZl97RurQ0bcMPkD4R1Ffqgx/UdQ9G1odhcNCEAswIhAKa/kRu09cZY73N23A7C+/QpsZiWLTocl1Kv0IlB7LvJ"}], "size": 5813}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "publish_time": 1705360261855, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.4.1": {"name": "@parcel/watcher", "version": "2.4.1", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0", "@parcel/watcher-darwin-x64": "2.4.1", "@parcel/watcher-darwin-arm64": "2.4.1", "@parcel/watcher-win32-x64": "2.4.1", "@parcel/watcher-win32-arm64": "2.4.1", "@parcel/watcher-win32-ia32": "2.4.1", "@parcel/watcher-linux-x64-glibc": "2.4.1", "@parcel/watcher-linux-x64-musl": "2.4.1", "@parcel/watcher-linux-arm64-glibc": "2.4.1", "@parcel/watcher-linux-arm64-musl": "2.4.1", "@parcel/watcher-linux-arm-glibc": "2.4.1", "@parcel/watcher-android-arm64": "2.4.1", "@parcel/watcher-freebsd-x64": "2.4.1"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.4.1", "@parcel/watcher-darwin-arm64": "2.4.1", "@parcel/watcher-win32-x64": "2.4.1", "@parcel/watcher-win32-arm64": "2.4.1", "@parcel/watcher-win32-ia32": "2.4.1", "@parcel/watcher-linux-x64-glibc": "2.4.1", "@parcel/watcher-linux-x64-musl": "2.4.1", "@parcel/watcher-linux-arm64-glibc": "2.4.1", "@parcel/watcher-linux-arm64-musl": "2.4.1", "@parcel/watcher-linux-arm-glibc": "2.4.1", "@parcel/watcher-android-arm64": "2.4.1", "@parcel/watcher-freebsd-x64": "2.4.1"}, "devDependencies": {"esbuild": "^0.19.8", "fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^5.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-HNjmfLQEVRZmHRET336f20H/8kOozUGwk7yajvsonjNxbj2wBTK1WsQuHkD5yYh9RxFGL2EyDHryOihOwUoKDA==", "shasum": "a50275151a1bb110879c6123589dba90c19f1bf8", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.4.1.tgz", "fileCount": 7, "unpackedSize": 16179, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwAZOArl7VSsq658pHOYhQIjhnOkCI4XfT4Lph9cnmlAiBQ8TctGUkqKGSCPtncnU8FeQ+UDcgMJ+b6FS3qmt+xBA=="}], "size": 5867}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702689857, "_source_registry_name": "default", "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher", "version": "2.4.2-alpha.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.4.2-alpha.0", "@parcel/watcher-darwin-x64": "2.4.2-alpha.0", "@parcel/watcher-win32-ia32": "2.4.2-alpha.0", "@parcel/watcher-freebsd-x64": "2.4.2-alpha.0", "@parcel/watcher-win32-arm64": "2.4.2-alpha.0", "@parcel/watcher-darwin-arm64": "2.4.2-alpha.0", "@parcel/watcher-android-arm64": "2.4.2-alpha.0", "@parcel/watcher-linux-x64-musl": "2.4.2-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.4.2-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.4.2-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.4.2-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.4.2-alpha.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "esbuild": "^0.19.8", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^6.0.1"}, "directories": {}, "dist": {"shasum": "60d727d0594192512bea6927cce6ca610819db93", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.4.2-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-SHBGWwLtzbRAt/lP76bCmsqysbnQiyocW2+kQAWTA7EkfXETLrHpbkbQb6HHkvXK3HnV4GBvKqRkKWneE95a0A==", "signatures": [{"sig": "MEUCIQDKR7ZUJOeQkNpdwryVeuigG3BaHweKspfQnZBEjEtNcAIgX3Jne7vRQNHq7utP/HltLOohU+yQCFzIjADvHlnG098=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16283, "size": 5874}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "publish_time": 1718259265137, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher", "version": "2.5.0", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.5.0", "@parcel/watcher-darwin-arm64": "2.5.0", "@parcel/watcher-win32-x64": "2.5.0", "@parcel/watcher-win32-arm64": "2.5.0", "@parcel/watcher-win32-ia32": "2.5.0", "@parcel/watcher-linux-x64-glibc": "2.5.0", "@parcel/watcher-linux-x64-musl": "2.5.0", "@parcel/watcher-linux-arm64-glibc": "2.5.0", "@parcel/watcher-linux-arm64-musl": "2.5.0", "@parcel/watcher-linux-arm-glibc": "2.5.0", "@parcel/watcher-linux-arm-musl": "2.5.0", "@parcel/watcher-android-arm64": "2.5.0", "@parcel/watcher-freebsd-x64": "2.5.0"}, "devDependencies": {"esbuild": "^0.19.8", "fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^6.0.1", "prettier": "^2.3.2"}, "directories": {}, "dist": {"integrity": "sha512-i0GV1yJnm2n3Yq1qw6QrUrd/LI9bE8WEBOTtOkpCXHHdyN3TAGgqAK/DAT05z4fq2x04cARXt2pDmjWjL92iTQ==", "shasum": "5c88818b12b8de4307a9d3e6dc3e28eba0dfbd10", "tarball": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.5.0.tgz", "fileCount": 45, "unpackedSize": 129459, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7jJks1W1Ih2h+yqbP2CVHgdmuddsCjnSK44z9pGBfkAiEA9SEBfeQ5mGNEIY+izTLwEioHMoMFfvGMhCCyxkEx4+Q="}], "size": 33813}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "hasInstallScript": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701352517, "_source_registry_name": "default"}}, "_source_registry_name": "default"}