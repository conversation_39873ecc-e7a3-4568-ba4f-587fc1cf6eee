{"dist-tags": {"latest": "2.5.0"}, "modified": "2024-11-04T06:24:18.122Z", "name": "@parcel/watcher-win32-x64", "versions": {"2.2.0-alpha.0": {"name": "@parcel/watcher-win32-x64", "version": "2.2.0-alpha.0", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-KCVykU20hreS54CyqtkouCQq4e8Iv5Fr+vIA2y3EnH64E6upanim/iObpeCXfb8m8ORbeAywlAt11YXCw4/3pw==", "shasum": "1229f9e776001bdf8cf61981dc727682cd75110c", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.2.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 541956, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKLkX6r9UkGw2MU7cg1stPjZ4c0yxQ+CRhVrjsE9RLgQIhAO6IutqvQKibXCTmhPzeAVAISUpP14X+2ceZVpcmeJJ3"}], "size": 264893}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687727622021, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0-alpha.1": {"name": "@parcel/watcher-win32-x64", "version": "2.2.0-alpha.1", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-aT1tQibRw8HMPx+4ywe3ZbSwaGkcKpMfq/QO4CY7OL76+D4Y963VEgGhkC0ceZ8omYohzd3psOFPrVc8kyFQgg==", "shasum": "f85f10192d70fe8899a93b6dde63888899dc3947", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.2.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 541956, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD60Cz7VINvKw0WJXVr8sZhUDPfVLiI+2WAlp5od+kyXwIhAM0dSdXA1S0COdCnB9NqCpq5lzpUi2L18taV/i3rg3Z6"}], "size": 264894}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1687738119454, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.0": {"name": "@parcel/watcher-win32-x64", "version": "2.2.0", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-JqGW0RJ61BkKx+yYzIURt9s53P7xMVbv0uxYPzAXLBINGaFmkIKSuUPyBVfy8TMbvp93lvF4SPBNDzVRJfvgOw==", "shasum": "f2fcc255b7cadfd0afa7c51cde36413632075bcc", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.2.0.tgz", "fileCount": 4, "unpackedSize": 541948, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC9RIe9vosjveiOoZLRtc4zoVpng4/sa4Tt4UXsg/0AfAiBIFrJaoGvI+ZgTxEp//uzr4nlSwIK53ajo593BMZeUeg=="}], "size": 264842}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688269830159, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.2.1-alpha.0": {"name": "@parcel/watcher-win32-x64", "version": "2.2.1-alpha.0", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-C75GAgu3PzfiSkF9ex6Sdqq/zv5ZomJkPVH6/Wq0mADkL6BiFMd8uBT5F/iSPnkWVJjQnA976k/rWJZCLdyX8A==", "shasum": "5973fe20177edfa03345b2c450c6baa82c49bd2c", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.2.1-alpha.0.tgz", "fileCount": 4, "unpackedSize": 544516, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAb4xrAJEcY6KgwIbUgf/PwV7//96Qpx8fWeBtRaiW/tAiEAgx4adobXT2vcLmdDlmT3GacZWzbjGQLmf+woB/KMRdc="}], "size": 266009}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1688571417014, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.0": {"name": "@parcel/watcher-win32-x64", "version": "2.3.0-alpha.0", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-q7D9VJ8ZNRvOwV9COY2sKn8OnPa8fbrLUGzl08ohGt7rBaGKB2Y+BQqDcIoxP/GPnvt3ICUVTGSjpMu61aqAtA==", "shasum": "f3dad11f2cb0c99b3c67910eab2d51abb494ca3b", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.3.0-alpha.0.tgz", "fileCount": 4, "unpackedSize": 533252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICc5WJwWk1413ny44rHTO9/BtjSAXqeWs7ANbaBn9a76AiBStuJ4xHj/t6e+qCxpWL8cwSLwPXoc0tSxOLxdOsrplQ=="}], "size": 261940}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690750517267, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.1": {"name": "@parcel/watcher-win32-x64", "version": "2.3.0-alpha.1", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-AdSJzQyKG26F/uFtwi1icB2OitXh7h8Kken7zQ9tw2+VJ7I7Z0un/rTIjm9sJlSxVP5X4dzArZsYBSoqXDcMkA==", "shasum": "2fc3c0ee3fd8a9627a3a5d21738ae9e4629b85b0", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.3.0-alpha.1.tgz", "fileCount": 4, "unpackedSize": 533252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID3q3qJTyN/gfUqkPxEyUrXyi3qtX2tJW/vpF9fYiiX6AiBkkKoLg5gFNXVrI6H/Byy9n5DQTxUcWE5/i7easMXS3g=="}], "size": 261938}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1690753413048, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.2": {"name": "@parcel/watcher-win32-x64", "version": "2.3.0-alpha.2", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-JmFxok33xb2EkLPlSW9jtzkt19YrWKnzRmFJwT+t+xNUjbYHNCK5IpnsWncuksedNlkWbZ9nfrCJ1cwAzPjExQ==", "shasum": "96c55dc9a57280de8bfa69fb3b1fa8ee3071f50d", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.3.0-alpha.2.tgz", "fileCount": 4, "unpackedSize": 533252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFdJZWgNKWH7qKihqUzjPjumkCoBnuQo4+QuAs/EXrKeAiBInwyNrwP6mm+NwjrSdGLnGppoi9rAPxNixNbRTvFiXQ=="}], "size": 261939}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692485217017, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0-alpha.3": {"name": "@parcel/watcher-win32-x64", "version": "2.3.0-alpha.3", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-trYoxxgpy/3h73+AZg899JqOT2iqhBb5omQ0a7dgYJ0a/xs2DmmAdPQhlqUSpoQHTx566OdyN9ky+lxay6uJyQ==", "shasum": "83febdd8f6786e784add57049e046b9b982e82a9", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.3.0-alpha.3.tgz", "fileCount": 4, "unpackedSize": 533252, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7RZdYFc2wQeWT3XfwRWGRms2GNL5hfj6zuN6wVQXd4wIgWqANpvwLiDf3xijVaVyuNSx73S/UWWTS2BvJRIt57gE="}], "size": 261940}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692557615214, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.3.0": {"name": "@parcel/watcher-win32-x64", "version": "2.3.0", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-dLx+0XRdMnVI62kU3wbXvbIRhLck4aE28bIGKbRGS7BJNt54IIj9+c/Dkqb+7DJEbHUZAX1bwaoM8PqVlHJmCA==", "shasum": "14e7246289861acc589fd608de39fe5d8b4bb0a7", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.3.0.tgz", "fileCount": 4, "unpackedSize": 533244, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDf4NiKTT1uH+zWtqe0KTbqxxJx+z7NhV/q17fScrma6AiEA/Wb+S0JCesC6s7VCePzGdeY9E3QouK4uT2rAiE7DQV0="}], "size": 261926}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1692850823873, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.0": {"name": "@parcel/watcher-win32-x64", "version": "2.4.0", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-pAUyUVjfFjWaf/pShmJpJmNxZhbMvJASUpdes9jL6bTEJ+gDxPRSpXTIemNyNsb9AtbiGXs9XduP1reThmd+dA==", "shasum": "93e0bd0ad1bda2c9a688764b9b30b71dc5b72a71", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.4.0.tgz", "fileCount": 4, "unpackedSize": 510204, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/ycnPv9P5kE4VERaT6A7kdBc9OJGbbSnzFKl5watdSgIhAO7Hm3jSIx91IHA6BYJ8f7xcEtA3CLHihoAoeRCNP1qc"}], "size": 248221}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1705360260328, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.1": {"name": "@parcel/watcher-win32-x64", "version": "2.4.1", "os": ["win32"], "cpu": ["x64"], "directories": {}, "dist": {"integrity": "sha512-+DvS92F9ezicfswqrvIRM2njcYJbd5mb9CUgtrHCHmvn7pPPa+nMDRu1o1bYYz/l5IB2NVGNJWiH7h1E58IF2A==", "shasum": "4bf920912f67cae5f2d264f58df81abfea68dadf", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.4.1.tgz", "fileCount": 4, "unpackedSize": 514812, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEuJDJdP15voPmFs4lhR1B8OKPBoo62HQwzA0bfEZ5+BAiATr59cVr19Ue0zxcEwHCg+ucpIHFPMRn3pvppHWi5swQ=="}], "size": 249794}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "publish_time": 1708702697736, "_source_registry_name": "default", "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher-win32-x64", "version": "2.4.2-alpha.0", "directories": {}, "os": ["win32"], "cpu": ["x64"], "dist": {"integrity": "sha512-U2abMKF7JUiIxQkos19AvTLFcnl2Xn8yIW1kzu+7B0Lux4Gkuu/BUDBroaM1s6+hwgK63NOLq9itX2Y3GwUThg==", "shasum": "6f201f48438e8183b0d86d7d12f7d664676357d3", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.4.2-alpha.0.tgz", "fileCount": 4, "unpackedSize": 516356, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuG4EUXZPOX3jzJ146YYOn1zdhE4uo6FdXGILOrRqDtwIgWvFRPOptjw48DvPNflbGr/V9q9VKbkRZHWXUldzlpp4="}], "size": 257320}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1718259271199, "_source_registry_name": "default"}, "2.5.0": {"name": "@parcel/watcher-win32-x64", "version": "2.5.0", "directories": {}, "os": ["win32"], "cpu": ["x64"], "dist": {"integrity": "sha512-lPrxve92zEHdgeff3aiu4gDOIt4u7sJYha6wbdEZDCDUhtjTsOMiaJzG5lMY4GkWH8p0fMmO2Ppq5G5XXG+DQw==", "shasum": "33873876d0bbc588aacce38e90d1d7480ce81cb7", "tarball": "https://registry.npmmirror.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.0.tgz", "fileCount": 4, "unpackedSize": 516348, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHZ1mCMcDqvWe7/UiO2XmOEPzyZdNAza7hZ2tXzl3fvrAiAA+28r6wyEzdAtiwt/irGlsxLo93otUQUkO0RAq2Oudg=="}], "size": 257498}, "engines": {"node": ">= 10.0.0"}, "_hasShrinkwrap": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "publish_time": 1730701358913, "_source_registry_name": "default"}}, "_source_registry_name": "default"}