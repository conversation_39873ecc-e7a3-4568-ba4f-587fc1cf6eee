import request from '@/utils/request'

/**
 * 获取VIP用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise对象
 */
export function getVipList(params) {
  return request({
    url: '/api/vip/list',
    method: 'get',
    params
  })
}

/**
 * 获取VIP用户详情
 * @param {String} id - VIP用户ID
 * @returns {Promise} 返回Promise对象
 */
export function getVipDetail(id) {
  return request({
    url: `/api/vip/${id}`,
    method: 'get'
  })
}

/**
 * 添加VIP用户
 * @param {Object} data - VIP用户数据
 * @returns {Promise} 返回Promise对象
 */
export function addVip(data) {
  return request({
    url: '/api/vip',
    method: 'post',
    data
  })
}

/**
 * 更新VIP用户
 * @param {String} id - VIP用户ID
 * @param {Object} data - 更新的数据
 * @returns {Promise} 返回Promise对象
 */
export function updateVip(id, data) {
  return request({
    url: `/api/vip/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除VIP用户
 * @param {String} id - VIP用户ID
 * @returns {Promise} 返回Promise对象
 */
export function deleteVip(id) {
  return request({
    url: `/api/vip/${id}`,
    method: 'delete'
  })
}

/**
 * 获取充值流水列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise对象
 */
export function getRechargeRecords(params) {
  return request({
    url: '/api/vip/recharge/records',
    method: 'get',
    params
  })
}

/**
 * 添加充值记录
 * @param {Object} data - 充值记录数据
 * @returns {Promise} 返回Promise对象
 */
export function addRechargeRecord(data) {
  return request({
    url: '/api/vip/recharge',
    method: 'post',
    data
  })
}

/**
 * 更新充值记录
 * @param {String} id - 充值记录ID
 * @param {Object} data - 更新的数据
 * @returns {Promise} 返回Promise对象
 */
export function updateRechargeRecord(id, data) {
  return request({
    url: `/api/vip/recharge/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除充值记录
 * @param {String} id - 充值记录ID
 * @returns {Promise} 返回Promise对象
 */
export function deleteRechargeRecord(id) {
  return request({
    url: `/api/vip/recharge/${id}`,
    method: 'delete'
  })
}

/**
 * 导出VIP用户数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 返回Promise对象
 */
export function exportVipData(params) {
  return request({
    url: '/api/vip/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导出充值记录数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 返回Promise对象
 */
export function exportRechargeData(params) {
  return request({
    url: '/api/vip/recharge/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 