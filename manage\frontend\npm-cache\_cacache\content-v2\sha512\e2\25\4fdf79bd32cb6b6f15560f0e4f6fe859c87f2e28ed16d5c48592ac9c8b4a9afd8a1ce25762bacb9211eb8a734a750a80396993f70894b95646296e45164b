{"dist-tags": {"latest": "6.10.8"}, "modified": "2024-12-23T17:06:34.400Z", "name": "@codemirror/language", "versions": {"0.19.7": {"name": "@codemirror/language", "version": "0.19.7", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-pNNUtYWMIMG0lUSKyUXJr8U0rFiCKsKFXbA2Oj17PC+S1FY99hV0z1vcntW67ekAIZw9DMEUQnLsKBuIbAUX7Q==", "shasum": "9eef8e827692d93a701b18db9d46a42be34ecca6", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.7.tgz", "fileCount": 8, "unpackedSize": 115777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqMzcCRA9TVsSAnZWagAAwNUP/003oWQzpFgAm6mqZS2Y\ngPs6f4x2e+1HqZicCrANxqZmD4LyEYPXDH938V4Cblzkuu/IVGAzqoq8pigq\nWXVNDAcbefGYW6PmWsz9dz9pto9wGYasYdgZVodmiwng3sIvnSrHCbvuWtxr\nFqeuJWV1ify4JcKmQombg37CSZNo23HK7FNZdhAG8oEF5rW57WmJJhUDMiSu\n/dtStolKwGoHg1UMmEHXfCrIUrPQqsDv9qSx2TsV/6AaOT64Rl0LoxAv58We\nMCi9lVoG9yJAn1qONQ6iuWC3LPdbSzEDqm0kRpJdlpoZenEDq8s8THeZvX++\nmBCeYVTERIW7K4Ke1U5ZCS09e5UYHiOHcFhaq+ertaYyuaBbD8fWl5/fmG46\nBBGvFMj7HkSNE5sUclJYLOVG7h8CHfWYuL7Rba46ZAsU6soKbVxw+DEucvwn\nhoP9prC/NF9YO36AyCAo+CKRpEeBbpOmTyKYh4Q3HxupRmkQ/CrB9rZK1Jxu\nK+NcJ02upgzzUHXobp1gIoloxE/niisH6Wzo+/WgtanVOW34ljpa7kifcsEq\nBX9++t+Z2/1jZADK9hXMP3IWpWctmTLWLpDY177fM7Z5L9HA88r7VP30nlZu\nSjtFBYuXaILLj2eJL2JJmoYOM/QJutwKHAvrriByuysPw1L0XfwB2T0klq5u\nfkiw\r\n=zhZL\r\n-----END PGP SIGNATURE-----\r\n", "size": 31927, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.6": {"name": "@codemirror/language", "version": "0.19.6", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-or5TU/bxhsQdhXT70SK/h9V/gX/rElDEQyG29VbEDnHZ8TaDo3xsX900EYP02/tpIj8sConefmuMQfrePr+OCA==", "shasum": "d43cabe852ab974eed379d14a8ba68b3fb4414b0", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.6.tgz", "fileCount": 8, "unpackedSize": 115530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoK3tCRA9TVsSAnZWagAAhuwP/2aHI/+1+mD1oUckHxrW\nvWuG0oybvgGyKo5X71Qxj81ZdAgfvu0QI+0WTuuLxbLrL/OOvcjiQJYfTpb7\nX7sMsSfgxxdHOfCRe2DZAC+tl6nkEyDOec0Ws5ey5ZhiZ5C7bm/IIgbiGNt/\n7dSqsrttX3p6CYywUPraxEzNP7Y72nPyvweQ91ZuyyS7VBhhSkzXoG8M1gd7\nk8AgrRQt0NLlyOyx7h0NbV+qj+tmGmDyW+N/E8mnHHIELsakUqIttxxizjxy\n/yZTs5KGlDC6fEGrdpDQPoIrm9C6+50pU84N6jqNt1pVlBcA/oBI6vPmPYxv\nY0AB+9piaGgFYxXA9vh2TVTTlUJ8rBeIbruwqNSg0Dn7msKdrhyiRbjWQpNc\ny88oZ6Ll/UDky5/h4DDAPb60xZeyslHa0dl8u0WLZc+OI2CkLA5jtUmiRzXk\nqeGsVTklCQBL/WSowv2kGAHQml7ROsn5wgrFbxcTm2PZSS2Lpbm6mNjcX1k6\nWa1R62paffc79nO1VuWWAzDEdyUXndYpBz/Wm4c2hem5yAquRPuFkuQ6FMGt\nPq07Gl2uSrfeRIiI9v6hwh1x/O1zgiBU9n41mL02DKBzRjMcBfldU+fZDX8Z\nHxCm2Ko9Al3C2jpRNyxnodbtah9t8tkMcoSZ2TvVBkX4SSmfE7IE4vIdQklN\nEmWF\r\n=GHuR\r\n-----END PGP SIGNATURE-----\r\n", "size": 31728, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.5": {"name": "@codemirror/language", "version": "0.19.5", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-FnIST07vaM99mv1mJaMMLvxiHSDGgP3wdlcEZzmidndWdbxjrYYYnJzVUOEkeZJNGOfrtPRMF62UCyrTjQMR3g==", "shasum": "4d816ce3f72974ad443cbf1a18ff4fcd1e56f1d0", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.5.tgz", "fileCount": 8, "unpackedSize": 114614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlRCECRA9TVsSAnZWagAAHZIP/16f9v6oS8cBCfWdNWHE\nhyjt3AwlAS1TEce69jJl0HGdWMnxbkxCVjWIsTlH6viuph1WQUi9NNN2cQLd\nY7eafIog7/8j1E9/mLxt+TN5pSSJ9MRHgDZs4mYAy7ePsJ3AYrEDOUKmWCoL\nwwW5NvQ+NSVw0Unalct82W8ivQaVaWW4fAdKcyPBYKt+cuHp5lqKipyTm/PQ\nFkytpcndwfmxQVHUl8eksM9faPfVqC9HHZWHhOJ/88IpvsxLX8zTi2IgIeOD\nujMTBgmXMYwAVZ32KzM0RkOhE/l314pd1KhwGejsHgchtLs8XGqJHlorMob+\nrhxMcSZTnaAEJv0vJMdMdH3Dgi9CGv7k8EeyAeJ0BAZppoQnI9vuw5jZBgVE\niEOqaHCfpTBLNe4fyWbRrUPk2Kwk4TAnWBBjfSYzjsIlVU6Czx0VCPht/5mx\nwhoJJZzF1KSdvExnaFl2kkrqvHRVS3jNPxmKP05fWRAIjIOYSXVsI1XNnE1r\nGM7DZMbqBFQW/s8DWm8qHiN88skuq0M5ip9f289iqaQlTkMpNbs5wryFA1XC\nAOMNcRB8HH8CMNbltUMk/lfEHintDkoGErEZQSMMB2K0py/AytFjyVrMNhf6\nz+81jnBbLEj9p7OdoWXS8pL8KaESsh0+va1wXPsFt6wkIINJnBAyV1nzcWzL\nunU9\r\n=40WR\r\n-----END PGP SIGNATURE-----\r\n", "size": 31375, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.4": {"name": "@codemirror/language", "version": "0.19.4", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-yLnLDUkK00BlRVXpPkoJMYEssYKuRLOmK+DdJJ8zOOD4D62T7bSQ05NPyWzWr3PQX1k7sxGICGKR7INzfv9Snw==", "shasum": "14e54003ae822bf51a298a7cc6e4e7d51a21e8ff", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.4.tgz", "fileCount": 8, "unpackedSize": 110328, "size": 29772, "noattachment": false}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.3": {"name": "@codemirror/language", "version": "0.19.3", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"shasum": "e4f61555dec0787f757b78348a54a00f3bb23c9c", "size": 29370, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.3.tgz", "integrity": "sha512-6vjkRYHRJg/z9wdAk75nU2fQwCJBsh2HpkIjKXIHfzISSgLt5qSDxVhPd8Uu8PD5WMmFFP8tX7I9kdIt873o0A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.2": {"name": "@codemirror/language", "version": "0.19.2", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"shasum": "01d03d746abc82aa3a8aaf5dcdfeab4b918b35fe", "size": 29580, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.2.tgz", "integrity": "sha512-Zz/gVezTknHhH2HcaMsYauxz06+3+IFU0nhhZmdTMcGbDL/ryQI2R543k9Zc58wA31lGa6uN+JzpZynEcGBwMQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.1": {"name": "@codemirror/language", "version": "0.19.1", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.0", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"shasum": "3695c8c7d650a4b9b7a2b5bbe8f56dc8c1d62954", "size": 29457, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.1.tgz", "integrity": "sha512-xukssIvUo/xa4QC++pdludp+ggjFiWoBf3WkTcB6l+vd/3Xlp36k3AjKq5L/czTtV/KPl47HkRlsC37xnVvFUg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.0": {"name": "@codemirror/language", "version": "0.19.0", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.14.0", "@lezer/lr": "^0.14.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.14.0"}, "directories": {}, "dist": {"shasum": "aaf2448d687d320925927b328fe7b29502053672", "size": 29430, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.0.tgz", "integrity": "sha512-HluCXKY8GTZ1LiomWtEjo66Uq8xWr/jxqc07U0V8lwFK7qbBnatJllcwsZi1pomAIkWz/BYoBkEOd/Xf/rZIGw=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.2": {"name": "@codemirror/language", "version": "0.18.2", "dependencies": {"@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer": "^0.13.4", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "lezer-javascript": "^0.13.0"}, "directories": {}, "dist": {"shasum": "92b0e2ba0debfa8473d805efdabda5c24376cb16", "size": 28024, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.18.2.tgz", "integrity": "sha512-2Kz0Xyfvt1Ex2KfTUcYZ3IBxpnFCqHaJijwZknGBT7JXv9dwbOPs9SfPfL4oxVuDIHZx8JTPfoV3LTTJrm8M3Q=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.1": {"name": "@codemirror/language", "version": "0.18.1", "dependencies": {"@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer": "^0.13.4", "lezer-tree": "^0.13.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "23682324228606c4ae5b6a9f7cd0a4b9fdff83dd", "size": 28094, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.18.1.tgz", "integrity": "sha512-j/TWV8sNmzU79kk/hPLb9NqSPoH59850kQSpgY11LxOEBlKVRKgaWabgNtUCSeVCAnfisGekupk3aq2ftILqug=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.18.0": {"name": "@codemirror/language", "version": "0.18.0", "dependencies": {"@codemirror/state": "^0.18.0", "@codemirror/text": "^0.18.0", "@codemirror/view": "^0.18.0", "lezer": "^0.13.4", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "16c3beaf372d0ecfcb76d708a8f55efccaa25563", "size": 40441, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.18.0.tgz", "integrity": "sha512-gryu0Sej1vG3S3njwsJ+bhz9zLoJxZ2TahLlxpqOB3uqVGZrGDyE+GmZBnA6I3hwHvaO1O4WXKScVsKoW6HqFA=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.5": {"name": "@codemirror/language", "version": "0.17.5", "dependencies": {"@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer": "^0.13.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "77b551680f0bb8a6e40de7659e518de1e0c637a0", "size": 39749, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.17.5.tgz", "integrity": "sha512-eFpWMv4anbinagEziYUO62mNKUFBPXeJ96HRuxYQI3yt4mJIVjzS7FkB/4VHqOcvfJYqYI3TURyu0T8MM4se2A=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.4": {"name": "@codemirror/language", "version": "0.17.4", "dependencies": {"@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer": "^0.13.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "c5755385cb0c2765e8d959e40491ea9a9a30d703", "size": 39250, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.17.4.tgz", "integrity": "sha512-PAYrnmc1GaUcWjnxydbrhrMUYl/biIv4/fCJdInwHdox7f3WOdOhYwST/B803xVBzlse8spJo2M1+syMxJPM5w=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.3": {"name": "@codemirror/language", "version": "0.17.3", "dependencies": {"@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer": "^0.13.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "3d6001e160f7ccaf28e3d35fdc9edf7d4ac6b2df", "size": 39247, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.17.3.tgz", "integrity": "sha512-Nhrxa06vAabgngPRf3qEPL9OUauOtUtHySf+xqpTbaA3z3VoN2gTlKXEMKGFLxZ2kzT6nLthF8OOtO3nOAhYzQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.2": {"name": "@codemirror/language", "version": "0.17.2", "dependencies": {"@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer": "^0.13.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "564d176faefa5302be834a5a8f5b1044a95d8048", "size": 38641, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.17.2.tgz", "integrity": "sha512-mcAj5qDhv7GE9jpAwUB0ewfRPj0LuoUv4gMRnCraxpKeMUF0BAxr2zXCWt7JKEXYLmueJ7QEjMk8vRaArOXxTQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.1": {"name": "@codemirror/language", "version": "0.17.1", "dependencies": {"@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer": "^0.13.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "fe9524dc8591b1850fd621791dacf05ac86338de", "size": 28800, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.17.1.tgz", "integrity": "sha512-qJJWoucPxr2hUEQRHG+Vr1+kE+duvLup6lUb3ITQDyeUYqApPgR9bAb0ORkNnUPIaXyek6YSnK3Vd3AzxR+8Fg=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.17.0": {"name": "@codemirror/language", "version": "0.17.0", "dependencies": {"@codemirror/state": "^0.17.0", "@codemirror/text": "^0.17.0", "@codemirror/view": "^0.17.0", "lezer": "^0.13.0", "lezer-tree": "^0.13.0"}, "devDependencies": {"rollup": "^2.35.1", "rollup-plugin-dts": "^2.0.1", "typescript": "^4.1.3", "@types/mocha": "^5.2.0", "ist": "^1.1.6", "lezer-javascript": "^0.13.0", "mocha": "^7.1.1"}, "directories": {}, "dist": {"shasum": "b4374d65a60d8e93afd6aba3786ef7f1f15ead26", "size": 28356, "noattachment": false, "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.17.0.tgz", "integrity": "sha512-mVRUHm7VdNuYGpDR9ozrQr3CUonesh3UbJVMYQso5250lsOJ68h6xumGdHucnyUBXaZAqtEk12h9eazihBNVbQ=="}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.8": {"name": "@codemirror/language", "version": "0.19.8", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-KhRne8qmzSKkaw+qhkwgNsPKxmThlyeJ3umfc33B9kJzVP7xhTkwX2MEPl0almM3brxMi+lPYx7gCPOy1gHsWw==", "shasum": "fddaa76affb44d71e84dfde7308058732b88c064", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.8.tgz", "fileCount": 8, "unpackedSize": 116204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIG9cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRkg/+MQcfvUTyI0QAqvPYex2gt6HdvEbhKVgahA0P51ezefMs5icV\r\nRTq8+vbM8kKZZK/D0T/eaLGV5VoXHxfqfguquIDevcEf7+p37e5P3KF0QnQI\r\nlg9SeEzZuzZ5RI7AgR4BSiXxKBoCnZ68MzbhYysl88VDBTOxjc+IssuSHCiR\r\nmGPIQ8RyYMhZwF8y4SvbDwrBo3EZXPEIvYFgoUzuy9iut3qGrVaR8k0Jy5RS\r\n4sAyQtb94KMBz3M/RYJdQQg0ywkv8ERGfcat5C9h/3PVDswP8XUbx5LwnYXK\r\nOA6CWc5aoXE2YrXYujI7gD536lK5IRjSnhjsmypmv+0Wl6EJkO3DnxBShH6t\r\nRPW1qFfjnONlHPtwfTsrz4e4s59y4R3YNNSO4WgqEXsoH31QqDgNW6gLXw9F\r\nxASh2U/LB8vnlPBI5mtD1d2FHJcfCfoKvKXE5gfnkkEgXkZQA8xyYb5t+5oR\r\nO2IXnacIJ7vCa+N8Vnts3rzCXRGRJ+SjqOJB339/ycULPr4d03W8yT5TOAtb\r\nAw9Cae0MrpBcVvnFCoHRjNF1Qccs0UEJYrA4X3mNnbUl37QjUiWVbxQMQGod\r\n3qm3AvYmZ2DH3sYpJhj0Rm2pP5K4gUpqp7gtawOXSE6aUNIWjktu5B58BfiR\r\nBszqNX7bxaYQ0erRe0R5PO+K3/ebjHNngtw=\r\n=+JoX\r\n-----END PGP SIGNATURE-----\r\n", "size": 31999}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.9": {"name": "@codemirror/language", "version": "0.19.9", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-Rqh7sJduwNVWoLXbOx1nf1vh0zXSIMJ3XDSyISuiIDaOo6Ps7aXgO5BCn+oa3/1RIroEu6vo4PP/zP6B7NSRWg==", "shasum": "9025b88933a05ebb27f16db6f9297b7c069109f0", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.9.tgz", "fileCount": 8, "unpackedSize": 117791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQ/+vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpykA/+OJYFEkvVGYKedZ2RarBffVraoPOvf7y//U2tOqQIZ43uxY6z\r\nsXYOyr/PkpG5j53CoOhZJINoiLLcDWw7yPoqOKcmbB3P4sqIzfQMKZWNrwW5\r\nxm4Y+pKKXpuvZKEhnOSfH5OTydFOF7lWNtBsI0uJXZs6TLpvYUxWTOThZ8x8\r\nU4y9QTT6sd/OG/t0enPWl7Httcd+Okwmoek0t6XtuZ6gTKcE5EnFiXEfepID\r\nJnlpFN5o9Oaka/yABABigzrei6FYl2nQYl1aDlKzCASQ7RivzNedIG730ypj\r\nt4azKzkEN2nokEN4MYsZKVsccglXf30yF9/FHWeuHBrtr3HuhUQjezlujdQ7\r\nVLwz7YZgZMNCHmmN2Oi7Ca+y94hWD6Vb486ETO+Z5dB39HZL09Bd0MnrCKjs\r\n+r7VzrsPELvdnWw+sikvVtKDvY2REO/MH3zbzrDvVjnMc1Wx3WSiBXIbqTcW\r\nl792/buSeoO9U/fRDDLhhc+mzxPn+MI0jve1HPy0lB8/uEQHqU3qwBgHRHYL\r\nbbRs3rOPe1Hs1W0hC2HWvUGKsQp/sKXfkXL7JbDy+OXgPGh8WXmZGpcsUkdx\r\nGCcS21iyS5MhEFeYmCZmVylxGEeGcpdpX3VUBLcSaDvDb2C0QGJU5k89sSI6\r\nzqGgLfQa+x0Bes9sMLy0RI9uO+aWCsbxsm8=\r\n=O0Ac\r\n-----END PGP SIGNATURE-----\r\n", "size": 32482}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.19.10": {"name": "@codemirror/language", "version": "0.19.10", "dependencies": {"@codemirror/state": "^0.19.0", "@codemirror/text": "^0.19.0", "@codemirror/view": "^0.19.0", "@lezer/common": "^0.15.5", "@lezer/lr": "^0.15.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.15.0"}, "directories": {}, "dist": {"integrity": "sha512-yA0DZ3RYn2CqAAGW62VrU8c4YxscMQn45y/I9sjBlqB1e2OTQLg4CCkMBuMSLXk4xaqjlsgazeOQWaJQOKfV8Q==", "shasum": "c3d1330fa5de778c6b6b5177af5572a3d9d596b5", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.19.10.tgz", "fileCount": 8, "unpackedSize": 117975, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdWzRAzZyY9Qf0e9jHP2H2tELi5XaJBhYWZf2vhTdnBQIgAocaUMy6jno2Rk/SFJnwvB/hDCW5h+gJhKhs130CAyM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRciFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos+xAAozvIOWleK9rp8z6QFZ1Pvw3yv11HPAeY9IJeV+FB+Gg4yjMr\r\ngyLVNU0vZs4131KoFviYxkcWmQxjypIQJR5wUlYSPLCa2NUdnnqdPz3EKvDV\r\nhgH0AR2GMVnAe5L6X7B7yFUWZE5mLFKzLPORX9yu1cWYFyA7R5GzP/+bJiBx\r\n/A8LGsJLziyY2PSzUWOlc30zvEpNa4ni04gByHfzKHk1E26DKgBygZolGhXx\r\nmj7P/AOj9W2RwFlARhLHYlo+MZDWgvWh69XBjVy8EhhlyOV0D/Pir2+ziGTQ\r\nevj/SDsHzDYsCSfE286li1zM4pQKl15UtBqewvndWj5zOQIvMe6HEXDD4Ytn\r\nyjtEGiX7MJayqFbL/02OwZZvM+BRxijQGLIunO3aEKc1ExdLQMsZ33zvjNQd\r\nwej0PRvg8vI3mxPqqo6OyBtbs0fRzgXuOw1pv1/bR0hxowl68tYjXjP8g/JE\r\n8rRcMKukeFKkYE382WHEoRIg3ldBig1M0lG8q5Lf8VT1ZQk9qxrVEd8JWpNs\r\nMRao5CUJBCJ+4VjNXHZRUyS357D7YnCHHcv58wzNI0g81EW5mmq0dsjzF2tZ\r\ncVPr2urQgJpSNI6tTa/UI/Il2qjiHkAcfkquqYQP9wBCIICwf3FDMy7UWc9j\r\nB5DSNCmkJJ0ivsfpLFvJhkNqliqdjUVTYY8=\r\n=p+ZW\r\n-----END PGP SIGNATURE-----\r\n", "size": 32526}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.0": {"name": "@codemirror/language", "version": "0.20.0", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0", "@lezer/highlight": "^0.16.0", "@lezer/lr": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.16.0"}, "directories": {}, "dist": {"integrity": "sha512-lPsF5Y2ZFd5lZ9+7HXTxu57Po3dms3+7q2iAffzrbis2wyJo0lzi/j2312EKStEzwd0pGGpvrUk2dEd333N2jw==", "shasum": "959967901e780c1612934cf5d6df3d7b7e5bf9f0", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.20.0.tgz", "fileCount": 8, "unpackedSize": 221320, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLbqPk/8/rFAYR1iMPeTwqpZNa+85mS+oyumdv0NPxRwIhANUfmGeEkvve9c41dzEijVQzVakwnkM6dWQbSkGw25xD"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYBUyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjfBAAhXDrIUDYhbBuSkEwWcQxLSV4dN4oJl2fb2ax4UTIISEpnMIp\r\nsY6aqbpKrE1tK0srmHbigrsv9Vp8ri0UCDtwkPFwfrnzTwhv9j7fH2ccryIC\r\nTHj4YtZ012Hv36iRpn3ok/G3ReHek8n9UbIzHCMX+WqdP91pMcZeE9Pz8Q46\r\nXlZd6pM2TZm0dvw4V2eP6f+pGWE4QMaUw/cLPuaXMS1e+o/Kh/13ffW82zVT\r\nMqCPW7UP89gI+xPkDlRnjQQJNvnGknuURsz0qwenA3v/2imasHTLFVNm6+xU\r\n1jh0cYmu4VT1K+5WfJJYZcOunDAgd++nWRGAiTTPhQMgjEevAz2V74wVq+PZ\r\nWYL8xl0tJBmxDCooHdv4K2Dh0BAbPSLfMHAEn2nh/rtZ6zfRehfYKO/LdDB0\r\ncGhB0UeOsimzwq0kiX6/2SGjGWVd2G9XQOcaaWuWfglK4fiMr7fgynKLbU6B\r\nSVz1YvGQ6J1+Ek3aMs/xt0zjaMh+UgIb8hMjHBXQnxrlzakADpV1J7vE/hTb\r\nCIhnkM3FPV3B8m/1jmJIYb+uWEVOOcAYlspOQC1gpLotHUnwaYEB0WxLjX/I\r\nGbt4myi/Uq+ShMsBU4MXpyNscoF4ShVzquCUU34xYc5rqTvoROO48F9jDyPv\r\nu8tpq5M0ey06VFQrK7kAqzoLEF7e/OlVP8k=\r\n=9YJG\r\n-----END PGP SIGNATURE-----\r\n", "size": 59339}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.1": {"name": "@codemirror/language", "version": "0.20.1", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0", "@lezer/highlight": "^0.16.0", "@lezer/lr": "^0.16.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.16.0"}, "directories": {}, "dist": {"integrity": "sha512-AqaFeI3hbsuxU0IpVrR8dhHkrEAAEuVcwEO66JwAbJaE7IPOov+Y38KWqcQNgE1/osQGKHx9Zag0hng9V6OLsw==", "shasum": "27b41f9a5234455af083cbc90db531ef296899e7", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.20.1.tgz", "fileCount": 8, "unpackedSize": 222248, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjRlSnrtvIsEYxcYin1IPDbKzJc814EhU0WS8fm/pslQIhAO9iplovJcN9PvQ5CjN4DJ31M2wcHkn/sEdyESHhr7rJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihLAHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZUxAAk5AR8k6LAgEMmkhMDMuK+g1/6vjB7/wa+NE3PF8Z6laxKXbi\r\nglIbekW0ogLDcOSv6wtFS+mvqrBx95mF+IB+zEI3qqpra9pZjK68TKKf49oJ\r\n4/6+biP27VllU0chpwauqE+HzJ7QXH3PB8fHF9kHpI56SDFBfoUAZZIi3z2U\r\nAqdcYmEWiiNOn9y88nyaUOjgUWOhBMcDHKAC555V9lfRjPGrl27fP4llV4Hp\r\nevUTX8/YFQRANz05pAtHbepH0Jb7JyLgRIAj3SLoN+Ug5zILKzvLUDSETIlC\r\njzrRvSI2LM1vYilYhfwZM9EN/GIS5euHwTtSDGmjjRZy27z2mlAc7bujY8Uh\r\n6l6jbVY4rT6/7eX/Jls4I3SmCDUx/waaxWQBhIS2iDvMWKP9yv2cClChP5u2\r\n2Z/XDhX+yjYGn+KVV+dEKToGs8XPuIcL6NYcbueqHVlDB9+eIbKfwfhNyWuO\r\nSAV9hhKkjrSPd33PdPd9w5QmAYU5GkR9+FLLtNS4k19FxlFZbjPoSXDOdn30\r\nVTGTAF+nbH3oITCd5fSpU366W29ziQC1Mk5gMA46NFJztwpXPBDHMYUOUvr/\r\nMctN+MbI6CWUy1GUA5NLh6dfFh7wVycJ1hpVHkha3PW4MbyRtBR3v/Vw9ZhD\r\njttShY391XoQj/hghI0JE0SbSl5FI6JRIwY=\r\n=N427\r\n-----END PGP SIGNATURE-----\r\n", "size": 59512}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "0.20.2": {"name": "@codemirror/language", "version": "0.20.2", "dependencies": {"@codemirror/state": "^0.20.0", "@codemirror/view": "^0.20.0", "@lezer/common": "^0.16.0", "@lezer/highlight": "^0.16.0", "@lezer/lr": "^0.16.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^0.16.0"}, "directories": {}, "dist": {"integrity": "sha512-WB3Bnuusw0xhVvhBocieYKwJm04SOk5bPoOEYksVHKHcGHFOaYaw+eZVxR4gIqMMcGzOIUil0FsCmFk8yrhHpw==", "shasum": "31c3712eac2251810986272dcd6a50510e0c1529", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-0.20.2.tgz", "fileCount": 8, "unpackedSize": 222347, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDkjvpaWHHjpBe+2t61xEnQWVF8gQO6lOAHTL4OLEwGAiEAv3jmJsBJkh0cxhJYsVCjmrszgXKvRm4dQK+LZS/3zPI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihz+7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc8w//dlIjD832zWcc9FJdUkSvLUrhWdFestSqXuFBnJkfmGV9tcsH\r\n4QrWTfriR17R2BBgRuRnxugLz2G+p0cA99O8KEpM/Uo1NRH+/fout0xJOv/6\r\nUle2coBdrjGYJrSVqn/eoribecpjQ9jwjdNPyeuTsI/r1Kq1zsmgS/VyYFPo\r\n/rnpXmm7CciueSNto1osVPQZ28rHWvDijh/IUE6vNNbw0e0CDALR1qoM9SEC\r\nkt/DUeOexLxdVsdeINvF/0vnAsdT95n4DPTb9iKzvFShwUDUaXSJYM6SS4Jz\r\n6ZHgKJgGD9/K1QEZ3C/5BHO7fdHV0/JiXtE3ukEduW82IShnjGq6IUTjFWwR\r\nu2yGC1X5XX5S/MbcjKyxvLF2dfQ3Uj+x4/bMnjIwwRR6fUznRoIVsqLUgMly\r\nnqw0O01ZQYYA5OaZWrRxNIb6siZWyRjofiS1Y2W2b2Ydjk7IJ+HZQWM4ojAl\r\nfFYC7KqYVh1gHRI4PHpJJCcp36U83Y2nGrimO5dmLZRD5ZlPvrUyHZPhZkIT\r\noET4QO/Ok4MJoRwjrlWVwG5lMPdBNXPEQ64Sy7eLu0pIMMt9IVBnrH7gkYYs\r\nH3mK4JrZ5UYtp6Yfqar4L7Bx7W5egJNG63MOEba9mEMr79ZXnME1rLJKueaj\r\nTwYMDcmCjhpiFaCiOwO1HpN+k/33Pi27sdo=\r\n=IEPq\r\n-----END PGP SIGNATURE-----\r\n", "size": 59535}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.0.0": {"name": "@codemirror/language", "version": "6.0.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-rtjk5ifyMzOna1c7PBu7J1VCt0PvA5wy3o8eMVnxMKb7z8KA7JFecvD04dSn14vj/bBaAbqRsGed5OjtofEnLA==", "shasum": "f590558447c01f430fb3ef3297c41b8cd3ae9190", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.0.0.tgz", "fileCount": 8, "unpackedSize": 222835, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDz+3/jzYFg6Tq3MazOFjPvE7aymmIZssXyKHqMLQpz8AiEA3NNRFza/MODCVlHmD2B3NbivBwN+isiWsLXkqphILLE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioErPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjwBAAnwfpE54Wjz+JX3wREkhB3o6Oc+OGAR0nO+Xp+8T6mMLzwUbo\r\noMc82Sj7Co7/M+xWQ5T+D6OI5nlmK6+xedS8x2vfR/hv/sNIUi35vsqxBibV\r\nBQyN61TrxLslkSFDJiRDYLB3l6I3C3nZ4IykR1MYjQlDK2j0l/g5uFWZrZNg\r\nerIXBNfUNpTbhQRERLf00TKobPbkTvgW215C89cUPrEWda7zRZCQ1BbMEbvb\r\nU7Ygh6XXo6ySSeOCQPqtmDfwTFeElvXkiT1Yn3bLgW74nhxqn0XWfkfa3Qa8\r\nCwV8Z2lxvuFOAF/znvY/YrreLWe6ZObAzpSMQKO+uShfL5Do4tbcSxJTNDXC\r\no+nNo7281pu+hQio59mrodldkPzzFxPujeNxIXu/2CUjZjoWwctvOq6n45La\r\ngNf149VRqSz01Cl2qpmFDChrD0I14MXuh1M1iD2Tp46qnAsU+4utT0As4sl7\r\nFv32TxQe3SlpH91/NJiEBUVefjrdRW99ALXvF126RkdtdmyOPhmECjr47Pzc\r\nJIWQEYcV/VXTXdlO+wHYgGDoAlra/c39VZydrMI2+sZwEJTyuieYzcLplo6d\r\nudBlEBZHW6KRwzPdmp6jNMJOAY4FRWbQ/1gVOIjH/xlwC/QA7nZMdIiE29jX\r\nWw9jFuOZlasc6JWqIyKIuPx6BsZG9UdHtEQ=\r\n=3b2v\r\n-----END PGP SIGNATURE-----\r\n", "size": 59705}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.1.0": {"name": "@codemirror/language", "version": "6.1.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-CeqY80nvUFrJcXcBW115aNi06D0PS8NSW6nuJRSwbrYFkE0SfJnPfyLGrcM90AV95lqg5+4xUi99BCmzNaPGJg==", "shasum": "7686f0ecafd958c35332c3cc2aa3d564fd33dc44", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.1.0.tgz", "fileCount": 8, "unpackedSize": 225953, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDvKzgsag8w4yVHts5/XSozthhfwDRp6ZZfnNrNqe67JAiEAvJP4jjwsYKOkr21zczZ8iDpIqhXPkoaUbLI0RiBxGlg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisLGYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKsQ//WugwBWZ2Sb9e40eJUaTubZtNRLJgbXgQZD2YG1yHO62t1D8w\r\nb9b3KUVOgRa6p5QjsdCuoubgX4TrD2Ihq38G/2PAn+0viCejfS2GhZfP2YZq\r\nqrOkJub4U+8EpYqvIHo+K3K+av9AjwpxmhTXEtJLVH6UD6jTdLb3k+GyVwBh\r\nm99yh41IWO2jFNR0Zt5w/FO0UpYIoUFyxQHqxz4/kJH8j3yrXUJbShziEEV4\r\nmoblXd8DiNtGZpZ4R6RqSgfnbriMnXXF3diXUISS+v+hknR6vp8ixdoJaPvh\r\nM/g62FROWP3GOsu8XmuVSVpxvEKeJxOU5yATy5V6KL8Bod2HnPdrEXfPFVUx\r\njmS7ex5db6yDsFxICt2Q0f/aGOr2UPmVOht2zvLKFZzdpB5QJMDSp9x1/kMB\r\nxqzDZKGk9VP2AhrxQ1hPzQuP1OT3neAjwqrMAcKXAFYZBH3Hk0Dt89vr6sQd\r\nULaLUoRK4TxQjUvL+1J6uIdR33if6698zieF4q6wGclHYRwHUfw8OGWeRbLT\r\nSVQ6ZNROJlB3Deuf0w4Mx7TFs/Ola6z61xzCC6QFrZpJMyLoZ+zXjnh1uqqW\r\nQh4IhWkNKj3hiJocCOweVaXOb99ncQMljkSbcyuJ1MWvlgVqHtwepTfyJEOU\r\n5mLzwNUhfW1iUOYrV+a7f6gbHean9cEK2Lo=\r\n=dMU8\r\n-----END PGP SIGNATURE-----\r\n", "size": 60620}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.0": {"name": "@codemirror/language", "version": "6.2.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-tabB0Ef/BflwoEmTB4a//WZ9P90UQyne9qWB9YFsmeS4bnEqSys7UpGk/da1URMXhyfuzWCwp+AQNMhvu8SfnA==", "shasum": "f8d103927bb61346e93781b1ca7d3f4ac3c9280b", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.2.0.tgz", "fileCount": 8, "unpackedSize": 228347, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHksUJYJaFi2sRhed5+GwjYiH06RIpg0p0fOSDrODiMNAiBck+XBQhESsjCCKE5800fNU8938CawgnG34Fbui04IUQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivVtTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRjg//ZBPuZ1jL63KKpm8KSCSiyuaHYOkDMi5ER6eDS2C16sixg6pm\r\nhBuMWNMTIy6oICit721yLo9SLvvj+OMGhcfKz5EL9tMGEPjVCrgHjq84QDKj\r\ngp87bEJJRURlHpiJUvM9Pntbm6Kj8EPkSdtotzVAV2heH9F1D334eTc+7D5A\r\n6Kn9VC/7KKgSh7ufn9m+zF1me+QEm6b/NIAojud3Tz9SptODMo7BSLiGKeus\r\nQxSa1OABnm/HOFNN9f1SfWMuWR9d7Z29N9ePYCaIUkBcctqZhaLENsEQpHBj\r\n3g8tZ+32UAPS1RBqvKvOVPTor9eU4NHiDLT7bQnUrkiTkXMeNZndybXdwn1I\r\nZ2/qv5OwF1HLfkSjQFHjGdFYvQeBcJ7bW+P5eStaYWQN3txyUr4fOYxSlC5M\r\ngTkcY1b1LDm0RxLTpYsMsLQOPyA2iFYqQTCoUAQhVM3h5LoWxuBLTt4pe849\r\nONKd6gKihEaLn1ygaoInyMB/Tc/qZvYuF1Gd0eiU4sW6fW1gtZKIVvTstFuM\r\nXGXbsdtG/4ixJb8cVb9SgxaEVff8toGr1LYInJdJVCQqz/duZiYNWhlxXeVO\r\nnenu2mFaovUM5EXUOHyOUfZc7rgNoenh36K5zZYKpNyiZRIQJujzuJHppkac\r\nefGdFJTH+djj8V5tbsMr+gze3v+xWb+M1IM=\r\n=Vo8m\r\n-----END PGP SIGNATURE-----\r\n", "size": 61110}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.2.1": {"name": "@codemirror/language", "version": "6.2.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-MC3svxuvIj0MRpFlGHxLS6vPyIdbTr2KKPEW46kCoCXw2ktb4NTkpkPBI/lSP/FoNXLCBJ0mrnUi1OoZxtpW1Q==", "shasum": "cb10cd785a76e50ecd2fe2dc59ff66af8a41b87a", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.2.1.tgz", "fileCount": 8, "unpackedSize": 228599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFSCbrbRqxFWr/llwiMQJO/rEt4LaWtJV5Iooiwy3YhNAiBkoZfH3PMXiFYxZdkoGdWlDdiRPnuLfcTGO543zGer9A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2RhmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqrfg//T4qwONqeJTBOLp0Xpqqu8pturA0F0RzX06zXxBifBZPJgQVV\r\nYJQxYWg6HfW9bCLGtQZcmAfAm/6KsEZLiAWksj0afIZVQs+fMmUfP1NJO0Ar\r\nhEkQrvvbh3z9r/JvMTbFjba1HqWTMKjgtCd1+9W3ASikk+TODKZuIxZqPfKV\r\nhKsjasvFnl032ZozgCXAKlm/x118NC1Jj4VP/QKXoDEaFR7we2MNumUUkYjO\r\nqHHJQPG9nRyWbx33vpczgZMljqWj9uogJ8O5pppoHDN9kjTpid+as//zRfD1\r\nGZEO/RYlZl0UQ7WGLiz9+QN4G/edljIqGb+xobGwlCz4gYQdsAyDNKaDWlhx\r\nJGCCuN9rwOG56IznIcY9kLtN2W+eOen5kQ6ycmAJjpaxHLAerRKrdLQ3FBRU\r\nPrSLSaq3Y7SeOAea6pscUCVBjM8JvKte62a3BHs3e1iMwsuZPeU0BdRkyfVN\r\nEKDDkHuG0DqwWhuL/q1IVSBSADxFQvRY6G7N4nTaaQadsMHTvq1dmzkVRDu6\r\n/E4qMrUzwkMhDbazEulWIE4S01KSbcX0U5/ljvBIcLZowd2r1q4vyPKCgZTo\r\n1NxmLn8wS598zHaDYuX6rKVexNc84dCCnDm1GHmJ0gNkDm3hyUnaX4ROy4/L\r\nIKHjlg0jvDX7gwn+vTf0aqAVGTectGkUPLM=\r\n=LNxv\r\n-----END PGP SIGNATURE-----\r\n", "size": 61198}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.0": {"name": "@codemirror/language", "version": "6.3.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-6jOE5<PERSON>t6sKD46SXhn3xPbBehn+l48ACcA6Uxs2k+E2YNH9XGF5WdGMTYr2DlggfK4h0QZBK6zEb5S7lkTriWA==", "shasum": "141c715e1fce5f6dcca3b1b984ed8f03f583dd5c", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.3.0.tgz", "fileCount": 8, "unpackedSize": 231163, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHWKgdoKTRPVbETfX97yn89NhyhoPFMNzCPTgb7qjHp3AiEA+BneGJGPhfS/aYkdVWgWbq4mGMM31t6y/nHyujajAAI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVny8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7bBAAi35v8n9lEkZhAfGVavnkrrM2k5YeZ/703jM6CQt7cNt74wp9\r\n75OXb135zuyy85VIm5SGP4qrZd+SCtlz6P3bV5BhINn2iAgsNPjh9rcbewP6\r\nwtj5Dtq29Jfk0u1GWO6F/3QtI9gT3hzrX9+9UcDFtqOb85YTAmmtpUt2h0LR\r\n9iYwCyWse52dVa1zdjqZjrt8WfExLYwknb5yJL8Nt36NtRO8CZOxhZSJnXL1\r\nlBzpZRCz4h7zLJGe/HngmzNTwzsIeVVWcN0XdgpMsa4THVaM3Sdb2sc/9WMU\r\nXsFYEoBagFpdBMJ8FK3gc2fLQaR4tnG6w6g9g1x+eF6sl/ONvTaA3guh0tz7\r\nPp6Lap6f6QzjJGJBjLgIQ0o6TPXhhtegC0z//UP/KkkgMDkZdECTqLuXGXDR\r\nobkVQaEmPFo7oFM0XKwLi+skcVLtCMVEIbWVzpCSNdaueA+hCelIHQ0QopFG\r\nA3vyk6PGiYAblsIduIbk6w/KJBx65odcA7fVpZBG8sQAQtvaAgTY9y+ilWFI\r\nyGXB7ZWq5C97BtrwRo7y9TKRO6gdwicbIpPC9DgCgrdJvNILHfUdPzyevgq+\r\n+e/TLvFlkPLvIUrvg0NnSlZtmT7+8qDDLpTNpTKLCsOkWAk9OoUq+AtF13Fe\r\n2SADDJ1TVn6DC2TOfUdsXtBQJG8p2K90oLs=\r\n=Eddc\r\n-----END PGP SIGNATURE-----\r\n", "size": 61755}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.1": {"name": "@codemirror/language", "version": "6.3.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-MK+G1QKaGfSEUg9YEFaBkMBI6j1ge4VMBPZv9fDYotw7w695c42x5Ba1mmwBkesYnzYFBfte6Hh9TDcKa6xORQ==", "shasum": "1d61f33aa5de9aa74a713ee1f5ce600adc74df6b", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.3.1.tgz", "fileCount": 8, "unpackedSize": 231913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVc965Oi57VjhLBlLpDxVWJvuhYB/GOT1KWbDGAI6FyAiBnHsgSOfr4RWmc03mgxB+waO1dtyvr5YGwN6Y2jSWjIQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcjm0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1aQ//cm9pcXyeVBkHXSHvJ+z3c6k5hzGqcgszKBpUfRbHV1eBBE1u\r\n4MZEfKQyV6p/tS5YV+gv0M6Wc5eivN+etUhyjSXexKyWHkn1e5lNVkRmO8Ie\r\nqQrgitONb7PBRNvCeHZHQfPpj0AAJZlCW8Th/FwxNxI5a3gqYHEcZKMOzNtj\r\nNoy92vNca/2T3sV64bRS/5ZZKltQPwLhSowf2RLZgxVbb3ZIzohBdl2/hQVG\r\nkSRxfysPWY3Y06ES5CUgGXs1Si1Ixon3jfl/pKNvfacfhNmhBYXZx9ZTxUf0\r\nqTsmjjYzdMoHH07TJAu1jhNqV5HfUtq1dLGXucQ4/hOkzJm6f8Rwiujn/cRc\r\ns0qNYQFkQnXGEjXLoJxeU9sWmEvcmuseSd9Y94yPZLg4/OWlu92TFaRDMnlq\r\ncvBjNaiyFSk3zzq9+FylRzzYvEAzz6v2OcJOiOAwJAy6OUOS9ZTBOgBYHLfu\r\nUnspbRx1vkUi/8G/wgLciEGIl+QrUb79oQ6ZUyGVGNvpUc1eXrCDT0JTIyFO\r\nPGGLLpRqukNLOK3LWv1+H8ZPlt6W72drpxvYOhOLAlv64csFh3tcm+zNLEGh\r\n68WJNDZgh/NzCWoZpTOFHM/V/cNTmwPH0eJdbjY6X5DUP90PE7+Ejfj3ScA/\r\nWDOW1PFCPsxBeXP4WoJ+DkUjCpFdmcXA71Y=\r\n=tmNu\r\n-----END PGP SIGNATURE-----\r\n", "size": 62020}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.3.2": {"name": "@codemirror/language", "version": "6.3.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-g42uHhOcEMAXjmozGG+rdom5UsbyfMxQFh7AbkeoaNImddL6Xt4cQDL0+JxmG7+as18rUAvZaqzP/TjsciVIrA==", "shasum": "a3d5796d17a2cd3110bac0f5126db67c7e90a0f3", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.3.2.tgz", "fileCount": 8, "unpackedSize": 232418, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDX40+pJrBrTdWj9YVOUBW4PHhWWpkuU3EOLYDFU6ehAiAjTznE5+Vt6m4gnKIAmppJKyWl7493rpifKOFlCYkHzA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnCEQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvYQ/9HxPVopMi+VCN8RR10V7g2Sv0+q4rLYnGPTFYMpuDgx+cTZai\r\nMoqPDshqs7FKmnHJI2I2D6v1k4Ch0HQTnsPWhcNlFcJWRjpy6fNXl4lq6wcY\r\ny3YVYDXhRQCuO0e7yaGxWwuSFbzGYPJnMCAnsjGIqxFTdIumvRHWcIb6gtKx\r\nf9GlA9NP/iBTEeafpNBuzGn7S7lNgrJXfyDAitC03eBBa37V2aU/MfLD3s+9\r\nfVmMEc41d5zooj+fAqNUVzVa61z7nUSmQlCu3hxbxiyRpplkdnrQgZqNSGLK\r\nGqVAPWATk2ivZFRMHsyh0J69HWcEybtUAgzA+8jdwCQ7QuhzRyN7t/79m6dt\r\nltwS9L+R/FmE65HiAWVmFC6VGwR+ZBv3GxBGad/YdKoA5FKGLtfTKeY7+SiW\r\nXISQOw//8e74buaFfOWSkUr+VqOtGXuLNvTIcpwDtMqwec6FZ8VjCgR7vZGs\r\ndeFvharfQn6UBDxcxbtjy6jpRRhHieTeH+69KEt9MKCt/WW215saRLHwVv9K\r\nX6ASlGlxFnZWasiO5KiCD0WEsMc4OBLfwAyNsZuQzX0f0t7HUl5/3yolx2RV\r\n1pkGWpWibryEH74cWc4LDdEU4JJjlMkkkzvYcUJkxk7PLPmhNnaCZ/xNKel8\r\nxaPMxyiFdvQEQ/619K84YIl8FnedKYuUgcY=\r\n=M+4i\r\n-----END PGP SIGNATURE-----\r\n", "size": 62110}, "_hasShrinkwrap": false, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.4.0": {"name": "@codemirror/language", "version": "6.4.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-Wzb7GnNj8vnEtbPWiOy9H0m1fBtE28kepQNGLXekU2EEZv43BF865VKITUn+NoV8OpW6gRtvm29YEhqm46927Q==", "shasum": "803990e0f07bbb619e915651d3a57d143765dbcc", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.4.0.tgz", "fileCount": 8, "unpackedSize": 235012, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCm9WhYbfOp/ufuAFGZuNuDlI2V0MFAXajTmgyVlS9kCwIhAKqpSErOL/uu3jsVeYXleg1rHF70nAyTsnjwgS8roGkZ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwB46ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrffw/9Hq8FpTPFDkw4tts9uZfPmrriVJXNRY0+F/Ly2JRxkL8ogqdO\r\nILwiDULOp0xWjtt4bETbkJe18LL14CxmvZ5V5RA/cLvCPItzgX1ZcW7G4rvB\r\nlEp4wSiWYyHhiW+X6B08P2V0IGxE304jBBgVMEGTHIb6EJisVE8OH5hu28V2\r\nI5oBl0ghJ7S48nS9qpB6KYzSkICgm5pWvJRXge+cJqjVdAQQB4kg2s8R0/un\r\nSLZg8bui55i9qwBf3MerCyO+gCcLtGzKa3QsLL+dHWabTtqZYBY5o0Dq9ofL\r\nau0fLunc5aB2TMlJi+2c6xgDeC42vLsj+rdv7ZYKgm4P3oz6xxDjWktXfKmR\r\n33LyuhcRBHh5QIpDptlFtwA+8wkx65fhuFmJWj0QFRjiDX0wNx7FfKUOB678\r\nhwQPx1O14xuaDy/6VcDfbLEABUz8m8a/ypBH99KLHBBjzCQ4oqpNgqHYwxZa\r\nChK40LRAwDDZWY4DLpZEauxcll5Un4X1eUX9AGHxCWPswXxQ67LPkB1LXzr/\r\n3/DAeTPp6ZTZ5FO2zXDKQEU5hjV36fI+TssCTGdf4C4UjLB9DxXMlVv2nIEG\r\nfJ4ALV8IEic+1Jf0B5CVy/Kc97n77uAlgI+x95lePm0xQX7knu3GL+ONfQBx\r\nHdyII3rDMkIFwbLNnC1wYQNGjnF16U7ZAh4=\r\n=G8+H\r\n-----END PGP SIGNATURE-----\r\n", "size": 62912}, "_hasShrinkwrap": false, "publish_time": 1673535034081, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.5.0": {"name": "@codemirror/language", "version": "6.5.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-dI+dV/u2klIt0Y9kE3TH9vuBidAB3xuuDPofwzvnW8ZKqJnKTbT3EjyV7DeKcmrRgXMhlPTL7AdH1V5KOCYuHQ==", "shasum": "08f4e1599d3c21acb7f78f99aa253c842d308e3c", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.5.0.tgz", "fileCount": 8, "unpackedSize": 239934, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIClkre+l/Rp49+iBb+dyOFvnw6vjC2k5jhU1HFUxSHVPAiEAv/PgUCWYxMYezhw29KMJvItb40X7MeiNZmr73TxeC80="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4onoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP2Q//TJ9UNqEwE4IPUjgx6jETyfB8MasFx/TbikkzDHZPEQatpjMt\r\n/KxMQqR5v5REDu/pC/lHUN94+VVATVRhBFnuMOkqwD4E8dNY4GVzOcQgIGl1\r\nVG77uYSeZ0YnpEoA+MrJX7pL9T+VMMWAvXRD1svh/L2deScSPoKArpEPOukQ\r\n3G6slu8Vj2rmcX9e9L7Kn+UQDPPg/oM7PbXrghCjKkYIpYsBLm2RCxPMM06G\r\n7e7WQFAekXoo4ygFaL0ayoedNLosAB2XNfDk5LvprM5Ratb3A/S3Er367hmx\r\nim28e+6E5THZClOMj9cbUkSbAicOVviVFO9DuAQTmD3AG5vbyVq/r2/QLuD5\r\njLM65R71ulqDwYD+R8wIkc8L4qRJ/VPkXuIAlTMCwCTQ816xP+bdrxSio617\r\nmqaXD6RSn5rmN0ksABYCKMqzHq/5SLHQoHzOR74Jfoc9oMQ7PKgZXx/Wh4aU\r\noTVFC7yEYIYnn3CeGJCvcMtRHyLxW1HRLr6iPWAwSIm/1Sd2iBxDA/UbcsxZ\r\n4BOaLWxnPEJ+T3BY1Ij/15iIp7p9F7hHcpDFNdOiPvjKCx/JMQuhDRg5aZZu\r\nBhUgc9KEK+j9Tvnoz9FZ4VwYrr8O0JeldGj9p489X/VzjlxAP3gSTGJLCYBF\r\n+F5WngaMVahNkPjNV+8Asl1VdGN0ds/r1+0=\r\n=wZ/0\r\n-----END PGP SIGNATURE-----\r\n", "size": 64059}, "_hasShrinkwrap": false, "publish_time": 1675790824657, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.6.0": {"name": "@codemirror/language", "version": "6.6.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-cwUd6lzt3MfNYOobdjf14ZkLbJcnv4WtndYaoBkbor/vF+rCNguMPK0IRtvZJG4dsWiaWPcK8x1VijhvSxnstg==", "shasum": "2204407174a38a68053715c19e28ad61f491779f", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.6.0.tgz", "fileCount": 8, "unpackedSize": 243135, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICg7ITdx/fBAp2ieQGOR4ByHRu2r/aVKOWJdfrhW06BHAiEAvIGO4sJ+O1SYwCwePlLfpaeQZm5/fuRCjKQ/uNmRMNg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6rfdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmplaw/9HmJPOAyOin0xQqg5uV1cGbicjGaul4oPp9umnQyv+3UQZzzF\r\nGgQE3R9bu1fJUZMyPCS0Rysp1EDbgRJ+WDWAt6LK7CJfoYLT6VOy+N9HlqU+\r\ndmwDrE384Sd1jzeciaFOxhm0vEqCKLpO8f/8wiuHK1mhmgF8h0s5dUWcsyXY\r\n8C/SWuWoFxzi2mc8L5lMMvzWDGNymsEOnqFkMAlhtzOvx9C7Z9vhYrK1VCDX\r\n9t06ZvBkhtS2sKvGXuJabE93jyBuotUOkOXkW2Vp6Dw4U8LGRbHJ+PHsjmAu\r\nbJLlA3ZL4dHoyf5xpK8Q7UO5wQ/po76A7HlrZvKBLgdE71H04wDW4fZ7c8c8\r\nwQ7cpmeUBpKrPj+NvfRCO5hQ/wkDGeASQM1dphchliMsqJk2lHrbIeJl5ESw\r\nm5waH/uqM13gc7HJ5FGBRnEmblbZq98bBTwkgMdJShGaqdpIPNIJ+O1uN4P1\r\ntvYGhGeLAK3DF/Qbpnic4pusSDT2EdSDvzsPRYBprmzP+blMwTpAeX1XN+CT\r\nigYOu28Bv93ctGosNg8qAQz54u5LJXTDU7OYuZJyV3fxjKdOlAWhuGIegQEF\r\n+1iqtwgrNdkg+2sTFXnH4G0/vOBzDHe0WIO+CfTlYekJGTus6asMohzDUp9i\r\niS8r+eRkKBQsHi4WBiYNMIgvJdF5MZFIZs4=\r\n=Hy1/\r\n-----END PGP SIGNATURE-----\r\n", "size": 64872}, "_hasShrinkwrap": false, "publish_time": 1676326877398, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.7.0": {"name": "@codemirror/language", "version": "6.7.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.5", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-4SMwe6Fwn57klCUsVN0y4/h/iWT+XIXFEmop2lIHHuWO0ubjCrF3suqSZLyOQlznxkNnNbOOfKe5HQbQGCAmTg==", "shasum": "9f1c8923e3234376a40f3392e4a0451e9b4adb8f", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.7.0.tgz", "fileCount": 8, "unpackedSize": 244181, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC2rJZC3CTkTQ1W5o0geqT44IzaHLs1cgtAyXOZl+UwwAiEA/QgUpe8SxptRDeL15y7NakItaS4CXQ0YrEKO6GJCrng="}], "size": 65140}, "_hasShrinkwrap": false, "publish_time": 1684504357194, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.8.0": {"name": "@codemirror/language", "version": "6.8.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-r1paAyWOZkfY0RaYEZj3Kul+MiQTEbDvYqf8gPGaRvNneHXCmfSaAVFjwRUPlgxS8yflMxw2CTu6uCMp8R8A2g==", "shasum": "f2d7eea6b338c25593d800f2293b062d9f9856db", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.8.0.tgz", "fileCount": 9, "unpackedSize": 287426, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1EO476TL8LPP2n8tTbXQiJI6vCj9X5nuCDb4XcwQ7nwIgbyODEkiV2X2CyINSKcCw10At02KEf2tyDPTBNGLDuU4="}], "size": 76982}, "_hasShrinkwrap": false, "publish_time": 1686549787136, "_source_registry_name": "default", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}, "6.9.0": {"name": "@codemirror/language", "version": "6.9.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.0.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-nFu311/0ne/qGuGCL3oKuktBgzVOaxCHZPZv1tLSZkNjPYxxvkjSbzno3MlErG2tgw1Yw1yF8BxMCegeMXqpiw==", "shasum": "c471ce1fdb9b1577f312bb68ef54fb4b76f7a420", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.9.0.tgz", "fileCount": 9, "unpackedSize": 289861, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBr81Ax6SA9gBAShtGvbMOb0wyeEm3VuWPgihm9vTnxPAiBIfO9r2BAA/Z5ExHPLcSblrvRBoatlIGUP+muxIF+Ivw=="}], "size": 77665}, "_hasShrinkwrap": false, "publish_time": 1692202106924, "_source_registry_name": "default"}, "6.9.1": {"name": "@codemirror/language", "version": "6.9.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-lWRP3Y9IUdOms6DXuBpoWwjkR7yRmnS0hKYCbSfPz9v6Em1A1UCRujAkDiCrdYfs1Z0Eu4dGtwovNPStIfkgNA==", "shasum": "97e2c3e44cf4ff152add865ed7ecec73868446a4", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.9.1.tgz", "fileCount": 9, "unpackedSize": 291044, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFdqkvj6wXBTllR9U2GYY+Gada74+wjKI3tjIbImenFAiEAtsQEV2H9aMjohcgKsBPELBPdC0skAfhbQi9Vmv/yb2w="}], "size": 78043}, "_hasShrinkwrap": false, "publish_time": 1695196762928, "_source_registry_name": "default"}, "6.9.2": {"name": "@codemirror/language", "version": "6.9.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-QGTQXSpAKDIzaSE96zNK1UfIUhPgkT1CLjh1N5qVzZuxgsEOhz5RqaN8QCIdyOQklGLx3MgHd9YrE3X3+Pl1ow==", "shasum": "1fba6e0eab995afda683d4e6c556365a982b5258", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.9.2.tgz", "fileCount": 9, "unpackedSize": 292009, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFPMKP6iPzc7+1XUPdBft9gO2r3TFTReadztO2XV4ixwIhAJfPWuaaYh8YFMJ4SH+PrpQwPIdEJuFw9P4WdDGKO1rY"}], "size": 78272}, "_hasShrinkwrap": false, "publish_time": 1698139180058, "_source_registry_name": "default"}, "6.9.3": {"name": "@codemirror/language", "version": "6.9.3", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-qq48pYzoi6ldYWV/52+Z9Ou6QouVI+8YwvxFbUypI33NbjG2UeRHKENRyhwljTTiOqjQ33FjyZj6EREQ9apAOQ==", "shasum": "1c127dc43e025d4c9b1ba1b79f4b1ba081d5aeaa", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.9.3.tgz", "fileCount": 9, "unpackedSize": 292604, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGd5tDZYkTpEmP8G1hUTHiVlLVr/GbjBFvVFLoAUbodKAiEAuesAJAiZ613sVv+tSbIZomL5PC1cYhqbE0rAVZvSDhc="}], "size": 78469}, "_hasShrinkwrap": false, "publish_time": 1701078573817, "_source_registry_name": "default"}, "6.10.0": {"name": "@codemirror/language", "version": "6.10.0", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-2vaNn9aPGCRFKWcHPFksctzJ8yS5p7YoaT+jHpc0UGKzNuAIx4qy6R5wiqbP+heEEdyaABA582mNqSHzSoYdmg==", "shasum": "2d0e818716825ee2ed0dacd04595eaa61bae8f23", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.0.tgz", "fileCount": 9, "unpackedSize": 302375, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWz38kbJIkf5ARM+xICQVzJUYUFp72qA73l7nnsbLLXwIgWDmi+XnWkYqGAwIOP96ThJ9Dn75ShnRTY24FZrfeyWg="}], "size": 81172}, "_hasShrinkwrap": false, "publish_time": 1703784732380, "_source_registry_name": "default"}, "6.10.1": {"name": "@codemirror/language", "version": "6.10.1", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-5GrXzrhq6k+gL5fjkAwt90nYDmjlzTIJV8THnxNFtNKWotMIlzzN+CpqxqwXOECnUdOndmSeWntVrVcv5axWRQ==", "shasum": "428c932a158cb75942387acfe513c1ece1090b05", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.1.tgz", "fileCount": 9, "unpackedSize": 303189, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJS3oNPstYlnnUHD5tgs0ZpOOaWsxUvQ1NhQpy+HYtTAiBOe0f+BNxmq03r5iAjTCJnoEAbuT2dbxYH61LIA863EQ=="}], "size": 81380}, "_hasShrinkwrap": false, "publish_time": 1706873530730, "_source_registry_name": "default"}, "6.10.2": {"name": "@codemirror/language", "version": "6.10.2", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-kgbTYTo0Au6dCSc/TFy7fK3fpJmgHDv1sG1KNQKJXVi+xBTEeBPY/M30YXiU6mMXeH+YIDLsbrT4ZwNRdtF+SA==", "shasum": "4056dc219619627ffe995832eeb09cea6060be61", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.2.tgz", "fileCount": 9, "unpackedSize": 303633, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDutqFYHYhQgLPdbrmLG/hsew8dPuIIMKz7vSeUQq+h3gIhANViRRdUWZ3gQmuY+FXM1s1QwedKW9yMINGRZi4xETd/"}], "size": 81471}, "_hasShrinkwrap": false, "publish_time": 1717411982068, "_source_registry_name": "default"}, "6.10.3": {"name": "@codemirror/language", "version": "6.10.3", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-kDqEU5sCP55Oabl6E7m5N+vZRoc0iWqgDVhEKifcHzPzjqCegcO4amfrYVL9PmPZpl4G0yjkpTpUO/Ui8CzO8A==", "shasum": "eb25fc5ade19032e7bf1dcaa957804e5f1660585", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.3.tgz", "fileCount": 9, "unpackedSize": 304206, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8ZRVLzwncn+XJatotWhV+VvYixDlchMeZ4Fr2DKXWnQIhALrJdQdAcH8fS0Iz1fId6wDwh/lHj39cG2I4+8sT6EWm"}], "size": 81631}, "_hasShrinkwrap": false, "publish_time": 1726762147342, "_source_registry_name": "default"}, "6.10.4": {"name": "@codemirror/language", "version": "6.10.4", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-qjt7Wn/nxGuI278GYVlqE5V93Xn8ZQwzqZtgS0FaWr7K2yWgd5/FlBNqNi4jtUvBVvWJzAGfnggIlpyjTOaF4A==", "shasum": "8791824f06eaa428d9c0291902b2461c005c5ad1", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.4.tgz", "fileCount": 9, "unpackedSize": 304812, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSlQimlt3Y25FeJeUcBOGO05B6+dtd6Xck9MZ3h6GJDAiEAgNNNdiHQ1pEPsDbdknjMEbNtzhdWQsObtae7xxTXyf4="}], "size": 81806}, "_hasShrinkwrap": false, "publish_time": 1732473120866, "_source_registry_name": "default"}, "6.10.5": {"name": "@codemirror/language", "version": "6.10.5", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-sECWJyNmwqw6mSO6Qf0IVPHwhEnuYbqHBZaaIbdcXtZ6Y2r5vU/dxgC7K1ppWaJFy8XGtTBC0Pd60qI7NfJreQ==", "shasum": "69ce17e3601b2667706a7a2056be4df40d102d8e", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.5.tgz", "fileCount": 9, "unpackedSize": 305007, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAb2TK67dB2mteJ6UXB1EI1w7MsKWqInsCLItpblzmZEAiEAjSIf+dFKJWnxP9Y8bBpGOVtg3E4J4B/ozFAqTAfStFs="}], "size": 81871}, "_hasShrinkwrap": false, "publish_time": 1732715141542, "_source_registry_name": "default"}, "6.10.6": {"name": "@codemirror/language", "version": "6.10.6", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-KrsbdCnxEztLVbB5PycWXFxas4EOyk/fPAfruSOnDDppevQgid2XZ+KbJ9u+fDikP/e7MW7HPBTvTb8JlZK9vA==", "shasum": "3770aa55fce575b45b1037b390b576907f0061c7", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.6.tgz", "fileCount": 9, "unpackedSize": 305294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDMhy/cy8dnm/ENU5YIER98BF5MQ+CCwktHSsNSiDyfAiEA8nh8kbBwbVOCiPpYiLDrfKcvOQL8tuOdegKvMlNCgEU="}], "size": 81928}, "_hasShrinkwrap": false, "publish_time": 1732865587884, "_source_registry_name": "default"}, "6.10.7": {"name": "@codemirror/language", "version": "6.10.7", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-aOswhVOLYhMNeqykt4P7+ukQSpGL0ynZYaEyFDVHE7fl2xgluU3yuE9MdgYNfw6EmaNidoFMIQ2iTh1ADrnT6A==", "shasum": "415ba3bb983416daa98084c010f4db59db45920e", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.7.tgz", "fileCount": 9, "unpackedSize": 305694, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+eAm2EwNIh8kAa+VInr/UaIrvT71/Z7XeY2NJXWj+IgIhAO5Gk7N8rDmBI0PJF9TzuzX3bKvGKt1lTDHXKZIm4OF9"}], "size": 82024}, "_hasShrinkwrap": false, "publish_time": 1734423770872, "_source_registry_name": "default"}, "6.10.8": {"name": "@codemirror/language", "version": "6.10.8", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0", "@lezer/javascript": "^1.0.0"}, "directories": {}, "dist": {"integrity": "sha512-wcP8XPPhDH2vTqf181U8MbZnW+tDyPYy0UzVOa+oHORjyT+mhhom9vBd7dApJwoDz9Nb/a8kHjJIsuA/t8vNFw==", "shasum": "3e3a346a2b0a8cf63ee1cfe03349eb1965dce5f9", "tarball": "https://registry.npmmirror.com/@codemirror/language/-/language-6.10.8.tgz", "fileCount": 9, "unpackedSize": 305943, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICnoX1B5Uju4eiH9FrqAmqjJqe7SN01V6XZYSgII0a5eAiAbiJ1dyWveZosI4UONb7G2ZRkzmZeLtuwhi3Z3agBzow=="}], "size": 82095}, "_hasShrinkwrap": false, "publish_time": 1734972677669, "_source_registry_name": "default"}}, "_source_registry_name": "default"}