<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - 页面未找到</title>
    <!-- 添加noindex标签，防止搜索引擎索引404页面 -->
    <meta name="robots" content="noindex, nofollow">
    <!-- 确保不会有任何方式链接回原网站 -->
    <meta name="referrer" content="no-referrer">
    <style>
        :root {
            --primary-color: #4e54c8;
            --secondary-color: #8f94fb;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-color: #f8f9fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            color: var(--text-color);
        }

        .container {
            position: relative;
            width: 100%;
            max-width: 800px;
            padding: 40px;
            z-index: 10;
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            text-align: center;
            transform-style: preserve-3d;
            perspective: 1000px;
            transition: transform 0.5s ease;
        }

        .card:hover {
            transform: translateY(-10px) rotateX(5deg);
        }

        h1 {
            font-size: 8rem;
            font-weight: 900;
            margin-bottom: 0;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: translateX(-100%);
        }

        .btn:hover::before {
            animation: shine 1.5s infinite;
        }

        @keyframes shine {
            100% {
                transform: translateX(100%);
            }
        }

        /* 浮动粒子背景 */
        .area {
            position: absolute;
            width: 100%;
            height: 100vh;
            top: 0;
            left: 0;
        }

        .circles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .circles li {
            position: absolute;
            display: block;
            list-style: none;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            animation: animate 25s linear infinite;
            bottom: -150px;
            border-radius: 50%;
        }

        .circles li:nth-child(1) {
            left: 25%;
            width: 80px;
            height: 80px;
            animation-delay: 0s;
        }

        .circles li:nth-child(2) {
            left: 10%;
            width: 20px;
            height: 20px;
            animation-delay: 2s;
            animation-duration: 12s;
        }

        .circles li:nth-child(3) {
            left: 70%;
            width: 20px;
            height: 20px;
            animation-delay: 4s;
        }

        .circles li:nth-child(4) {
            left: 40%;
            width: 60px;
            height: 60px;
            animation-delay: 0s;
            animation-duration: 18s;
        }

        .circles li:nth-child(5) {
            left: 65%;
            width: 20px;
            height: 20px;
            animation-delay: 0s;
        }

        .circles li:nth-child(6) {
            left: 75%;
            width: 110px;
            height: 110px;
            animation-delay: 3s;
        }

        .circles li:nth-child(7) {
            left: 35%;
            width: 150px;
            height: 150px;
            animation-delay: 7s;
        }

        .circles li:nth-child(8) {
            left: 50%;
            width: 25px;
            height: 25px;
            animation-delay: 15s;
            animation-duration: 45s;
        }

        .circles li:nth-child(9) {
            left: 20%;
            width: 15px;
            height: 15px;
            animation-delay: 2s;
            animation-duration: 35s;
        }

        .circles li:nth-child(10) {
            left: 85%;
            width: 150px;
            height: 150px;
            animation-delay: 0s;
            animation-duration: 11s;
        }

        @keyframes animate {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 1;
                border-radius: 0;
            }
            100% {
                transform: translateY(-1000px) rotate(720deg);
                opacity: 0;
                border-radius: 50%;
            }
        }

        /* 宇航员动画 */
        .astronaut {
            width: 150px;
            height: 150px;
            margin: 0 auto 30px;
            position: relative;
            animation: float 6s ease-in-out infinite;
            cursor: pointer;
            transform-origin: center;
            transition: transform 0.3s ease;
        }

        .astronaut:hover {
            transform: scale(1.1) rotate(5deg);
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .card {
                padding: 30px;
            }

            h1 {
                font-size: 5rem;
            }

            h2 {
                font-size: 1.5rem;
            }

            p {
                font-size: 1rem;
            }
        }

        /* 提示文字 */
        .hint {
            margin-top: 20px;
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
            font-style: italic;
        }

        /* 星星样式 */
        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            filter: blur(1px);
            animation: twinkle 1.5s infinite alternate;
            pointer-events: none;
            z-index: 5;
        }

        @keyframes twinkle {
            0% {
                opacity: 0.2;
                transform: scale(0.8);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 重力球 */
        .ball {
            width: 30px;
            height: 30px;
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
            border-radius: 50%;
            position: fixed;
            top: 20px;
            left: 20px;
            box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
            cursor: grab;
            z-index: 100;
        }

        .ball:active {
            cursor: grabbing;
        }

        /* UFO飞碟 */
        .ufo {
            position: absolute;
            width: 60px;
            height: 30px;
            top: 20px;
            right: 20px;
            cursor: grab;
            z-index: 100;
            transition: transform 0.3s ease;
        }

        .ufo:hover {
            transform: translateY(-5px);
        }

        .ufo:active {
            cursor: grabbing;
        }

        .ufo-body {
            position: absolute;
            width: 60px;
            height: 20px;
            background: linear-gradient(to right, #8f94fb, #4e54c8);
            border-radius: 50%;
            top: 10px;
            box-shadow: 0 0 15px rgba(78, 84, 200, 0.6);
        }

        .ufo-body::before {
            content: '';
            position: absolute;
            width: 30px;
            height: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            top: -5px;
            left: 15px;
        }

        .ufo-light {
            position: absolute;
            bottom: -10px;
            left: 25px;
            width: 10px;
            height: 20px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
            border-radius: 50% 50% 0 0;
            animation: ufo-light 2s infinite alternate;
        }

        @keyframes ufo-light {
            0% {
                opacity: 0.3;
                height: 15px;
            }
            100% {
                opacity: 0.8;
                height: 25px;
            }
        }
    </style>
</head>
<body>
    <!-- 浮动粒子背景 -->
    <div class="area">
        <ul class="circles" id="starsContainer">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
    </div>

    <!-- 重力球 - 放在左上角 -->
    <div class="ball" id="ball"></div>

    <div class="container">
        <div class="card">
            <div class="astronaut" id="astronaut">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="#4e54c8"/>
                    <path d="M9 9H15M9 12H15M9 15H12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="white" stroke-width="1.5"/>
                </svg>
            </div>
            <h1>404</h1>
            <h2>页面未找到</h2>
            <p>您访问的页面不存在或已被移除。</p>

            <!-- 404文字下方的提示 -->
            <div class="hint">试试与页面互动，看看会发生什么...</div>

            <!-- UFO飞碟 - 放在卡片右上角 -->
            <div class="ufo" id="ufo">
                <div class="ufo-body"></div>
                <div class="ufo-light"></div>
            </div>
        </div>
    </div>

    <script>
        // 宇航员点击动画
        const astronaut = document.getElementById('astronaut');
        astronaut.addEventListener('click', function() {
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'float 6s ease-in-out infinite';
            }, 10);

            // 添加旋转动画
            this.style.transform = 'rotate(360deg) scale(1.2)';
            setTimeout(() => {
                this.style.transform = '';
            }, 1000);
        });

        // 星星生成功能 - 点击页面任何地方生成星星
        document.addEventListener('click', function(e) {
            // 不要在卡片内生成星星
            if (e.target.closest('.card')) return;

            // 创建一个新的星星
            const star = document.createElement('div');
            star.classList.add('star');

            // 随机大小
            const size = Math.random() * 4 + 2;
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;

            // 位置 (相对于点击位置)
            star.style.left = `${e.clientX}px`;
            star.style.top = `${e.clientY}px`;

            // 随机动画延迟
            star.style.animationDelay = `${Math.random() * 2}s`;

            // 添加到body
            document.body.appendChild(star);

            // 5秒后移除星星
            setTimeout(() => {
                star.remove();
            }, 5000);
        });

        // 重力球功能
        const ball = document.getElementById('ball');

        let isDragging = false;
        let velocity = { x: 0, y: 0 };
        let position = { x: ball.offsetLeft, y: ball.offsetTop };
        let lastPosition = { x: position.x, y: position.y };

        // 重力和摩擦系数
        const gravity = 0.5;
        const friction = 0.97;

        // 鼠标按下
        ball.addEventListener('mousedown', function(e) {
            isDragging = true;
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            e.preventDefault();
        });

        // 触摸开始
        ball.addEventListener('touchstart', function(e) {
            isDragging = true;
            document.addEventListener('touchmove', onTouchMove);
            document.addEventListener('touchend', onTouchEnd);
            e.preventDefault();
        });

        function onMouseMove(e) {
            if (isDragging) {
                position.x = e.clientX - ball.offsetWidth / 2;
                position.y = e.clientY - ball.offsetHeight / 2;

                // 限制在窗口内
                position.x = Math.max(0, Math.min(position.x, window.innerWidth - ball.offsetWidth));
                position.y = Math.max(0, Math.min(position.y, window.innerHeight - ball.offsetHeight));

                ball.style.left = `${position.x}px`;
                ball.style.top = `${position.y}px`;
            }
        }

        function onTouchMove(e) {
            if (isDragging && e.touches.length > 0) {
                position.x = e.touches[0].clientX - ball.offsetWidth / 2;
                position.y = e.touches[0].clientY - ball.offsetHeight / 2;

                // 限制在窗口内
                position.x = Math.max(0, Math.min(position.x, window.innerWidth - ball.offsetWidth));
                position.y = Math.max(0, Math.min(position.y, window.innerHeight - ball.offsetHeight));

                ball.style.left = `${position.x}px`;
                ball.style.top = `${position.y}px`;
            }
        }

        function onMouseUp() {
            isDragging = false;
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);

            // 计算释放时的速度
            velocity.x = position.x - lastPosition.x;
            velocity.y = position.y - lastPosition.y;

            // 开始物理模拟
            requestAnimationFrame(updateBall);
        }

        function onTouchEnd() {
            isDragging = false;
            document.removeEventListener('touchmove', onTouchMove);
            document.removeEventListener('touchend', onTouchEnd);

            // 计算释放时的速度
            velocity.x = position.x - lastPosition.x;
            velocity.y = position.y - lastPosition.y;

            // 开始物理模拟
            requestAnimationFrame(updateBall);
        }

        function updateBall() {
            if (!isDragging) {
                // 保存上一帧的位置
                lastPosition.x = position.x;
                lastPosition.y = position.y;

                // 应用重力
                velocity.y += gravity;

                // 应用摩擦力
                velocity.x *= friction;
                velocity.y *= friction;

                // 更新位置
                position.x += velocity.x;
                position.y += velocity.y;

                // 碰撞检测 - 窗口边界
                if (position.x < 0) {
                    position.x = 0;
                    velocity.x *= -0.7; // 反弹，损失一些能量
                } else if (position.x > window.innerWidth - ball.offsetWidth) {
                    position.x = window.innerWidth - ball.offsetWidth;
                    velocity.x *= -0.7;
                }

                if (position.y < 0) {
                    position.y = 0;
                    velocity.y *= -0.7;
                } else if (position.y > window.innerHeight - ball.offsetHeight) {
                    position.y = window.innerHeight - ball.offsetHeight;
                    velocity.y *= -0.7;
                }

                // 更新球的位置
                ball.style.left = `${position.x}px`;
                ball.style.top = `${position.y}px`;

                // 如果还在移动，继续更新
                if (Math.abs(velocity.x) > 0.1 || Math.abs(velocity.y) > 0.1) {
                    requestAnimationFrame(updateBall);
                }
            }
        }

        // UFO拖动功能
        const ufo = document.getElementById('ufo');

        let ufoIsDragging = false;
        let ufoPosition = { x: ufo.offsetLeft, y: ufo.offsetTop };

        // 鼠标按下
        ufo.addEventListener('mousedown', function(e) {
            ufoIsDragging = true;
            document.addEventListener('mousemove', onUfoMouseMove);
            document.addEventListener('mouseup', onUfoMouseUp);
            e.preventDefault();
            e.stopPropagation(); // 防止触发星星生成
        });

        // 触摸开始
        ufo.addEventListener('touchstart', function(e) {
            ufoIsDragging = true;
            document.addEventListener('touchmove', onUfoTouchMove);
            document.addEventListener('touchend', onUfoTouchEnd);
            e.preventDefault();
        });

        function onUfoMouseMove(e) {
            if (ufoIsDragging) {
                const rect = document.querySelector('.card').getBoundingClientRect();
                ufoPosition.x = e.clientX - rect.left - ufo.offsetWidth / 2;
                ufoPosition.y = e.clientY - rect.top - ufo.offsetHeight / 2;

                // 限制在卡片内
                ufoPosition.x = Math.max(0, Math.min(ufoPosition.x, rect.width - ufo.offsetWidth));
                ufoPosition.y = Math.max(0, Math.min(ufoPosition.y, rect.height - ufo.offsetHeight));

                // 更新UFO位置
                ufo.style.left = `${ufoPosition.x}px`;
                ufo.style.top = `${ufoPosition.y}px`;

                // 添加倾斜效果 - 根据移动方向
                const tiltX = (e.movementX * 2);
                ufo.style.transform = `rotate(${tiltX}deg)`;
            }
        }

        function onUfoTouchMove(e) {
            if (ufoIsDragging && e.touches.length > 0) {
                const rect = document.querySelector('.card').getBoundingClientRect();
                ufoPosition.x = e.touches[0].clientX - rect.left - ufo.offsetWidth / 2;
                ufoPosition.y = e.touches[0].clientY - rect.top - ufo.offsetHeight / 2;

                // 限制在卡片内
                ufoPosition.x = Math.max(0, Math.min(ufoPosition.x, rect.width - ufo.offsetWidth));
                ufoPosition.y = Math.max(0, Math.min(ufoPosition.y, rect.height - ufo.offsetHeight));

                // 更新UFO位置
                ufo.style.left = `${ufoPosition.x}px`;
                ufo.style.top = `${ufoPosition.y}px`;
            }
        }

        function onUfoMouseUp() {
            ufoIsDragging = false;
            document.removeEventListener('mousemove', onUfoMouseMove);
            document.removeEventListener('mouseup', onUfoMouseUp);

            // 恢复UFO的旋转
            setTimeout(() => {
                ufo.style.transform = 'rotate(0deg)';
            }, 300);
        }

        function onUfoTouchEnd() {
            ufoIsDragging = false;
            document.removeEventListener('touchmove', onUfoTouchMove);
            document.removeEventListener('touchend', onUfoTouchEnd);

            // 恢复UFO的旋转
            setTimeout(() => {
                ufo.style.transform = 'rotate(0deg)';
            }, 300);
        }

        // 粒子交互效果
        document.addEventListener('mousemove', function(e) {
            const circles = document.querySelectorAll('.circles li');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            circles.forEach((circle, index) => {
                const factor = (index % 5) * 0.1;
                const offsetX = (mouseX - 0.5) * 50 * factor;
                const offsetY = (mouseY - 0.5) * 50 * factor;

                circle.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
            });
        });
    </script>
</body>
</html>
