<template>
  <div class="user-list-container">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="showAddUserDialog">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button @click="router.push('/')" type="primary">
          <el-icon><Back /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <el-card class="user-table">
      <el-table
        v-loading="loading"
        :data="users"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" min-width="120">
          <template #default="scope">
            <div class="username-cell">
              <el-icon><User /></el-icon>
              <span>{{ scope.row.username }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="email" label="邮箱" width="200" /> -->
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_admin" label="管理员" width="80" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.is_admin"
              :disabled="!currentUser.is_admin || scope.row.id === currentUser.id"
              @change="handleAdminChange(scope.row)"
              :class="{ 'is-admin': scope.row.is_admin }"
            />
          </template>
        </el-table-column>
        <el-table-column prop="has_car_permission" label="赛车权限" width="80" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.has_car_permission"
              :disabled="!currentUser.is_admin"
              @change="handlePermissionChange(scope.row)"
              :class="{ 'has-permission': scope.row.has_car_permission }"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              type="warning"
              size="small"
              :disabled="!currentUser.is_admin || scope.row.id === currentUser.id"
              :loading="scope.row.resetting"
              @click="handleResetPassword(scope.row)"
            >
              重置密码
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增用户对话框 -->
    <el-dialog
      v-model="addUserDialogVisible"
      title="新增用户"
      width="400px"
    >
      <el-form
        ref="addUserFormRef"
        :model="addUserForm"
        :rules="addUserRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="addUserForm.username" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="addUserForm.email" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addUserDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddUser" :loading="addUserLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, updateUserPermission, updateAdminStatus } from '@/api/user'
import { Back, User, Plus } from '@element-plus/icons-vue'
import { register, updatePassword } from '@/api/auth'

const router = useRouter()
const loading = ref(false)
const users = ref([])
const currentUser = ref({})

// 新增用户相关
const addUserDialogVisible = ref(false)
const addUserLoading = ref(false)
const addUserFormRef = ref(null)
const addUserForm = ref({
  username: '',
  email: ''
})

// 表单验证规则
const addUserRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] }
  ]
}

// 显示新增用户对话框
const showAddUserDialog = () => {
  addUserForm.value = {
    username: '',
    email: ''
  }
  addUserDialogVisible.value = true
}

// 处理新增用户
const handleAddUser = async () => {
  if (!addUserFormRef.value) return
  
  try {
    await addUserFormRef.value.validate()
    addUserLoading.value = true
    
    // 使用注册接口，设置默认密码
    await register({
      username: addUserForm.value.username,
      email: addUserForm.value.email || null,  // 如果没有填写邮箱，则传 null
      password: '123456'  // 设置默认密码
    })
    
    ElMessage.success('用户创建成功，默认密码为: 123456')
    addUserDialogVisible.value = false
    fetchUsers()  // 刷新用户列表
  } catch (error) {
    console.error('Failed to add user:', error)
    ElMessage.error(error.response?.data?.message || '创建用户失败')
  } finally {
    addUserLoading.value = false
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    const response = await getUserList()
    if (response.success) {
      users.value = response.data
      // 找到当前用户（假设是已登录用户）
      const token = localStorage.getItem('token')
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]))
        currentUser.value = users.value.find(user => user.id === payload.id) || {}
      }
    }
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理权限变更
const handlePermissionChange = async (user) => {
  try {
    const response = await updateUserPermission(user.id, {
      has_car_permission: user.has_car_permission
    })
    if (response.success) {
      ElMessage.success('权限更新成功')
    } else {
      throw new Error('权限更新失败')
    }
  } catch (error) {
    console.error('Failed to update permission:', error)
    ElMessage.error('权限更新失败')
    // 回滚状态
    user.has_car_permission = !user.has_car_permission
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 处理管理员状态变更
const handleAdminChange = async (user) => {
  try {
    // 如果要取消管理员权限，先确认
    if (!user.is_admin) {
      await ElMessageBox.confirm(
        '确定要取消该用户的管理员权限吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    const response = await updateAdminStatus(user.id, {
      is_admin: user.is_admin
    })

    if (response.success) {
      ElMessage.success(`${user.is_admin ? '设置' : '取消'}管理员成功`)
    } else {
      throw new Error('更新失败')
    }
  } catch (error) {
    console.error('Failed to update admin status:', error)
    ElMessage.error('更新管理员状态失败')
    // 回滚状态
    user.is_admin = !user.is_admin
  }
}

// 处理重置密码
const handleResetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${user.username} 的密码吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 添加重置中状态
    user.resetting = true

    await updatePassword(
      user.id,
      {
        new_password: '123456'  // 默认密码
      }
    )

    ElMessage.success('密码已重置为: 123456')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to reset password:', error)
      ElMessage.error('重置密码失败')
    }
  } finally {
    user.resetting = false
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-table {
  margin-top: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-switch) {
  margin-right: 8px;
}

.username-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-switch.is-admin) {
  --el-switch-on-color: #e6a23c;
}

:deep(.el-switch.has-permission) {
  --el-switch-on-color: #67c23a;
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 6px;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table--border) {
  border-radius: 8px;
  overflow: hidden;
}

.header-actions {
  display: flex;
  gap: 12px;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 